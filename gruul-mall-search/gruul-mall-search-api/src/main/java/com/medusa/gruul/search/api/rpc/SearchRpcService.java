package com.medusa.gruul.search.api.rpc;

import com.medusa.gruul.search.api.model.ProductActivityBind;
import com.medusa.gruul.search.api.model.ProductActivityUnbind;
import com.medusa.gruul.search.api.model.dto.ProductBrandDTO;

import javax.validation.Valid;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2023/3/22
 */
public interface SearchRpcService {

    /**
     * 商品活动绑定
     *
     * @param bindParam 绑定参数
     */
    void activityBind(@Valid ProductActivityBind bindParam);

    /**
     * 批量 商品活动解绑
     *
     * @param unbindParams 解绑参数
     */
    void activityUnbind(@Valid Set<ProductActivityUnbind> unbindParams);

    /**
     *  根据品牌名和分类id查询品牌，没有就创建
     * @param productBrandDTO 品牌DTO
     * @return
     */
     Long checkBrandByName(@Valid ProductBrandDTO productBrandDTO);
}
