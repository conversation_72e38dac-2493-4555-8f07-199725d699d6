package com.medusa.gruul.search.api.model.dto;

import com.medusa.gruul.search.api.enums.BrandStatus;
import com.medusa.gruul.search.api.mp.entity.SearchBrand;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductBrandDTO {

    /**
     * 品牌id
     */
    private Long id;


    /**
     * 品牌名称
     */
    @Size(max = 30)
    @NotBlank(message = "品牌名称不能为空")
    private String brandName;

    /**
     * 品牌描述
     */
    private String brandDesc;

    /**
     * 品牌logo
     */
    @NotBlank(message = "品牌logo不能为空")
    private String brandLogo;


    /**
     * 检索首字母
     */
    @Size(max = 1)
    @NotBlank(message = "检索首字母不能为空")
    @Pattern(regexp = "^[A-Z]+$")
    private String searchInitials;

    /**
     * 上级类目/上级分类
     */
    @NotNull
    private Long parentCategoryId;

    /**
     * 关注人数
     */
    private Integer followers;

    /**
     * 状态(默认上架，0--下架 1--上架)
     */
    private BrandStatus status;

    /**
     * 排序
     */
    @NotNull
    private Integer sort;

    public SearchBrand saveBrand(Boolean update) {
        SearchBrand brand = new SearchBrand();
        brand.setBrandName(brandName)
                .setBrandDesc(brandDesc)
                .setBrandLogo(brandLogo)
                .setSearchInitials(searchInitials)
                .setParentCategoryId(parentCategoryId)
                .setStatus(update ? status : BrandStatus.SELL_ON)
                .setFollowers(followers)
                .setSort(sort)
                .setId(id);
        return brand;
    }
}
