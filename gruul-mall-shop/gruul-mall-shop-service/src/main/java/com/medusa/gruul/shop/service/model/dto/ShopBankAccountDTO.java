package com.medusa.gruul.shop.service.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * date 2022/4/15
 */
@Getter
@Setter
@ToString
public class ShopBankAccountDTO {
    /**
     * 收款人
     */
    @NotBlank
    private String payee;

    /**
     * 银行名称
     */
    @NotBlank
    private String bankName;

    /**
     * 银行账号
     */
    @NotBlank
    private String bankAccount;

    /**
     * 开户行
     */
    @NotBlank
    private String openAccountBank;
}
