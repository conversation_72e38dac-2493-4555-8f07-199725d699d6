package com.medusa.gruul.shop.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.mp.model.TenantConst;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.enums.ShopRabbit;
import com.medusa.gruul.shop.api.model.dto.ShopInfoDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.model.enums.ShopError;
import com.medusa.gruul.shop.service.mp.service.IShopService;
import com.medusa.gruul.shop.service.properties.ShopConfigurationProperties;
import com.medusa.gruul.shop.service.service.ShopInfoService;
import com.medusa.gruul.shop.service.service.addon.ShopAddonSupporter;
import com.medusa.gruul.shop.service.util.DistanceUtils;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import com.medusa.gruul.storage.api.vo.ProductStatisticsVO;
import com.vividsolutions.jts.geom.Point;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.domain.Sort;
import org.springframework.data.geo.Circle;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * date 2022-05-26 11:28
 */
@Service
@RequiredArgsConstructor
public class ShopInfoServiceImpl implements ShopInfoService {

    private final RabbitTemplate rabbitTemplate;
    private final IShopService shopService;
    private final StorageRpcService storageRpcService;
    private final Executor shopExecutor;
    private final ShopConfigurationProperties shopConfigurationProperties;

    private final ShopAddonSupporter shopAddonSupporter;


    /**
     * 店铺设置信息修改 并入redis
     *
     * @param shopInfo 店铺修改信息
     */
    @Override
    public void updateShopInfo(ShopInfoDTO shopInfo) {
        Long shopId = ISecurity.userMust().getShopId();
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw ShopError.SHOP_NOT_EXIST.exception();
        }
        boolean success = RedisUtil.doubleDeletion(
                () -> shopService.lambdaUpdate()
                        .set(Shop::getLogo, shopInfo.getLogo())
                        .set(Shop::getHeadBackground, shopInfo.getHeadBackground())
                        .set(Shop::getCompanyName, shopInfo.getCompanyName())
                        .set(Shop::getStart, shopInfo.getStart())
                        .set(Shop::getEnd, shopInfo.getEnd())
                        .set(Shop::getName, shopInfo.getName())
                        .set(Shop::getContractNumber, shopInfo.getContractNumber())
                        .set(Shop::getNewTips, shopInfo.getNewTips())
                        .set(Shop::getBriefing, shopInfo.getBriefing())
                        .eq(BaseEntity::getId, shopId)
                        .update(),
                TenantConst.GRUUL_MALL_SHOP_BASE_INFO,
                shopId
        );
        SystemCode.DATA_UPDATE_FAILED.falseThrow(success);
        shopExecutor.execute(
                () -> rabbitTemplate.convertAndSend(
                        ShopRabbit.SHOP_UPDATE.exchange(),
                        ShopRabbit.SHOP_UPDATE.routingKey(),
                        ShopInfoVO.fromShop(shop)
                )
        );


    }

    @Override
    public Option<Shop> getShopById(Long shopId) {
        ShopConfigurationProperties.CacheExpire cacheExpire = shopConfigurationProperties.getCacheExpire();
        return Option.of(
                RedisUtil.getCacheMap(
                        Shop.class,
                        () -> shopService.getById(shopId),
                        Duration.ofSeconds(cacheExpire.getShopExpireTime()),
                        Duration.ofSeconds(cacheExpire.getShopRenewalTime()),
                        TenantConst.GRUUL_MALL_SHOP_BASE_INFO,
                        shopId
                )
        );
    }

    /**
     * C端店铺搜索
     *
     * @param shopQueryPageDTO 搜索条件
     * @return 查询店铺结果
     */
    @Override
    public IPage<ShopInfoVO> searchShop(ShopQueryPageDTO shopQueryPageDTO) {
        return shopService.searchShop(shopQueryPageDTO);
    }

    /**
     * 根据销量查询店铺
     *
     * @param sortAsc 是否升序
     * @return 查询店铺结果
     */
    @Override
    public List<ShopInfoVO> searchShopBySales(Boolean sortAsc) {
        // 查询店铺销量
        List<ProductStatisticsVO> shopSales = storageRpcService.getShopSales(sortAsc);
        if (CollectionUtils.isEmpty(shopSales)) {
            return null;
        }
        List<Long> shopIds = shopSales.stream()
                .map(ProductStatisticsVO::getShopId)
                .toList();
        List<Shop> shops = shopService.lambdaQuery()
                .in(Shop::getId, shopIds)
                .eq(Shop::getMode, Mode.B2B2C)
                .last("ORDER BY FIELD(id," + CollUtil.join(shopIds, ",") + ")")
                .list();
        Map<Long, Long> shopSalesMap = shopSales.stream()
                .collect(Collectors.toMap(ProductStatisticsVO::getShopId, ProductStatisticsVO::getSalesVolume));
        return shops.stream()
                .map(shop -> ShopInfoVO.fromShop(shop).setSalesVolume(shopSalesMap.get(shop.getId())))
                .collect(Collectors.toList());

    }


    /**
     * 根据距离查询店铺
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param sortAsc   是否升序
     * @return 查询店铺结果
     */
    @Override
    public List<ShopInfoVO> searchShopByDistance(Double longitude, Double latitude, Boolean sortAsc) {
        Circle circle = new Circle(longitude, latitude, Double.MAX_VALUE);
        RedisTemplate<String, Object> redisTemplate = RedisUtil.getRedisTemplate();
        // 店铺数量
        Long size = redisTemplate.opsForZSet().size(TenantConst.GRUUL_MALL_SHOP_GEO);
        if (size == null || size == 0) {
            return null;
        }
        RedisGeoCommands.GeoRadiusCommandArgs args = RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs()
                .includeDistance()
                .includeCoordinates()
                .sort(sortAsc ? Sort.Direction.ASC : Sort.Direction.DESC)
                .limit(size);

        GeoResults<RedisGeoCommands.GeoLocation<Object>> radius = redisTemplate.opsForGeo().radius(TenantConst.GRUUL_MALL_SHOP_GEO, circle, args);
        if (radius == null) {
            return null;
        }
        Set<Long> shopIds = radius.getContent()
                .stream()
                .map(geoLocationGeoResult ->
                        Long.valueOf(geoLocationGeoResult.getContent().getName().toString())
                ).collect(Collectors.toSet());
        Map<Long, BigDecimal> shopInitialDeliveryCharge = shopAddonSupporter.ShopInitialDeliveryCharge(shopIds);
        if (CollUtil.isEmpty(shopInitialDeliveryCharge)) {
            shopInitialDeliveryCharge = CollectionUtils.newHashMap(1);
        }
        Map<Long, Long> shopSalesMap = CollectionUtil.emptyIfNull(storageRpcService.getShopSales(sortAsc))
                .stream()
                .collect(Collectors.toMap(ProductStatisticsVO::getShopId, ProductStatisticsVO::getSalesVolume));
        Map<Long, BigDecimal> finalShopInitialDeliveryCharge = shopInitialDeliveryCharge;
        return radius.getContent()
                .stream()
                .filter(geoLocationGeoResult -> !getShopById(Long.valueOf(geoLocationGeoResult.getContent().getName().toString())).isEmpty())
                .map(geoLocationGeoResult -> {
                    double distance = DistanceUtils.getDistance(longitude, latitude, geoLocationGeoResult.getContent().getPoint().getX(), geoLocationGeoResult.getContent().getPoint().getY());
                    Long shopId = Long.valueOf(geoLocationGeoResult.getContent().getName().toString());
                    Shop shop = getShopById(shopId).get();
                    return ShopInfoVO.fromShop(shop)
                            .setDistance(distance)
                            .setSalesVolume(shopSalesMap.get(shopId))
                            .setInitialDeliveryCharge(finalShopInitialDeliveryCharge.get(shopId));
                }).toList();
    }

    @Override
    public ShopInfoVO getShopSaleAndDistance(Long shopId, Double longitude, Double latitude) {
        Shop shop = getShopById(shopId).get();
        if (shop == null) {
            return null;
        }
        double distance = 0;
        if (longitude != null && latitude != null) {
            Point location = shop.getLocation();
            distance = DistanceUtils.getDistance(longitude, latitude, location.getX(), location.getY());
        }
        Long shopSaleVolume = storageRpcService.getShopSaleVolume(shopId);
        return new ShopInfoVO()
                .setId(shopId)
                .setDistance(distance)
                .setSalesVolume(shopSaleVolume);
    }

    /**
     * 获取供应商信息
     *
     * @param supplierName 供应商名称
     * @return List<获取供应商信息>
     */
    @Override
    public List<Shop> getSupplierInfo(String supplierName) {
        return shopService.lambdaQuery().select(
                BaseEntity::getId,
                Shop::getName
        ).eq(Shop::getShopMode, ShopMode.SUPPLIER).like(Shop::getName, supplierName).list();
    }


}
