package com.medusa.gruul.shop.service.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品同步表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface ShopDataSyncRecordMapper extends BaseMapper<ShopDataSyncRecord> {
    /**
     * 查询目标店铺最新的同步记录
     * @param targetShopId 目标店铺id
     * @return
     */
    List<ShopDataSyncRecord> getLatestRecordByTargetShopId(@Param("targetShopId") Long targetShopId, @Param("syncType") Integer syncType);


}
