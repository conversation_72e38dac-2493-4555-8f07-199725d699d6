package com.medusa.gruul.shop.service.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.model.dto.ShopInfoDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import io.vavr.control.Option;

import java.util.List;

/**
 * <AUTHOR>
 * Description
 * date 2022-05-26 11:28
 */
public interface ShopInfoService {
    /**
     * 店铺设置信息修改
     *
     * @param shopInfo 店铺修改信息
     */
    void updateShopInfo(ShopInfoDTO shopInfo);

    /**
     * 根据店铺ID 查询店铺信息 先查缓存 缓存查不到查数据库 不为null则 放入缓存中
     *
     * @param shopId 店铺id
     * @return 店铺信息 Option
     */
    Option<Shop> getShopById(Long shopId);

    /**
     * C端店铺搜索
     *
     * @param shopQueryPageDTO 搜索条件
     * @return 查询店铺结果
     */
    IPage<ShopInfoVO> searchShop(ShopQueryPageDTO shopQueryPageDTO);

    /**
     * 根据销量查询店铺
     *
     * @param sortAsc 是否升序
     * @return 查询店铺结果
     */
    List<ShopInfoVO> searchShopBySales(Boolean sortAsc);

    /**
     * 根据距离查询店铺
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param sortAsc   是否升序
     * @return 查询店铺结果
     */
    List<ShopInfoVO> searchShopByDistance(Double longitude, Double latitude, Boolean sortAsc);

    ShopInfoVO getShopSaleAndDistance(Long shopId, Double longitude, Double latitude);

    /**
     * 获取供应商信息 List
     *
     * @param supplierName 供应商名称
     * @return List<shop>
     */
    List<Shop> getSupplierInfo(String supplierName);

}
