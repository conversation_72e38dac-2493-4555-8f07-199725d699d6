package com.medusa.gruul.shop.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.model.base.ShopProductSkuKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.common.mp.model.TenantConst;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.model.enums.Roles;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.global.model.helper.CompletableTask;
import com.medusa.gruul.order.api.model.OrderEvaluateCountDTO;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.overview.api.model.ShopBalanceVO;
import com.medusa.gruul.overview.api.rpc.OverviewRpcService;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopVisitor;
import com.medusa.gruul.shop.api.enums.ExtractionType;
import com.medusa.gruul.shop.api.enums.OperaReason;
import com.medusa.gruul.shop.api.enums.ShopRabbit;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.model.dto.ShopAdminMapDTO;
import com.medusa.gruul.shop.api.model.dto.ShopsEnableDisableDTO;
import com.medusa.gruul.shop.api.model.dto.SigningCategoryDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.service.model.ShopConstant;
import com.medusa.gruul.shop.service.model.dto.ShopDTO;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.model.enums.ShopError;
import com.medusa.gruul.shop.service.model.vo.ShopStatusQuantityVO;
import com.medusa.gruul.shop.service.model.vo.ShopVO;
import com.medusa.gruul.shop.service.model.vo.SupplierStatisticsVO;
import com.medusa.gruul.shop.service.mp.service.IShopBankAccountService;
import com.medusa.gruul.shop.service.mp.service.IShopRegisterInfoService;
import com.medusa.gruul.shop.service.mp.service.IShopService;
import com.medusa.gruul.shop.service.mp.service.IShopVisitorService;
import com.medusa.gruul.shop.service.service.ShopManageService;
import com.medusa.gruul.shop.service.service.addon.ShopAddonSupporter;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.core.GeoOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * date 2022/4/15
 */
@Service
@RequiredArgsConstructor
public class ShopManageServiceImpl implements ShopManageService {

    private final Executor shopExecutor;
    private final IShopService shopService;
    private final RabbitTemplate rabbitTemplate;
    private final OrderRpcService orderRpcService;
    private final OverviewRpcService overviewRpcService;
    private final IShopVisitorService shopVisitorService;
    private final IShopRegisterInfoService shopRegisterInfoService;
    private final IShopBankAccountService shopBankAccountService;
    private final ShopAddonSupporter shopAddonSupporter;
    private ShopManageService shopManageService;

    @Override
    public IPage<ShopVO> pageShop(ShopQueryPageDTO page) {
        if (CollUtil.isEmpty(page.getShopModes())) {
            page.setShopModes(Set.of(ShopMode.COMMON, ShopMode.O2O));
        }
        IPage<ShopVO> shopPage = TenantShop.disable(() -> shopService.pageShop(page));
        List<ShopVO> records = shopPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return shopPage;
        }
        AtomicReference<Map<Long, OrderEvaluateCountDTO>> orderEvaluateCountMapRef = new AtomicReference<>();
        AtomicReference<Map<Long, ShopBalanceVO>> shopBalanceMapRef = new AtomicReference<>();
        Set<Long> shopIds = records.stream().map(ShopVO::getId).collect(Collectors.toSet());
        CompletableTask.getOrThrowException(
                CompletableTask.allOf(
                        shopExecutor,
                        //评价
                        () -> orderEvaluateCountMapRef.set(orderRpcService.getOrderEvaluateCount(shopIds)),
                        //余额
                        () -> shopBalanceMapRef.set(overviewRpcService.getShopBalanceMap(shopIds))
                )
        );
        Map<Long, OrderEvaluateCountDTO> orderEvaluateCountMap = orderEvaluateCountMapRef.get();
        Map<Long, ShopBalanceVO> shopBalanceMap = shopBalanceMapRef.get();
        records.forEach(
                shop -> {
                    Long shopId = shop.getId();
                    //评分
                    shop.setScore(Option.of(orderEvaluateCountMap.get(shopId)).map(OrderEvaluateCountDTO::score).getOrElse(OrderEvaluateCountDTO::defaultScore));
                    //余额
                    shop.setShopBalance(Option.of(shopBalanceMap.get(shopId)).getOrElse(ShopBalanceVO::defaultBalance));
                }
        );
        return shopPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(ShopConstant.SHOP_UPDATE_LOCK)
    public void newShop(ShopDTO shop) {
        boolean match = ISecurity.matcher().any(SecureUser::getRoles, Roles.SUPER_ADMIN, Roles.SUPER_CUSTOM_ADMIN).match();
        Shop shopInfo = shop.saveShop(false, match, shopService, null, ISecurity.userMust().getId());
        Long shopId = shopInfo.getId();
        //多租户 数据
        ISystem.shopId(
                shopId,
                () -> {
                    shop.saveShopRegisterInfo(false, shopRegisterInfoService);
                    shop.saveShopBankAccount(false, shopBankAccountService);
                }

        );
        if (shop.getExtractionType() == ExtractionType.ORDER_SALES_EXTRACTION && CollUtil.isNotEmpty(shop.getSigningCategory())) {
            List<SigningCategoryDTO> signingCategoryList = shop.getSigningCategory()
                    .stream()
                    .peek(
                            signingCategory ->
                                    signingCategory.setCustomDeductionRatio(Long.valueOf(shop.getDrawPercentage())))
                    .toList();
            shop.setSigningCategory(signingCategoryList);
        }
        // 编辑签约类目
        Optional.ofNullable(shop.getSigningCategory())
                .filter(category -> match)
                .filter(signingCategory -> BooleanUtil.isFalse(shopAddonSupporter.editSingingCategory(signingCategory, shopId)))
                .ifPresent(signingCategory -> {
                    throw ShopError.SHOP_CATEGORY_UPDATE_FAIL.exception();
                });
        shopExecutor.execute(
                () -> {
                    boolean isOkStatus = shopInfo.getStatus() == ShopStatus.NORMAL;
                    rabbitTemplate.convertAndSend(
                            ShopRabbit.SHOP_ADMIN_CHANGE.exchange(),
                            ShopRabbit.SHOP_ADMIN_CHANGE.routingKey(),
                            new ShopAdminMapDTO()
                                    .setShopMode(shopInfo.getShopMode())
                                    .setShopId(shopInfo.getId())
                                    .setUserId(shop.getUserId())
                                    .setEnable(isOkStatus)
                    );
                    if (shop.getMode() == Mode.O2O) {
                        updateShopGeo(shop.getLocation(), shopId);
                    }
                    rabbitTemplate.convertAndSend(
                            ShopRabbit.SHOP_UPDATE.exchange(),
                            ShopRabbit.SHOP_UPDATE.routingKey(),
                            ShopInfoVO.fromShop(shopInfo)
                    );
                }
        );


    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private void updateShopGeo(com.vividsolutions.jts.geom.Point location, Long shopId) {
        RedisUtil.executePipelined(
                redisOperations -> {
                    GeoOperations geoOperations = redisOperations.opsForGeo();
                    geoOperations.remove(TenantConst.GRUUL_MALL_SHOP_GEO, shopId);
                    geoOperations.add(TenantConst.GRUUL_MALL_SHOP_GEO, new Point(location.getX(), location.getY()), shopId);
                }
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = ShopConstant.SHOP_UPDATE_LOCK, key = "#shopId")
    public void editShop(Long shopId, ShopDTO shop) {
        boolean match = ISecurity.matcher().any(SecureUser::getRoles, Roles.SUPER_ADMIN, Roles.SUPER_CUSTOM_ADMIN).match();
        Shop shopEntity = RedisUtil.doubleDeletion(
                () -> shop.saveShop(true, match, shopService, shopId, ISecurity.userMust().getId()),
                TenantConst.GRUUL_MALL_SHOP_BASE_INFO,
                shopId
        );
        //多租户数据
        ISystem.shopId(
                shopId,
                () -> {
                    shop.saveShopRegisterInfo(true, shopRegisterInfoService);
                    shop.saveShopBankAccount(true, shopBankAccountService);
                }
        );

        if (shop.getExtractionType() == ExtractionType.ORDER_SALES_EXTRACTION && CollUtil.isNotEmpty(shop.getSigningCategory())) {
            List<SigningCategoryDTO> signingCategoryList = shop.getSigningCategory()
                    .stream()
                    .peek(
                            signingCategory ->
                                    signingCategory.setCustomDeductionRatio(Long.valueOf(shop.getDrawPercentage())))
                    .toList();
            shop.setSigningCategory(signingCategoryList);
        }
        // 编辑签约类目
        Optional.ofNullable(shop.getSigningCategory())
                .filter(category -> match)
                .filter(signingCategory -> BooleanUtil.isFalse(shopAddonSupporter.editSingingCategory(signingCategory, shopId)))
                .ifPresent(signingCategory -> {
                    throw SystemCode.DATA_UPDATE_FAILED.exception();
                });
        shopExecutor.execute(
                () -> {
                    rabbitTemplate.convertAndSend(
                            ShopRabbit.SHOP_ADMIN_CHANGE.exchange(),
                            ShopRabbit.SHOP_ADMIN_CHANGE.routingKey(),
                            new ShopAdminMapDTO().setShopMode(shop.getShopMode()).setShopId(shopId).setUserId(shop.getUserId())
                    );
                    if (shop.getMode() == Mode.O2O) {
                        this.updateShopGeo(shop.getLocation(), shopId);
                    }
                    rabbitTemplate.convertAndSend(
                            ShopRabbit.SHOP_UPDATE.exchange(),
                            ShopRabbit.SHOP_UPDATE.routingKey(),
                            ShopInfoVO.fromShop(shopEntity)
                    );

                }
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = ShopConstant.SHOP_UPDATE_LOCK, batchParamName = "#shopIds", key = "#item")
    public void deleteShop(Set<Long> shopIds) {
        if (CollUtil.isEmpty(shopIds)) {
            return;
        }
        Map<Long, ShopBalanceVO> shopBalanceMap = overviewRpcService.getShopBalanceMap(shopIds);

        Set<String> keys = shopIds.stream()
                .map(
                        shopId -> {
                            ShopBalanceVO balance = shopBalanceMap.get(shopId);
                            //是否有余额不能删除
                            boolean dontAllowDelete = balance != null && (0 != balance.getUncompleted() || balance.getUndrawn() != 0);
                            if (dontAllowDelete) {
                                throw ShopError.SHOP_EXIST_BALANCE_NOT_DEL.dataEx(new ShopProductSkuKey().setShopId(shopId));
                            }
                            RedisUtil.getRedisTemplate().opsForGeo().remove(TenantConst.GRUUL_MALL_SHOP_GEO, shopId);
                            return RedisUtil.key(TenantConst.GRUUL_MALL_SHOP_BASE_INFO, shopId);

                        }
                ).collect(Collectors.toSet());
        boolean success = RedisUtil.doubleDeletion(
                () -> shopService.removeBatchByIds(shopIds),
                () -> RedisUtil.delete(keys)
        );
        SystemCode.DATA_DELETE_FAILED.falseThrow(success);
        rabbitTemplate.convertAndSend(
                ShopRabbit.SHOP_ENABLE_DISABLE.exchange(),
                ShopRabbit.SHOP_ENABLE_DISABLE.routingKey(),
                new ShopsEnableDisableDTO()
                        .setEnable(Boolean.FALSE)
                        .setReason(OperaReason.DELETED)
                        .setShopIds(shopIds)
        );
    }

    @Override
    public void batchDeleteShop(Set<Long> shopIds) {
        shopManageService.deleteShop(shopIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = ShopConstant.SHOP_UPDATE_LOCK, batchParamName = "#shopIds", key = "#shopId")
    public void enableDisableShop(Boolean isEnable, Set<Long> shopIds) {
        if (CollUtil.isEmpty(shopIds)) {
            return;
        }
        Set<String> cacheKeys = shopIds.stream()
                .map(shopId -> RedisUtil.key(TenantConst.GRUUL_MALL_SHOP_BASE_INFO, shopId))
                .collect(Collectors.toSet());
        boolean success = RedisUtil.doubleDeletion(
                () -> shopService.lambdaUpdate()
                        .set(Shop::getStatus, isEnable ? ShopStatus.NORMAL : ShopStatus.FORBIDDEN)
                        .in(Shop::getId, shopIds)
                        .update(),
                () -> RedisUtil.delete(cacheKeys)
        );
        SystemCode.DATA_DELETE_FAILED.falseThrow(success);
        //启用/禁用 店铺
        rabbitTemplate.convertAndSend(
                ShopRabbit.SHOP_ENABLE_DISABLE.exchange(),
                ShopRabbit.SHOP_ENABLE_DISABLE.routingKey(),
                new ShopsEnableDisableDTO()
                        .setEnable(isEnable)
                        .setReason(OperaReason.UPDATE)
                        .setShopIds(shopIds)
        );
    }

    @Override
    public void shopAudit(Long shopId, boolean pass) {
        RedisUtil.doubleDeletion(
                () -> {
                    boolean success = shopService.lambdaUpdate()
                            .set(Shop::getStatus, pass ? ShopStatus.NORMAL : ShopStatus.REJECT)
                            .eq(Shop::getId, shopId)
                            .update();
                    SystemCode.DATA_UPDATE_FAILED.falseThrow(success);
                },
                TenantConst.GRUUL_MALL_SHOP_BASE_INFO,
                shopId
        );
        shopExecutor.execute(
                () -> rabbitTemplate.convertAndSend(
                        ShopRabbit.SHOP_ENABLE_DISABLE.exchange(),
                        ShopRabbit.SHOP_ENABLE_DISABLE.routingKey(),
                        new ShopsEnableDisableDTO()
                                .setEnable(pass)
                                .setReason(OperaReason.UPDATE)
                                .setShopIds(Collections.singleton(shopId))
                )
        );


    }

    /**
     * 获取当日新增店铺数量
     *
     * @return 当日新增店铺数量
     */
    @Override
    public Long getTodayAddShopQuantity() {
        return shopService.getTodayAddShopQuantity();
    }

    /**
     * 获取店铺数量 group by status
     *
     * @return 店铺数量
     */
    @Override
    public Map<ShopStatus, Long> getShopQuantity() {
        List<ShopStatusQuantityVO> shopQuantity = shopService.getShopQuantity();
        return shopQuantity.stream().collect(Collectors.toMap(ShopStatusQuantityVO::getStatus, ShopStatusQuantityVO::getQuantity));
    }

    /**
     * 添加用户访问
     */
    @Override
    public void addShopVisitor() {
        Long userId = ISecurity.userMust().getId();
        LocalDate nowDate = LocalDate.now();
        ShopVisitor shopVisitor = shopVisitorService.lambdaQuery()
                .eq(ShopVisitor::getUserId, userId)
                .eq(ShopVisitor::getDate, nowDate).one();
        if (BeanUtil.isEmpty(shopVisitor)) {
            shopVisitor = new ShopVisitor();
            shopVisitor.setDate(nowDate).setUserId(userId).setUv(Long.valueOf(CommonPool.NUMBER_ONE));
            shopVisitorService.save(shopVisitor);
            return;
        }
        shopVisitor.setUv(shopVisitor.getUv() + CommonPool.NUMBER_ONE);
        shopVisitorService.updateById(shopVisitor);
    }

    /**
     * 获取供应商数量 by status
     *
     * @return 店铺数量
     */
    @Override
    public List<SupplierStatisticsVO> getSupplierQuantity() {
        return shopService.getSupplierQuantity();
    }

    @Override
    public Long getShopVisitorNum() {
        return shopVisitorService.lambdaQuery().eq(ShopVisitor::getDate, LocalDate.now()).count();
    }


    @Autowired
    public void setShopManageService(ShopManageService shopManageService) {
        this.shopManageService = shopManageService;
    }
}
