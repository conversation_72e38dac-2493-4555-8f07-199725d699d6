package com.medusa.gruul.shop.service;

import com.medusa.gruul.global.model.constant.AspectOrder;
import com.medusa.gruul.shop.service.properties.ShopConfigurationProperties;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;

/**
 * <AUTHOR>
 * date 2022/3/29
 */
@SpringBootApplication
@EnableCaching(order = AspectOrder.CACHE_ASPECT)
@EnableDubbo(scanBasePackages = "com.medusa.gruul.shop.service.service.rpc")
@EnableConfigurationProperties(ShopConfigurationProperties.class)
public class ShopServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(ShopServiceApplication.class, args);
    }
}
