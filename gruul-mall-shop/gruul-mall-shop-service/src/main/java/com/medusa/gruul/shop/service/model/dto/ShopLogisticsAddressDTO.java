package com.medusa.gruul.shop.service.model.dto;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.shop.api.entity.ShopLogisticsAddress;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Description: 物流地址DTO
 * @Author: xiaoq
 * @Date : 2022-05-05 10:25
 */
@Data
public class ShopLogisticsAddressDTO {
    private Long id;

    /**
     * 联系人名称
     */
    @NotNull
    private String contactName;

    /**
     * 联系人电话
     */
    @NotNull
    private String contactPhone;

    /**
     * 详细地址
     */
    @NotNull
    private String address;

    /**
     * 邮编码
     */
    @Max(6)
    @Min(6)
    private String zipCode;


    /**
     * 省级code
     */
    @NotNull
    private String provinceCode;

    /**
     * 市级code
     */
    @NotNull
    private String cityCode;

    /**
     * 区级code
     */
    @NotNull
    private String regionCode;


    public ShopLogisticsAddress coverLogisticsAddress() {
        ShopLogisticsAddress shopLogisticsAddress = new ShopLogisticsAddress();
        BeanUtil.copyProperties(this, shopLogisticsAddress);
        return shopLogisticsAddress;
    }
}
