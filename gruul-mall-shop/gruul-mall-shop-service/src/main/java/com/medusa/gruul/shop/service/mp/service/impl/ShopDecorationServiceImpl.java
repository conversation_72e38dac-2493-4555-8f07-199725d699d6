package com.medusa.gruul.shop.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.shop.api.entity.ShopDecoration;
import com.medusa.gruul.shop.service.mp.mapper.ShopDecorationMapper;
import com.medusa.gruul.shop.service.mp.service.IShopDecorationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 
 *
 * 
 *
 * <AUTHOR>
 * @Description
 * @date 2022-08-18 13:14
 */
@Service
public class ShopDecorationServiceImpl extends ServiceImpl<ShopDecorationMapper, ShopDecoration> implements IShopDecorationService {
    @Override
    public List<AggregationDecorationVO> getAggregationPlatformDecorate() {
        return  this.baseMapper.queryAggregationPlatformDecorate();
    }
}
