package com.medusa.gruul.shop.service.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.idem.annotation.Idem;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.model.dto.ShopInfoDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.mp.service.IShopService;
import com.medusa.gruul.shop.service.service.ShopInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 店铺基本信息控制器
 *
 * <AUTHOR>
 * Description
 * date 2022-05-26 11:26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop/info")
public class ShopInfoController {

    private final IShopService shopService;

    private final ShopInfoService shopInfoService;

    private final ShopRpcService shopRpcService;

    /**
     * 获取店铺设置信息
     *
     * @return 店铺信息
     */
    @Log("获取店铺设置信息")
    @PreAuthorize("""
                    @S.matcher()
                    .any(@S.ROLES,@S.SHOP_ADMIN,@S.R.SUPPLIER_ADMIN)
                    .or(@S.consumer().eq(@S.ROLES,@S.SHOP_CUSTOM_ADMIN).eq(@S.PERMS,'mall:general:setting'))
                    .or(@S.consumer().eq(@S.ROLES,@S.R.SUPPLIER_CUSTOM_ADMIN).eq(@S.PERMS,'mall:general:setting')).match()
            """)
    @GetMapping
    public Result<Shop> getShopInfo() {
        Shop shop = shopService.getById(ISecurity.userMust().getShopId());
        return Result.ok(shop);
    }

    /**
     * 店铺设置信息修改
     *
     * @param shopInfo 店铺信息
     */
    @Idem
    @Log("店铺设置信息修改")
    @PostMapping("update")
    @PreAuthorize("""
                    @S.matcher()
                    .any(@S.ROLES,@S.SHOP_ADMIN,@S.R.SUPPLIER_ADMIN)
                    .or(@S.consumer().eq(@S.ROLES,@S.SHOP_CUSTOM_ADMIN).eq(@S.PERMS,'mall:general:setting'))
                    .match()
            """)
    public Result<Void> updateShopInfo(@RequestBody ShopInfoDTO shopInfo) {
        shopInfoService.updateShopInfo(shopInfo);
        return Result.ok();
    }

    /**
     * 获取店铺基础信息
     *
     * @param shopId 店铺id
     * @return 店铺基本信息
     */
    @Log("获取店铺基础信息")
    @GetMapping("/base/{shopId}")
    public Result<ShopInfoVO> getShopBaseInfo(@PathVariable Long shopId) {
        ShopInfoVO shopInfoByShopId = shopRpcService.getShopInfoByShopId(shopId);
        return Result.ok(
                shopInfoByShopId
        );
    }

    /**
     * 获取店铺基础信息
     *
     * @param shopId 店铺id
     * @return 店铺基本信息
     */
    @Log("获取店铺基础信息")
    @GetMapping("/{shopId}")
    public Result<Shop> getShopInfo(@PathVariable Long shopId) {
        return Result.ok(
                shopInfoService.getShopById(shopId).getOrNull()
        );
    }

    /**
     * C端店铺搜索
     *
     * @param shopQueryPageDTO 搜索条件
     * @return 查询店铺结果
     */
    @Log("C端店铺搜索")
    @GetMapping("/search/shop")
    public Result<IPage<ShopInfoVO>> searchShop(ShopQueryPageDTO shopQueryPageDTO) {
        return Result.ok(shopInfoService.searchShop(shopQueryPageDTO));
    }


    /**
     * 根据销量查询店铺
     *
     * @param sortAsc 是否升序
     * @return 查询店铺结果
     */
    @Log("根据销量查询店铺")
    @GetMapping("/search/shop/sales")
    public Result<List<ShopInfoVO>> searchShopBySales(@RequestParam Boolean sortAsc) {
        return Result.ok(
                shopInfoService.searchShopBySales(sortAsc)
        );
    }


    /**
     * 根据距离查询店铺
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param sortAsc   是否升序
     * @return 查询店铺结果
     */
    @Log("根据距离查询店铺")
    @GetMapping("/search/shop/distance")
    public Result<List<ShopInfoVO>> searchShopByDistance(
            @RequestParam(value = "longitude") Double longitude,
            @RequestParam(value = "latitude") Double latitude,
            @RequestParam Boolean sortAsc) {
        return Result.ok(
                shopInfoService.searchShopByDistance(longitude, latitude, sortAsc)
        );
    }

    /**
     * 获取指定店铺的距离、销量
     *
     * @param shopId    店铺id
     * @param longitude 经度
     * @param latitude  纬度
     */
    @GetMapping("/shopSaleAndDistance")
    public Result<ShopInfoVO> getShopSaleAndDistance(
            @RequestParam Long shopId,
            @RequestParam(value = "longitude", required = false) Double longitude,
            @RequestParam(value = "latitude", required = false) Double latitude) {
        return Result.ok(
                shopInfoService.getShopSaleAndDistance(shopId, longitude, latitude)
        );
    }


    /**
     * 获取供应商信息
     *
     * @param supplierName 供应商名称
     * @return List<Shop>
     */
    @GetMapping("getSupplierInfo")
    public Result<List<Shop>> getSupplierInfo(String supplierName) {
        List<Shop> supplierInfoList = shopInfoService.getSupplierInfo(supplierName);
        return Result.ok(supplierInfoList);
    }

}
