package com.medusa.gruul.shop.service.model.enums;

import com.medusa.gruul.global.i18n.I18N;
import com.medusa.gruul.global.model.exception.Error;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <p></p>
 *
 * <AUTHOR>
 * date 2023/6/26
 */
@Getter
@RequiredArgsConstructor
public enum ShopError implements Error {

	/**
	 * 已存在同名店铺
	 */
	SHOP_NAME_EXISTED(80000, "shop.name.existed"),

	/**
	 * 装修控件不存在
	 */
	SHOP_DECORATE_COMPONENT_NOT_EXIST(80001, "shop.decorate.component.not.exist"),

	/**
	 * 装修数据不存在，无法使用
	 */
	SHOP_DECORATE_DATA_NOT_EXIST(80002, "shop.decorate.data.not.exist"),

	/**
	 * 已存在相同的地址，无法重复添加
	 */
	SHOP_LOGISTICS_ADDRESS_EXISTED(80003, "shop.logistics.address.existed"),

	/**
	 * 不能删除默认地址
	 */
	SHOP_DEFAULT_LOGISTICS_ADDRESS_NOT_DEL(80004, "shop.default.logistics.address.not.del"),

	/**
	 * 店铺存在余额不允许删除
	 */
	SHOP_EXIST_BALANCE_NOT_DEL(80005, "shop.exist.balance.not.del"),

	/**
	 * 店铺已不存在
	 */
	SHOP_NOT_EXIST(80006, "shop.not.exist"),

	/**
	 * 签约类目更新失败
	 */
	SHOP_CATEGORY_UPDATE_FAIL(80007, "shop.category.update.fail"),
	;

	private final int code;

	private final String msgCode;

	@Override
	public int code() {
		return getCode();
	}

	@Override
	public String msg() {
		return I18N.msg(getMsgCode());
	}
}
