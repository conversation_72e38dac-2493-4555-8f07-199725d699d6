package com.medusa.gruul.shop.service.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.shop.api.entity.ShopDecoration;

import java.util.List;

/**
 * 
 *
 * 
 *
 * <AUTHOR>
 * @Description
 * @date 2022-08-18 13:08
 */
public interface ShopDecorationMapper extends BaseMapper<ShopDecoration> {
    /**
     * 查询聚合类型页面名称
     * @return  List<AggregationDecorationVO>
     */
    List<AggregationDecorationVO> queryAggregationPlatformDecorate();
}
