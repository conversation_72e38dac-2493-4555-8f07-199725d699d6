package com.medusa.gruul.shop.service.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.shop.api.entity.ShopBankAccount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 
 * 商家收款账号信息 Mapper 接口
 * 
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
public interface ShopBankAccountMapper extends BaseMapper<ShopBankAccount> {
    /**
     * 查询店铺账号信息
     * @param shopIds          店铺id
     * @return 账号信息
     */
    List<ShopBankAccount> queryShopAccountsByShopId(@Param("shopIds") Set<Long> shopIds);
}
