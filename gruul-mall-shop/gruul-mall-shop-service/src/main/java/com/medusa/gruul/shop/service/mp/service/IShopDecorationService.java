package com.medusa.gruul.shop.service.mp.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.shop.api.entity.ShopDecoration;

import java.util.List;

/**
 * 
 *
 * 
 *
 * <AUTHOR>
 * @Description
 * @date 2022-08-18 13:11
 */
public interface IShopDecorationService extends IService<ShopDecoration> {

    /**
     * 获取聚合装修信息 类型+首页名称
     *
     * @return List<AggregationDecorationVO>
     */
    List<AggregationDecorationVO> getAggregationPlatformDecorate();
}
