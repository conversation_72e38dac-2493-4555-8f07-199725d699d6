package com.medusa.gruul.shop.service.mp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.shop.api.model.dto.ShopQueryDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.model.vo.ShopStatusQuantityVO;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.service.model.vo.ShopVO;
import com.medusa.gruul.shop.service.model.vo.SupplierStatisticsVO;
import com.medusa.gruul.shop.service.mp.mapper.ShopMapper;
import com.medusa.gruul.shop.service.mp.service.IShopService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家注册信息 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@Service
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements IShopService {

    @Override
    public IPage<ShopVO> pageShop(ShopQueryPageDTO page) {
        return baseMapper.pageShop(page);
    }

    @Override
    public Long getTodayAddShopQuantity() {
        return TenantShop.disable(() -> baseMapper.queryTodayAddShopQuantity());
    }

    @Override
    public List<ShopStatusQuantityVO> getShopQuantity() {
        return baseMapper.queryShopQuantity();
    }

    /**
     * 获取供应商数量
     *
     * @return 供应商数量
     */
    @Override
    public List<SupplierStatisticsVO> getSupplierQuantity() {
        return baseMapper.querySupplierQuantity();
    }

    @Override
    public IPage<ShopInfoVO> searchShop(ShopQueryPageDTO shopQueryPageDTO) {
        return baseMapper.searchShop(shopQueryPageDTO);
    }

    @Override
    public List<Shop> getShop(ShopQueryDTO shopQueryDTO) {
        return this.lambdaQuery()
                .eq(null != shopQueryDTO.getStatus(), Shop::getStatus, shopQueryDTO.getStatus().getValue())
                .eq(null != shopQueryDTO.getNo(), Shop::getNo, shopQueryDTO.getNo())
                .eq(null != shopQueryDTO.getId(), Shop::getId, shopQueryDTO.getId())
                .eq(null != shopQueryDTO.getShopType(), Shop::getShopType, shopQueryDTO.getShopType().getValue())
                .list();
    }
}
