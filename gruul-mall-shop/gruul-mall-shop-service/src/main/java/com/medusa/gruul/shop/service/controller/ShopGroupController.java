package com.medusa.gruul.shop.service.controller;

import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.shop.service.mp.entity.ShopGroup;
import com.medusa.gruul.shop.service.mp.service.IShopGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 店铺分组 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop/group")
public class ShopGroupController {

    private final IShopGroupService shopGroupService;

    /**
     * 店铺分组列表
     *
     * @return List<店铺分组>
     */
    @Log("店铺付费会员列表")
    @GetMapping("list")
    public Result<List<ShopGroup>> getList() {
        return Result.ok(shopGroupService.list());
    }


    /**
     * 店铺分组详情
     *
     * @param id 店铺分组id
     * @return 付费会员配置详情
     */
    @Log("店铺分组详情")
    @GetMapping("info")
    public Result<ShopGroup> getInfo(Long id) {
        return Result.ok(shopGroupService.getById(id));
    }
}
