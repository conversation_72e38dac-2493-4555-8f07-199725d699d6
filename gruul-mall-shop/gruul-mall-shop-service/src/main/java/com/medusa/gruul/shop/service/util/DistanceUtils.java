package com.medusa.gruul.shop.service.util;

import com.medusa.gruul.common.model.constant.CommonPool;
import com.vividsolutions.jts.geom.Point;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 经纬度计算
 *
 * <AUTHOR>
 * @date 2023/3/6
 */
public class DistanceUtils {
    private final static double PI = Math.PI;
    /**
     * 地球半径
     */
    private final static double R = 6378137.00;

    /**
     * 计算两个经纬度坐标之间的距离
     *
     * @param lon1 第一个点的经度
     * @param lat1 第一个点的纬度
     * @param lon2 第二个点的经度
     * @param lat2 第二个点的纬度
     * @return 两点之间的距离（单位：千米）
     */
    public static double getDistance(double lon1, double lat1, double lon2, double lat2) {
        double fistLon, fistLat, secondLon, secondLat;
        fistLon = rad(lon1);
        fistLat = rad(lat1);
        secondLon = rad(lon2);
        secondLat = rad(lat2);
        // 保留两位小数，截取策略
        return BigDecimal.valueOf(Math.sqrt((fistLon - secondLon) * (fistLon - secondLon) + (fistLat - secondLat) * (fistLat - secondLat))
        ).divide(BigDecimal.valueOf(1000), CommonPool.NUMBER_TWO, RoundingMode.DOWN).doubleValue();
    }

    /**
     * @param point 店铺定位
     * @param lon   用户经度
     * @param lat   用户纬度
     * @return 两点之间的距离（单位：千米）
     */
    public static double getDistance(Point point, double lon, double lat) {
        return getDistance(point.getX(), point.getY(), lon, lat);
    }

    private static double rad(double d) {
        return d * PI * R / 180.0;
    }

    public static int getDistance(double distance){
        BigDecimal bd = new BigDecimal(distance);
        bd = bd.setScale(0, RoundingMode.HALF_UP);
        return bd.intValue();
    }
}
