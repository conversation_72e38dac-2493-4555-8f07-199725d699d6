package com.medusa.gruul.shop.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.custom.aggregation.decoration.entity.DecorateDetailsEntity;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.FunctionType;
import com.medusa.gruul.common.custom.aggregation.decoration.util.AggregationPlatformUtil;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.PropertiesVO;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.global.model.helper.CompletableTask;
import com.medusa.gruul.shop.api.entity.ShopDecoration;
import com.medusa.gruul.shop.api.entity.ShopDecorationDetails;
import com.medusa.gruul.shop.service.model.dto.ShopFunctionDecorationDTO;
import com.medusa.gruul.shop.service.model.enums.ShopError;
import com.medusa.gruul.shop.service.mp.service.IShopDecorationDetailService;
import com.medusa.gruul.shop.service.mp.service.IShopDecorationService;
import com.medusa.gruul.shop.service.service.ShopRenovationService;
import com.medusa.gruul.shop.service.util.ShopUtil;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * Description
 * date 2022-08-17 15:12
 */
@Service
@RequiredArgsConstructor
public class ShopRenovationServiceImpl implements ShopRenovationService {

	private final Executor shopExecutor;


	private final IShopDecorationService shopDecorationService;

	private final IShopDecorationDetailService shopDecorationDetailService;


	/**
	 * 编辑装修功能详情(新增/修改)
	 *
	 * @param shopFunctionDecoration ShopFunctionDecorationDTO.class
	 */
	@Override
	public Long editFunctionDecoration(ShopFunctionDecorationDTO shopFunctionDecoration) {
		Option<Long> shopIdOpt = ISystem.shopIdOpt();
		Long shopId;
		if (shopIdOpt.isEmpty()) {
			shopId = ISecurity.userMust().getShopId();
		} else {
			shopId = shopIdOpt.get();
		}
		ShopDecoration shopDecoration = shopDecorationInit(shopFunctionDecoration.getPlatforms());
		ShopDecorationDetails shopDecorationDetails = new ShopDecorationDetails();
		String properties = JSON.toJSONString(shopFunctionDecoration.getProperties());
		if (ObjectUtil.isEmpty(shopFunctionDecoration.getId())) {
			Long count = shopDecorationDetailService.lambdaQuery()
					.eq(ShopDecorationDetails::getFunctionType, shopFunctionDecoration.getFunctionType())
					.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId()).count();
			SystemCode.DATA_ADD_FAILED.trueThrow(shopFunctionDecoration.getFunctionType() != FunctionType.PAGE && count > CommonPool.NUMBER_ZERO);
			shopDecorationDetails
					.setFunctionType(shopFunctionDecoration.getFunctionType())
					.setPageName(shopFunctionDecoration.getPageName() != null ? shopFunctionDecoration.getPageName() : "自定义页面")
					.setIsDef(count < CommonPool.NUMBER_ONE ? Boolean.TRUE : Boolean.FALSE)
					.setProperties(properties);
			shopDecorationDetails.setShopDecorationId(shopDecoration.getId());
			shopDecorationDetailService.save(shopDecorationDetails);
			return shopDecorationDetails.getId();
		}
		shopDecorationDetails = shopDecorationDetailService.lambdaQuery()
				.eq(BaseEntity::getId, shopFunctionDecoration.getId())
				.one();
		ShopError.SHOP_DECORATE_COMPONENT_NOT_EXIST.trueThrow(shopDecorationDetails == null);
		String pageName = shopDecorationDetails.getPageName();
		Supplier<Boolean> supplier = () -> shopDecorationDetailService.lambdaUpdate()
				.set(ShopDecorationDetails::getPageName, pageName)
				.set(ShopDecorationDetails::getProperties, properties)
				.set(shopFunctionDecoration.getIsDef() != null, ShopDecorationDetails::getIsDef, shopFunctionDecoration.getIsDef())
				.eq(BaseEntity::getId, shopFunctionDecoration.getId()).update();

		// 功能类型不为page时 不进行延迟双删
		if (shopFunctionDecoration.getFunctionType() != FunctionType.PAGE) {
			SystemCode.DATA_UPDATE_FAILED.falseThrow(supplier.get());
			return shopDecorationDetails.getId();
		}

		RedisUtil.doubleDeletion(
				supplier,
				() -> RedisUtil.delete(ShopUtil.shopCacheKey(shopId, shopFunctionDecoration.getPlatforms()))
		);
		return shopDecorationDetails.getId();
	}

	/**
	 * 删除装修页面
	 *
	 * @param ids 装修页面ids
	 */
	@Override
	public void delDecorationPage(Set<Long> ids, AggregationPlatform aggregationPlatform) {
		Option<Long> shopIdOpt = ISystem.shopIdOpt();
		Long shopId;
		if (shopIdOpt.isEmpty()) {
			shopId = ISecurity.userMust().getShopId();
		} else {
			shopId = shopIdOpt.get();
		}
		// 无论删除页面是否为首页都清空redis
		Boolean flag = RedisUtil.doubleDeletion(
				() -> shopDecorationDetailService.lambdaUpdate()
						.in(BaseEntity::getId, ids)
						.ne(ShopDecorationDetails::getFunctionType, FunctionType.TABBAR)
						.remove(),
				() -> RedisUtil.delete(ShopUtil.shopCacheKey(shopId, aggregationPlatform)));
		SystemCode.DATA_DELETE_FAILED.falseThrow(flag);
	}


	/**
	 * 修改页面名称
	 *
	 * @param name 名称
	 * @param id   页面id
	 */
	@Override
	public void updatePageName(String name, Long id) {
		boolean update = shopDecorationDetailService.lambdaUpdate()
				.eq(BaseEntity::getId, id)
				.eq(ShopDecorationDetails::getFunctionType, FunctionType.PAGE)
				.set(ShopDecorationDetails::getPageName, name)
				.update();
		SystemCode.DATA_UPDATE_FAILED.falseThrow(update);
	}


	/**
	 * 获取页面基础信息 list
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return List<页面基础信息>
	 */
	@Override
	public List<ShopDecorationDetails> getPageBasicsInfoList(AggregationPlatform aggregationPlatform) {
		ShopDecoration shopDecoration = shopDecorationInit(aggregationPlatform);
		return shopDecorationDetailService.lambdaQuery()
				.select(BaseEntity::getId,
						ShopDecorationDetails::getShopId,
						ShopDecorationDetails::getFunctionType,
						ShopDecorationDetails::getPageName,
						ShopDecorationDetails::getIsDef
				)
				.eq(ShopDecorationDetails::getFunctionType, FunctionType.PAGE)
				.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId()).list();
	}

	/**
	 * 获取pc端装修首页信息
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return PropertiesVO.class
	 */
	@Override
	public PropertiesVO getDecorationInfo(AggregationPlatform aggregationPlatform) {
		ShopDecoration shopDecoration = shopDecorationInit(aggregationPlatform);
		//获取装修首页数据
		ShopDecorationDetails shopDecorationDetails = shopDecorationDetailService.lambdaQuery()
				.eq(ShopDecorationDetails::getFunctionType, FunctionType.PAGE)
				.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId())
				.eq(ShopDecorationDetails::getIsDef, Boolean.TRUE).one();

		return assemblingPropertiesVO(shopDecorationDetails);
	}


	/**
	 * 设置页面为首页
	 *
	 * @param id                  页面id
	 * @param aggregationPlatform 页面所属聚合app平台类型
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void setHomePage(Long id, AggregationPlatform aggregationPlatform) {
		Long shopId = ISecurity.userMust().getShopId();
		ShopDecoration shopDecoration = shopDecorationInit(aggregationPlatform);
		//首页更换时 删除首页redis信息
		RedisUtil.doubleDeletion(() -> shopDecorationDetailService.lambdaUpdate()
						.set(ShopDecorationDetails::getIsDef, Boolean.FALSE)
						.eq(ShopDecorationDetails::getFunctionType, FunctionType.PAGE)
						.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId())
						.eq(ShopDecorationDetails::getIsDef, Boolean.TRUE)
						.update(),
				() -> RedisUtil.delete(ShopUtil.shopCacheKey(shopId, aggregationPlatform)));

		//设置页面为首页
		shopDecorationDetailService.lambdaUpdate()
				.set(ShopDecorationDetails::getIsDef, Boolean.TRUE)
				.eq(BaseEntity::getId, id).update();
	}

	/**
	 * 获取装修页面组件信息
	 *
	 * @param id 页面id
	 * @return PropertiesVO.class
	 */
	@Override
	public PropertiesVO getDecorationInfoById(Long id) {
		ShopDecorationDetails shopDecorationDetails = shopDecorationDetailService.lambdaQuery()
				.select(ShopDecorationDetails::getProperties)
				.eq(BaseEntity::getId, id).one();

		return assemblingPropertiesVO(shopDecorationDetails);
	}


	/**
	 * 获取装修底部导航信息
	 *
	 * @param aggregationPlatform 页面所属聚合app平台类型
	 * @return PropertiesVO.class
	 */
	@Override
	public PropertiesVO getNavigationInfo(AggregationPlatform aggregationPlatform) {
		ShopDecorationDetails shopDecorationDetails = shopDecorationDetailService.lambdaQuery()
				.select(BaseEntity::getId,
						ShopDecorationDetails::getProperties)
				.eq(ShopDecorationDetails::getFunctionType, FunctionType.TABBAR).one();

		return assemblingPropertiesVO(shopDecorationDetails);
	}


	/**
	 * 获取客户端装修详情
	 *
	 * @return 装修数据 页面+底部导航
	 */
	@Override
	public Map<String, List<Object>> getClientDecoration() {
		Platform platform = ISystem.platformMust();
		AggregationPlatform aggregationPlatform = AggregationPlatformUtil.getAggregationPlatform(platform);
		Long shopId = ISystem.shopIdMust();
		ShopDecoration shopDecoration = shopDecorationService.lambdaQuery().eq(ShopDecoration::getPlatforms, platform).one();
		ShopError.SHOP_DECORATE_DATA_NOT_EXIST.trueThrow(shopDecoration == null);
		Map<String, List<Object>> map = new HashMap<>(CommonPool.NUMBER_FOUR);
		CompletableTask.getOrThrowException(
				CompletableTask.allOf(shopExecutor,
						() -> {
							//获取首页页面数据
							String homePageProperties = RedisUtil.getCacheMap(
									() -> RedisUtil.getCacheObject(ShopUtil.shopCacheKey(shopId, aggregationPlatform)),
									() -> shopDecorationDetailService
											.lambdaQuery()
											.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId())
											.eq(ShopDecorationDetails::getIsDef, Boolean.TRUE)
											.eq(ShopDecorationDetails::getFunctionType, FunctionType.PAGE)
											.one().getProperties(),
									Duration.ofSeconds(RedisUtil.expireWithRandom(CommonPool.UNIT_CONVERSION_TEN_THOUSAND)),
									Duration.ofMillis(CommonPool.NUMBER_FIFTEEN),
									ShopUtil.shopCacheKey(shopId, aggregationPlatform)
							);
							map.put("homePage", JSON.parseObject(homePageProperties, new TypeReference<>() {
							}));
						},
						() -> {
							//获取底部导航数据
							ShopDecorationDetails tabbar = shopDecorationDetailService.lambdaQuery()
									.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId())
									.eq(ShopDecorationDetails::getFunctionType, FunctionType.TABBAR).one();
							map.put("tabbar", tabbar.getProperties() != null ? JSON.parseObject(tabbar.getProperties(), new TypeReference<>() {
							}) : null);
						}

				)
		);
		return map;

	}

	@Override
	public PropertiesVO getClassifyPageInfo(AggregationPlatform aggregationPlatform) {
		ShopDecorationDetails shopDecorationDetails = shopDecorationDetailService.lambdaQuery()
				.select(BaseEntity::getId,
						ShopDecorationDetails::getProperties)
				.eq(ShopDecorationDetails::getFunctionType, FunctionType.CLASSIFY_PAGE).one();
		return assemblingPropertiesVO(shopDecorationDetails);
	}

	@Override
	public ShopDecorationDetails getNotPageBasicsInfo(AggregationPlatform aggregationPlatform, FunctionType functionType) {
		ShopDecoration shopDecoration = shopDecorationInit(aggregationPlatform);
		return shopDecorationDetailService.lambdaQuery()
				.select(BaseEntity::getId,
						ShopDecorationDetails::getShopId,
						ShopDecorationDetails::getFunctionType,
						ShopDecorationDetails::getPageName
				)
				.eq(ShopDecorationDetails::getFunctionType, functionType)
				.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId()).one();
	}

	@Override
	public PropertiesVO getFunctionTypeInfo(FunctionType functionType) {
		Platform platform = ISystem.platform().must();
		AggregationPlatform aggregationPlatform = AggregationPlatformUtil.getAggregationPlatform(platform);
		ShopDecoration shopDecoration = shopDecorationInit(aggregationPlatform);
		ShopDecorationDetails shopDecorationDetails = shopDecorationDetailService.lambdaQuery()
				.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId())
				.eq(DecorateDetailsEntity::getFunctionType, functionType)
				.one();
		return assemblingPropertiesVO(shopDecorationDetails);
	}

	/**
	 * 获取首页装修功能详情
	 *
	 * @return List<Object>
	 */
	@Override
	public List<Object> getHomePage() {

		Long shopId = ISystem.shopId().must();
		Platform platform = ISystem.platform().must();
		AggregationPlatform aggregationPlatform = AggregationPlatformUtil.getAggregationPlatform(platform);
		ShopDecoration shopDecoration = shopDecorationService.lambdaQuery().eq(ShopDecoration::getPlatforms, aggregationPlatform).one();
		ShopError.SHOP_DECORATE_DATA_NOT_EXIST.trueThrow(shopDecoration == null);
		// 获取首页装修数据
		String properties = RedisUtil.getCacheMap(
				() -> RedisUtil.getCacheObject(ShopUtil.shopCacheKey(shopId, aggregationPlatform)),
				() -> {
					ShopDecorationDetails shopDecorationDetails = shopDecorationDetailService
							.lambdaQuery()
							.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId())
							.eq(ShopDecorationDetails::getIsDef, Boolean.TRUE)
							.eq(ShopDecorationDetails::getFunctionType, FunctionType.PAGE)
							.one();
					// 增加判空 预防空指针
					if (BeanUtil.isEmpty(shopDecorationDetails)) {
						return null;
					}
					return shopDecorationDetails.getProperties();
				},
				Duration.ofSeconds(RedisUtil.expireWithRandom(CommonPool.UNIT_CONVERSION_TEN_THOUSAND)),
				Duration.ofMillis(CommonPool.NUMBER_FIFTEEN),
				ShopUtil.shopCacheKey(shopId, aggregationPlatform)
		);
		return properties != null ? JSON.parseObject(properties, new TypeReference<>() {
		}) : null;
	}


	/**
	 * @param aggregationPlatform 聚合平台类型
	 * @param functionType        装修功能类型
	 * @return PropertiesVO.java
	 */
	@Override
	public PropertiesVO getNotPageDecorationInfo(AggregationPlatform aggregationPlatform, FunctionType functionType) {
		SystemCode.PARAM_VALID_ERROR.trueThrow(functionType == FunctionType.PAGE);
		ShopDecoration shopDecoration = shopDecorationInit(aggregationPlatform);
		ShopDecorationDetails shopDecorationDetails = shopDecorationDetailService.lambdaQuery()
				.select(
						BaseEntity::getId,
						DecorateDetailsEntity::getProperties
				)
				.eq(DecorateDetailsEntity::getFunctionType, functionType)
				.eq(ShopDecorationDetails::getShopDecorationId, shopDecoration.getId())
				.one();
		return assemblingPropertiesVO(shopDecorationDetails);
	}


	/**
	 * 获取聚合装修类型及对应的首页名称
	 *
	 * @param aggregationPlatforms 聚合平台类型
	 * @return List<AggregationDecorationVO>
	 */
	@Override
	public List<AggregationDecorationVO> getAggregationPlatformDecorate(List<AggregationPlatform> aggregationPlatforms) {
		List<AggregationDecorationVO> aggregationDecorationList = shopDecorationService.getAggregationPlatformDecorate();
		if (aggregationDecorationList.size() < CommonPool.NUMBER_THREE) {
			aggregationDecorationList.clear();
			aggregationPlatforms.forEach(
					bean -> {
						ShopDecoration shopDecoration = shopDecorationInit(bean);
						aggregationDecorationList.add(new AggregationDecorationVO(shopDecoration.getPlatforms(), "自定义页面"));
					}
			);
		}
		return aggregationDecorationList;
	}


	/**
	 * PropertiesVO 组装
	 *
	 * @param shopDecorationDetails ShopDecorationDetails.class
	 * @return PropertiesVO
	 */
	private PropertiesVO assemblingPropertiesVO(ShopDecorationDetails shopDecorationDetails) {
		if (BeanUtil.isEmpty(shopDecorationDetails)) {
			return null;
		}
		PropertiesVO properties = new PropertiesVO();
		properties.setId(shopDecorationDetails.getId());
		properties.setProperties(JSON.parseObject(shopDecorationDetails.getProperties(), new TypeReference<>() {
		}));
		return properties;
	}


	/**
	 * 初始化 装修主表数据
	 *
	 * @return ShopDecoration.java
	 */
	private ShopDecoration shopDecorationInit(AggregationPlatform aggregationPlatform) {
		ShopDecoration shopDecoration = shopDecorationService.lambdaQuery().eq(ShopDecoration::getPlatforms, aggregationPlatform).one();
		if (BeanUtil.isEmpty(shopDecoration)) {
			shopDecoration = new ShopDecoration();
			shopDecoration.setPlatforms(aggregationPlatform);
			shopDecorationService.save(shopDecoration);
		}
		return shopDecoration;
	}
}
//