package com.medusa.gruul.shop.service.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.model.vo.ShopStatusQuantityVO;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.service.model.vo.ShopVO;
import com.medusa.gruul.shop.service.model.vo.SupplierStatisticsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * 商家注册信息 Mapper 接口
 * 
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
public interface ShopMapper extends BaseMapper<Shop> {

    /**
     * 分页查询店铺
     * @param page 分页参数
     * @return 分页查询结果
     */
    IPage<ShopVO> pageShop(@Param("page") ShopQueryPageDTO page);

    /**
     *  获取当日店铺新增数量
     *
     * @return 当日店铺新增数量
     */
    Long queryTodayAddShopQuantity();

    /**
     * 获取店铺数量 group by ShopStatus
     * @return 店铺数量
     */
    List<ShopStatusQuantityVO> queryShopQuantity();

    /**
     * 获取供应商数量
     *
     * @return 供应商数量
     */
    List<SupplierStatisticsVO> querySupplierQuantity();

    /**
     * C端店铺搜索
     * @param shopQueryPageDTO 搜索条件
     * @return 查询店铺结果
     */
    IPage<ShopInfoVO> searchShop(@Param("page") ShopQueryPageDTO shopQueryPageDTO);
}
