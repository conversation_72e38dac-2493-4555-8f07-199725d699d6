package com.medusa.gruul.shop.service.service;

import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.FunctionType;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.PropertiesVO;
import com.medusa.gruul.shop.api.entity.ShopDecorationDetails;
import com.medusa.gruul.shop.service.model.dto.ShopFunctionDecorationDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022-08-17 15:11
 */
public interface ShopRenovationService {

	/**
	 * 编辑装修功能详情(新增/修改)
	 *
	 * @param shopFunctionDecoration ShopFunctionDecorationDTO.class
	 */
	Long editFunctionDecoration(ShopFunctionDecorationDTO shopFunctionDecoration);


	/**
	 * 删除装修页面
	 *
	 * @param ids 装修页面ids
	 */
	void delDecorationPage(Set<Long> ids, AggregationPlatform aggregationPlatform);


	/**
	 * 修改页面名称
	 *
	 * @param name 名称
	 * @param id   页面id
	 */
	void updatePageName(String name, Long id);


	/**
	 * 获取页面基础信息 list
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return List<页面基础信息>
	 */
	List<ShopDecorationDetails> getPageBasicsInfoList(AggregationPlatform aggregationPlatform);


	/**
	 * 获取管理端装修首页信息
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return PropertiesVO.class
	 */
	PropertiesVO getDecorationInfo(AggregationPlatform aggregationPlatform);


	/**
	 * 设置页面为首页
	 *
	 * @param id                  页面id
	 * @param aggregationPlatform 聚合app平台类型
	 */
	void setHomePage(Long id, AggregationPlatform aggregationPlatform);


	/**
	 * 获取装修页面组件信息
	 *
	 * @param id 页面id
	 * @return PropertiesVO.class
	 */
	PropertiesVO getDecorationInfoById(Long id);


	/**
	 * 获取装修底部导航信息
	 *
	 * @param aggregationPlatform 页面所属聚合app平台类型
	 * @return PropertiesVO.class
	 */
	PropertiesVO getNavigationInfo(AggregationPlatform aggregationPlatform);

	/**
	 * 获取客户端装修详情
	 *
	 * @return 装修数据 页面+底部导航
	 */
	Map<String, List<Object>> getClientDecoration();


	/**
	 * 获取装修分类页信息
	 *
	 * @param aggregationPlatform 所属聚合app平台类型
	 * @return PropertiesVO.class
	 */
	PropertiesVO getClassifyPageInfo(AggregationPlatform aggregationPlatform);

	/**
	 * 获取非页面装修基础数据 by functionType
	 *
	 * @param aggregationPlatform 所属聚合app平台类型
	 * @param functionType        装修枚举
	 * @return 装修基础数据信息
	 */
	ShopDecorationDetails getNotPageBasicsInfo(AggregationPlatform aggregationPlatform, FunctionType functionType);

	/**
	 * 获取装修类型详情
	 *
	 * @param functionType 装修功能类型
	 * @return PropertiesVO.java
	 */
	PropertiesVO getFunctionTypeInfo(FunctionType functionType);

	/**
	 * 获取首页装修功能详情
	 *
	 * @return List<Object>
	 */
	List<Object> getHomePage();

	/**
	 * 获取非页面装修数据信息 by 枚举
	 *
	 * @param aggregationPlatform 聚合平台类型
	 * @param functionType        装修功能类型
	 * @return PropertiesVO.java
	 */
	PropertiesVO getNotPageDecorationInfo(AggregationPlatform aggregationPlatform, FunctionType functionType);


	/**
	 * 获取聚合装修类型及对应的首页名称
	 *
	 * @param aggregationPlatforms 聚合平台类型
	 * @return List<AggregationDecorationVO>
	 */
	List<AggregationDecorationVO> getAggregationPlatformDecorate(List<AggregationPlatform> aggregationPlatforms);
}
