package com.medusa.gruul.shop.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.shop.service.model.ShopConstant;
import com.medusa.gruul.shop.service.mp.entity.ShopGroup;
import com.medusa.gruul.shop.service.mp.entity.ShopGroupMapping;
import com.medusa.gruul.shop.service.mp.mapper.ShopGroupMapper;
import com.medusa.gruul.shop.service.mp.service.IShopGroupMappingService;
import com.medusa.gruul.shop.service.mp.service.IShopGroupService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * <p>
 * 店铺分组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Service
@AllArgsConstructor
public class ShopGroupServiceImpl extends ServiceImpl<ShopGroupMapper, ShopGroup> implements IShopGroupService {

    private final IShopGroupMappingService shopGroupMappingService;

    @Override
    public ShopGroup getByShopId(Long shopId) {
        return RedisUtil.getCacheMap(
                        ShopGroup.class,
                        () -> getMappingGroup(shopId),
                        Duration.ofHours(CommonPool.NUMBER_TWO),
                        Duration.ofSeconds(CommonPool.NUMBER_THIRTY),
                        ShopConstant.SHOP_GROUP_CACHE_KEY,
                        shopId
                );
    }

    private ShopGroup getMappingGroup(Long shopId) {
        ShopGroupMapping mapping = shopGroupMappingService.lambdaQuery().eq(ShopGroupMapping::getShopId, shopId).one();
        if (mapping == null) {
            return null;
        }
        return baseMapper.selectById(mapping.getShopGroupId());
    }
}
