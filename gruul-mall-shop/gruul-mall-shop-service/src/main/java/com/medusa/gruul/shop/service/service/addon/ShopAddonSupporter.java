package com.medusa.gruul.shop.service.service.addon;

import com.medusa.gruul.common.addon.supporter.annotation.AddonMethod;
import com.medusa.gruul.common.addon.supporter.annotation.AddonSupporter;
import com.medusa.gruul.shop.api.model.dto.SigningCategoryDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 店铺插件供应者
 *
 * <AUTHOR>
 * @Description ShopAddonSupporter.java
 * @date 2023-05-15 15:50
 */

@AddonSupporter(id = "shopSigningCategory")
public interface ShopAddonSupporter {

    /**
     * 编辑签约类目
     *
     * @param signingCategory 签约类目信息
     * @param shopId;
     * @return 是否成功
     */
    @AddonMethod(returnType = Boolean.class)
    Boolean editSingingCategory(List<SigningCategoryDTO> signingCategory, Long shopId);

    /**
     * 获取店铺的起送金额
     *
     * @param shopIds 店铺ids
     * @return 获取店铺的起送金额
     */
    @AddonMethod(returnType = Map.class)
    Map<Long, BigDecimal> ShopInitialDeliveryCharge(Set<Long> shopIds);


}
