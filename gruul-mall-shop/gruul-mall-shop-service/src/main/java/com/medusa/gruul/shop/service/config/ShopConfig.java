package com.medusa.gruul.shop.service.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.medusa.gruul.shop.service.properties.ShopConfigurationProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Description
 * @date 2022-09-23 11:14
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ShopConfig {

    /**
     * 异步执行线程池
     */
    @Bean
    public Executor shopExecutor(ShopConfigurationProperties shopConfigurationProperties) {
        ShopConfigurationProperties.TaskThreadPool taskThreadPool = shopConfigurationProperties.getThreadPool();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(taskThreadPool.getThreadNamePrefix());
        executor.setCorePoolSize(taskThreadPool.getCorePoolSize());
        executor.setMaxPoolSize(taskThreadPool.getMaxPoolSize());
        executor.setQueueCapacity(taskThreadPool.getQueueCapacity());
        executor.setKeepAliveSeconds(taskThreadPool.getKeepAliveSeconds());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return TtlExecutors.getTtlExecutor(executor);
    }
}
