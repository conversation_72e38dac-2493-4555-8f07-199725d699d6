package com.medusa.gruul.shop.service.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.idem.annotation.Idem;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.service.model.dto.ShopDTO;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.model.vo.ShopVO;
import com.medusa.gruul.shop.service.model.vo.SupplierStatisticsVO;
import com.medusa.gruul.shop.service.service.ShopManageService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商家注册信息 前端控制器
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop")
public class ShopController {

    private final ShopManageService shopManageService;


    /**
     * 分页查询店铺
     *
     * @param page 分页参数
     * @return 分页查询结果
     */
    @Log("分页查询商户列表")
    @GetMapping
    public Result<IPage<ShopVO>> pageShop(ShopQueryPageDTO page) {
        return Result.ok(
                shopManageService.pageShop(page)
        );
    }

    /**
     * 新增店铺信息
     *
     * @param shop 店铺信息
     */
    @Idem
    @Log("新增商户")
    @PostMapping
    @PreAuthorize("""
                              @S.matcher()
                              .any(@S.ROLES,@S.PLATFORM_ADMIN,@S.USER)
                              .or(@S.consumer().eq(@S.ROLES, @S.PLATFORM_CUSTOM_ADMIN).eq(@S.PERMS,'shopList'))
                              .match()
            """)
    public Result<Void> newShop(@RequestBody @Valid ShopDTO shop) {
        shopManageService.newShop(shop);
        return Result.ok();
    }

    /**
     * 编辑店铺信息
     *
     * @param shopId 店铺id
     * @param shop   店铺信息
     */
    @Idem
    @Log("编辑商户")
    @PutMapping("/{shopId}")
    public Result<Void> editShop(@PathVariable @Min(1) Long shopId, @RequestBody @Valid ShopDTO shop) {
        shopManageService.editShop(shopId, shop);
        return Result.ok();
    }

    /**
     * 店铺审核
     *
     * @param shopId 店铺id
     * @param pass   是否审核通过
     */
    @Idem
    @Log("店铺审核")
    @PutMapping("/{shopId}/{pass}")
    public Result<Void> shopAudit(@PathVariable("shopId") Long shopId, @PathVariable("pass") Boolean pass) {
        shopManageService.shopAudit(shopId, pass);
        return Result.ok();
    }

    /**
     * 批量删除商家信息
     *
     * @param shopIds 商家id列表
     */
    @Idem
    @Log("商户批量删除")
    @DeleteMapping
    public Result<Void> batchDeleteShop(@RequestParam @Size(min = 1) Set<Long> shopIds) {
        shopManageService.batchDeleteShop(shopIds);
        return Result.ok();
    }

    /**
     * 批量启用禁用禁用商家
     *
     * @param isEnable 是否启用
     * @param shopIds  商家id列表
     */
    @Idem(value = 500)
    @Log("商户批量启用禁用")
    @PatchMapping("/{isEnable}")
    public Result<Void> batchEnableDisableShops(@PathVariable Boolean isEnable, @RequestParam @Size(min = 1) Set<Long> shopIds) {
        shopManageService.enableDisableShop(isEnable, shopIds);
        return Result.ok();
    }

    /**
     * 获取今日新增店铺数量
     *
     * @return 今日店铺新增数量
     */
    @Log("获取今日新增店铺数量")
    @GetMapping("/today/shopQuantity")
    public Result<Long> getTodayAddShopQuantity() {
        return Result.ok(shopManageService.getTodayAddShopQuantity());
    }


    /**
     * 获取店铺数量
     *
     * @return 店铺数量
     */
    @Log("获取店铺数量")
    @GetMapping("/shopQuantity")
    public Result<Map<ShopStatus, Long>> getShopQuantity() {
        return Result.ok(shopManageService.getShopQuantity());
    }

    /**
     * 获取供应商数量
     *
     * @return 供应商数量
     */
    @Log("获取供应商数量")
    @GetMapping("/supplierQuantity")
    public Result<List<SupplierStatisticsVO>> getSupplierQuantity() {
        return Result.ok(shopManageService.getSupplierQuantity());
    }

}

