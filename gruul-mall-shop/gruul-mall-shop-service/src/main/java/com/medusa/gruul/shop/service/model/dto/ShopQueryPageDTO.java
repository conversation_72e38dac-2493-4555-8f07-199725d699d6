package com.medusa.gruul.shop.service.model.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.shop.api.enums.ExtractionType;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.enums.ShopType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;


/**
 * <AUTHOR>
 * date 2022/4/18
 */
@Getter
@Setter
@ToString
public class ShopQueryPageDTO extends Page<Object> {
	/**
	 * 商户id
	 */
	private String no;
	/**
	 * 商户名称
	 */
	private String name;
	/**
	 * 商户状态
	 */
	private ShopStatus status;

	/**
	 * 店铺类型
	 */
	private ShopType shopType;

	/**
	 * 店铺运营模式
	 */
	private Set<ShopMode> shopModes;


	/**
	 * 抽佣类型
	 */
	private ExtractionType extractionType;

	/**
	 * 经营模式
	 */
	private Mode mode;
}
