package com.medusa.gruul.shop.service.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import com.medusa.gruul.shop.api.enums.ShopDataSyncType;

import java.util.List;

/**
 * 商品同步表 服务类
 *
 * <AUTHOR>
 */
public interface IShopDataSyncRecordService extends IService<ShopDataSyncRecord> {
    /**
     * 查询目标店铺最新的同步记录
     * @param targetShopId 目标店铺id
     * @return
     */
    List<ShopDataSyncRecord> getLatestRecordByTargetShopId(Long targetShopId, ShopDataSyncType syncType);

}
