package com.medusa.gruul.shop.service.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.shop.api.entity.ShopLogisticsAddress;
import com.medusa.gruul.shop.api.enums.AddressDefaultEnum;
import com.medusa.gruul.shop.api.enums.AddressTypeEnum;
import com.medusa.gruul.shop.service.model.dto.ShopLogisticsAddressDTO;
import com.medusa.gruul.shop.service.model.enums.ShopError;
import com.medusa.gruul.shop.service.model.param.ShopLogisticsAddressParam;
import com.medusa.gruul.shop.service.mp.service.IShopLogisticsAddressService;
import com.medusa.gruul.shop.service.service.ShopLogisticsAddressManageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Description 店铺物流地址管理 ServiceImpl
 * @date 2023-05-08 13:57
 */
@Service
@RequiredArgsConstructor
public class ShopLogisticsAddressManageServiceImpl implements ShopLogisticsAddressManageService {

	private final IShopLogisticsAddressService shopLogisticsAddressService;


	/**
	 * 设置或修改物流地址
	 *
	 * @param logisticsAddress 物流地址dto
	 */
	@Override
	public void editAddress(ShopLogisticsAddressDTO logisticsAddress) {
		ShopLogisticsAddress shopLogisticsAddress = logisticsAddress.coverLogisticsAddress();
		long sameAddressCount = countSameLogisticsAddress(shopLogisticsAddress);
		ShopError.SHOP_LOGISTICS_ADDRESS_EXISTED.trueThrow(sameAddressCount > CommonPool.NUMBER_ZERO);
		if (shopLogisticsAddress.getId() == null) {
			// 新增地址时设置为默认收发货地址
			if (this.shopLogisticsAddressService.lambdaQuery().count() == CommonPool.NUMBER_ZERO.intValue()) {
				shopLogisticsAddress.setDefSend(AddressDefaultEnum.YES);
				shopLogisticsAddress.setDefReceive(AddressDefaultEnum.YES);
			} else {
				shopLogisticsAddress.setDefSend(AddressDefaultEnum.NO);
				shopLogisticsAddress.setDefReceive(AddressDefaultEnum.NO);
			}
			boolean saveResult = this.shopLogisticsAddressService.save(shopLogisticsAddress);
			SystemCode.DATA_ADD_FAILED.falseThrow(saveResult);
			return;
		}
		boolean updateResult = this.shopLogisticsAddressService.updateById(shopLogisticsAddress);
		SystemCode.DATA_UPDATE_FAILED.falseThrow(updateResult);
	}


	/**
	 * 地址信息删除
	 *
	 * @param id 物流地址id
	 */
	@Override
	public void delAddress(Long id) {
		ShopLogisticsAddress address = this.shopLogisticsAddressService
				.lambdaQuery()
				.select(ShopLogisticsAddress::getDefReceive, ShopLogisticsAddress::getDefSend)
				.eq(ShopLogisticsAddress::getId, id)
				.one();
		SystemCode.DATA_NOT_EXIST.trueThrow(address == null);
		ShopError.SHOP_DEFAULT_LOGISTICS_ADDRESS_NOT_DEL.trueThrow(isDefaultAddress(address));
		boolean removeResult = this.shopLogisticsAddressService.removeById(id);
		SystemCode.DATA_DELETE_FAILED.falseThrow(removeResult);
	}


	/**
	 * 获取店铺地址信息
	 *
	 * @param logisticsAddressParam 分页param
	 */
	@Override
	public IPage<ShopLogisticsAddress> getAddressListByPage(ShopLogisticsAddressParam logisticsAddressParam) {
		return this.shopLogisticsAddressService.lambdaQuery().page(logisticsAddressParam);
	}


	/**
	 * 获取物流地址信息By id
	 *
	 * @param id 物流地址id
	 * @return 物流地址
	 */
	@Override
	public ShopLogisticsAddress getAddressById(Long id) {
		ShopLogisticsAddress logisticsAddressInfo = shopLogisticsAddressService.getById(id);
		SystemCode.DATA_NOT_EXIST.trueThrow(logisticsAddressInfo == null);
		return logisticsAddressInfo;
	}

	/**
	 * 设置默认地址信息
	 *
	 * @param id   地址id
	 * @param type 收发货类型
	 */
	@Override
	public void setDefAddress(Long id, AddressTypeEnum type) {
		if (type == AddressTypeEnum.DEF_SEND) {
			updateDefaultAddress(id, ShopLogisticsAddress::getDefSend, "设置默认发货地址失败！");
		} else {
			updateDefaultAddress(id, ShopLogisticsAddress::getDefReceive, "设置默认收货地址失败！");
		}
	}

	private void updateDefaultAddress(Long id, SFunction<ShopLogisticsAddress, ?> defaultGetter,
	                                  String errorMessage) {
		boolean updateResult =
				this.shopLogisticsAddressService.lambdaUpdate()
						.eq(defaultGetter, AddressDefaultEnum.YES)
						.set(defaultGetter, AddressDefaultEnum.NO)
						.update()
						&&
						this.shopLogisticsAddressService.lambdaUpdate()
								.eq(BaseEntity::getId, id)
								.set(defaultGetter, AddressDefaultEnum.YES)
								.update();
		SystemCode.DATA_UPDATE_FAILED.falseThrow(updateResult);
	}


	private long countSameLogisticsAddress(ShopLogisticsAddress shopLogisticsAddress) {
		return this.shopLogisticsAddressService.lambdaQuery()
				.eq(ShopLogisticsAddress::getContactName, shopLogisticsAddress.getContactName())
				.eq(ShopLogisticsAddress::getProvinceCode, shopLogisticsAddress.getProvinceCode())
				.eq(ShopLogisticsAddress::getCityCode, shopLogisticsAddress.getCityCode())
				.eq(ShopLogisticsAddress::getRegionCode, shopLogisticsAddress.getRegionCode())
				.eq(ShopLogisticsAddress::getAddress, shopLogisticsAddress.getAddress())
				.eq(ShopLogisticsAddress::getZipCode, shopLogisticsAddress.getZipCode())
				.eq(ShopLogisticsAddress::getContactPhone, shopLogisticsAddress.getContactPhone())
				.count();
	}

	private boolean isDefaultAddress(ShopLogisticsAddress shopLogisticsAddress) {
		return AddressDefaultEnum.YES == shopLogisticsAddress.getDefReceive()
				|| AddressDefaultEnum.YES == shopLogisticsAddress.getDefSend();
	}
}
