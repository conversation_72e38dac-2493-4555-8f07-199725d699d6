package com.medusa.gruul.shop.service.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.FunctionType;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.PropertiesVO;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import com.medusa.gruul.shop.api.entity.ShopDecorationDetails;
import com.medusa.gruul.shop.api.enums.*;
import com.medusa.gruul.shop.api.model.dto.ShopProductSyncDTO;
import com.medusa.gruul.shop.api.model.dto.ShopQueryDTO;
import com.medusa.gruul.shop.service.model.dto.ShopFunctionDecorationDTO;
import com.medusa.gruul.shop.service.mp.service.IShopDataSyncRecordService;
import com.medusa.gruul.shop.service.mp.service.IShopService;
import com.medusa.gruul.shop.service.service.ShopDecorationSyncService;
import com.medusa.gruul.shop.service.service.ShopRenovationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopDecorationSyncServiceImpl implements ShopDecorationSyncService {
    @Value("${gruul.shop.defaultShopId}")
    private Long defaultShopId;

    private final IShopService shopService;

    private final ShopRenovationService shopRenovationService;

    private final IShopDataSyncRecordService shopDataSyncRecordService;


    @Override
    public void shopDecoration(ShopProductSyncDTO shopProductSyncDTO) {
        //检查当前操作的店铺是否为默认店铺
        if (!ISystem.shopIdOpt().get().equals(defaultShopId)) {
            throw new ServiceException("操作权限不足！");
        }
        log.debug("店铺装修同步数据条件:{}", JSONUtil.toJsonStr(shopProductSyncDTO));
        List<Shop> syncShopAll = getSyncShop(shopProductSyncDTO);
        //是否有店铺需要同步
        if (ObjectUtils.isEmpty(syncShopAll)) {
            log.debug("没有需要同步的店铺");
            return;
        }
        //要同步的首页数据
        List<ShopDecorationDetails> shopDecorationDetails = shopRenovationService.getPageBasicsInfoList(AggregationPlatform.OTHERS);
        if (ObjectUtils.isEmpty(shopDecorationDetails)) {
            return;
        }
        convertProperties(shopDecorationDetails);
        //分类数据
        PropertiesVO categoryInfo = shopRenovationService.getNotPageDecorationInfo(AggregationPlatform.OTHERS, FunctionType.CLASSIFY_PAGE);
        //底部导航
        PropertiesVO tabbarInfo = shopRenovationService.getNotPageDecorationInfo(AggregationPlatform.OTHERS, FunctionType.TABBAR);
        //
        syncShopAll.forEach(shop -> ISystem.shopId(shop.getId(), () -> {
            //原同步数据
            List<ShopDataSyncRecord> recordTargetShopId = shopDataSyncRecordService.getLatestRecordByTargetShopId(shop.getId(), ShopDataSyncType.DECORATION);
            Map<Long, ShopDataSyncRecord> oldRecordMap = recordTargetShopId.stream().collect(Collectors.toMap(ShopDataSyncRecord::getSyncDataId, v -> v));

            //原同步的商品数据
            List<ShopDataSyncRecord> recordTargetGoodsShopId = shopDataSyncRecordService.getLatestRecordByTargetShopId(shop.getId(), ShopDataSyncType.PRODUCT);
            Map<Long, ShopDataSyncRecord> oldGoodsRecordMap = recordTargetGoodsShopId.stream().collect(Collectors.toMap(ShopDataSyncRecord::getSyncDataId, v -> v));
            deleteDecoration(oldRecordMap, AggregationPlatform.OTHERS);
            //同步自定义页面
            syncBasicos(shopDecorationDetails, defaultShopId, shop, oldRecordMap, oldGoodsRecordMap);
            //分类
            syncCategory(categoryInfo, defaultShopId, shop, oldRecordMap);
            //底部导航
            syncTabbar(tabbarInfo, defaultShopId, shop, oldRecordMap);
        }));

    }
    //组装装修数据
    private void convertProperties(List<ShopDecorationDetails> shopDecorationDetails) {
        shopDecorationDetails.forEach(decoration -> {
            PropertiesVO decorationInfoById = shopRenovationService.getDecorationInfoById(decoration.getId());
            decoration.setProperties(JSON.toJSONString(decorationInfoById.getProperties()));
        });

    }

    /**
     * 同步底部导航
     *
     * @param propertiesVO 要同步的数据
     * @param shopId 源数据店铺的id
     * @param targetShop 同步的目标店铺
     * @param oldRecordMap 目标店铺历史同步数据
     */
    private void syncTabbar(PropertiesVO propertiesVO, Long shopId, Shop targetShop,
                              Map<Long, ShopDataSyncRecord> oldRecordMap) {

        //同步记录
        List<ShopDataSyncRecord> recordList = Lists.newArrayList();
        //同步
        ShopDataSyncRecord shopDataSyncRecord = new ShopDataSyncRecord();
        shopDataSyncRecord.setSyncDataId(propertiesVO.getId())
                .setSyncData(JSON.parseObject(JSONUtil.toJsonStr(propertiesVO)));
        //存在则更新
        if (oldRecordMap.containsKey(propertiesVO.getId())) {
            ShopDataSyncRecord oldRecord = oldRecordMap.get(propertiesVO.getId());
            JSONObject productJson = JSON.parseObject(JSONUtil.toJsonStr(propertiesVO));
            //判断数据是否变更
            if (null != oldRecord && null != oldRecord.getSyncData() && equalsJson(productJson, oldRecord.getSyncData())) {
                return;
            }
            //替换店铺id
            String jsonStr = JSONUtil.toJsonStr(propertiesVO.getProperties());
            String replace = jsonStr.replaceAll(shopId + "", targetShop.getId() + "");
            //入库
            ShopFunctionDecorationDTO decorationDTO = new ShopFunctionDecorationDTO();
            decorationDTO.setId(oldRecord.getTargetDataId());
            decorationDTO.setProperties(JSON.parseArray(replace));
            decorationDTO.setPlatforms(AggregationPlatform.OTHERS);
            decorationDTO.setFunctionType(FunctionType.TABBAR);
            shopRenovationService.editFunctionDecoration(decorationDTO);

            convertSyncRecord(ShopDataSyncChangeType.UPDATE, shopDataSyncRecord, shopId, propertiesVO.getId(), targetShop.getId(), decorationDTO);
        } else {
            ShopDecorationDetails tabbar = shopRenovationService.getNotPageBasicsInfo(AggregationPlatform.OTHERS, FunctionType.TABBAR);
            //替换店铺id
            String jsonStr = JSONUtil.toJsonStr(propertiesVO.getProperties());
            String replace = jsonStr.replaceAll(shopId + "", targetShop.getId() + "");
            //入库
            ShopFunctionDecorationDTO decorationDTO = new ShopFunctionDecorationDTO();
            if (null != tabbar) {
                decorationDTO.setId(tabbar.getId());
            } else {
                decorationDTO.setId(null);
            }
            decorationDTO.setProperties(JSON.parseArray(replace));
            decorationDTO.setPlatforms(AggregationPlatform.OTHERS);
            decorationDTO.setFunctionType(FunctionType.TABBAR);
            Long id = shopRenovationService.editFunctionDecoration(decorationDTO);
            decorationDTO.setId(id);
            convertSyncRecord(ShopDataSyncChangeType.ADD, shopDataSyncRecord, shopId, propertiesVO.getId(), targetShop.getId(), decorationDTO);
        }
        recordList.add(shopDataSyncRecord);
        //入库
        if (!ObjectUtils.isEmpty(recordList)) {
            shopDataSyncRecordService.saveBatch(recordList);
        }
    }
    /**
     * 同步分类
     *
     * @param propertiesVO 要同步的数据
     * @param shopId 源数据店铺的id
     * @param targetShop 同步的目标店铺
     * @param oldRecordMap 目标店铺历史同步数据
     */
    private void syncCategory(PropertiesVO propertiesVO, Long shopId, Shop targetShop,
                             Map<Long, ShopDataSyncRecord> oldRecordMap) {

        //同步记录
        List<ShopDataSyncRecord> recordList = Lists.newArrayList();
        //同步
        ShopDataSyncRecord shopDataSyncRecord = new ShopDataSyncRecord();
        shopDataSyncRecord.setSyncDataId(propertiesVO.getId())
                .setSyncData(JSON.parseObject(JSONUtil.toJsonStr(propertiesVO)));
        //存在则更新
        if (oldRecordMap.containsKey(propertiesVO.getId())) {
            ShopDataSyncRecord oldRecord = oldRecordMap.get(propertiesVO.getId());
            JSONObject productJson = JSON.parseObject(JSONUtil.toJsonStr(propertiesVO));
            //判断数据是否变更
            if (null != oldRecord && null != oldRecord.getSyncData() && equalsJson(productJson, oldRecord.getSyncData())) {
                return;
            }
            //入库
            ShopFunctionDecorationDTO decorationDTO = new ShopFunctionDecorationDTO();
            decorationDTO.setId(oldRecord.getTargetDataId());
            decorationDTO.setProperties(propertiesVO.getProperties());
            decorationDTO.setPlatforms(AggregationPlatform.OTHERS);
            decorationDTO.setFunctionType(FunctionType.CLASSIFY_PAGE);
            shopRenovationService.editFunctionDecoration(decorationDTO);

            convertSyncRecord(ShopDataSyncChangeType.UPDATE, shopDataSyncRecord, shopId, propertiesVO.getId(), targetShop.getId(), decorationDTO);
        } else {
            //入库
            ShopFunctionDecorationDTO decorationDTO = new ShopFunctionDecorationDTO();
            decorationDTO.setId(null);
            decorationDTO.setProperties(propertiesVO.getProperties());
            decorationDTO.setPlatforms(AggregationPlatform.OTHERS);
            decorationDTO.setFunctionType(FunctionType.CLASSIFY_PAGE);
            Long id = shopRenovationService.editFunctionDecoration(decorationDTO);
            decorationDTO.setId(id);

            convertSyncRecord(ShopDataSyncChangeType.ADD, shopDataSyncRecord, shopId, propertiesVO.getId(), targetShop.getId(), decorationDTO);
        }
        recordList.add(shopDataSyncRecord);
        //入库
        if (!ObjectUtils.isEmpty(recordList)) {
            shopDataSyncRecordService.saveBatch(recordList);
        }
    }

    private void deleteDecoration(Map<Long, ShopDataSyncRecord> oldRecordMap, AggregationPlatform aggregationPlatform) {
        //如果历史数据为空，则先删除后续在新增
        if (ObjectUtils.isEmpty(oldRecordMap)) {
            try {
                //删除首页
                List<ShopDecorationDetails> targetShopDecorationDetails = shopRenovationService.getPageBasicsInfoList(AggregationPlatform.OTHERS);
                if (!ObjectUtils.isEmpty(targetShopDecorationDetails)) {
                    Set<Long> delList = targetShopDecorationDetails.stream().map(ShopDecorationDetails::getId).collect(Collectors.toSet());
                    shopRenovationService.delDecorationPage(delList, aggregationPlatform);
                }
                //分类
                ShopDecorationDetails classify = shopRenovationService.getNotPageBasicsInfo(aggregationPlatform, FunctionType.CLASSIFY_PAGE);
                if (!ObjectUtils.isEmpty(classify)) {
                    Set<Long> delList = new HashSet<>(1);
                    delList.add(classify.getId());
                    shopRenovationService.delDecorationPage(delList, aggregationPlatform);
                }
            } catch (Exception e) {
                log.warn("删除失败: ", e);
            }
        }
    }

    /**
     * 同步自定义页面
     *
     * @param shopDecorationDetails 要同步的数据
     * @param shopId                源数据店铺的id
     * @param targetShop            同步的目标店铺
     * @param oldRecordMap          目标店铺历史同步数据
     */
    private void syncBasicos(List<ShopDecorationDetails> shopDecorationDetails, Long shopId, Shop targetShop,
                             Map<Long, ShopDataSyncRecord> oldRecordMap, Map<Long, ShopDataSyncRecord> recordTargetGoodsShopId) {
        //同步记录
        List<ShopDataSyncRecord> recordList = Lists.newArrayList();
        //变更数据
        //同步
        for (ShopDecorationDetails decoration : shopDecorationDetails) {
            ShopDataSyncRecord shopDataSyncRecord = new ShopDataSyncRecord();
            shopDataSyncRecord.setSyncDataId(decoration.getId())
                    .setSyncData(JSON.parseObject(JSONUtil.toJsonStr(decoration)));
            //存在则更新
            if (oldRecordMap.containsKey(decoration.getId())) {
                ShopDataSyncRecord oldRecord = oldRecordMap.get(decoration.getId());
                JSONObject productJson = JSON.parseObject(JSONUtil.toJsonStr(decoration));
                //判断数据是否变更
                if (null != oldRecord && null != oldRecord.getSyncData() && equalsJson(productJson, oldRecord.getSyncData())) {
                    continue;
                }
                //properties
                String propertiesStr = updateTargetProperties(decoration.getProperties(), recordTargetGoodsShopId);
                //入库
                ShopFunctionDecorationDTO decorationDTO = new ShopFunctionDecorationDTO();
                BeanUtils.copyProperties(decoration, decorationDTO);
                decorationDTO.setId(oldRecord.getTargetDataId());
                decorationDTO.setPlatforms(AggregationPlatform.OTHERS);
                decorationDTO.setProperties(JSON.parseArray(propertiesStr));
                shopRenovationService.editFunctionDecoration(decorationDTO);

                convertSyncRecord(ShopDataSyncChangeType.UPDATE, shopDataSyncRecord, shopId, decoration.getId(), targetShop.getId(), decorationDTO);
            } else {
                //properties
                String propertiesStr = updateTargetProperties(decoration.getProperties(), recordTargetGoodsShopId);
                //入库
                ShopFunctionDecorationDTO decorationDTO = new ShopFunctionDecorationDTO();
                BeanUtils.copyProperties(decoration, decorationDTO);
                decorationDTO.setId(null);
                decorationDTO.setPlatforms(AggregationPlatform.OTHERS);
                decorationDTO.setProperties(JSON.parseArray(propertiesStr));
                Long id = shopRenovationService.editFunctionDecoration(decorationDTO);
                decorationDTO.setId(id);
//                shopRenovationService.editFunctionDecoration(decorationDTO);

                convertSyncRecord(ShopDataSyncChangeType.ADD, shopDataSyncRecord, shopId, id, targetShop.getId(), decorationDTO);
            }
            recordList.add(shopDataSyncRecord);
        }
        //入库
        if (!ObjectUtils.isEmpty(recordList)) {
            shopDataSyncRecordService.saveBatch(recordList);
        }

    }
    /**
     * 处理渲染默认店铺首页的JSON数据
     *
     * @param properties 首页渲染的JSONString
     * @param targetShopGoodsRecordMap 目标店铺商品同步记录
     * @return 目标店铺的JSONString
     */
    private String updateTargetProperties(String properties, Map<Long, ShopDataSyncRecord> targetShopGoodsRecordMap) {
        List<JSONObject> propertiesList = JSON.parseObject(properties, new TypeReference<>(){});
        propertiesList.forEach(json -> {
            //处理轮播图
            if (json.containsKey("icon") && json.get("icon").equals("lunbotu")) {
                JSONObject formData = json.getJSONObject("formData");
                JSONArray swiperArr = formData.getJSONArray("swiperList");
                swiperArr.stream().forEach(item -> {
                    //处理link
                    JSONObject jsonObject = (JSONObject) item;
                    JSONObject link = jsonObject.getJSONObject("link");
                    if (!link.containsKey("type") || !"1".equals(link.getString("type"))) {
                        return;
                    }
                    Long id = link.getLong("id");
                    if (null == id || !targetShopGoodsRecordMap.containsKey(id)) {
                        return;
                    }
                    //查询目标店铺的商品
                    ShopDataSyncRecord shopDataSyncRecord = targetShopGoodsRecordMap.get(id);
                    link.put("id", shopDataSyncRecord.getTargetDataId());
                    link.put("shopId", shopDataSyncRecord.getTargetShopId());
                });
            }
            //处理金刚区
            if (json.containsKey("icon") && json.get("icon").equals("dianpudaohang")) {
                JSONObject formData = json.getJSONObject("formData");
                JSONArray navigationList = formData.getJSONArray("navigationList");
                navigationList.stream().forEach(item -> {
                    //处理link
                    JSONObject jsonObject = (JSONObject) item;
                    JSONObject link = jsonObject.getJSONObject("link");
                    if (!link.containsKey("type") || !"1".equals(link.getString("type"))) {
                        return;
                    }
                    Long id = link.getLong("id");
                    if (null == id || !targetShopGoodsRecordMap.containsKey(id)) {
                        return;
                    }
                    //查询目标店铺的商品
                    ShopDataSyncRecord shopDataSyncRecord = targetShopGoodsRecordMap.get(id);
                    link.put("id", shopDataSyncRecord.getTargetDataId());
                    link.put("shopId", shopDataSyncRecord.getTargetShopId());
                });
            }
        });
        System.out.println();
        return propertiesList.toString();
    }

    //同步记录
    private void convertSyncRecord(ShopDataSyncChangeType type, ShopDataSyncRecord shopDataSyncRecord, Long shopId,
                                   Long decorationId, Long targetShopId, ShopFunctionDecorationDTO targetDecoration) {
        shopDataSyncRecord.setTargetDataId(targetDecoration.getId());
        if (shopDataSyncRecord.getStatus() == null) {
            shopDataSyncRecord.setStatus(ShopDataStatus.SUCCESS);
        }
        if (shopDataSyncRecord.getSyncDataId() == null) {
            shopDataSyncRecord.setSyncDataId(decorationId);
        }
        shopDataSyncRecord.setShopId(shopId)
                .setSyncType(ShopDataSyncType.DECORATION)
                .setTargetShopId(targetShopId)
                .setSyncChangeType(type)
                .setSyncChangeData(JSON.parseObject(JSONUtil.toJsonStr(targetDecoration)))
        ;
    }
    //判断是否相同,相同true
    private boolean equalsJson(JSONObject a, JSONObject b) {
        cn.hutool.json.JSON obj1 = JSONUtil.parse(a.toJSONString());
        cn.hutool.json.JSON obj2 = JSONUtil.parse(b.toJSONString());
        return obj1.equals(obj2);
    }

    private List<Shop> getSyncShop(ShopProductSyncDTO shopProductSyncDTO) {
        //查询需要同步店铺
        ShopQueryDTO shopQueryDTO = new ShopQueryDTO();
        shopQueryDTO.setStatus(ShopStatus.NORMAL).setShopType(ShopType.PREFERRED);
        List<Shop> syncShopAll = shopService.getShop(shopQueryDTO);
        //只同步指定类型
        if (shopProductSyncDTO.getTargetShopId() != null) {
            syncShopAll = syncShopAll.stream()
                    .filter(shop -> shop.getId().equals(shopProductSyncDTO.getTargetShopId()))
                    .filter(shop -> !shop.getId().equals(defaultShopId))
                    .collect(Collectors.toList());
        } else {
            syncShopAll = syncShopAll.stream()
                    .filter(shop -> !shop.getId().equals(defaultShopId))
                    .collect(Collectors.toList());
        }
        return syncShopAll;
    }


}
