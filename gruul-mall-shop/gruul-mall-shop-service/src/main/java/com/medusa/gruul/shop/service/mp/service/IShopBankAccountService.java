package com.medusa.gruul.shop.service.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.shop.api.entity.ShopBankAccount;

import java.util.List;
import java.util.Set;

/**
 * 
 * 商家收款账号信息 服务类
 * 
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
public interface IShopBankAccountService extends IService<ShopBankAccount> {
    /**
     * 查询店铺账号信息
     * @param shopIds          店铺id
     * @return 账号信息
     */
    List<ShopBankAccount> queryShopAccountsByShopId(Set<Long> shopIds);
}
