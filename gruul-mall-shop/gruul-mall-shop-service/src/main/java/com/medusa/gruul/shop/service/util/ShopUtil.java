package com.medusa.gruul.shop.service.util;

import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.shop.api.constant.ShopConstant;

/**
 * <AUTHOR>
 * @Description
 * @date 2022-08-18 14:36
 */
public final class ShopUtil {
    private ShopUtil() {

    }

    public static String shopCacheKey(Long shopId, AggregationPlatform aggregationPlatform) {
        return RedisUtil.key(ShopConstant.SHOP_PAGE_CACHE_KEY, shopId, aggregationPlatform);
    }

}
