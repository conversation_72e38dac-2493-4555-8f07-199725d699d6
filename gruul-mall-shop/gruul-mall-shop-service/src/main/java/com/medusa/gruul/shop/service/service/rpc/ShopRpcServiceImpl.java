package com.medusa.gruul.shop.service.service.rpc;


import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONObject;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.shop.api.constant.LogisticsConstant;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopBankAccount;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import com.medusa.gruul.shop.api.entity.ShopLogisticsAddress;
import com.medusa.gruul.shop.api.enums.ShopDataSyncType;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.model.dto.ShopQueryDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.model.vo.ShopLogisticsAddressVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.shop.service.mp.entity.ShopGroup;
import com.medusa.gruul.shop.service.mp.service.*;
import com.medusa.gruul.shop.service.service.ShopInfoService;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@DubboService
@RequiredArgsConstructor
public class ShopRpcServiceImpl implements ShopRpcService {

	private final IShopLogisticsAddressService logisticsAddressService;

	private final IShopService shopService;
	private final IShopBankAccountService shopBankAccountService;
	private final ShopInfoService shopInfoService;
	private final IShopDataSyncRecordService shopDataSyncRecordService;
	private final IShopGroupService shopGroupService;

	/**
	 * 根据店铺id查询店铺基本信息
	 *
	 * @param shopId 店铺id
	 * @return 店铺基本信息
	 */
	@Override
	public ShopInfoVO getShopInfoByShopId(Long shopId) {
		return this.shopInfoService.getShopById(shopId)
				.map(ShopInfoVO::fromShop)
				.getOrNull();
	}

	/**
	 * 根据店铺id集合批量获取店铺基本信息
	 *
	 * @param shopIds 店铺id集合
	 * @return 店铺基本信息
	 */
	@Override
	public List<ShopInfoVO> getShopInfoByShopIdList(Set<Long> shopIds) {
		return this.shopService.listByIds(shopIds)
				.stream()
				.map(ShopInfoVO::fromShop)
				.collect(Collectors.toList());
	}

	/**
	 * 根据店铺id集合批量获取店铺基本信息与银行信息
	 *
	 * @param shopIds 店铺id集合
	 * @return 店铺基本信息
	 */
	@Override
	public List<ShopInfoVO> getShopAndShopBankInfoByShopIdList(Set<Long> shopIds) {
		List<ShopBankAccount> bankAccountList = shopBankAccountService.queryShopAccountsByShopId(shopIds);
		Map<Long, ShopBankAccount> bankAccountMap = bankAccountList.stream().collect(Collectors.toMap(
				ShopBankAccount::getShopId, Function.identity()
		));
		return this.shopService.listByIds(shopIds)
				.stream()
				.map(shop -> {
					ShopInfoVO shopInfoVO = ShopInfoVO.fromShop(shop);
					shopInfoVO.setBankAccount(bankAccountMap.get(shop.getId()));
					return shopInfoVO;
				})
				.collect(Collectors.toList());
	}


	@Override
	public Option<Shop> getShopAndShopBankInfo(Long shopId) {
		return shopInfoService.getShopById(shopId)
				.peek(shop -> shop
						.setLocation(null)
						.setBankAccount(
								shopBankAccountService.lambdaQuery()
										.eq(ShopBankAccount::getShopId, shopId)
										.one()
						));
	}

	/**
	 * 获取 默认的发货地址/退货地址
	 *
	 * @param shopId 店铺id
	 * @param isSend 是否是收货地址
	 * @return 发货地址/收货地址
	 */
	@Override
	public ShopLogisticsAddressVO getSendOrReceiveAddress(Long shopId, Boolean isSend) {
		ShopLogisticsAddress logisticsAddress = ISystem.shopId(
				shopId,
				() -> logisticsAddressService.lambdaQuery()
						.orderByDesc(isSend ? ShopLogisticsAddress::getDefSend : ShopLogisticsAddress::getDefReceive)
						.last(CommonPool.SQL_LIMIT_1)
						.one()
		);
		if (logisticsAddress == null) {
			return null;
		}
		String provinceCode = logisticsAddress.getProvinceCode();
		String cityCode = logisticsAddress.getCityCode();
		String regionCode = logisticsAddress.getRegionCode();
		JSONObject areaCache = RedisUtil.getCacheMap(LogisticsConstant.CHINA_AREA_CACHE_KEY, JSONObject.class);
		if (areaCache == null) {
			throw new NullPointerException("未正确获取省市区缓存,请检查物流服务是否正常加载省市区缓存");
		}
		return new ShopLogisticsAddressVO()
				.setContactName(logisticsAddress.getContactName())
				.setContactPhone(logisticsAddress.getContactPhone())
				.setZipCode(logisticsAddress.getZipCode())
				.setAreaCodes(ListUtil.toList(provinceCode, cityCode, regionCode))
				.setAreaNames(
						ListUtil.toList(
								areaCache.getByPath("86." + provinceCode, String.class),
								areaCache.getByPath(provinceCode + StrPool.DOT + cityCode, String.class),
								areaCache.getByPath(cityCode + StrPool.DOT + regionCode, String.class)
						)
				).setAddress(logisticsAddress.getAddress());
	}

	@Override
	public List<Shop> getShop(ShopQueryDTO shopQueryDTO) {
		return shopService.getShop(shopQueryDTO);
	}

	@Override
	public void createShopSyncRecord(List<ShopDataSyncRecord> shopDataSyncRecordList) {
		shopDataSyncRecordService.saveBatch(shopDataSyncRecordList);
	}

	@Override
	public List<ShopDataSyncRecord> getLatestRecordByTargetShopId(Long targetShopId, ShopDataSyncType syncType) {
		return shopDataSyncRecordService.getLatestRecordByTargetShopId(targetShopId, syncType);
	}

	@Override
	public ShopGroup getShopGroupByShopId(Long shopId) {
		return shopGroupService.getByShopId(shopId);
	}
}
