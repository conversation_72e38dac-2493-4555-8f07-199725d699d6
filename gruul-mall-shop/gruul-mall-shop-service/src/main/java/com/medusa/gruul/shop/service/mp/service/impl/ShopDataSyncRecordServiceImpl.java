package com.medusa.gruul.shop.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.google.common.collect.Lists;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import com.medusa.gruul.shop.api.enums.ShopDataSyncType;
import com.medusa.gruul.shop.service.mp.mapper.ShopDataSyncRecordMapper;
import com.medusa.gruul.shop.service.mp.service.IShopDataSyncRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 商品同步表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ShopDataSyncRecordServiceImpl extends ServiceImpl<ShopDataSyncRecordMapper, ShopDataSyncRecord> implements IShopDataSyncRecordService {


    @Override
    public List<ShopDataSyncRecord> getLatestRecordByTargetShopId(Long targetShopId, ShopDataSyncType syncType) {
        List<ShopDataSyncRecord> latestRecordByTargetShopId = baseMapper.getLatestRecordByTargetShopId(targetShopId, syncType.getValue());
        if (ObjectUtils.isEmpty(latestRecordByTargetShopId)) {
            return Lists.newArrayList();
        }
        return latestRecordByTargetShopId;
    }
}
