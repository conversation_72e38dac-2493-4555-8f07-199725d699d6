package com.medusa.gruul.shop.service.controller;

import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.FunctionType;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.PropertiesVO;
import com.medusa.gruul.common.idem.annotation.Idem;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.shop.api.entity.ShopDecorationDetails;
import com.medusa.gruul.shop.api.model.dto.ShopProductSyncDTO;
import com.medusa.gruul.shop.service.model.dto.ShopFunctionDecorationDTO;
import com.medusa.gruul.shop.service.service.ShopDecorationSyncService;
import com.medusa.gruul.shop.service.service.ShopRenovationService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.wildfly.common.annotation.NotNull;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 装修详情相关接口 控制层
 *
 * <AUTHOR>
 * @Description 装修详情相关接口
 * @date 2022-08-17 14:26
 */
@RestController
@RequiredArgsConstructor
@PreAuthorize("@S.shopPerm('decoration')")
@RequestMapping("/decoration")
public class ShopDecorationDetailsController {

	private final ShopRenovationService shopRenovationService;
	private final ShopDecorationSyncService shopDecorationSyncService;


	/**
	 * 获取pc端装修首页信息
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return 页面首页装修数据
	 */
	@Log("获取装修功能详情")
	@GetMapping("/info")
	public Result<PropertiesVO> getDecorationInfo(AggregationPlatform aggregationPlatform) {
		PropertiesVO properties = shopRenovationService.getDecorationInfo(aggregationPlatform);
		return Result.ok(properties);
	}


	/**
	 * 编辑装修功能
	 *
	 * @param shopFunctionDecoration ShopFunctionDecorationDTO.class
	 * @return Result.ok();
	 */
	@Log("编辑装修功能详情")
	@Idem(1000)
	@PostMapping("/edit")
	public Result<Long> editFunctionDecoration(@RequestBody ShopFunctionDecorationDTO shopFunctionDecoration) {
		return Result.ok(shopRenovationService.editFunctionDecoration(shopFunctionDecoration));
	}


	/**
	 * 删除装修页面
	 *
	 * @param ids 装修详情ids
	 * @return Result.ok();
	 */
	@Log("删除装修页面")
	@DeleteMapping("del/{ids}")
	public Result<Void> delDecorationPage(@PathVariable(name = "ids") Set<Long> ids, AggregationPlatform aggregationPlatform) {
		shopRenovationService.delDecorationPage(ids, aggregationPlatform);
		return Result.ok();
	}

	/**
	 * 同步装修
	 *
	 * @param shopProductSyncDTO 信息
	 * @return void
	 */
	@Log("同步装修")
	@Idem(500)
	@PostMapping("/syncDecoration")
	public Result<Void> syncDecoration(@RequestBody @Valid ShopProductSyncDTO shopProductSyncDTO) {
		shopDecorationSyncService.shopDecoration(shopProductSyncDTO);
		return Result.ok();
	}

	/**
	 * 修改页面名称
	 *
	 * @param name 修改名称
	 * @param id   修改页面id
	 * @return Result.ok();
	 */
	@Log("修改页面名称")
	@PutMapping("page/update/{name}")
	public Result<Void> updatePageName(@PathVariable(name = "name") String name, Long id) {
		shopRenovationService.updatePageName(name, id);
		return Result.ok();
	}

	/**
	 * 获取装修页面基础信息 用于页面名称展示
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return Result<List < ShopDecorationDetails>>
	 */
	@Log("获取装修页面基础信息")
	@PreAuthorize("permitAll()")
	@GetMapping("page/list")
	public Result<List<ShopDecorationDetails>> getPageBasicsInfoList(AggregationPlatform aggregationPlatform) {
		List<ShopDecorationDetails> shopDecorationDetails = shopRenovationService.getPageBasicsInfoList(aggregationPlatform);
		return Result.ok(shopDecorationDetails);
	}


	/**
	 * 获取装修 <功能信息(页面,底部导航,分类页)> by 页面id
	 *
	 * @param id 页面id
	 * @return PropertiesVO.class
	 */
	@Log("获取装修控件信息ById")
	@GetMapping("get")
	@PreAuthorize("permitAll()")
	public Result<PropertiesVO> getDecorationInfoById(@Validated @NotNull Long id) {
		PropertiesVO properties = shopRenovationService.getDecorationInfoById(id);
		return Result.ok(properties);
	}


	/**
	 * 获取底部导航信息
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return PropertiesVO.class
	 */
	@Log("获取装修底部导航信息")
	@GetMapping("navigation/list")
	@PreAuthorize("permitAll()")
	public Result<PropertiesVO> getNavigationInfo(AggregationPlatform aggregationPlatform) {
		return Result.ok(shopRenovationService.getNavigationInfo(aggregationPlatform));
	}


	/**
	 * 获取装修分类页信息
	 *
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return PropertiesVO.java
	 */
	@Log("获取装修分类页信息")
	@GetMapping("classify/page/list")
	@PreAuthorize("permitAll()")
	public Result<PropertiesVO> getClassifyPageInfo(AggregationPlatform aggregationPlatform) {
		return Result.ok(shopRenovationService.getClassifyPageInfo(aggregationPlatform));
	}

	/**
	 * 设置页面为首页
	 *
	 * @param id                  页面id
	 * @param aggregationPlatform 聚合APP平台类型
	 * @return Result<Void>
	 */

	@Log("设置为首页")
	@PutMapping("set/home/<USER>/{id}")
	public Result<Void> setHomePage(@PathVariable(name = "id") Long id, AggregationPlatform aggregationPlatform) {
		shopRenovationService.setHomePage(id, aggregationPlatform);
		return Result.ok();
	}


	/**
	 * 获取客户端装修详情
	 */
	@Log("获取客户端装修详情")
	@PutMapping("client")
	@PreAuthorize("permitAll()")
	public Result<Map<String, List<Object>>> getClientDecoration() {
		Map<String, List<Object>> clientDecoration = shopRenovationService.getClientDecoration();
		return Result.ok(clientDecoration);
	}


	/**
	 * 获取非页面数据 by 枚举
	 */
	@Log("获取非装修页面基础信息")
	@GetMapping("not/page")
	@PreAuthorize("permitAll()")
	public Result<ShopDecorationDetails> getNotPageBasicsInfo(AggregationPlatform aggregationPlatform, FunctionType functionType) {
		return Result.ok(shopRenovationService.getNotPageBasicsInfo(aggregationPlatform, functionType));
	}

	/**
	 * 获取装修类型详情
	 *
	 * @param functionType 装修功能类型
	 * @return PropertiesVO.java
	 */
	@Log("获取装修类型详情")
	@GetMapping("client/type/info")
	@PreAuthorize("permitAll()")
	public Result<PropertiesVO> getFunctionTypeInfo(FunctionType functionType) {
		return Result.ok(shopRenovationService.getFunctionTypeInfo(functionType));
	}


	/**
	 * 获取首页装修功能详情
	 *
	 * @return List<Object>
	 */
	@Log("获取首页装修功能详情")
	@GetMapping("client/page/info")
	@PreAuthorize("permitAll()")
	public Result<List<Object>> getHomePage() {
		return Result.ok(shopRenovationService.getHomePage());
	}


	/**
	 * 获取非页面装修数据信息 by 枚举
	 *
	 * @param aggregationPlatform 聚合平台类型
	 * @param functionType        查询装修功能类型
	 */
	@Log("获取非装修页面数据信息")
	@GetMapping("not/page/info")
	@PreAuthorize("permitAll()")
	public Result<PropertiesVO> getNotPageDecorationInfo(@NotNull AggregationPlatform aggregationPlatform,
	                                                     @NotNull FunctionType functionType) {
		return Result.ok(shopRenovationService.getNotPageDecorationInfo(aggregationPlatform, functionType));
	}


	/**
	 * 获取聚合装修类型及对应的首页名称
	 *
	 * @param aggregationPlatforms 聚合平台类型
	 * @return List<AggregationDecorationVO>
	 */
	@Log("获取聚合装修类型及对应的首页名称")
	@GetMapping("aggregation/decorate")
	@PreAuthorize("permitAll()")
	public Result<List<AggregationDecorationVO>> getAggregationPlatformDecorate(@RequestParam List<AggregationPlatform> aggregationPlatforms) {
		return Result.ok(shopRenovationService.getAggregationPlatformDecorate(aggregationPlatforms));
	}
}
