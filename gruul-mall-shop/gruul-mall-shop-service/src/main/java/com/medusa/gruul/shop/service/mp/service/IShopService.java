package com.medusa.gruul.shop.service.mp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.model.dto.ShopQueryDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.model.vo.ShopStatusQuantityVO;
import com.medusa.gruul.shop.service.model.vo.ShopVO;
import com.medusa.gruul.shop.service.model.vo.SupplierStatisticsVO;

import java.util.List;

/**
 * 商家注册信息 服务类
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
public interface IShopService extends IService<Shop> {

    /**
     * 分页查询店铺
     *
     * @param page 分页参数
     * @return 分页查询结果
     */
    IPage<ShopVO> pageShop(ShopQueryPageDTO page);


    /**
     * 获取当日新增店铺数量
     *
     * @return 当前新增店铺数量
     */
    Long getTodayAddShopQuantity();

    /**
     * 获取店铺数量
     *
     * @return 店铺数量
     */
    List<ShopStatusQuantityVO> getShopQuantity();

    /**
     * 获取供应商数量
     *
     * @return 供应商数量
     */
    List<SupplierStatisticsVO> getSupplierQuantity();

    /**
     * C端店铺搜索
     * @param shopQueryPageDTO 搜索条件
     * @return 查询店铺结果
     */
    IPage<ShopInfoVO> searchShop(ShopQueryPageDTO shopQueryPageDTO);
    /**
     * 获取店铺
     * @return
     */
    List<Shop> getShop(ShopQueryDTO shopQueryDTO);
}
