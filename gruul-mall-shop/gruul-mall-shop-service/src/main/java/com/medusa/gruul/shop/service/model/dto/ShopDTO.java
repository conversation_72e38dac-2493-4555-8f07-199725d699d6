package com.medusa.gruul.shop.service.model.dto;

import cn.hutool.core.lang.RegexPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.constant.RegexPools;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.global.model.o.BaseDTO;
import com.medusa.gruul.shop.api.constant.ShopConstant;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopBankAccount;
import com.medusa.gruul.shop.api.enums.ExtractionType;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.enums.ShopType;
import com.medusa.gruul.shop.api.model.dto.SigningCategoryDTO;
import com.medusa.gruul.shop.service.model.enums.ShopError;
import com.medusa.gruul.shop.service.mp.entity.ShopRegisterInfo;
import com.medusa.gruul.shop.service.mp.service.IShopBankAccountService;
import com.medusa.gruul.shop.service.mp.service.IShopRegisterInfoService;
import com.medusa.gruul.shop.service.mp.service.IShopService;
import com.vividsolutions.jts.geom.Point;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * date 2022/4/14
 */
@Getter
@Setter
@ToString
public class ShopDTO implements BaseDTO {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 店铺运行模式
	 */
	@NotNull
	private ShopMode shopMode;

	/**
	 * 店铺公司名称
	 */
	@NotBlank
	private String companyName;

	/**
	 * 店铺名称
	 */
	@NotBlank
	private String name;

	/**
	 * 联系电话 手机号或座机号
	 */
	@NotBlank
	@Pattern(regexp = RegexPools.MOBILE_TEL)
	private String contractNumber;

	/**
	 * 联系地址
	 */
	@NotBlank
	private String address;

	/**
	 * logo url
	 */
	@NotBlank
	@Pattern(regexp = RegexPool.URL_HTTP)
	private String logo;

	/**
	 * 用户id
	 */
	private Long userId;

	/**
	 * 介绍
	 */
	@NotBlank
	@Size(min = 2, max = 200)
	private String briefing;

	/**
	 * 定位
	 */
	@NotNull
	private Point location;

	/**
	 * 店铺司法注册信息
	 */
	@Valid
	private ShopRegisterInfoDTO registerInfo;

	/**
	 * 银行账户信息
	 */
	@Valid
	@NotNull
	private ShopBankAccountDTO bankAccount;

	/**
	 * 签约类目
	 */
	private List<SigningCategoryDTO> signingCategory;


	/**
	 * 店铺类型
	 */
	private ShopType shopType;


	/**
	 * 抽佣类型
	 */
	private ExtractionType extractionType;

	/**
	 * 抽成百分比
	 */
	private Integer drawPercentage;

	/**
	 * 店铺模式
	 */
	private Mode mode;


	public void validParam(boolean isPlatform, Long userId) {
		if (!isPlatform) {
			setUserId(userId);
			return;
		}
		SystemCode.PARAM_VALID_ERROR.trueThrow(getUserId() == null);
	}

	/**
	 * 保存商家注册基础信息
	 *
	 * @param isEdit      是否是编辑模式
	 * @param shopService 店铺持久化服务
	 * @param shopId      店铺id 编辑模式必填
	 * @return 基础信息id
	 */
	public Shop saveShop(boolean isEdit, boolean isPlatform, IShopService shopService, Long shopId, Long userId) {
		this.validParam(isPlatform, userId);
		boolean exists = shopService.lambdaQuery()
				.eq(Shop::getName, getName())
				.ne(isEdit, Shop::getId, shopId)
				.exists();
		ShopError.SHOP_NAME_EXISTED.trueThrow(exists);
		Shop shop = null;
		SystemCode.DATA_NOT_EXIST.trueThrow(isEdit && (shop = shopService.getById(shopId)) == null);
		shop = toShop(shop, isPlatform);
		boolean success = isEdit ? shopService.updateById(shop) : shopService.save(shop);
		SystemCode.DATA_UPDATE_FAILED.falseThrow(success);
		return shop;
	}

	/**
	 * @param isEdit                  是否是编辑模式
	 * @param shopRegisterInfoService 持久化服务
	 *                                保存商家司法注册信息
	 */
	public void saveShopRegisterInfo(boolean isEdit, IShopRegisterInfoService shopRegisterInfoService) {
		ShopRegisterInfo shopRegisterInfo = new ShopRegisterInfo();
		if (isEdit) {
			shopRegisterInfo = shopRegisterInfoService.lambdaQuery().one();
			shopRegisterInfo = shopRegisterInfo == null ? new ShopRegisterInfo() : shopRegisterInfo;
		}
		shopRegisterInfo
				.setLicense(registerInfo.getLicense())
				.setLegalPersonIdFront(registerInfo.getLegalPersonIdFront())
				.setLegalPersonIdBack(registerInfo.getLegalPersonIdBack());

		boolean success = shopRegisterInfo.getId() != null ? shopRegisterInfoService.updateById(shopRegisterInfo) : shopRegisterInfoService.save(shopRegisterInfo);
		SystemCode.DATA_ADD_FAILED.falseThrow(success);
	}

	/**
	 * @param isEdit                 是否是编辑模式
	 * @param shopBankAccountService 持久化服务
	 *                               保存商家银行账户信息
	 */
	public void saveShopBankAccount(boolean isEdit, IShopBankAccountService shopBankAccountService) {
		ShopBankAccount shopBankAccount = new ShopBankAccount();
		if (isEdit) {
			shopBankAccount = shopBankAccountService.lambdaQuery().eq(ShopBankAccount::getShopId, ISystem.shopIdOpt().get()).one();
			SystemCode.DATA_NOT_EXIST.trueThrow(shopBankAccount == null);
		}
		shopBankAccount
				.setShopId(ISystem.shopIdOpt().get())
				.setBankAccount(bankAccount.getBankAccount())
				.setBankName(bankAccount.getBankName())
				.setOpenAccountBank(bankAccount.getOpenAccountBank())
				.setPayee(bankAccount.getPayee());
		boolean success = isEdit ? shopBankAccountService.updateById(shopBankAccount) : shopBankAccountService.save(shopBankAccount);
		SystemCode.DATA_ADD_FAILED.falseThrow(success);
	}


	private Shop toShop(Shop shop, boolean isPlatform) {
		Shop newShop = shop == null ? new Shop() : shop;
		boolean isEdit = newShop.getId() != null;
		return newShop.setNo(isEdit ? newShop.getNo() : RedisUtil.no(ShopConstant.SHOP_NO_KEY_HEAD, ShopConstant.SHOP_NO_KEY))
				.setCompanyName(getCompanyName())
				.setUserId(getUserId())
				.setName(getName())
				.setContractNumber(getContractNumber())
				.setAddress(getAddress())
				.setLocation(getLocation())
				.setLogo(getLogo())
				.setBriefing(getBriefing())
				.setShopType((getShopType() == null && !isPlatform) ? ShopType.ORDINARY : getShopType())
				.setExtractionType((getExtractionType() == null && !isPlatform) ? ExtractionType.CATEGORY_EXTRACTION : getExtractionType())
				.setDrawPercentage((getExtractionType() == ExtractionType.ORDER_SALES_EXTRACTION && isPlatform) ? getDrawPercentage() : null)
				.setMode(newShop.getStatus() == ShopStatus.UNDER_REVIEW ? getMode() : newShop.getMode() == null ? getMode() : newShop.getMode())
				.setShopMode(newShop.getShopMode() == null ? getShopMode() : newShop.getShopMode())
				.setStatus(isEdit ? newShop.getStatus() : (isPlatform ? ShopStatus.NORMAL : ShopStatus.UNDER_REVIEW));
	}


}
