<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shop.service.mp.mapper.ShopBankAccountMapper">
    <resultMap id="baseMap" type="com.medusa.gruul.shop.api.entity.ShopBankAccount">
        <result column="id" property="id"/>
        <result column="shopId" property="shopId"/>
        <result column="payee" property="payee"/>
        <result column="bankName" property="bankName"/>
        <result column="bankAccount" property="bankAccount"/>
        <result column="openAccountBank" property="openAccountBank"/>
        <result column="accounts" property="accounts"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="version" property="version"/>
    </resultMap>

    <select id="queryShopAccountsByShopId" resultMap="baseMap">
        SELECT account.id,
               account.shop_id as shopId,
               account.payee as payee,
               account.bank_name as bankName,
               account.bank_account as bankAccount,
               account.open_account_bank as openAccountBank,
               account.accounts as accounts,
               account.create_time as createTime,
               account.update_time as updateTime,
               account.version as version
        FROM t_shop_bank_account account
        WHERE deleted = 0
        <if test="shopIds != null">
          AND shop_id IN
            <foreach collection="shopIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
