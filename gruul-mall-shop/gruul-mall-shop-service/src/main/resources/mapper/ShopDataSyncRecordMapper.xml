<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shop.service.mp.mapper.ShopDataSyncRecordMapper">


    <resultMap id="baseMap" type="com.medusa.gruul.shop.api.entity.ShopDataSyncRecord">
        <id property="id" column="id" />
        <result property="syncDataId" column="syncDataId" />
        <result property="syncDataNo" column="syncDataNo" />
        <result property="syncData" column="syncData"
                typeHandler="com.medusa.gruul.common.mp.FastJson2TypeHandler"/>
        <result property="shopId" column="shopId" />
        <result property="targetShopId" column="targetShopId" />
        <result property="targetDataId" column="targetDataId" />
        <result property="targetDataNo" column="targetDataNo" />
        <result property="syncType" column="syncType" />
        <result property="syncChangeType" column="syncChangeType" />
        <result property="syncChangeData" column="syncChangeData"
                typeHandler="com.medusa.gruul.common.mp.FastJson2TypeHandler"/>
        <result property="status" column="status" />
        <result property="syncMessage" column="syncMessage"
                typeHandler="com.medusa.gruul.common.mp.FastJson2TypeHandler"/>
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="version" column="version" />
    </resultMap>

    <select id="getLatestRecordByTargetShopId" resultMap="baseMap">
        SELECT
            b.id,
            sync_data_id AS syncDataId,
            sync_data_no AS syncDataNo,
            sync_data AS syncData,
            shop_id AS shopId,
            target_shop_id AS targetShopId,
            target_data_id AS targetDataId,
            target_data_no AS targetDataNo,
            sync_type AS syncType,
            sync_change_type AS syncChangeType,
            sync_change_data AS syncChangeData,
            STATUS,
            sync_message AS syncMessage,
            create_time AS createTime,
            update_time AS updateTime,
            version
        FROM
            ( SELECT MAX( id ) AS id FROM t_shop_data_sync_record
                                     WHERE target_shop_id = #{targetShopId}
                                       AND sync_type = #{syncType}
                                       AND status = 1 GROUP BY sync_data_id
                                                      ) a
                LEFT JOIN t_shop_data_sync_record b ON b.id = a.id

    </select>


</mapper>
