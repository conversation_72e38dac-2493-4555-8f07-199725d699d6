<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.shop.service.mp.mapper.ShopDecorationMapper">


    <resultMap id="baseMap" type="com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO">
        <result column="platforms" property="aggregationPlatform"/>
        <result column="page_name" property="homePageName"/>
    </resultMap>

    <select id="queryAggregationPlatformDecorate"
            resultMap="baseMap">

   SELECT
                decoration.platforms,
                decorationDetails.page_name
            FROM
                t_shop_decoration AS decoration
            INNER JOIN
                t_shop_decoration_details  decorationDetails
            ON   decoration.id = decorationDetails.shop_decoration_id
            WHERE
                decorationDetails.is_def = 1  AND
                decorationDetails.function_type = 2


    </select>
</mapper>