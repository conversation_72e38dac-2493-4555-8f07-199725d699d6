server:
  port: 8180
spring:
  application:
    name: gruul-mall-shop
  profiles:
    active: prod
  main:
    allow-circular-references: true
  cloud:
    nacos:
      server-addr: **************:8884
      discovery:
        namespace: ${spring.profiles.active}
        ip: *************
      config:
        namespace: ${spring.profiles.active}
        file-extension: yml
        shared-configs:
          - dataId: application-common.${spring.cloud.nacos.config.file-extension}


