package com.medusa.gruul.shop.service;


import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Sort;
import org.springframework.data.geo.Circle;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * date 2022/4/19
 */
//@RunWith(SpringRunner.class)
@SpringBootTest
public class TestS {

    @Autowired
    private RedisTemplate redisTemplate;

    public void test() {
        String PASSWORD = "^(?![A-Z]+$)(?![a-z]+$)(?!\\d+$)(?![\\W_]+$)\\S{6,16}$";
        System.out.println(java.util.regex.Pattern.matches(PASSWORD, "aaaa5."));
    }



}
