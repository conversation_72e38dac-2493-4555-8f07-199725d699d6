{"code": 200, "data": {"records": [{"id": "1502113855162359808", "orderNo": "22031100000004", "status": "WAITING_FOR_PAY", "type": "COMMON", "createTime": "2022-03-11 10:47:06", "updateTime": "2022-03-11 10:47:06", "version": 0, "user": {"id": "1502113855496417280", "userId": "1", "remark": "haha", "name": "张三", "mobile": "17621228898", "province": "北京市", "city": "市辖区", "region": "东城区", "address": "三里屯", "createTime": "2022-03-11 10:47:06", "updateTime": "2022-03-11 10:47:06", "version": 0}, "payment": {"id": "1502113880530321408", "status": "UNPAID", "totalAmount": 0.7, "freightAmount": 0.0, "discountAmount": 0.0, "createTime": "2022-03-11 10:47:12", "updateTime": "2022-03-11 10:47:12", "version": 0}, "deliveries": [{"id": "1502113855667707904", "type": "EXPRESS", "receiverName": "张三", "receiverMobile": "17621228898", "receiverProvince": "北京市", "receiverCity": "市辖区", "receiverRegion": "东城区", "receiverAddress": "三里屯", "status": "NOT_SEND", "createTime": "2022-03-11 10:47:06", "updateTime": "2022-03-11 10:47:06", "version": 0, "products": [{"id": "1501504102093324288", "name": "商品名1", "image": "https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20220309/66e5b0eec0224348b9de3af17c1b2797.jpg", "skus": [{"itemId": "1502113880355106816", "id": "1501504104193859584", "specs": ["红色"], "quantity": 1, "image": "https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20220309/66e5b0eec0224348b9de3af17c1b2797.jpg", "price": 0.1, "createTime": "2022-03-11 10:47:12", "updateTime": "2022-03-11 10:47:12", "version": 0}]}, {"id": "1501505382484312064", "name": "商品名2", "image": "https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20220309/66e5b0eec0224348b9de3af17c1b2797.jpg", "skus": [{"itemId": "1502113880355106817", "id": "1501505384052170752", "specs": ["XL"], "quantity": 2, "image": "https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20220309/66e5b0eec0224348b9de3af17c1b2797.jpg", "price": 0.2, "createTime": "2022-03-11 10:47:12", "updateTime": "2022-03-11 10:47:12", "version": 0}, {"itemId": "1502113880355106818", "id": "1501505384215748608", "specs": ["XXL"], "quantity": 1, "image": "https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20220309/66e5b0eec0224348b9de3af17c1b2797.jpg", "price": 0.2, "createTime": "2022-03-11 10:47:12", "updateTime": "2022-03-11 10:47:12", "version": 0}]}]}]}, {"id": "1502479030247825408", "orderNo": "22031200000006", "status": "WAITING_FOR_PAY", "type": "COMMON", "createTime": "2022-03-12 10:58:10", "updateTime": "2022-03-12 10:58:10", "version": 0, "user": {"id": "1502479088912068608", "userId": "1", "remark": "haha", "name": "张三", "mobile": "17621228898", "province": "北京市", "city": "市辖区", "region": "东城区", "address": "三里屯", "createTime": "2022-03-12 10:58:24", "updateTime": "2022-03-12 10:58:24", "version": 0}, "payment": {"id": "1502479174742409216", "status": "UNPAID", "totalAmount": 0.2, "freightAmount": 0.0, "discountAmount": 0.0, "createTime": "2022-03-12 10:58:45", "updateTime": "2022-03-12 10:58:45", "version": 0}, "deliveries": [{"id": "1502479089100136448", "type": "EXPRESS", "receiverName": "张三", "receiverMobile": "17621228898", "receiverProvince": "北京市", "receiverCity": "市辖区", "receiverRegion": "东城区", "receiverAddress": "三里屯", "status": "NOT_SEND", "createTime": "2022-03-12 10:58:24", "updateTime": "2022-03-12 10:58:24", "version": 0, "products": [{"id": "1501504102093324288", "name": "xiaoq测试", "image": "https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20220309/66e5b0eec0224348b9de3af17c1b2797.jpg", "skus": [{"itemId": "1502479174596554752", "id": "1501504104193859584", "specs": ["红色"], "quantity": 2, "image": "https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20220309/66e5b0eec0224348b9de3af17c1b2797.jpg", "price": 0.1, "createTime": "2022-03-12 10:58:45", "updateTime": "2022-03-12 10:58:45", "version": 0}]}]}]}], "total": 2, "size": 10, "current": 1, "orders": [], "optimizeCountSql": true, "searchCount": true, "pages": 1}, "success": true}