package generator;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.medusa.gruul.common.mp.model.BaseEntity;

import java.util.HashMap;

/**
 * <AUTHOR>
 * date 2022/2/24
 */
public class MybatisplusGenerator {

    public static void main(String[] args) {
        FastAutoGenerator.create(
                        "***************************************************",
                        "root",
                        "Test@yhzj_17"
                )
                .globalConfig(builder -> {
                    builder.author("weijian")
                            //.enableSwagger()
                            //.fileOverride()
                            .outputDir("E:\\work\\gruul-mall\\gruul-mall\\gruul-mall-shop\\gruul-mall-shop-service\\src\\main\\java");
                })
            .packageConfig(builder -> {
                builder.parent("com.medusa.gruul.shop.service")
                    .moduleName("mp")
                    .pathInfo(
                        new HashMap<OutputFile, String>(){
                            {
                                put(OutputFile.xml,"E:\\work\\gruul-mall\\gruul-mall\\gruul-mall-shop\\gruul-mall-shop-service\\src\\main\\resources\\mapper");
                                put(OutputFile.entity,"E:\\work\\gruul-mall\\gruul-mall\\gruul-mall-shop\\gruul-mall-shop-api\\src\\main\\java\\com\\medusa\\gruul\\shop\\api\\entity");
                            }
                        }
                    );
            })
            .strategyConfig(builder -> {
                builder.addInclude("t_shop_group_mapping")
                    .addTablePrefix("t_")
                    .entityBuilder()
                    .enableLombok()
                .superClass(BaseEntity.class);
            }).execute();
    }
}
