<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.live.service.mp.mapper.LiveRoomMapper">


    <update id="liveRoomReviewStart" >
        update
        t_live_room
        set
        `status` = 1
        where
        `status` = 0
        and start_time &lt;= #{currentTime}
        and end_time > #{currentTime}
        and deleted = 0
    </update>

    <update id="liveRoomReviewEnd">
        update
        t_live_room
        set
        `status` = 2
        where
        `status` in (0,1)
        and
        end_time &lt; #{currentTime}
        and
        deleted = 0
    </update>

</mapper>
