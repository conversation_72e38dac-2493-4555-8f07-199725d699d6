package com.medusa.gruul.payment.api.model.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 支付账户开通参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PaymentAccountOpenResult {

    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 账户id
     */
    private String accountId;
    /**
     * 绑定的银行卡id
     */
    private String cardId;
}
