package com.medusa.gruul.payment.api.model.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 支付账户开通参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class WithdrawAccountParam {
    /**
     * 提现金额(毫)
     */
    @NotNull
    private Long amount;
    /**
     * userId
     */
    @NotNull
    private Long userId;
    /**
     * 账户id(支付渠道方)
     */
    @NotBlank
    private String accountId;
    /**
     * 绑定的银行卡id(支付渠道方)
     */
    @NotBlank
    private String cardId;

    /**
     * 提现工单号
     */
    @NotBlank
    private String no;

}
