package com.medusa.gruul.payment.api.model.param;

import com.medusa.gruul.common.model.base.ShopOrderKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.global.model.o.BaseDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * 服务费基础转信息
 *
 * <AUTHOR>
 * date 2023/10/25
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public abstract class ServiceChangeParam implements BaseDTO {

    /**
     * 订单id
     */
    @NotBlank
    private String orderId;

    /**
     * 店铺id
     */
    @NotNull
    private ShopOrderKey shopOrderKey;

    /**
     * 服务费接收方
     */
    @NotNull
    @Size(min = 1)
    @Valid
    private ServiceReceiver serviceReceiver;


    /**
     * 服务费接收方
     */
    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class ServiceReceiver {

        /**
         * 接收方帐号
         */
        private String accountId;

        /**
         * 接收金额
         */
        @NotNull
        private Long amount;

        /**
         * 获取decimal 价格 单位：元 精确到分 小数点后两位
         */
        public BigDecimal getAmountDecimal() {
            Long amount = getAmount();
            if (amount == null || amount <= 0) {
                return BigDecimal.ZERO;
            }
            return BigDecimal.valueOf(amount)
                    .divide(
                            CommonPool.BIG_DECIMAL_UNIT_CONVERSION_TEN_THOUSAND,
                            2,
                            RoundingMode.DOWN
                    );
        }

    }
}
