package com.medusa.gruul.payment.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
public interface PaymentUserAccountType {

	@Getter
	@RequiredArgsConstructor
	enum Type {
		/**
		 * 用户
		 */
		USER(1),

		/**
		 * 店铺
		 */
		SHOP(2);

		@EnumValue
		private final Integer value;
	}

	@Getter
	@RequiredArgsConstructor
	enum BizType {
		/**
		 * 创建用户
		 */
		INDV(1),

		/**
		 * 开账户
		 */
		OPEN(2),
		/**
		 * 提现
		 */
		WITHDRAW(3),

		;

		@EnumValue
		private final Integer value;
	}
}
