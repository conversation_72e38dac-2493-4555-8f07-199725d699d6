package com.medusa.gruul.payment.api.model.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 支付账户开通参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PaymentAccountOpenParam {
    /**
     * userid
     */
    @NotNull
    private Long userId;
    /**
     * 姓名
     */
    @NotBlank
    private String name;
    /**
     * 手机号
     */
    @NotBlank
    private String mobileNo;
    /**
     * 身份证类型
     */
    private String certType = "00";
    /**
     * 身份证号
     */
    @NotBlank
    private String certNo;
    /**
     * 证件有效期类型, 1:长期, 0:短期有效
     */
    @NotBlank
    private String certValidityType;
    /**
     * 证件有效期起始时间yyyyMMdd
     */
    @NotBlank
    private String certBeginDate;
    /**
     * 证件有效期结束时间yyyyMMdd
     */
    @NotBlank
    private String certEndDate;

    /**
     * 银行卡
     */
    @NotBlank
    private String cardNo;
    /**
     * 银行卡开户省代码
     */
    @NotBlank
    private String provId;
    /**
     * 银行卡开户市代码
     */
    @NotBlank
    private String areaId;
    /**
     * 银行卡绑定手机号
     */
    private String cardMobileNo;
}
