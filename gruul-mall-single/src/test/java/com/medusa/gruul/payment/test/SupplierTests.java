package com.medusa.gruul.payment.test;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.service.client.YihuSupplierClient;
import com.medusa.gruul.goods.service.mp.mapper.SupplierMerchantMapper;
import com.medusa.gruul.single.SingleApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class SupplierTests {

    @Autowired
    private YihuSupplierClient yihuSupplierClient;
    @Autowired
    private SupplierMerchantMapper supplierMerchantMapper;

    @Test
    public void test() throws Exception {
        SupplierMerchant supplierMerchant = supplierMerchantMapper.selectById(1665914868573381004L);

        Map<String, Object> params = Maps.newHashMap();
        params.put("appCode", supplierMerchant.getAppId());
        params.put("businessNo", "S123456");
        params.put("productCode", "YH_YUEHUI00004");
        Result<Object> card = yihuSupplierClient.createCard(supplierMerchant, params);
        System.out.println(JSONUtil.toJsonStr(card));
//        shopProductSyncService.productRelease(productBroadcastDTO);
        System.out.println();

    }



}
