package com.medusa.gruul.payment.test;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.medusa.gruul.addon.supplier.model.dto.SupplierDrugSyncDTO;
import com.medusa.gruul.addon.supplier.model.dto.SupplierOrderNotifyDTO;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;
import java.util.TreeMap;

import static cn.hutool.json.JSONUtil.toJsonStr;
import static com.medusa.gruul.addon.supplier.utils.YujiSignUtils.convertToMapAndDeleteKeys;
import static com.medusa.gruul.addon.supplier.utils.YujiSignUtils.sortJson;
import static com.medusa.gruul.goods.service.client.impl.YujiSupplierClientImpl.getSignature;

@SpringBootTest
public class YujiSignTests {
    private final String POST_ORDER = StrUtil.format("/drug/batch/sync");

    /**
     * 测试批量同步药品信息接口
     * @throws Exception
     */
    @Test
    public void testSign() throws Exception {


        SupplierMerchant yujiMerchant = new SupplierMerchant()
                .setKeyPrivate("bea4cdcef2954d1d8e706f6c92bc0992")
                .setKeyPublic("1100001")
                .setRequestDomainUrl("127.0.0.1:9998/")
                .setMchId("YH_YJYK");
        String random = RandomUtil.randomString(28);
        String timeMis = String.valueOf(System.currentTimeMillis());
        SupplierDrugSyncDTO supplierDrugSyncDTO = JSON.parseObject("{\"drugList\": [{\"no\": \"1001910\", \"pic\": \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964372319539200862403732.jpg\", \"name\": \"六君子丸55\", \"note\": \" 1.忌食生冷油腻不易消化食物。 2.不适用于脾胃阴虚，主要表现为口干、舌红少津、大便干。 3.小儿、年老体弱者应在医师指导下服用。 4.对本品过敏者禁用，过敏体质者慎用。 5.本品性状发生改变时禁止使用。 6.儿童必须在成人监护下使用。 7.请将本品放在儿童不能接触的地方。 8.如正在使用其他药品，使用本品前请咨询医师或药师。\", \"skuId\": \"1001910\", \"specs\": \"9g*10袋\", \"usage\": 2, \"dosage\": \"9\", \"isDrug\": true, \"status\": \"1\", \"weight\": 120, \"barcode\": \"6931294400020\", \"icdList\": [438, 530, 565], \"stockNum\": 4, \"frequency\": 94, \"packaging\": \"盒\", \"salePrice\": 5800, \"commonName\": \"六君子丸\", \"dosageForm\": \"\", \"dosageUnit\": 48, \"weightUnit\": \"2\", \"composition\": \"党参、麸炒白术、茯苓、姜半夏、陈皮、炙甘草；辅料为生姜、大枣。\", \"storageMode\": \"常温\", \"usageDosage\": \"口服，一次9克，一日2次。\", \"manufacturer\": \"江西庐山制药有限公司\", \"approvalNumber\": \"国药准字*********\", \"detailImageList\": [\"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964372319539200862403732.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539643983955271681833473829.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964466083205120583616755.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539644844961996801684393255.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/165396450417587404873944568.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539645225846743042004846693.jpg\"], \"settlementPrice\": 1269, \"isPrescriptionDrug\": false}]}", SupplierDrugSyncDTO.class);
        TreeMap signParams = JSON.parseObject("{\"drugList\": [{\"no\": \"1001910\", \"pic\": \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964372319539200862403732.jpg\", \"name\": \"六君子丸55\", \"note\": \" 1.忌食生冷油腻不易消化食物。 2.不适用于脾胃阴虚，主要表现为口干、舌红少津、大便干。 3.小儿、年老体弱者应在医师指导下服用。 4.对本品过敏者禁用，过敏体质者慎用。 5.本品性状发生改变时禁止使用。 6.儿童必须在成人监护下使用。 7.请将本品放在儿童不能接触的地方。 8.如正在使用其他药品，使用本品前请咨询医师或药师。\", \"skuId\": \"1001910\", \"specs\": \"9g*10袋\", \"usage\": 2, \"dosage\": \"9\", \"isDrug\": true, \"status\": \"1\", \"weight\": 120, \"barcode\": \"6931294400020\", \"icdList\": [438, 530, 565], \"stockNum\": 4, \"frequency\": 94, \"packaging\": \"盒\", \"salePrice\": 5800, \"commonName\": \"六君子丸\", \"dosageForm\": \"\", \"dosageUnit\": 48, \"weightUnit\": \"2\", \"composition\": \"党参、麸炒白术、茯苓、姜半夏、陈皮、炙甘草；辅料为生姜、大枣。\", \"storageMode\": \"常温\", \"usageDosage\": \"口服，一次9克，一日2次。\", \"manufacturer\": \"江西庐山制药有限公司\", \"approvalNumber\": \"国药准字*********\", \"detailImageList\": [\"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964372319539200862403732.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539643983955271681833473829.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964466083205120583616755.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539644844961996801684393255.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/165396450417587404873944568.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539645225846743042004846693.jpg\"], \"settlementPrice\": 1269, \"isPrescriptionDrug\": false}]}", TreeMap.class);
//        Map<String, Object> params = RedisUtil.toBean(yujiOrderCreateDTO, Map.class);

        String drugList = sortJson(toJsonStr(signParams.get("drugList")));
        signParams.put("timestamp", timeMis);
        signParams.put("randomStr", random);
        signParams.put("appSecret", yujiMerchant.getKeyPrivate());
        signParams.put("appKey", yujiMerchant.getKeyPublic());
        signParams.put("mchId", yujiMerchant.getMchId());
        //签名

        signParams.put("drugList", drugList);

        String signature = getSignature(yujiMerchant, signParams);
        String fullUrl = convertRequestUrl(yujiMerchant.getRequestDomainUrl(), POST_ORDER);
        try {
            String str = HttpUtil.createPost(fullUrl)
                    .header("timestamp", timeMis)
                    .header("randomStr", random)
                    .header("signature", signature)
                    .header("mchId", "YH_YJYK")
                    .body("{\"drugList\": [{\"no\": \"1001910\", \"pic\": \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964372319539200862403732.jpg\", \"name\": \"六君子丸55\", \"note\": \" 1.忌食生冷油腻不易消化食物。 2.不适用于脾胃阴虚，主要表现为口干、舌红少津、大便干。 3.小儿、年老体弱者应在医师指导下服用。 4.对本品过敏者禁用，过敏体质者慎用。 5.本品性状发生改变时禁止使用。 6.儿童必须在成人监护下使用。 7.请将本品放在儿童不能接触的地方。 8.如正在使用其他药品，使用本品前请咨询医师或药师。\", \"skuId\": \"1001910\", \"specs\": \"9g*10袋\", \"usage\": 2, \"dosage\": \"9\", \"isDrug\": true, \"status\": \"1\", \"weight\": 120, \"barcode\": \"6931294400020\", \"icdList\": [438, 530, 565], \"stockNum\": 4, \"frequency\": 94, \"packaging\": \"盒\", \"salePrice\": 5800, \"commonName\": \"六君子丸\", \"dosageForm\": \"\", \"dosageUnit\": 48, \"weightUnit\": \"2\", \"composition\": \"党参、麸炒白术、茯苓、姜半夏、陈皮、炙甘草；辅料为生姜、大枣。\", \"storageMode\": \"常温\", \"usageDosage\": \"口服，一次9克，一日2次。\", \"manufacturer\": \"江西庐山制药有限公司\", \"approvalNumber\": \"国药准字*********\", \"detailImageList\": [\"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964372319539200862403732.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539643983955271681833473829.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/1653964466083205120583616755.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539644844961996801684393255.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/165396450417587404873944568.jpg\", \"https://file-prod-1304455996.cos.ap-shanghai.myqcloud.com/16539645225846743042004846693.jpg\"], \"settlementPrice\": 1269, \"isPrescriptionDrug\": false}]}")
                    .execute().body();
            JSONObject resultData = JSON.parseObject(str);
            if (!resultData.get("code").toString().equals("0")) {

                return;
            }
            Map<String, String> dataMap = JSONUtil.toBean(resultData.get("data").toString(), Map.class);
        } catch (Exception e) {

        } finally {

        }
    }


    public String convertRequestUrl(String baseUrl, String apiUrl) {
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        if (!apiUrl.startsWith("/")) {
            baseUrl += "/";
        }
        return baseUrl + apiUrl;
    }
@Test
    public  void testOrderNotify() {
        SupplierOrderNotifyDTO supplierOrderNotifyDTO = JSON.parseObject("{\"orderNo\": \"SY1909874816349782016\", \"expressNo\": \"\", \"orderStatus\": \"150\"}", SupplierOrderNotifyDTO.class);
        System.out.println(supplierOrderNotifyDTO);
        Map<String, Object> stringObjectMap = convertToMapAndDeleteKeys(supplierOrderNotifyDTO, true, "expressCompanyCode", "freightPrice");
        System.out.println(stringObjectMap);

    }
}
