package com.medusa.gruul.payment.test;

import cn.hutool.core.thread.ThreadUtil;
import com.google.common.collect.Lists;
import com.medusa.gruul.addon.notify.model.enums.NotifyOrderType;
import com.medusa.gruul.addon.notify.service.SendNotifyService;
import com.medusa.gruul.single.SingleApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
//@ActiveProfiles("prod")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class YihuTests {

    @Autowired
    private SendNotifyService sendNotifyService;

    @Test
    public void test() {
        List<String> list = Lists.newArrayList();
        list.add("SY1820727482066239488");
        list.add("SY1820758117354905600");
        list.add("SY1832005798156939264");
        list.add("SY1854817278061248512");
        list.add("SY1855800448759955456");
        list.add("SY1855807112166834176");
        list.add("SY1855837346593284096");
        list.add("SY1855857598374912000");
        list.add("SY1855857747713105920");
        list.add("SY1855859368018935808");
        list.add("SY1855903189675065344");
        list.add("SY1856113701826379776");
        list.add("SY1856359674859233280");
        list.add("SY1856504253310836736");
        list.add("SY1856513222204989440");
        list.add("SY1856529651373477888");
        list.add("SY1856533184307040256");
        list.add("SY1856538948434169856");
        list.add("SY1856541958266720256");
        list.add("SY1856591530301100032");
        list.add("SY1856615290668355584");
        list.add("SY1856631193816567808");
        list.add("SY1856883806172667904");
        list.add("SY1856954374699933696");
        list.add("SY1856986132581507072");
        list.add("SY1857003875192070144");
        list.add("SY1857025251214942208");
        list.add("SY1857027795953704960");
        list.add("SY1857056847095717888");
        list.add("SY1857205751154008064");
        list.add("SY1857264940169576448");
        list.add("SY1857269154182610944");
        list.add("SY1857279152832921600");
        list.add("SY1857313090519449600");
        list.add("SY1857314242745090048");
        list.add("SY1857333379521343488");
        list.add("SY1857401799872901120");
        list.add("SY1857574802145439744");
        list.add("SY1857582108396515328");
        list.add("SY1857676503305187328");
        list.add("SY1857732943944380416");
        list.add("SY1857735900383440896");
        list.add("SY1857984928610271232");
        list.add("SY1857999143349796864");
        list.add("SY1858048330632282112");
        list.add("SY1858066895171239936");
        list.add("SY1858081315670736896");
        list.add("SY1858111025515610112");
        list.add("SY1858255636091387904");
        list.add("SY1858296829441490944");
        list.add("SY1858314598023184384");
        list.add("SY1858320629809823744");
        list.add("SY1858336698247168000");
        list.add("SY1858351305498050560");
        list.add("SY1858364289821720576");
        list.add("SY1858392105292414976");
        list.add("SY1858497745314660352");
        list.add("SY1858507260172976128");
        list.add("SY1858512623098150912");
        list.add("SY1858856493145149440");
        list.add("SY1859455332830957568");
        list.add("SY1859918102357680128");
        list.add("SY1860898292089036800");
        list.add("SY1862335568203563008");
        list.add("SY1862504805618499584");

        for (String s : list) {
            System.out.println("---" + s);
            ThreadUtil.sleep(1000);
            sendNotifyService.send(NotifyOrderType.RETURN_REFUND_AGREE, s);
            ThreadUtil.sleep(1000);
            sendNotifyService.send(NotifyOrderType.RETURNED_REFUNDED, s);
        }


    }

}
