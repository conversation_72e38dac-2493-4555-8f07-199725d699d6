package com.medusa.gruul.payment.test;

import com.medusa.gruul.addon.gift.handler.ShopCoMemberHandler;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.single.SingleApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class GiftTests {

    @Autowired
    private ShopCoMemberHandler shopCoMemberHandler;


    @Test
    public void test() throws Exception {
        TenantShop.disable(
                () -> shopCoMemberHandler.execute()
        );

        System.out.println();

    }


}
