package com.medusa.gruul.payment.test;

import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.charset.Charset;

@Slf4j
@RunWith(SpringRunner.class)
public class SignTests {


    @Test
    public void testSync() throws Exception {
        String originalUrl = "http://************:7003/hydeeServerBSON/HydeeServerController/ftp?path=/2025/5/09/13445208_151543696169_2_20250509000001.jpg";

        // 通用正则：匹配任意 HTTP URL（含 IP/域名 + 端口）
        String replacedUrl = originalUrl.replaceAll(
                "http://[^/]+/",  // 匹配 http://<任意非斜杠字符>/
                "https://app.wei-health.cn/srimg/"
        );

        System.out.println("原始 URL: " + originalUrl);
        System.out.println("替换后 URL: " + replacedUrl);
    }



}
