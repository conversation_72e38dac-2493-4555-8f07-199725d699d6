package com.medusa.gruul.payment.test;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.hash.Hashing;
import com.medusa.gruul.addon.supplier.model.dto.SupplierDrugDTO;
import com.medusa.gruul.addon.supplier.model.dto.SupplierDrugSyncDTO;
import com.medusa.gruul.addon.supplier.service.SupplierHandleService;
import com.medusa.gruul.addon.supplier.utils.YujiSignUtils;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.api.enums.FrequencyEnum;
import com.medusa.gruul.goods.api.enums.UsageEnum;
import com.medusa.gruul.goods.api.enums.UsageUnitEnum;
import com.medusa.gruul.goods.api.model.dto.SupplierOrderCreateDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierOrderItemCreateDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierReceiveDTO;
import com.medusa.gruul.goods.service.mp.mapper.SupplierMerchantMapper;
import com.medusa.gruul.single.SingleApplication;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.watcher.ResourceWatcherService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class YujiTests {

    @Autowired
    private SupplierHandleService supplierHandleService;
    @Autowired
    private SupplierMerchantMapper supplierMerchantMapper;

    @Test
    public void testSyncSetProductCategoryAndBrand() {
        SupplierDrugSyncDTO drugSyncDTO = new SupplierDrugSyncDTO();
        List<SupplierDrugDTO> drugList = Lists.newArrayList();
        SupplierDrugDTO drugDTO = new SupplierDrugDTO();
        drugDTO.setNo("10012s4");
        drugDTO.setSkuId("10012s4");
        drugDTO.setName("s氨咖黄敏口服溶液" );
        drugDTO.setPackaging("盒");
        drugDTO.setBarcode(RandomUtil.randomString(8));
        drugDTO.setCommonName(drugDTO.getName());
        drugDTO.setApprovalNumber("approvalNumber");
        drugDTO.setDosageForm("dosageForm");
        drugDTO.setSpecs("specs");
        drugDTO.setStockNum(101L); // 注意这里使用int类型
        drugDTO.setManufacturer("manufacturer");
        drugDTO.setUsageDosage("3");
        drugDTO.setNote("note");
        drugDTO.setComposition("composition");
        drugDTO.setStorageMode("storageMode");
        drugDTO.setSalePrice(160L); // 注意这里使用double类型，并明确给出小数点
        drugDTO.setSettlementPrice(72L); // 同样使用double类型
        drugDTO.setPic("https://whealthcn-**********.cos.ap-shanghai.myqcloud.com/mall/20240305/a50e0af305de4d1cbb64ba0c2978e7ad.png");

        // 设置detailImageList，这里只有一个元素，但用List表示
        List<String> detailImageList = Arrays.asList("https://whealthcn-**********.cos.ap-shanghai.myqcloud.com/mall/20240305/a50e0af305de4d1cbb64ba0c2978e7ad.png");
        drugDTO.setDetailImageList(detailImageList);
        drugDTO.setWeight(17L); // 注意这里使用int类型
        drugDTO.setWeightUnit("2"); // 这里假设保持为String类型，如果应该是int则需要进行转换
        drugDTO.setIsDrug(true); // 注意布尔值
        drugDTO.setIsPrescriptionDrug(false); // 同样注意布尔值

        drugDTO.setUsage(UsageEnum.ORAL_INHALATION.getCode());
        drugDTO.setDosage("3");
        drugDTO.setDosageUnit(UsageUnitEnum.UNIT.getCode());
        drugDTO.setFrequency(FrequencyEnum.BEFORE_BED.getCode());
        drugDTO.setIcdList(Lists.newArrayList());
        drugDTO.setStatus("0");
        drugList.add(drugDTO);
        SupplierMerchant supplierMerchant = supplierMerchantMapper.selectById(1665914868573914029L);
        drugSyncDTO.setDrugList(drugList);
        supplierHandleService.supplierProductBatchSave(supplierMerchant, drugSyncDTO);
    }

    @Test
    public void testSync() throws Exception {

        SupplierDrugSyncDTO drugSyncDTO = new SupplierDrugSyncDTO();
        List<SupplierDrugDTO> drugList = Lists.newArrayList();
        for (int i = 0; i < 300; i++) {
            String no = RandomUtil.randomString(8);
            SupplierDrugDTO drugDTO = new SupplierDrugDTO();
            drugDTO.setNo(no);
            drugDTO.setSkuId(no);
            drugDTO.setName("随机测试" + RandomUtil.randomString(4));
            drugDTO.setPackaging("盒");
            drugDTO.setBarcode(RandomUtil.randomString(8));
            drugDTO.setCommonName(drugDTO.getName());
            drugDTO.setApprovalNumber("approvalNumber");
            drugDTO.setDosageForm("dosageForm");
            drugDTO.setSpecs("specs");
            drugDTO.setStockNum(101L); // 注意这里使用int类型
            drugDTO.setManufacturer("manufacturer");
            drugDTO.setUsageDosage("3");
            drugDTO.setNote("note");
            drugDTO.setComposition("composition");
            drugDTO.setStorageMode("storageMode");
            drugDTO.setSalePrice(160L); // 注意这里使用double类型，并明确给出小数点
            drugDTO.setSettlementPrice(72L); // 同样使用double类型
            drugDTO.setPic("https://whealthcn-**********.cos.ap-shanghai.myqcloud.com/mall/20240305/a50e0af305de4d1cbb64ba0c2978e7ad.png");

            // 设置detailImageList，这里只有一个元素，但用List表示
            List<String> detailImageList = Arrays.asList("https://whealthcn-**********.cos.ap-shanghai.myqcloud.com/mall/20240305/a50e0af305de4d1cbb64ba0c2978e7ad.png");
            drugDTO.setDetailImageList(detailImageList);

            drugDTO.setWeight(17L); // 注意这里使用int类型
            drugDTO.setWeightUnit("2"); // 这里假设保持为String类型，如果应该是int则需要进行转换
            drugDTO.setIsDrug(true); // 注意布尔值
            drugDTO.setIsPrescriptionDrug(false); // 同样注意布尔值

            drugDTO.setUsage(UsageEnum.ORAL_INHALATION.getCode());
            drugDTO.setDosage("3");
            drugDTO.setDosageUnit(UsageUnitEnum.UNIT.getCode());
            drugDTO.setFrequency(FrequencyEnum.BEFORE_BED.getCode());
            drugDTO.setIcdList(Lists.newArrayList());
            drugDTO.setStatus("0");
            drugList.add(drugDTO);
        }
        drugSyncDTO.setDrugList(drugList);
        SupplierMerchant supplierMerchant = supplierMerchantMapper.selectById(1665914868573914029L);
        long start = System.currentTimeMillis();
        System.out.println("时间start=" + start);
        supplierHandleService.supplierProductBatchSave(supplierMerchant, drugSyncDTO);
        long end = System.currentTimeMillis();
        System.out.println("----------------");
        System.out.println("时间=" + (end - start));
        System.out.println("时间end=" + end);
    }


    @Test
    public void test() throws Exception {
        try {
            //创建订单
            List<SupplierOrderItemCreateDTO> itemList = Lists.newArrayList();
            SupplierOrderItemCreateDTO item = new SupplierOrderItemCreateDTO();
            item.setSkuId("0000974");
            item.setBuyCount(1);
            item.setSettlePrice(3000);
            item.setPrescription(2);
            itemList.add(item);
            SupplierOrderItemCreateDTO item2 = new SupplierOrderItemCreateDTO();
            item2.setSkuId("1000642");
            item2.setBuyCount(1);
            item2.setSettlePrice(3000);
            item2.setPrescription(2);
            itemList.add(item2);

            SupplierReceiveDTO receive = new SupplierReceiveDTO();
            receive.setName("测试");
            receive.setMobile("13312341234");
            receive.setProvinceCode(120000);
            receive.setCityCode(120100);
            receive.setAreaCode(120114);
            receive.setAddress("测试");
            receive.setExpressCompany("shentong");

            String[] prescriptionUrl = new String[]{"https://test.com/1.png"};
            SupplierOrderCreateDTO order = new SupplierOrderCreateDTO();
//            order.setAppKey("1100001");
            order.setOrderId(RandomUtil.randomString(18));
            order.setTotalPrice(6000);
            order.setDeliverSettlePrice(600);
            order.setItemList(itemList);
            order.setPrescriptionUrl(prescriptionUrl);
            order.setReceive(receive);
            order.setExtra("");
            order.setHeavy(10);
            String random = RandomUtil.randomString(28);
            String s = String.valueOf(System.currentTimeMillis());

            Map<String, Object> orderMap = JSON.parseObject(JSONUtil.toJsonStr(order), TreeMap.class);
            Map<String, Object> receiveMap = JSON.parseObject(JSONUtil.toJsonStr(receive), TreeMap.class);
            String itemListStr = YujiSignUtils.sortJson(JSONUtil.toJsonStr(itemList));
            orderMap.put("itemList", itemListStr);
            orderMap.put("receive", receiveMap);
//            String verify = verify(JSONUtil.toJsonStr(order), "", "", random, s);
            String verify = verify(JSONUtil.toJsonStr(orderMap), "YH_YJYK", "bea4cdcef2954d1d8e706f6c92bc0992", "1100001", s, random);
            String str = HttpUtil.createPost("https://shop-gateway.test.yesdream.cn/order/create")
                    .header("timestamp", s)
                    .header("randomStr", random)
                    .header("signature", verify)
                    .body(JSONUtil.toJsonStr(order))
                    .execute().body();
            log.debug("调用昱极接口成功：{}", JSONUtil.toJsonStr(order));
            log.debug("调用昱极接口成功，结果：{}", str);
            System.out.println("返回--" + str);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println();

    }

    @Test
    public void test2() throws Exception {
        try {
            //查询快递费
            Map<String, String> param = Maps.newHashMap();
            param.put("appKey", "1100001");
            param.put("provinceCode", "120000");
            param.put("cityCode", "120100");
            param.put("areaCode", "120114");
            param.put("heavy", "10");
            param.put("deliveryCompany", "shentong");
            String random = RandomUtil.randomString(28);
            String s = String.valueOf(System.currentTimeMillis());

            String json = YujiSignUtils.sortJson(JSONUtil.toJsonStr(param));
            String verify = verify(json, "YH_YJYK", "bea4cdcef2954d1d8e706f6c92bc0992", "1100001", s, random);
            String str = HttpUtil.createGet("https://shop-gateway.test.yesdream.cn/order/freight")
                    .header("timestamp", s)
                    .header("randomStr", random)
                    .header("signature", verify)
                    .formStr(param)
                    .execute().body();
            log.debug("调用查询昱极快递费接口成功，结果：{}", str);
            System.out.println("返回--" + str);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println();

    }

    @Test
    public void test3() throws Exception {
        try {
            // ⽀付订单
            Map<String, String> param = Maps.newHashMap();
            param.put("appKey", "1100001");
            //todo 昱极订单号，需要手动改
            param.put("orderNo", "YH1816751810004824064");
            param.put("orderStatus", "200");
            String random = RandomUtil.randomString(28);
            String s = String.valueOf(System.currentTimeMillis());

            String json = YujiSignUtils.sortJson(JSONUtil.toJsonStr(param));
            String verify = verify(json, "YH_YJYK", "bea4cdcef2954d1d8e706f6c92bc0992", "1100001", s, random);
            String str = HttpUtil.createPost("https://shop-gateway.test.yesdream.cn/order/pay")
                    .header("timestamp", s)
                    .header("randomStr", random)
                    .header("signature", verify)
                    .body(JSONUtil.toJsonStr(param))
                    .execute().body();
            log.debug("调用昱极⽀付订单接口成功，结果：{}", str);
            System.out.println("返回--" + str);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println();

    }

    @Test
    public void syncSupplierProduct() throws Exception {
        try {
            // ⽀付订单
            Map<String, String> param = Maps.newHashMap();
            param.put("appKey", "1100001");
            //todo 昱极订单号，需要手动改
            param.put("orderNo", "YH1816751810004824064");
            param.put("orderStatus", "200");
            String random = RandomUtil.randomString(28);
            String s = String.valueOf(System.currentTimeMillis());

            String json = YujiSignUtils.sortJson(JSONUtil.toJsonStr(param));
            String verify = verify(json, "YH_YJYK", "bea4cdcef2954d1d8e706f6c92bc0992", "1100001", s, random);
            String str = HttpUtil.createPost("https://shop-gateway.test.yesdream.cn/order/pay")
                    .header("timestamp", s)
                    .header("randomStr", random)
                    .header("signature", verify)
                    .body(JSONUtil.toJsonStr(param))
                    .execute().body();
            log.debug("调用昱极⽀付订单接口成功，结果：{}", str);
            System.out.println("返回--" + str);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println();

    }


    public static String verify(String data, String mchId, String appSecret, String appKey,
                                String timestamp, String randomStr) {
        try {
            //timestamp十分钟内有效
//            if (!isWithinTenMinutes(System.currentTimeMillis(), Long.parseLong(timestamp))) {
//                throw new GlobalException("签名十分钟内有效");
//            }

            Map<String, Object> signatureMap = JSON.parseObject(data, TreeMap.class);
            signatureMap.put("timestamp", timestamp);
            signatureMap.put("randomStr", randomStr);
            signatureMap.put("appSecret", appSecret);
            signatureMap.put("appKey", appKey);
//            signatureMap.put("mchId", mchId);

            List<String> paramList = signatureMap.entrySet().stream().map(entry ->
                    String.format("%s=%s", entry.getKey(), entry.getValue())).collect(Collectors.toList());
            log.debug(String.join("&", paramList));
            String signature = Hashing.md5().hashBytes(String.join("&",
                    paramList).getBytes(Charset.defaultCharset())).toString();
            log.debug("签名:{}", signature);
            return signature;
        } catch (Exception e) {

        }
        return "";
    }

}
