package com.medusa.gruul.payment.test;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.distribute.model.dto.DistributorInCodeQueryDTO;
import com.medusa.gruul.addon.distribute.mp.entity.DistributorInCode;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeInCodeService;
import com.medusa.gruul.single.SingleApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class IDistributeInCodeTests {

    @Autowired
    private IDistributeInCodeService distributeInCodeService;

    @Test
    public void test() throws Exception {
        distributeInCodeService.generateCode(1710549018772815872L, 100, 6);


        try {
            distributeInCodeService.checkCode("UHTXDU", 1739466695864954880L, true);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
        try {
            distributeInCodeService.checkCode("UHTX2U", 1739466695864954880L, true);
        }catch (Exception e){
            System.out.println(e.getMessage());
        }


        IPage<DistributorInCode> distributorInCodeIPage = distributeInCodeService.distributorInCodePage(new DistributorInCodeQueryDTO());
        System.out.println("=============");
    }


}
