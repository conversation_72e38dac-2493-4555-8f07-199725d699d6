package com.medusa.gruul.payment.test;

import cn.hutool.json.JSONUtil;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.dto.ProductBroadcastDTO;
import com.medusa.gruul.shop.api.model.dto.ShopProductSyncDTO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.goods.service.mp.service.impl.ProductServiceImpl;
import com.medusa.gruul.goods.service.service.ShopProductSyncService;
import com.medusa.gruul.single.SingleApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class ShopTests {

    @Autowired
    private ShopProductSyncService shopProductSyncService;

    @Autowired
    private ProductServiceImpl productService;
    @Autowired
    private GoodsRpcService goodsRpcService;

    @Test
    public void test() throws Exception {
        Product productById = productService.getProductInfo(1710549018772815872L, 1744983240773365760L);
        ProductBroadcastDTO productBroadcastDTO = new ProductBroadcastDTO();
        productBroadcastDTO.setProduct(productById);
        System.out.println(JSONUtil.toJsonStr(productBroadcastDTO));
//        shopProductSyncService.productRelease(productBroadcastDTO);
        System.out.println();

    }

    @Test
    public void test2() throws Exception {
        ShopProductSyncDTO shopProductSyncDTO = new ShopProductSyncDTO();
        shopProductSyncDTO.setTargetShopId(1732633724964093952L);
        shopProductSyncDTO.setProductId(1743168886461591552L);
//        goodsRpcService.shopSyncProduct(shopProductSyncDTO);
        System.out.println();
    }

    @Test
    public void ybbscsj() throws Exception {
        //已报备商品上架
//        Product productById = goodsRpcService.d(1710549018772815872L, 1744983240773365760L);
//        ProductBroadcastDTO productBroadcastDTO = new ProductBroadcastDTO();
//        productBroadcastDTO.setProduct(productById);
//        System.out.println(JSONUtil.toJsonStr(productBroadcastDTO));
////        shopProductSyncService.productRelease(productBroadcastDTO);
//        System.out.println();

    }


}
