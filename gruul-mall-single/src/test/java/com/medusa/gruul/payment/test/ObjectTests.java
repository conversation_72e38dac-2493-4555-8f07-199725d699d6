package com.medusa.gruul.payment.test;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import com.medusa.gruul.goods.api.model.dto.SupplierMerchantSyncExtraDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RunWith(SpringRunner.class)
public class ObjectTests {

    @Test
    public void test() throws Exception {
        String host = "https://idcard.csapi.com.cn/base2Factors";
        String path = "/base2Factors";
        String method = "POST";
        String appcode = "672a20416f594a70a9bdfc78d0c86f15";
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appcode);
        //根据API的要求，定义相对应的Content-Type
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        //需要给X-Ca-Nonce的值生成随机字符串，每次请求不能相同
        headers.put("X-Ca-Nonce", UUID.randomUUID().toString());
        Map<String, String> querys = new HashMap<String, String>();
        Map<String, Object> bodys = new HashMap<String, Object>();
        bodys.put("name", "卫健");
        bodys.put("idcard", "220122199507278112");


        try {
            /**
             * 重要提示如下:
             * HttpUtils请从
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
             * 下载
             *
             * 相应的依赖请参照
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
             */
            String result2 = HttpRequest.post(host)
                    .header("Authorization", "APPCODE " + appcode)//头信息，多个头信息多次调用此方法即可
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")//头信息，多个头信息多次调用此方法即可
                    .header("X-Ca-Nonce", UUID.randomUUID().toString())//头信息，多个头信息多次调用此方法即可
                    .form(bodys)//表单内容
                    .timeout(20000)//超时，毫秒
                    .execute().body();
            System.out.println(result2);
            //获取response的body
            //System.out.println(EntityUtils.toString(response.getEntity()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println();

    }


}
