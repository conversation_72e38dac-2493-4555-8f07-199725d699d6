package com.medusa.gruul.payment.test;

import cn.hutool.json.JSONUtil;
import com.medusa.gruul.payment.api.model.param.PaymentAccountOpenParam;
import com.medusa.gruul.payment.api.model.param.PaymentAccountOpenResult;
import com.medusa.gruul.payment.api.rpc.PaymentRpcService;
import com.medusa.gruul.single.SingleApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class AccountOpenTests {

    @Autowired
    private PaymentRpcService paymentRpcService;

    @Test
    public void test() throws Exception {
        PaymentAccountOpenParam param = new PaymentAccountOpenParam()
                .setUserId(123L)
                .setName("卫健")
                .setMobileNo("***********")
                .setCertNo("220122199507278112")
                .setCertValidityType("0")
                .setCertBeginDate("********")
                .setCertEndDate("********")
                .setCardNo("6217001210057640250")
                .setProvId("310000")
                .setAreaId("310100")
                .setCardMobileNo("***********");

        PaymentAccountOpenResult result = paymentRpcService.openAccount(param);
        System.out.println(JSONUtil.toJsonStr(result));
        System.out.println();

    }


}
