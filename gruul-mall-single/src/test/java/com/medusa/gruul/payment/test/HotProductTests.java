package com.medusa.gruul.payment.test;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.addon.hot.model.enums.ProductType;
import com.medusa.gruul.addon.hot.mp.entity.HotProduct;
import com.medusa.gruul.addon.hot.mp.service.HotProductService;
import com.medusa.gruul.single.SingleApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class HotProductTests {
    @Autowired
    private HotProductService hotProductService;

    @Test
    public void insertHotProducts() {
        HotProduct hotProduct = new HotProduct();
        hotProduct.setShopId(1710549018772815872L)
                .setProductId(1755139128289030144L).setPic("https://whealthcn-**********.cos.ap-shanghai.myqcloud.com/mall/20240207/3ba62a42dd0c4a4a8d430cf6a62a8401.jpg")
                .setProductName("测试实物1")
                .setSalePrice(30000L)
                .setType(ProductType.HOT)
                .setNo("24020700000008");
        Random random = new Random();

        for (int i = 0; i < 10; i++) {
            System.out.println(hotProduct.getId());
            hotProduct.setNo(String.valueOf(random.nextLong(1000000L))).setId(null);
            hotProductService.save(hotProduct);
        }
        for (int i = 0; i < 10; i++) {
            hotProduct.setNo(String.valueOf(random.nextLong(1000000L))).setType(ProductType.RECOMMAND).setId(null);
            hotProductService.save(hotProduct);
        }
    }

}
