package com.medusa.gruul.payment.test;

import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.afs.api.enums.AfsReason;
import com.medusa.gruul.afs.api.enums.AfsType;
import com.medusa.gruul.afs.api.model.AfsApplyDTO;
import com.medusa.gruul.afs.service.service.AfsOrderApplyService;
import com.medusa.gruul.single.SingleApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SingleApplication.class)
public class AfsorderTests {

    @Autowired
    private AfsOrderApplyService afsOrderApplyService;

    @Test
    public void apply() throws Exception {
        AfsApplyDTO afsApplyDTO = new AfsApplyDTO();
        afsApplyDTO.setOrderNo("SS1792388679396335616");
        afsApplyDTO.setItemId(1792388679422660608L);
        afsApplyDTO.setRemark("平台主动申请退货退款");
        afsApplyDTO.setReason(AfsReason.SEVEN_DAYS_NO_REASON);
        afsApplyDTO.setType(AfsType.RETURN_REFUND);
        afsApplyDTO.setShopId(1710549018772815872L);
        afsApplyDTO.setRefundAmount(10000L);
        afsApplyDTO.setBuyerId(1716342201210318848L);
        System.out.println(JSON.toJSONString(afsApplyDTO));
        afsOrderApplyService.apply(afsApplyDTO);
        System.out.println();

    }


}
