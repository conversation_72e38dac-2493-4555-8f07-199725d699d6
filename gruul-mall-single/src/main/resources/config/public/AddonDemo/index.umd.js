(function(e,n){typeof exports=="object"&&typeof module<"u"?module.exports=n(require("vue")):typeof define=="function"&&define.amd?define(["vue"],n):(e=typeof globalThis<"u"?globalThis:e||self,e.AddonDemo=n(e.AddonDemoContext.Vue))})(this,function(e){"use strict";var n=document.createElement("style");n.textContent=`.container[data-v-23a14354]{width:100%;height:200px;background-color:red}
`,document.head.appendChild(n);const l="",c=(t,d)=>{const o=t.__vccOpts||t;for(const[r,i]of d)o[r]=i;return o},a={},_=(t=>(e.pushScopeId("data-v-23a14354"),t=t(),e.popScopeId(),t))(()=>e.createElementVNode("div",{class:"container"},[e.createElementVNode("div",null,"1111"),e.createElementVNode("h1",null,"asdsad")],-1));function s(t,d){const o=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(o,null,{default:e.withCtx(()=>[e.createTextVNode("aaaa")]),_:1}),_],64)}return c(a,[["render",s],["__scopeId","data-v-23a14354"]])});
