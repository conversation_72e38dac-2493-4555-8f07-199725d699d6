(function(e,C){typeof exports=="object"&&typeof module<"u"?module.exports=C(require("vue"),require("@vueuse/core"),require("@element-plus/icons-vue"),require("@/apis/http"),require("@/components/PageManage.vue"),require("vue-router"),require("element-plus"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@element-plus/icons-vue","@/apis/http","@/components/PageManage.vue","vue-router","element-plus","@/composables/useConvert"],C):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformBargain=C(e.PlatformBargainContext.Vue,e.PlatformBargainContext.VueUse,e.PlatformBargainContext.ElementPlusIconsVue,e.PlatformBargainContext.Request,e.PlatformBargainContext.PageManageTwo,e.PlatformBargainContext.VueRouter,e.PlatformBargainContext.ElementPlus,e.PlatformBargainContext.UseConvert))})(this,function(e,C,k,y,L,T,d,I){"use strict";const O=e.defineComponent({__name:"select-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Array,default:()=>[{label:"1",value:"2"}]}},emits:["update:modelValue","change"],setup(c,{emit:f}){const m=c,s=C.useVModel(m,"modelValue",f);return(u,V)=>{const n=e.resolveComponent("el-option"),h=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(h,{modelValue:e.unref(s),"onUpdate:modelValue":V[0]||(V[0]=p=>e.isRef(s)?s.value=p:null),placeholder:m.placeholder,style:{width:"150px"},onChange:V[1]||(V[1]=p=>f("change",p))},{default:e.withCtx(()=>[e.renderSlot(u.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.list,(p,g)=>(e.openBlock(),e.createBlock(n,{key:g,label:p.label,value:p.value},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),M=e.defineComponent({__name:"head-search",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},selectOptions:{type:Object,default(){return{}}}},emits:["update:modelValue","batchDel","search"],setup(c,{emit:f}){const m=c,s=[{value:"NOT_STARTED",label:"未开始"},{value:"PROCESSING",label:"进行中"},{value:"OVER",label:"已结束"},{value:"ILLEGAL_SELL_OFF",label:"违规下架"}],u=C.useVModel(m,"modelValue",f);return(V,n)=>{const h=e.resolveComponent("el-button"),p=e.resolveComponent("el-col"),g=e.resolveComponent("el-option"),w=e.resolveComponent("el-space"),E=e.resolveComponent("el-input"),N=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(N,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px"}},{default:e.withCtx(()=>[e.createVNode(p,{span:14},{default:e.withCtx(()=>[e.createVNode(h,{round:"",plain:"",onClick:n[0]||(n[0]=i=>f("batchDel"))},{default:e.withCtx(()=>[e.createTextVNode("批量移除")]),_:1})]),_:1}),e.createVNode(p,{span:9},{default:e.withCtx(()=>[e.createVNode(w,null,{default:e.withCtx(()=>[e.createVNode(O,{modelValue:e.unref(u).status,"onUpdate:modelValue":n[1]||(n[1]=i=>e.unref(u).status=i),list:s,onChange:n[2]||(n[2]=i=>f("search"))},{default:e.withCtx(()=>[e.createVNode(g,{label:"全部状态",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(E,{modelValue:e.unref(u).keywords,"onUpdate:modelValue":n[4]||(n[4]=i=>e.unref(u).keywords=i),placeholder:"输入关键词",style:{width:"55%"}},{append:e.withCtx(()=>[e.createVNode(h,{icon:e.unref(k.Search),onClick:n[3]||(n[3]=i=>f("search"))},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),x="addon-bargain/bargain/",D=c=>y.get({url:x,params:c}),b=c=>y.del({url:x,data:c}),R=c=>y.put({url:x+`sellOf/${c.shopId}/${c.activityId}`}),F={class:"goods"};return e.defineComponent({__name:"PlatformBargain",setup(c){const{divTenThousand:f}=I(),m=e.ref({keywords:"",status:""}),s=e.ref({size:10,current:1}),u=e.ref(0),V=T.useRouter(),n=e.ref([]),h=e.ref([]),p={NOT_STARTED:"未开始",PROCESSING:"进行中",OVER:"已结束",ILLEGAL_SELL_OFF:"违规下架"},g=async a=>{try{if(!await d.ElMessageBox.confirm("确定下架该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:o,data:_}=await R({shopId:a.shopId,activityId:a.id});if(o!==200){d.ElMessage.error("下架失败");return}d.ElMessage.success("下架成功");const l=n.value.find(B=>B.id===a.id);l&&(l.status="ILLEGAL_SELL_OFF")}catch{}},w=async()=>{if(!h.value.length){d.ElMessage.info("请选择需要删除的活动");return}if(!await d.ElMessageBox.confirm("确定删除活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const r=h.value.map(l=>({shopId:l.shopId,activityId:l.id})),{code:o,data:_}=await b(r);if(o!==200){d.ElMessage.error("删除失败");return}d.ElMessage.success("删除成功"),i()},E=a=>{V.push({name:"bargainBaseinfo",query:{activityId:a.id,shopId:a.shopId}})},N=()=>{i()};async function i(){const{status:a,keywords:r}=m.value,o={...s.value,status:a,keyword:r},{code:_,data:l}=await D(o);if(_!==200)return d.ElMessage.error("获取活动列表失败");n.value=l.records,s.value.current=l.current,s.value.size=l.size,u.value=l.total}const z=async a=>{try{if(!await d.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:o,data:_}=await b([{shopId:a.shopId,activityId:a.id}]);if(o!==200){d.ElMessage.error("删除失败");return}d.ElMessage.success("删除成功"),n.value=n.value.filter(l=>l.id!==a.id),u.value--}catch{}},A=a=>{s.value.size=a,i()},$=a=>{s.value.current=a,i()};return e.onBeforeMount(()=>{i()}),(a,r)=>{const o=e.resolveComponent("el-table-column"),_=e.resolveComponent("el-link"),l=e.resolveComponent("el-row"),B=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(M,{modelValue:m.value,"onUpdate:modelValue":r[0]||(r[0]=t=>m.value=t),onBatchDel:w,onSearch:N},null,8,["modelValue"]),e.createVNode(B,{ref:"multipleTableRef",data:n.value,stripe:"",height:"calc(100vh - 250px)","header-row-style":{fontSize:"12px",color:"#909399"},"header-cell-style":{background:"#f6f8fa"},"cell-style":{fontSize:"12px",color:"#333333"},onSelectionChange:r[1]||(r[1]=t=>h.value=t)},{default:e.withCtx(()=>[e.createVNode(o,{type:"selection",width:"55"}),e.createVNode(o,{label:"店铺名称",width:"160"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",F,e.toDisplayString(t.shopName),1)]),_:1}),e.createVNode(o,{label:"活动名称",width:"150"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.name),1)]),_:1}),e.createVNode(o,{label:"活动时间",align:"center",width:"250"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",null,e.toDisplayString(t.startTime),1),e.createElementVNode("div",null,e.toDisplayString(t.endTime),1)]),_:1}),e.createVNode(o,{label:"参加人数",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.users||0),1)]),_:1}),e.createVNode(o,{label:"应收金额",align:"center",width:"150"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.amountReceivable&&e.unref(f)(t.amountReceivable).toString()||0),1)]),_:1}),e.createVNode(o,{label:"状态",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:t.status==="ILLEGAL_SELL_OFF"?"#F12F22":""})},e.toDisplayString(p[t.status]),5)]),_:1}),e.createVNode(o,{label:"操作",align:"center",width:"200px",fixed:"right"},{default:e.withCtx(({row:t})=>[e.createVNode(l,{justify:"center",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(_,{style:{padding:"0 10px"},underline:!1,type:"primary",size:"small",onClick:S=>E(t)},{default:e.withCtx(()=>[e.createTextVNode(" 查看 ")]),_:2},1032,["onClick"]),["ILLEGAL_SELL_OFF","OVER"].includes(t.status)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(_,{key:0,style:{padding:"0 10px"},underline:!1,type:"primary",size:"small",onClick:S=>g(t)},{default:e.withCtx(()=>[e.createTextVNode(" 下架 ")]),_:2},1032,["onClick"])),e.createVNode(_,{style:{padding:"0 10px"},underline:!1,type:"primary",size:"small",onClick:S=>z(t)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),e.createVNode(l,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(L,{modelValue:s.value,"onUpdate:modelValue":r[2]||(r[2]=t=>s.value=t),"load-init":!0,total:u.value,onHandleSizeChange:A,onHandleCurrentChange:$,onReload:i},null,8,["modelValue","total"])]),_:1})])}}})});
