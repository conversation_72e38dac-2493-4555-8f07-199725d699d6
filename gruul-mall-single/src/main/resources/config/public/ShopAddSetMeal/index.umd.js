(function(e,T){typeof exports=="object"&&typeof module<"u"?module.exports=T(require("vue"),require("vue-router"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("element-plus"),require("@/composables/useConvert"),require("@/apis/http"),require("@/utils/date"),require("@/components/q-upload/q-upload.vue"),require("@/store/modules/shopInfo")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","element-plus","@/composables/useConvert","@/apis/http","@/utils/date","@/components/q-upload/q-upload.vue","@/store/modules/shopInfo"],T):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopAddSetMeal=T(e.ShopAddSetMealContext.Vue,e.ShopAddSetMealContext.VueRouter,e.ShopAddSetMealContext.QChooseGoodsPopup,e.ShopAddSetMealContext.ElementPlus,e.ShopAddSetMealContext.UseConvert,e.ShopAddSetMealContext.Request,e.ShopAddSetMealContext.DateUtil,e.ShopAddSetMealContext.QUpload,e.ShopAddSetMealContext.ShopInfoStore))})(this,function(e,T,F,C,H,J,v,ee,te){"use strict";var j=document.createElement("style");j.textContent=`@charset "UTF-8";.com[data-v-1f9cc598]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-1f9cc598]{width:62px;height:62px}.com__name[data-v-1f9cc598]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.add[data-v-8eda12f8]{margin:-20px -15px;height:calc(100vh - 85px);overflow-y:auto;scrollbar-width:none;-ms-overflow-style:none}.add[data-v-8eda12f8]::-webkit-scrollbar{display:none}.bargaining_amount[data-v-8eda12f8]{position:relative;width:100%}.bargaining_amount__description[data-v-8eda12f8]{position:absolute;top:-35px;right:0;width:480px}.title[data-v-8eda12f8]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-8eda12f8]{font-size:12px;margin-left:12px;color:#c4c4c4}.use_discount[data-v-8eda12f8]{display:flex;width:100%}.discount_msg[data-v-8eda12f8]{display:inline-block;width:400px;flex:1}.rules[data-v-8eda12f8]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-8eda12f8]{width:300px;display:flex}.text[data-v-8eda12f8]{font-size:14px;color:#333}.goodsData[data-v-8eda12f8]{border:1px solid #ccc}.goods-list[data-v-8eda12f8]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-8eda12f8]{display:flex}.goods-list__goods-list__info-name[data-v-8eda12f8]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-8eda12f8]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-8eda12f8]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-8eda12f8]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-8eda12f8]{font-size:16px}.ruleform-date[data-v-8eda12f8]{width:100%;display:flex;align-items:center}.flex[data-v-8eda12f8]{margin-top:10px;height:50px}.flex-item[data-v-8eda12f8]{width:40%}.coupon-rules[data-v-8eda12f8]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-8eda12f8]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild(j);const oe={class:"com"},ae={class:"com__name"},le=e.defineComponent({__name:"select-goods-table",props:{productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}},productAttributes:{type:String,default:"MAIN_PRODUCT"}},setup(h,{expose:x}){const g=h,{divTenThousand:M,mulTenThousand:b}=H(),_=e.ref([]);e.watch(()=>g.productList,o=>{const c=E(o);console.log("flatGoodList",c),_.value=G(c)}),e.watch(()=>g.flatGoodList,o=>{_.value=G(o)});function D(o){return o.skuItem.stockType==="LIMITED"?Number(o.skuItem.skuStock):1/0}function E(o,c){if(!o.length)return[];const s=[];return o.forEach(n=>{n.skuIds.forEach((m,f)=>{s.push({productId:n.productId,productName:n.productName,productPic:n.pic,shopId:n.shopId,skuItem:{productId:n.productId,skuId:m,skuName:n.specs[f],skuPrice:n.salePrices[f],skuStock:n.stocks[f],stockType:n.stockTypes[f]},rowTag:0,matchingStock:0,isJoin:!0,matchingPrice:.01})})}),s}function G(o,c){let s=0,n=o.length;for(let m=0;m<n;m++){const f=o[m];m===0&&(f.rowTag=1,s=0),m!==0&&(f.productId===o[m-1].productId?(f.rowTag=0,o[s].rowTag=o[s].rowTag+1):(f.rowTag=1,s=m))}return o}const S=({row:o,column:c,rowIndex:s,columnIndex:n})=>{if(n===0)return{rowspan:o.rowTag,colspan:o.rowTag?1:0}};function I(o){return o.stockType==="UNLIMITED"?"不限购":o.skuStock}function l(){return e.toRaw(_.value).filter(c=>c.isJoin).map(c=>{const{setMealId:s,productId:n,productPic:m,matchingPrice:f,productName:y,matchingStock:i,shopId:V,skuItem:{skuId:R,skuStock:Y,skuPrice:q,skuName:L,stockType:O}}=c;return{setMealId:s||"",shopId:V,productId:n,productPic:m,productName:y,productAttributes:g.productAttributes,skuId:R,skuName:L,skuStock:+Y,skuPrice:q,stockType:O,matchingPrice:b(f).toString(),matchingStock:i}})}function u(){let o=!0;const c=_.value;if(!c.length)C.ElMessage.warning("请选择商品"),o=!1;else for(let s=0;s<c.length;s++)if(console.log("productList",c[s]),!!c[s].isJoin&&!c[s].matchingStock){C.ElMessage.warning("商品库存必须大于零"),o=!1;break}return o}return x({getProduct:l,validateProduct:u}),(o,c)=>{const s=e.resolveComponent("el-image"),n=e.resolveComponent("el-table-column"),m=e.resolveComponent("el-input-number"),f=e.resolveComponent("el-switch"),y=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(y,{data:_.value,"max-height":500,"span-method":S},{default:e.withCtx(()=>[e.createVNode(n,{label:"商品信息",width:"215"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",oe,[e.createVNode(s,{src:i.productPic,class:"com__pic"},null,8,["src"]),e.createElementVNode("div",ae,e.toDisplayString(i.productName),1)])]),_:1}),e.createVNode(n,{label:"规格"},{default:e.withCtx(({row:i})=>[e.createTextVNode(e.toDisplayString(i.skuItem.skuName),1)]),_:1}),e.createVNode(n,{label:"套餐库存"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",null,[e.createVNode(m,{controls:!1,disabled:g.isEdit,max:D(i),min:0,"model-value":+i.matchingStock,precision:0,style:{width:"80px"},"onUpdate:modelValue":V=>i.matchingStock=V},null,8,["disabled","max","model-value","onUpdate:modelValue"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(I(i.skuItem)),1)]),_:1}),e.createVNode(n,{label:"套餐价（元）"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",null,[e.createVNode(m,{controls:!1,disabled:g.isEdit,max:e.unref(M)(i.skuItem.skuPrice).toNumber(),min:.01,"model-value":+i.matchingPrice,precision:2,style:{width:"80px"},"onUpdate:modelValue":V=>i.matchingPrice=V},null,8,["disabled","max","model-value","onUpdate:modelValue"])]),e.createElementVNode("div",null,"销售价"+e.toDisplayString(e.unref(M)(i.skuItem.skuPrice)),1)]),_:1}),e.createVNode(n,{label:"是否参与"},{default:e.withCtx(({row:i})=>[e.createVNode(f,{modelValue:i.isJoin,"onUpdate:modelValue":V=>i.isJoin=V,disabled:g.isEdit,size:"large"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),we="",K=(h,x)=>{const g=h.__vccOpts||h;for(const[M,b]of x)g[M]=b;return g},X=K(le,[["__scopeId","data-v-1f9cc598"]]),Q="addon-matching-treasure/setMeal/",de=h=>J.post({url:Q,data:h}),ne=(h,x)=>J.get({url:`${Q}${h}/${x}`}),N=h=>(e.pushScopeId("data-v-8eda12f8"),h=h(),e.popScopeId(),h),re={class:"add",style:{padding:"40px"}},se=N(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),ie={class:"ruleform-date"},ce=N(()=>e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1)),pe={class:"use_discount"},me=N(()=>e.createElementVNode("div",{class:"msg discount_msg"},[e.createTextVNode(" 叠加优惠可能导致实付金额为 "),e.createElementVNode("strong",{style:{color:"red"}},"0"),e.createTextVNode(" (实付金额 = 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1)),ue=N(()=>e.createElementVNode("span",{class:"msg"},"自选套餐：主商品+至少1种搭配商品以上 ；固定套餐：主商品+库存不为0的所有搭配商品各1件以上",-1)),fe=N(()=>e.createElementVNode("span",{class:"msg"},"主商品：必买商品，此商品详情页展示搭配套餐 搭配商品：用户选择购买",-1)),he=N(()=>e.createElementVNode("span",{class:"msg"},"是否参与：SKU的粒度设置商品是否参与活动，是(默认)则参与活动，反则否",-1)),ge=e.defineComponent({__name:"ShopAddSetMeal",setup(h){const x=T.useRouter(),g=T.useRoute(),M=new v,b=e.ref(),_=e.ref(),D=e.ref(),E=e.ref(),G=e.reactive({form:{setMealId:"",shopId:"",shopName:te.useShopInfoStore().shopInfo.name,setMealName:"",setMealDescription:"",setMealMainPicture:"",setMealType:"FIXED_COMBINATION",setMealStatus:"NOT_STARTED",startTime:"",endTime:"",stackable:{coupon:!1,vip:!1,full:!1},mainProduct:[],matchingProducts:[]},isEditDisable:!1,fullReductionTime:[],rules:{setMealName:[{required:!0,message:"请输入套餐名称",trigger:"blur"}],setMealDescription:[{required:!0,message:"请输入套餐描述",trigger:"blur"}],product:[{required:!0,message:"请选择商品",trigger:["blur","change"]}],startTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]},{validator:Ve,trigger:["blur","change"]}],endTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]},{validator:Me,trigger:["blur","change"]}],setMealMainPicture:[{required:!0,message:"请添加套餐主图",trigger:["blur","change"]}],setMealType:[{required:!0,message:"请选择套餐类型",trigger:["blur","change"]}]},flatMainGoodList:[],flatMatchingGoodList:[],choosedMainGoods:[],choosedMatchingGoods:[],choosedMainGoodsPopup:!1,choosedMatchingGoodsPopup:!1}),S=e.reactive({maxPrice:"",minPrice:"",keyword:"",categoryFirstId:"",excludeProductIds:[]}),I=e.reactive({maxPrice:"",minPrice:"",keyword:"",categoryFirstId:"",excludeProductIds:[]}),{form:l,isEditDisable:u,rules:o,choosedMainGoodsPopup:c,choosedMatchingGoodsPopup:s,choosedMainGoods:n,choosedMatchingGoods:m,flatMainGoodList:f,flatMatchingGoodList:y}=e.toRefs(G),i=e.ref(),{mulTenThousand:V,divTenThousand:R}=H();Y();async function Y(d=g.query){if(d.shopId&&d.setMealId){u.value=!0;const{code:t,data:r,msg:p}=await ne(d.shopId,d.setMealId);if(t!==200)return C.ElMessage.error(p||"获取活动详情失败");l.value=r,f.value=q(r.mainProduct),y.value=q(r.matchingProducts)}}function q(d){return d.map(t=>{const{productId:r,skuPrice:p,productPic:w,productName:P,skuId:A,skuStock:$,stockType:k,skuName:B,setMealId:z,matchingPrice:a,matchingStock:Ne,productAttributes:ye}=t;return{isJoin:!0,productId:r,productName:P,productPic:w,setMealId:z,matchingPrice:R(a).toString(),matchingStock:Ne,productAttributes:ye,skuItem:{productId:r,skuId:A,skuName:B,skuPrice:p,skuStock:$,stockType:k}}})}const L=async()=>{if(!(!i.value||!await i.value.validate())&&!(!b.value&&!_.value)&&b.value.validateProduct()&&_.value.validateProduct()){l.value.mainProduct=b.value.getProduct(),l.value.matchingProducts=_.value.getProduct();const{code:t,data:r,msg:p}=await de(l.value);if(t===200)return O();C.ElMessage.error(p||"添加活动失败")}};function O(){C.ElMessage.success("添加活动成功"),x.push({name:"bundlePriceIndex"})}const _e=d=>{S.excludeProductIds=d.tempGoods.map(t=>t.productId),m.value=d.tempGoods,D.value.retrieveCommodity()},xe=d=>{I.excludeProductIds=[d.tempGoods[0].productId],n.value=d.tempGoods,E.value.retrieveCommodity()};function W(d){const t=M.getYMD(new Date),r=M.getYMD(d);return t===r?!1:new Date().getTime()>d.getTime()}const Z=async d=>be(d);function be(d){if(d==="main"){c.value=!0;return}s.value=!0}function Ve(d,t,r){t?t&&l.value.endTime?U(new Date(l.value.endTime).getTime(),r,"开始日期和结束日期最少间隔5分钟",new Date(t).getTime(),1e3*60*5):U(new Date(t).getTime(),r,"开始日期必须是一个将来的时间",new Date().getTime(),1e3):r(new Error("请选择活动开始日期"))}function Me(d,t,r){t?t&&l.value.startTime?U(new Date(t).getTime(),r,"开始日期和结束日期最少间隔5分钟",new Date(l.value.startTime).getTime(),1e3*60*5):U(new Date(t).getTime(),r,"结束日期最少大当前时间5分钟",new Date().getTime()+1e3,1e3*60*5):r(new Error("请选择活动结束日期"))}function Te(d,t=new Date().getTime()){const r=d-t;return(p=1e3)=>r>=p}function U(d,t,r,p,w){Te(d,p)(w)?t():t(new Error(r))}return(d,t)=>{const r=e.resolveComponent("el-input"),p=e.resolveComponent("el-form-item"),w=e.resolveComponent("el-date-picker"),P=e.resolveComponent("el-checkbox"),A=e.resolveComponent("el-radio"),$=e.resolveComponent("el-radio-group"),k=e.resolveComponent("el-button"),B=e.resolveComponent("el-form"),z=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",re,[se,e.createVNode(B,{ref_key:"ruleFormRef",ref:i,"inline-message":!1,model:e.unref(l),rules:e.unref(o),"label-position":"left","label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(p,{label:"套餐名称",prop:"setMealName"},{default:e.withCtx(()=>[e.createVNode(r,{modelValue:e.unref(l).setMealName,"onUpdate:modelValue":t[0]||(t[0]=a=>e.unref(l).setMealName=a),modelModifiers:{trim:!0},disabled:e.unref(u),maxlength:"15",placeholder:"限15个字",style:{width:"551px"}},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(p,{label:"套餐描述",prop:"setMealDescription"},{default:e.withCtx(()=>[e.createVNode(r,{modelValue:e.unref(l).setMealDescription,"onUpdate:modelValue":t[1]||(t[1]=a=>e.unref(l).setMealDescription=a),modelModifiers:{trim:!0},disabled:e.unref(u),maxlength:"40",placeholder:"套餐描述不超过40个字",style:{width:"551px"}},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(p,{label:"活动日期",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",ie,[e.createVNode(p,{"inline-message":!1,prop:"startTime"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:e.unref(l).startTime,"onUpdate:modelValue":t[2]||(t[2]=a=>e.unref(l).startTime=a),disabled:e.unref(u),"disabled-date":W,format:"YYYY/MM/DD HH:mm:ss",placeholder:"请选择开始时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","disabled"]),ce]),_:1}),e.createVNode(p,{"inline-message":!1,prop:"endTime"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:e.unref(l).endTime,"onUpdate:modelValue":t[3]||(t[3]=a=>e.unref(l).endTime=a),disabled:e.unref(u),"disabled-date":W,format:"YYYY/MM/DD HH:mm:ss",placeholder:"请选择结束时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(p,{label:"套餐主图",prop:"setMealMainPicture"},{default:e.withCtx(()=>[e.createVNode(ee,{src:e.unref(l).setMealMainPicture,"onUpdate:src":t[4]||(t[4]=a=>e.unref(l).setMealMainPicture=a),format:{size:1},height:100,width:100},null,8,["src"])]),_:1}),e.createVNode(p,{label:"叠加优惠"},{default:e.withCtx(()=>[e.createElementVNode("div",pe,[e.createVNode(P,{modelValue:e.unref(l).stackable.vip,"onUpdate:modelValue":t[5]||(t[5]=a=>e.unref(l).stackable.vip=a),disabled:e.unref(u),label:"会员价"},null,8,["modelValue","disabled"]),e.createVNode(P,{modelValue:e.unref(l).stackable.coupon,"onUpdate:modelValue":t[6]||(t[6]=a=>e.unref(l).stackable.coupon=a),disabled:e.unref(u),label:"优惠券"},null,8,["modelValue","disabled"]),e.createVNode(P,{modelValue:e.unref(l).stackable.full,"onUpdate:modelValue":t[7]||(t[7]=a=>e.unref(l).stackable.full=a),disabled:e.unref(u),label:"满减"},null,8,["modelValue","disabled"]),me])]),_:1}),e.createVNode(p,{label:"套餐类型",prop:"setMealType"},{default:e.withCtx(()=>[e.createVNode($,{modelValue:e.unref(l).setMealType,"onUpdate:modelValue":t[8]||(t[8]=a=>e.unref(l).setMealType=a),disabled:e.unref(u),class:"ml-4"},{default:e.withCtx(()=>[e.createVNode(A,{label:"OPTIONAL_PRODUCT"},{default:e.withCtx(()=>[e.createTextVNode("自选商品套餐")]),_:1}),e.createVNode(A,{label:"FIXED_COMBINATION"},{default:e.withCtx(()=>[e.createTextVNode("固定套餐")]),_:1})]),_:1},8,["modelValue","disabled"]),ue]),_:1}),e.createVNode(p,{label:"主商品(限1种商品)",required:""},{default:e.withCtx(()=>[e.createVNode(k,{disabled:e.unref(u),plain:"",round:"",type:"primary",onClick:t[9]||(t[9]=a=>Z("main"))},{default:e.withCtx(()=>[e.createTextVNode(" 选择商品 ")]),_:1},8,["disabled"]),fe]),_:1})]),_:1},8,["model","rules"]),e.createVNode(X,{ref_key:"selectMainGoodsTableRef",ref:b,"flat-good-list":e.unref(f),"is-edit":e.unref(u),"product-list":e.unref(n),style:{"margin-bottom":"20px"}},null,8,["flat-good-list","is-edit","product-list"]),e.createVNode(p,{label:"搭配商品（限4种）",required:""},{default:e.withCtx(()=>[e.createVNode(k,{disabled:e.unref(u),plain:"",round:"",type:"primary",onClick:t[10]||(t[10]=a=>Z("matching"))},{default:e.withCtx(()=>[e.createTextVNode(" 选择商品 ")]),_:1},8,["disabled"]),he]),_:1}),e.createVNode(X,{ref_key:"selectMatchingGoodsTableRef",ref:_,"flat-good-list":e.unref(y),"is-edit":e.unref(u),"product-list":e.unref(m),"product-attributes":"MATCHING_PRODUCTS"},null,8,["flat-good-list","is-edit","product-list"]),e.createVNode(z,{justify:"center",style:{"margin-top":"60px"}},{default:e.withCtx(()=>[e.createVNode(k,{plain:"",round:"",onClick:t[11]||(t[11]=a=>e.unref(x).back())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.unref(u)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(k,{key:0,round:"",type:"primary",onClick:L},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1}))]),_:1})]),e.createVNode(F,{ref_key:"choosedMainGoodsRef",ref:D,modelValue:e.unref(c),"onUpdate:modelValue":t[12]||(t[12]=a=>e.isRef(c)?c.value=a:null),"search-param":S,"onUpdate:searchParam":t[13]||(t[13]=a=>S=a),quota:1,onOnConfirm:xe},null,8,["modelValue","search-param"]),e.createVNode(F,{ref_key:"choosedMatchingGoodsRef",ref:E,modelValue:e.unref(s),"onUpdate:modelValue":t[14]||(t[14]=a=>e.isRef(s)?s.value=a:null),"search-param":I,"onUpdate:searchParam":t[15]||(t[15]=a=>I=a),quota:4,onOnConfirm:_e},null,8,["modelValue","search-param"])])}}}),ke="";return K(ge,[["__scopeId","data-v-8eda12f8"]])});
