(function(e,m){typeof exports=="object"&&typeof module<"u"?module.exports=m(require("vue"),require("@/components/q-mapcity/q-mapcity.vue"),require("@/store/modules/shopInfo"),require("element-plus"),require("@/apis/http"),require("@/components/q-input-number/q-input-number.vue")):typeof define=="function"&&define.amd?define(["vue","@/components/q-mapcity/q-mapcity.vue","@/store/modules/shopInfo","element-plus","@/apis/http","@/components/q-input-number/q-input-number.vue"],m):(e=typeof globalThis<"u"?globalThis:e||self,e.CityDistribution=m(e.CityDistributionContext.Vue,e.CityDistributionContext.QMapCity,e.CityDistributionContext.ShopInfoStore,e.CityDistributionContext.ElementPlus,e.CityDistributionContext.Request,e.CityDistributionContext.QInputNumber))})(this,function(e,m,V,d,u,f){"use strict";var g=document.createElement("style");g.textContent=`.save[data-v-4093d65e]{background-color:#fff;height:60px;box-shadow:0 0 2px #00000012;display:flex;justify-content:center;align-items:center}.save__btn[data-v-4093d65e]{width:188px;height:32px;border-radius:16px;font-size:14px;color:#fff}.red-text[data-v-4093d65e]{color:red;font-size:14px;font-weight:700}.form-area[data-v-4093d65e]{width:50px;height:20px;border:1px solid #e2e2e2;border-radius:4px;text-align:center;line-height:20px}.form-control[data-v-4093d65e]{display:flex;align-items:center}.form-count[data-v-4093d65e]{width:20px;height:20px;border:1px solid #e2e2e2;border-radius:4px;text-align:center;line-height:20px;margin:0 5px}.ruleForm__name[data-v-4093d65e]{width:466px}.ruleForm__desc[data-v-4093d65e]{width:667px}.ruleForm__center[data-v-4093d65e]{width:1007px;height:31px;line-height:31px;background:#fcf8f8;font-size:13px;font-family:-apple-system,-apple-system-Bold;font-weight:700;text-align:LEFT;color:#0c0b0b;margin-bottom:22px}.ruleForm__item[data-v-4093d65e]{width:141px;height:30px;line-height:30px;display:flex;padding-right:0}.ruleForm__item[data-v-4093d65e] .el-input__wrapper{padding-right:0}.ruleForm__item--span[data-v-4093d65e]{display:block;width:38px;line-height:30px;color:#333;background:#d5d5d5;border-radius:0 4px 4px 0}.ruleForm__tip[data-v-4093d65e]{width:390px;height:20px;font-size:14px;color:#d0caca;line-height:20px;margin-left:25px}.rates[data-v-4093d65e]{padding-left:70px;display:flex;align-items:center;margin-bottom:20px}.rates__span[data-v-4093d65e]{padding:5px}.explain[data-v-4093d65e]{display:flex;flex-direction:column;padding-left:100px;font-size:13px;color:#bcbbbb;line-height:25px;margin-bottom:20px}.explain__span[data-v-4093d65e]{margin-left:20px}
`,document.head.appendChild(g);const h=e.computed(()=>i=>{var p,r;return console.log((p=window==null?void 0:window.permissionList)==null?void 0:p.includes(i),"是否包含"),(r=window==null?void 0:window.permissionList)==null?void 0:r.includes(i)}),b=i=>u.post({url:"addon-intra-city-distribution/intraCityDistribution/config/add/intraCityDistributionConfig",data:i}),N=()=>u.get({url:"addon-intra-city-distribution/intraCityDistribution/config/"}),w=i=>u.put({url:"addon-intra-city-distribution/intraCityDistribution/config/update/intraCityDistributionConfig",data:i}),E=i=>u.get({url:`gruul-mall-shop/shop/info/${i}`}),D={class:"main"},k={class:"form-control"},v={class:"form-area"},F={class:"rates"},I={class:"rates__span"},S={class:"ruleForm__item--span"},q={class:"ruleForm__item--span"},B={class:"rates__span"},R={class:"explain"},M={class:"explain__span"},T={class:"explain__span"},U={class:"explain__span"},z={key:1,style:{height:"50px"}},A={class:"save"},G=e.defineComponent({__name:"CityDistribution",setup(i){const p=V.useShopInfoStore(),r=e.ref({address:"",location:[]}),l=e.ref({isDefault:!1,id:"",name:"",deliveryRange:5,description:"",initialDeliveryCharge:0,deliveryCost:0,rates:"DISTANCE",ratesrule:"按距离收费",billingPrimacy:0,everyBilling:0,deliveryCostBilling:0,isDeliveryRelief:!1,orderMitigateCost:0,createTime:"",deleted:!1,updateTime:"",version:0}),c=e.reactive({deliveryRange:[{required:!0,message:"请输入配送范围",trigger:"change"}],name:[{required:!0,message:"请输入自定义名称",trigger:"blur"},{min:1,max:20,message:"长度在 1 到 20 个字符",trigger:"blur"}],initialDeliveryCharge:[{required:!0,message:"请输入起送金额",trigger:"change"}],deliveryCost:[{required:!0,message:"请输入基础配送费用",trigger:"change"}],rates:[{required:!0}]});e.onMounted(async()=>{await L(),await y()});const L=async()=>{const o=p.shopInfo.shopId,t=await E(o);t.code===200&&(r.value.address=t.data.address,r.value.location=t.data.location.coordinates)},_=o=>{if(l.value.deliveryRange===1&&o==="-"){d.ElMessage.warning("配送范围不能小于1km");return}if(l.value.deliveryRange===10&&o==="+"){d.ElMessage.warning("配送范围不能大于10km");return}o==="+"?l.value.deliveryRange++:l.value.deliveryRange--};e.watchEffect(()=>{});const x=e.ref(),j=async o=>{l.value.ratesrule==="按距离收费"?l.value.rates="DISTANCE":l.value.rates="WEIGHT",o&&await o.validate(async(t,s)=>{t?l.value.id===""?(await O(l.value),y()):H(l.value):(console.log("error submit!",s),d.ElMessage.error(s.name[0].message))})},O=async o=>{if((await b(o)).code===200)return d.ElMessage.success("保存成功");d.ElMessage.error(o.msg||"保存失败")},y=async()=>{const o=await N();o.code===200&&(l.value=o.data,l.value.rates==="DISTANCE"?l.value.ratesrule="按距离收费":l.value.ratesrule="按重量收费")},H=async o=>{const t=await w(o);if(t.code===200)return d.ElMessage.success("编辑成功");d.ElMessage.error(t.msg||"编辑失败")};return(o,t)=>{const s=e.resolveComponent("el-input"),n=e.resolveComponent("el-form-item"),C=e.resolveComponent("el-radio"),K=e.resolveComponent("el-radio-group"),Q=e.resolveComponent("el-switch"),W=e.resolveComponent("el-form"),$=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",D,[e.createVNode(W,{ref_key:"ruleFormRef",ref:x,model:l.value,rules:c,class:"ruleForm","label-position":"left","label-width":"100px",size:"default"},{default:e.withCtx(()=>[e.createVNode(n,{label:"自定义名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:l.value.name,"onUpdate:modelValue":t[0]||(t[0]=a=>l.value.name=a),placeholder:"限20字以内",class:"ruleForm__name",maxlength:"20"},null,8,["modelValue"])]),_:1}),e.createVNode(n,{label:"店铺定位地址"},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r.value.address),1)]),_:1}),e.createVNode(n,{label:"配送范围",prop:"deliveryRange"},{default:e.withCtx(()=>[e.createElementVNode("div",k,[e.createElementVNode("div",{class:"form-count",onClick:t[1]||(t[1]=()=>_("-"))},"-"),e.createElementVNode("div",v,e.toDisplayString(l.value.deliveryRange+"km"),1),e.createElementVNode("div",{class:"form-count",onClick:t[2]||(t[2]=()=>_("+"))},"+")])]),_:1}),e.createVNode(n,null,{default:e.withCtx(()=>[(e.openBlock(),e.createBlock(e.KeepAlive,null,[e.createVNode(m,{area:l.value.deliveryRange,coordinates:r.value.location},null,8,["area","coordinates"])],1024))]),_:1}),e.createVNode(n,{label:"配送说明",prop:"description"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:l.value.description,"onUpdate:modelValue":t[3]||(t[3]=a=>l.value.description=a),placeholder:"限100字以内",rows:4,class:"ruleForm__desc",maxlength:"100",resize:"none",type:"textarea"},null,8,["modelValue"])]),_:1}),t[23]||(t[23]=e.createElementVNode("div",{class:"ruleForm__center"},"配送费用设置",-1)),e.createVNode(n,{label:"起送金额",prop:"initialDeliveryCharge"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:l.value.initialDeliveryCharge,"onUpdate:modelValue":t[4]||(t[4]=a=>l.value.initialDeliveryCharge=a),modelModifiers:{number:!0},class:"ruleForm__item",maxlength:"6"},{suffix:e.withCtx(()=>t[13]||(t[13]=[e.createElementVNode("span",{class:"ruleForm__item--span"},"元",-1)])),_:1},8,["modelValue"]),t[14]||(t[14]=e.createElementVNode("div",{class:"ruleForm__tip"},"订单实付金额要大于等于该数值才能满足同城配送条件",-1))]),_:1}),e.createVNode(n,{label:"基础配送费",prop:"deliveryCost"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:l.value.deliveryCost,"onUpdate:modelValue":t[5]||(t[5]=a=>l.value.deliveryCost=a),modelModifiers:{number:!0},class:"ruleForm__item",maxlength:"6"},{suffix:e.withCtx(()=>t[15]||(t[15]=[e.createElementVNode("span",{class:"ruleForm__item--span"},"元",-1)])),_:1},8,["modelValue"])]),_:1}),e.createVNode(n,{label:"计费方式",prop:"rates"},{default:e.withCtx(()=>[e.createVNode(K,{modelValue:l.value.ratesrule,"onUpdate:modelValue":t[6]||(t[6]=a=>l.value.ratesrule=a)},{default:e.withCtx(()=>[e.createVNode(C,{label:"按距离收费"},null,8,["label"]),e.createVNode(C,{label:"按重量收费"},null,8,["label"])]),_:1},8,["modelValue"])]),_:1}),e.createElementVNode("div",F,[e.createElementVNode("span",I,e.toDisplayString(l.value.ratesrule==="按距离收费"?"按距离收费":"按重量收费"),1),e.createVNode(s,{modelValue:l.value.billingPrimacy,"onUpdate:modelValue":t[7]||(t[7]=a=>l.value.billingPrimacy=a),class:"ruleForm__item",maxlength:"6"},{suffix:e.withCtx(()=>[e.createElementVNode("span",S,e.toDisplayString(l.value.ratesrule==="按距离收费"?"km":"kg"),1)]),_:1},8,["modelValue"]),t[17]||(t[17]=e.createElementVNode("span",{class:"rates__span"}," 内不额外收费，每超出",-1)),e.createVNode(f,{modelValue:l.value.everyBilling,"onUpdate:modelValue":t[8]||(t[8]=a=>l.value.everyBilling=a),controls:!1,min:1,class:"ruleForm__item"},{append:e.withCtx(()=>[e.createElementVNode("span",q,e.toDisplayString(l.value.ratesrule==="按距离收费"?"km":"kg"),1)]),_:1},8,["modelValue"]),e.createElementVNode("span",B,e.toDisplayString(l.value.ratesrule==="按距离收费"?"，配送费增加":"，续重费增加"),1),e.createVNode(s,{modelValue:l.value.deliveryCostBilling,"onUpdate:modelValue":t[9]||(t[9]=a=>l.value.deliveryCostBilling=a),class:"ruleForm__item",maxlength:"6"},{suffix:e.withCtx(()=>t[16]||(t[16]=[e.createElementVNode("span",{class:"ruleForm__item--span"},"元",-1)])),_:1},8,["modelValue"])]),e.createElementVNode("div",R,[e.createElementVNode("span",null,e.toDisplayString(l.value.ratesrule==="按距离收费"?"距离收费说明：":"计重收费说明："),1),e.createElementVNode("span",M,e.toDisplayString(l.value.ratesrule==="按距离收费"?"1. 配送费 = 基础配送费 + 超距收费 （小于配送费减免金额）":"1. 配送费 = 基础配送费 + 续重收费 （小于配送费减免金额）"),1),e.createElementVNode("span",T,e.toDisplayString(l.value.ratesrule==="按距离收费"?"2. 以店铺定位地址为中心，因考虑实际送货路况，配送费计算按骑行距离，非地图直线距离 ":" 2. 计重收费需设置商品重量"),1),e.createElementVNode("span",U,e.toDisplayString(l.value.ratesrule==="按距离收费"?"3.不足 1 km 按1 km计算":"3.不足 1 kg 按1 kg计算"),1)]),e.createVNode(n,{label:"配送费减免",prop:"isDeliveryRelief"},{default:e.withCtx(()=>[t[18]||(t[18]=e.createElementVNode("span",{style:{"padding-right":"10px"}},"是否开启",-1)),e.createVNode(Q,{modelValue:l.value.isDeliveryRelief,"onUpdate:modelValue":t[10]||(t[10]=a=>l.value.isDeliveryRelief=a)},null,8,["modelValue"])]),_:1}),l.value.isDeliveryRelief?(e.openBlock(),e.createBlock(n,{key:0,prop:"orderMitigateCost"},{default:e.withCtx(()=>[t[20]||(t[20]=e.createElementVNode("span",null,"订单实付金额大于等于",-1)),e.createVNode(f,{modelValue:l.value.orderMitigateCost,"onUpdate:modelValue":t[11]||(t[11]=a=>l.value.orderMitigateCost=a),controls:!1,max:999999,class:"ruleForm__item"},{append:e.withCtx(()=>t[19]||(t[19]=[e.createElementVNode("span",{class:"ruleForm__item--span"},"元",-1)])),_:1},8,["modelValue"]),t[21]||(t[21]=e.createElementVNode("span",null,"时，",-1)),t[22]||(t[22]=e.createElementVNode("span",{class:"red-text"},"减免全部配送费",-1))]),_:1})):(e.openBlock(),e.createElementBlock("div",z))]),_:1},8,["model","rules"]),e.createElementVNode("div",A,[e.unref(h)("intra:city:setting")?(e.openBlock(),e.createBlock($,{key:0,class:"save__btn",type:"primary",onClick:t[12]||(t[12]=()=>j(x.value))},{default:e.withCtx(()=>t[24]||(t[24]=[e.createTextVNode("保存")])),_:1})):e.createCommentVNode("",!0)])])}}}),J="";return((i,p)=>{const r=i.__vccOpts||i;for(const[l,c]of p)r[l]=c;return r})(G,[["__scopeId","data-v-4093d65e"]])});
