(function(e,R){typeof exports=="object"&&typeof module<"u"?module.exports=R(require("vue"),require("@/components/MCard.vue"),require("lodash"),require("@element-plus/icons-vue"),require("@/composables/useConvert"),require("vue-clipboard3"),require("element-plus"),require("@/components/PageManage.vue"),require("@/utils/date"),require("@/components/remark/remark-popup.vue"),require("decimal.js"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/components/MCard.vue","lodash","@element-plus/icons-vue","@/composables/useConvert","vue-clipboard3","element-plus","@/components/PageManage.vue","@/utils/date","@/components/remark/remark-popup.vue","decimal.js","@/apis/http"],R):(e=typeof globalThis<"u"?globalThis:e||self,e.Rebate=R(e.RebateContext.Vue,e.RebateContext.MCard,e.RebateContext.Lodash,e.RebateContext.ElementPlusIconsVue,e.RebateContext.UseConvert,e.RebateContext.VueClipboard3,e.RebateContext.ElementPlus,e.RebateContext.PageManageTwo,e.RebateContext.DateUtil,e.RebateContext.RemarkPopup,e.RebateContext.Decimal,e.RebateContext.Request))})(this,function(e,R,K,U,I,te,M,ae,oe,ne,le,H){"use strict";var Y=document.createElement("style");Y.textContent=`@charset "UTF-8";.rebate-data[data-v-cee3199c]{padding:20px;display:flex;justify-content:center;align-items:center;justify-content:space-between}.rebate-data__left[data-v-cee3199c]{display:flex;justify-content:center;align-items:center}.rebate-data__item[data-v-cee3199c]{display:flex;justify-content:center;align-items:center;margin-right:30px}.rebate-data__item--price[data-v-cee3199c]{font-weight:600;font-size:16px}.rebate-data__item--price[data-v-cee3199c]:before{content:"￥";font-size:12px;font-weight:400}.o_table{width:100%;table-layout:fixed;border-collapse:separate;font-size:12px}.o_table .hide{display:none}.o_table--container{background:#eef1f6;padding:11px 8px;position:relative}.o_table--container.single{background:none}.o_table--container.single .o_table--head th:first-child{border-radius:10px 0 0}.o_table--container.single .o_table--head th:last-child{border-radius:0 10px 0 0}.o_table--container.single .o_table--head:after{display:none}.o_table--shrink{flex:1}.o_table--center{display:flex;align-items:center}.o_table .close{background:#f5f5f5!important}.o_table .hover--class:hover .body--header{border-color:#bebebe!important;position:relative}.o_table .hover--class:hover .body--content td,.o_table .hover--class:hover .body--content td:first-child{border-color:#bebebe!important}.o_table .hover--class:hover .body--content td:last-child{border-color:#bebebe!important}.o_table .ordinary--class:hover .body--header,.o_table .ordinary--class:hover .body--content td{border-color:#bcdfff}.o_table .ordinary--class:hover .body--content td:last-child{border-color:#bcdfff}.o_table .need--border .body--content td{border-right:1px solid #bcdfff}.o_table .need--border .body--content td:last-child{border-color:#bcdfff}.o_table--empty .empty__td{width:960px;height:80px;background-color:#fff;margin-left:-15px;font-size:14px;color:#b3b3b3}.o_table--head th{background:#fff;padding:10px;text-align:center;color:#515a6e;font-weight:200;border-top:1px solid #d8eaf9;border-bottom:1px solid #d8eaf9;height:50px;vertical-align:middle;font-size:14px;font-weight:400;color:#586884}.o_table--head th:first-child{border-left:1px solid #d8eaf9}.o_table--head th:last-child{border-right:1px solid #d8eaf9}.o_table--head:after{content:"-";display:block;line-height:14px;color:transparent}.o_table--head.padding:after{content:"-";display:block;color:transparent}.o_table--body .body--header{display:flex;justify-content:flex-start;align-items:center;padding:0 10px;border:1px solid #d8eaf9;font-size:13px;border-radius:10px 10px 0 0;height:55px;vertical-align:middle;background:#fff}.o_table--body.default .body--content .o_table--item:first-child{border-radius:0}.o_table--body.default .body--content .o_table--item:last-child{border-radius:0}.o_table--body.default .o_table--item{border-top:1px solid #d8eaf9;vertical-align:middle;border-right:1px solid #d8eaf9}.o_table--body .body--content td{padding:8px 10px;border-top:0px;border-bottom:1px solid #d8eaf9;border-right:0px;font-size:12px;color:#50596d;background:#fff;vertical-align:middle}.o_table--body .body--content td .item__content{display:flex;justify-content:center;align-items:center}.o_table--body .body--content td .selection__checkbox{display:inline-block;width:100%;height:100%}.o_table--body .body--content td .selection__checkbox.selection{display:flex;justify-content:flex-start;align-items:center}.o_table--body .body--content td:first-child{border-left:1px solid #d8eaf9}.o_table--body .body--content td:last-child{border-right:1px solid #d8eaf9}.o_table--body .body--content.is--multiple td:first-child{border-right:1px solid #d8eaf9!important}.o_table--body:after{content:"-";display:block;line-height:14px;color:transparent;width:100%}.o_table .el-checkbox{margin-right:10px!important;float:left}.goods__content[data-v-dee8cc69]{display:flex;justify-content:center;align-items:center}.shop-name[data-v-dee8cc69]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.orderIndex-table[data-v-dee8cc69]{position:relative;margin-top:10px;overflow-x:auto;height:calc(100vh - 620px);transition:height .5s}.orderIndex-table__top[data-v-dee8cc69]{display:flex;justify-content:space-between;align-items:center;width:100%}.orderIndex-table__top-time[data-v-dee8cc69]{display:flex;justify-content:center;align-items:center}.orderIndex-table__top-time>div[data-v-dee8cc69]:nth-child(2){padding:0 60px}.orderIndex-table__img-box[data-v-dee8cc69]{width:100%;display:flex;justify-content:space-between}.orderIndex-table__img[data-v-dee8cc69]{flex-shrink:0;border-radius:5px;position:relative}.orderIndex-table__img-mask[data-v-dee8cc69]{display:flex;flex-direction:column;align-items:center;font-size:12px;color:#000}.caozuo_btn[data-v-dee8cc69]:hover{color:#fff;background:#309af3!important}.money_text[data-v-dee8cc69]{font-size:12px;color:#000;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.avatar_text[data-v-dee8cc69]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.avatar_text__bottom[data-v-dee8cc69]{margin-bottom:5px}.el-icon--right-top[data-v-dee8cc69]{margin-left:5px}.el-icon--right[data-v-dee8cc69]{padding-top:-10px;position:absolute;left:-20px;opacity:0}.tableUp[data-v-dee8cc69]{height:calc(100vh - 410px)}.header-table[data-v-dee8cc69]{width:100%;display:flex;justify-content:space-between;align-items:center}.order-info[data-v-dee8cc69]{flex:1;margin:0 8px;word-break:break-all;line-height:1.5;overflow:hidden;width:210px}.order-info__name[data-v-dee8cc69],.order-info__spec[data-v-dee8cc69]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.customer[data-v-dee8cc69]{text-align:left;width:100%;line-height:1.3}.customer__copy[data-v-dee8cc69]{text-align:right;margin-bottom:8px}.copy[data-v-dee8cc69]{color:#1890ff;margin-left:8px;cursor:pointer}.example-showcase .el-dropdown-link[data-v-dee8cc69]{cursor:pointer;color:var(--el-color-primary);display:flex;align-items:center}.el-popper.is-customized[data-v-dee8cc69]{padding:6px 12px;background:linear-gradient(90deg,rgb(159,229,151),rgb(204,229,129))}.el-popper.is-customized .el-popper__arrow[data-v-dee8cc69]:before{background:linear-gradient(45deg,#b2e68d,#bce689);right:0}.submit[data-v-6c3451fb]{position:fixed;bottom:40px;display:flex;justify-content:center;align-items:center;margin-left:350px}
`,document.head.appendChild(Y);const re=e.defineComponent({__name:"Rebate",setup(t){const o=[{label:"消费返利订单",name:"REBATE_ORDER"},{label:"消费返利设置",name:"REBATE_SET"}],n=e.ref("REBATE_ORDER"),c={REBATE_ORDER:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Ye)),REBATE_SET:e.defineAsyncComponent(()=>Promise.resolve().then(()=>nt))};return(p,x)=>{const r=e.resolveComponent("el-tab-pane"),l=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(l,{modelValue:n.value,"onUpdate:modelValue":x[0]||(x[0]=m=>n.value=m)},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(o,m=>e.createVNode(r,{key:m.label,label:m.label,name:m.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(c[n.value])))])}}}),de={style:{background:"#f9f9f9"}},ce=e.defineComponent({__name:"search",emits:["getSearchParams","showChange"],setup(t,{emit:o}){const n=e.reactive({orderNo:"",productName:"",shopName:"",buyerNickname:"",clinchTime:""}),c=e.ref(),p=e.ref(!1),x=()=>{var l,m;const r=K.cloneDeep(n);r.clinchTime&&Array.isArray(r.clinchTime)&&(r.orderCreateTime=(l=r.clinchTime)==null?void 0:l[0],r.orderEndTime=(m=r.clinchTime)==null?void 0:m[1]),delete r.clinchTime,o("getSearchParams",r)};return e.watch(()=>p.value,r=>{o("showChange",r)}),(r,l)=>{const m=e.resolveComponent("el-input"),i=e.resolveComponent("el-form-item"),u=e.resolveComponent("el-col"),y=e.resolveComponent("el-date-picker"),d=e.resolveComponent("el-row"),f=e.resolveComponent("el-button"),V=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",de,[e.createVNode(R,{modelValue:p.value,"onUpdate:modelValue":l[5]||(l[5]=h=>p.value=h)},{default:e.withCtx(()=>[e.createVNode(V,{ref_key:"ruleForm",ref:c,model:n},{default:e.withCtx(()=>[e.createVNode(d,{gutter:20},{default:e.withCtx(()=>[e.createVNode(u,{span:8},{default:e.withCtx(()=>[e.createVNode(i,{label:"订单号",prop:"orderNo","label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:n.orderNo,"onUpdate:modelValue":l[0]||(l[0]=h=>n.orderNo=h),placeholder:"请填写订单号",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(u,{span:8},{default:e.withCtx(()=>[e.createVNode(i,{label:"商品名称",prop:"productName","label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:n.productName,"onUpdate:modelValue":l[1]||(l[1]=h=>n.productName=h),placeholder:"请填写商品名称",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(u,{span:8},{default:e.withCtx(()=>[e.createVNode(i,{label:"店铺名称",prop:"shopName","label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:n.shopName,"onUpdate:modelValue":l[2]||(l[2]=h=>n.shopName=h),placeholder:"请填写店铺名称",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(u,{span:8},{default:e.withCtx(()=>[e.createVNode(i,{label:"买家昵称",prop:"buyerNickname","label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:n.buyerNickname,"onUpdate:modelValue":l[3]||(l[3]=h=>n.buyerNickname=h),placeholder:"请填写买家昵称",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(u,{span:8},{default:e.withCtx(()=>[e.createVNode(i,{label:"下单时间","label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:n.clinchTime,"onUpdate:modelValue":l[4]||(l[4]=h=>n.clinchTime=h),format:"YYYY/MM/DD","value-format":"YYYY-MM-DD",type:"daterange","start-placeholder":"开始时间","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(d,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(u,{span:8},{default:e.withCtx(()=>[e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(f,{type:"primary",round:"",onClick:x},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),j=t=>(e.pushScopeId("data-v-cee3199c"),t=t(),e.popScopeId(),t),ie={class:"rebate-data"},se={class:"rebate-data__left"},me={class:"rebate-data__item"},pe=j(()=>e.createElementVNode("h5",null,"累计总返利：",-1)),be={class:"rebate-data__item--price",style:{color:"#000"}},_e={class:"rebate-data__item"},fe=j(()=>e.createElementVNode("h5",null,"待结算总返利：",-1)),he={class:"rebate-data__item--price",style:{color:"red"}},ye={class:"rebate-data__item"},ge=j(()=>e.createElementVNode("h5",null,"已失效总返利：",-1)),xe={class:"rebate-data__item--price",style:{color:"#ccc"}},ue=j(()=>e.createElementVNode("div",{class:"demo-rich-conent",style:{display:"flex",gap:"16px","flex-direction":"column"}},[e.createElementVNode("div",{class:"title"},"消费返利订单说明"),e.createElementVNode("div",null,[e.createElementVNode("p",{class:"demo-rich-content__name",style:{margin:"0","font-weight":"500"}}," 统计开启消费返利期间整个平台（积分订单除外）的所有订单，消费返利以商品为统计粒度计算。 "),e.createElementVNode("h5",{style:{"margin-top":"10px"}},"订单状态"),e.createElementVNode("div",null,"1、已付款：是指支付成功，订单处于待发货或物流配送中"),e.createElementVNode("div",null,"2、已完成：是指商品客户已确认收货未退款或部分退款"),e.createElementVNode("div",null,"3、已关闭：是指订单内商品【退款成功】"),e.createElementVNode("h5",{style:{"margin-top":"10px"}},"结算状态"),e.createElementVNode("div",null,"1、待结算：是指订单未完结(可能退款退货等情况)暂不计入累计返利；"),e.createElementVNode("div",null,"2、已返：是指对应商品没有退款(或退款失败)，返利统计到【累计返利】中"),e.createElementVNode("div",null,"3、已失效：是指对应商品已退款成功，您无法获得对应返利"),e.createElementVNode("h5",{style:{"margin-top":"10px"}},"返利统计维度"),e.createElementVNode("div",null,"1、累计返利：已返的返利之和"),e.createElementVNode("div",null,"2、待结算返利：待结算返利之和"),e.createElementVNode("div",null,"3、已失效返利：已失效返利之和")])],-1)),Ne=e.defineComponent({__name:"rebate-data",props:{rebateDetailStatistic:{default:()=>({totalExpired:"",totalPendingSettlement:"",totalRebate:""})}},setup(t){const o=t,{divTenThousand:n}=I();return(c,p)=>{const x=e.resolveComponent("el-icon"),r=e.resolveComponent("el-popover");return e.openBlock(),e.createElementBlock("div",ie,[e.createElementVNode("div",se,[e.createElementVNode("div",me,[pe,e.createElementVNode("span",be,e.toDisplayString(e.unref(n)(o.rebateDetailStatistic.totalRebate)),1)]),e.createElementVNode("div",_e,[fe,e.createElementVNode("span",he,e.toDisplayString(e.unref(n)(o.rebateDetailStatistic.totalPendingSettlement)),1)]),e.createElementVNode("div",ye,[ge,e.createElementVNode("span",xe,e.toDisplayString(e.unref(n)(o.rebateDetailStatistic.totalExpired)),1)])]),e.createElementVNode("div",null,[e.createVNode(r,{placement:"top-start",title:"",width:400,trigger:"hover"},{reference:e.withCtx(()=>[e.createVNode(x,null,{default:e.withCtx(()=>[e.createVNode(e.unref(U.QuestionFilled))]),_:1})]),default:e.withCtx(()=>[ue]),_:1})])])}}}),ut="",L=(t,o)=>{const n=t.__vccOpts||t;for(const[c,p]of o)n[c]=p;return n},Ve=L(Ne,[["__scopeId","data-v-cee3199c"]]),Ce=()=>({data:{type:Array,default(){return[]}},selection:{type:Boolean,default:!1},custom:{type:Boolean,default:!1},needBorder:{type:Boolean,default:!1},columns:{type:Array,default(){return[]}},tableHeaderClass:{type:String,default:""},rowHeaderClass:{type:String,default:""},needHoverBorder:{type:Boolean,default:!1},multipleKey:{type:String,default:""}}),Ee=e.defineComponent({props:Ce(),emits:["check"],setup(t,{slots:o,emit:n}){const c=e.ref([]),p=e.computed({get:()=>!!(r.value.length&&r.value.every(d=>d.checked)),set:d=>{i(d),n("check",u())}}),x=e.computed(()=>{const d=r.value.filter(f=>f.checked).length;return d!==0&&d!==r.value.length}),r=e.computed(()=>{const d=c.value;return d?l(d):[]}),l=d=>d?(d.forEach(f=>{const V=f,h=V.rebateOrderItems;V.packageMap=h.reduce((E,_)=>{var N;const C=`${_.productId}-${_.skuId}`;return E.has(C)?(N=E.get(C))==null||N.push(_):E.set(C,[_]),E},new Map)}),d):[];e.watch(t,d=>{c.value=d.data},{immediate:!0});const m=(d,f)=>{Object.assign(d.props?d.props:{},f)};function i(d){r.value=r.value.map(f=>(f.checked=d,f))}function u(){return r.value.filter(d=>d.checked)}const y=(d,f)=>{const V=[],h=d.packageMap;let E=!0;return h.forEach((_,C)=>{V.push(e.createVNode("tr",{class:"body--content"},[o.default&&o.default().map((N,B)=>{var w;m(N,{row:d,packageId:C,shopOrderItems:_});const S=(w=N.props)==null?void 0:w["is-mixed"];if(S&&E||!S)return e.createVNode("td",{class:["o_table--item",!N&&"hide"],rowspan:S?h.size:void 0},[e.createVNode("div",{class:["selection__checkbox",t.selection&&B===0&&"selection"]},[e.createVNode("div",{class:["o_table--shrink"]},[N])])])})])),E=!1}),V};return()=>e.createVNode("table",{class:["o_table"],cellpadding:"0",cellspacing:"0"},[e.createVNode("colgroup",null,[t.columns.map(d=>e.createVNode("col",{width:d.width},null))]),e.createVNode("thead",{class:["o_table--head",t.tableHeaderClass,o.header&&"padding"]},[e.createVNode("tr",{class:"m__tr"},[t.columns.map((d,f)=>e.createVNode("th",{style:d.customStyle,class:f===0&&t.selection&&["o_table--center"]},[f===0&&t.selection&&e.createVNode(e.resolveComponent("el-checkbox"),{indeterminate:x.value,modelValue:p.value,"onUpdate:modelValue":V=>p.value=V,onChange:i.bind(this,p.value)},null),e.createVNode("div",{class:["o_table--shrink"]},[d.label])]))])]),r.value.length?r.value.map((d,f)=>e.createVNode("tbody",{class:["o_table--body",t.custom?"custom":"default",t.needBorder&&"need--border",t.needHoverBorder&&d.close?"hover--class":"ordinary--class"]},[o.header&&e.createVNode("tr",null,[e.createVNode("td",{colspan:t.columns.length},[e.createVNode("div",{class:["body--header",t.rowHeaderClass,{close:d.close}]},[o.header({row:d,index:f})])])]),y(d)])):e.createVNode("tbody",{class:"o_table--empty"},[e.createVNode("tr",null,[e.createVNode("td",{class:"empty__td",colspan:t.columns.length,align:"center"},[e.createTextVNode("暂无数据~")])])])])}}),Nt="",we=()=>({data:{type:Array,default(){return[]}},selection:{type:Boolean,default:!1},custom:{type:Boolean,default:!1},needBorder:{type:Boolean,default:!1},columns:{type:Array,default(){return[]}},tableHeaderClass:{type:String,default:""},rowHeaderClass:{type:String,default:""},needHoverBorder:{type:Boolean,default:!1},multipleKey:{type:String,default:""}}),ke=e.defineComponent({props:we(),emits:["update:checkedItem"],setup(t,{slots:o,emit:n}){const c=e.ref(),p=e.ref(),x=()=>t.columns.length>0&&t.columns||o.default&&o.default().map(m=>{const i=m.props;return{label:i==null?void 0:i.label,width:i==null?void 0:i.width,prop:i==null?void 0:i.prop,customStyle:i==null?void 0:i.customStyle,isMixed:(i==null?void 0:i.isMixed)||!1}}),r=m=>{n("update:checkedItem",m)},l=()=>{const m={default:()=>o.default&&o.default()};if(!o.default&&!t.columns.length&&!o.custom)throw new Error("请传入MTableColumn");return o.header&&(m.header=i=>o.header&&o.header(i)),m};return e.watch(t,m=>{p.value=K.cloneDeep(m.data)},{immediate:!0}),()=>e.createVNode("div",{class:["o_table--container"]},[e.createVNode(Ee,{onCheck:r,columns:x(),data:p.value,custom:t.custom,selection:t.selection,tableHeaderClass:t.tableHeaderClass,rowHeaderClass:t.rowHeaderClass,needHoverBorder:t.needHoverBorder,multipleKey:t.multipleKey,needBorder:t.needBorder,ref:c},l())])}}),D=e.defineComponent({__name:"split-table-column",props:{prop:{type:String,default:""},align:{type:String,default:"center"},row:{type:Object,default(){return{}}},packageId:{type:String,default(){}},shopOrderItems:{type:Object,default(){return[]}}},setup(t){const o=t,n=c=>{switch(c){case"left":return"flex-start";case"right":return"flex-end";default:return"center"}};return(c,p)=>(e.openBlock(),e.createElementBlock("div",{class:"item__content",style:e.normalizeStyle({justifyContent:n(o.align)})},[e.renderSlot(c.$slots,"default",{row:o.row,shopOrderItems:o.shopOrderItems})],4))}}),Se=()=>H.get({url:"addon-rebate/rebateConf"}),Te=t=>H.put({url:"addon-rebate/rebateConf",data:t}),Re=t=>H.get({url:"addon-rebate/rebateOrder",params:t});var W=(t=>(t.UNPAID="待付款",t.PAID="已付款",t.COMPLETED="已完成",t.CLOSED="已关闭",t))(W||{});const Me=["onClick"],De=["onClick"],Be={class:"orderIndex-table__img-box"},Pe={class:"order-info"},Ae={class:"order-info__name"},Ue={class:"order-info__spec"},je={class:"orderIndex-table__img-mask"},$e={style:{color:"#838383","font-size":"10px"}},ze={class:"delivery"},Fe={key:0},Ie={key:1},He={key:2},Le={key:0},qe={key:1},Oe={key:2},Ge=e.defineComponent({__name:"rebate-list",props:{searchRetract:{type:Boolean,default:!1},searchForm:{type:Object,default:()=>({})},statistic:{type:Object,default:()=>({})}},emits:["update:searchForm","update:statistic"],setup(t,{emit:o}){const n=t,c=["近三个月订单","近一个月订单","全部订单"],{toClipboard:p}=te(),x=e.computed({get(){return n.searchForm},set(s){o("update:searchForm",s)}}),r=e.computed({get(){return n.statistic},set(s){o("update:statistic",s)}});e.watch(()=>{var s;return(s=n.searchForm)==null?void 0:s.forceSearch},s=>{s&&(x.value.forceSearch=!1,w())});const{divTenThousand:l}=I(),m=e.ref("全部订单"),i=[],u=e.ref(),y=e.ref(!1),d=e.ref([]),f=e.ref(" "),V=e.ref([]),h=e.ref(!1),E=e.ref([]),_=e.ref([]),C=e.ref(""),N=e.reactive({size:20,current:1,total:0}),B=new oe,S=e.reactive({params:{buyerNickname:"",receiverName:"",orderNo:"",productName:"",startTime:"",endTime:""}});w(),lt(),rt();function w(){e.nextTick(()=>{const s={...N,...n.searchForm,forceSearch:void 0,status:f.value.trim()};m.value==="近一个月订单"?s.monthOrders="ONE_MONTH_AGO":m.value==="近三个月订单"&&(s.monthOrders="THREE_MONTHS_AGO"),Re(s).then(({data:b})=>{const k=b==null?void 0:b.statistic;Object.keys(r.value).forEach(P=>{const F=P;r.value[F]=(k==null?void 0:k[F])||""}),N.current=b.current,N.size=b.size,N.total=b.total,E.value=b.records})})}function lt(){const s={UNPAID:"待付款",PAID:"已付款",COMPLETED:"已完成",CLOSED:"已关闭"};for(const[b,k]of Object.entries(s))i.push({key:b,value:k})}function rt(){const s=i.map(b=>({title:b.value,name:b.key}));s.unshift({title:"全部订单",name:" "}),d.value=s}const Z=s=>{m.value=s,dt()};function dt(){if(m.value==="近一个月订单"){const s=B.getLastMonth();v(s)}else if(m.value==="近三个月订单"){const s=B.getLastThreeMonth();v(s)}else S.params.startTime="",S.params.endTime="",w()}const v=async s=>{const b=B.getYMDs();S.params.startTime=s,S.params.endTime=b,w()},ct=async()=>{if(f.value!==" "){S.params.endTime="",S.params.startTime="",w();return}else Z(m.value)},it=()=>{y.value=!y.value,y.value?u.value.handleOpen():u.value.handleClose()},st=e.computed(()=>s=>l(s[0].salePrice)),mt=e.computed(()=>s=>s.reduce((b,k)=>k.num+b,0)),pt=s=>{N.size=s,w()},bt=s=>{N.current=s,w()},_t=()=>{_.value=[],w()},ft=async s=>{try{await p(s),M.ElMessage.success("复制成功")}catch{M.ElMessage.error("复制失败")}},O=(s,b)=>!!s.find(P=>P.settlementStatus===b);return(s,b)=>{const k=e.resolveComponent("el-icon"),P=e.resolveComponent("el-dropdown-item"),F=e.resolveComponent("el-dropdown-menu"),ht=e.resolveComponent("el-dropdown"),ee=e.resolveComponent("el-tab-pane"),yt=e.resolveComponent("el-tabs"),gt=e.resolveComponent("el-image"),xt=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(yt,{modelValue:f.value,"onUpdate:modelValue":b[0]||(b[0]=a=>f.value=a),style:{"margin-top":"15px"},onTabChange:ct},{default:e.withCtx(()=>[e.createVNode(ee,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(m.value),1),e.createVNode(k,{class:"el-icon--right-top"},{default:e.withCtx(()=>[e.createVNode(e.unref(U.ArrowDown))]),_:1}),e.createVNode(ht,{ref_key:"dropdownRef",ref:u,placement:"bottom-end",trigger:"click",onCommand:Z},{dropdown:e.withCtx(()=>[e.createVNode(F,null,{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(c,a=>e.createVNode(P,{key:a,command:a},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(a),1)]),_:2},1032,["command"])),64))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(it,["stop"])},[e.createVNode(k,{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(e.unref(U.ArrowDown))]),_:1})],8,Me)]),_:1},512)]),_:1}),(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(i,a=>e.createVNode(ee,{key:a.key,label:a.value,name:a.key},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),e.createVNode(e.unref(ke),{checkedItem:_.value,"onUpdate:checkedItem":b[1]||(b[1]=a=>_.value=a),class:e.normalizeClass(["orderIndex-table",{tableUp:!t.searchRetract}]),style:{"margin-top":"13px"},data:E.value,selection:!0},{header:e.withCtx(({row:a})=>[e.createElementVNode("div",{class:e.normalizeClass(["header-table",{"is-complete":a.status==="CLOSED"}])},[e.createElementVNode("div",null,[e.createElementVNode("span",null,"订单号:"+e.toDisplayString(a.orderNo),1),e.createElementVNode("span",{class:"copy",onClick:g=>ft(a.orderNo)},"复制",8,De)]),e.createElementVNode("div",null,"下单时间:"+e.toDisplayString(a.orderTime),1),e.createElementVNode("div",null,"买家："+e.toDisplayString(a.buyerNickname),1),e.createElementVNode("div",null,"实付款："+e.toDisplayString(a.rebateOrderItems.reduce((g,T)=>g.plus(e.unref(l)(T.salePrice)),new e.unref(le)(0))),1),e.createElementVNode("div",null,"所属店铺："+e.toDisplayString(a.shopName),1),e.createElementVNode("div",null,e.toDisplayString(e.unref(W)[a.status]),1)],2)]),default:e.withCtx(()=>[e.createVNode(D,{prop:"name",label:"商品",width:"350px"},{default:e.withCtx(({shopOrderItems:a})=>{var g,T,A;return[e.createElementVNode("div",Be,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.slice(0,1),G=>(e.openBlock(),e.createBlock(gt,{key:G.skuId,fits:"cover",style:{width:"63px",height:"63px"},shape:"square",size:"large",src:G.image,title:G.productName},null,8,["src","title"]))),128)),e.createElementVNode("span",Pe,[e.createElementVNode("p",Ae,e.toDisplayString((g=a==null?void 0:a[0])==null?void 0:g.productName),1),e.createElementVNode("p",Ue,e.toDisplayString((A=(T=a==null?void 0:a[0])==null?void 0:T.specs)==null?void 0:A.join(",")),1)]),e.createElementVNode("div",je,[e.createElementVNode("span",null,"￥"+e.toDisplayString(st.value(a)),1),e.createElementVNode("span",$e,"共"+e.toDisplayString(mt.value(a))+"件",1)])])]}),_:1}),e.createVNode(D,{prop:"age",label:"平台服务费",width:"110"},{default:e.withCtx(({shopOrderItems:a})=>{var g;return[e.createTextVNode(e.toDisplayString(e.unref(l)((g=a==null?void 0:a[0])==null?void 0:g.platformServiceFee)),1)]}),_:1}),e.createVNode(D,{prop:"orderStatus",label:"预计返利",width:"180"},{default:e.withCtx(({shopOrderItems:a})=>{var g;return[e.createTextVNode(e.toDisplayString((g=a==null?void 0:a[0])==null?void 0:g.rebateCalculation),1)]}),_:1}),e.createVNode(D,{prop:"deliveryStatus",label:"结算状态"},{default:e.withCtx(({shopOrderItems:a})=>{var g,T,A;return[e.createElementVNode("div",ze,[((g=a==null?void 0:a[0])==null?void 0:g.settlementStatus)==="PENDING_SETTLEMENT"?(e.openBlock(),e.createElementBlock("span",Fe,"待结算")):((T=a==null?void 0:a[0])==null?void 0:T.settlementStatus)==="EXPIRED"?(e.openBlock(),e.createElementBlock("span",Ie,"已失效")):((A=a==null?void 0:a[0])==null?void 0:A.settlementStatus)==="SETTLED"?(e.openBlock(),e.createElementBlock("span",He,"已返")):e.createCommentVNode("",!0)])]}),_:1}),e.createVNode(D,{prop:"sex",label:"佣金结算",width:"200","is-mixed":!0},{default:e.withCtx(({row:a,shopOrderItems:g})=>[e.createElementVNode("div",null,[e.createElementVNode("div",null,"返利合计："+e.toDisplayString(e.unref(l)(a==null?void 0:a.totalRebate)),1),O(g,"PENDING_SETTLEMENT")?(e.openBlock(),e.createElementBlock("div",Le,"待结算："+e.toDisplayString(e.unref(l)(a==null?void 0:a.pendingSettlement)),1)):e.createCommentVNode("",!0),O(g,"SETTLED")?(e.openBlock(),e.createElementBlock("div",qe,"已返："+e.toDisplayString(e.unref(l)(a==null?void 0:a.settled)),1)):e.createCommentVNode("",!0),O(g,"EXPIRED")?(e.openBlock(),e.createElementBlock("div",Oe,"已失效："+e.toDisplayString(e.unref(l)(a==null?void 0:a.expired)),1)):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["checkedItem","class","data"]),e.createVNode(xt,{justify:"end",align:"bottom"},{default:e.withCtx(()=>[e.createVNode(ae,{"page-size":N.size,"page-num":N.current,total:N.total,onReload:w,onHandleSizeChange:pt,onHandleCurrentChange:bt},null,8,["page-size","page-num","total"])]),_:1}),e.createVNode(ne,{isShow:h.value,"onUpdate:isShow":b[2]||(b[2]=a=>h.value=a),ids:V.value,"onUpdate:ids":b[3]||(b[3]=a=>V.value=a),remark:C.value,"onUpdate:remark":b[4]||(b[4]=a=>C.value=a),"remark-type":"GOODS",onSuccess:_t},null,8,["isShow","ids","remark"])])}}}),Vt="",Ct="",Ke=L(Ge,[["__scopeId","data-v-dee8cc69"]]),Ye=Object.freeze(Object.defineProperty({__proto__:null,default:e.defineComponent({__name:"rebate-order",setup(t){const o=e.ref(!1),n=e.reactive({orderNo:"",productName:"",shopName:"",buyerNickname:"",orderCreateTime:"",orderEndTime:"",forceSearch:!1}),c=x=>{Object.keys(n).forEach(r=>{const l=r;n[l]=x[l]}),n.forceSearch=!0},p=e.reactive({totalExpired:"",totalPendingSettlement:"",totalRebate:""});return(x,r)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode(ce,{onShowChange:r[0]||(r[0]=l=>o.value=l),onGetSearchParams:c}),e.createVNode(Ve,{"rebate-detail-statistic":p},null,8,["rebate-detail-statistic"]),e.createVNode(Ke,{"search-form":n,"onUpdate:searchForm":r[1]||(r[1]=l=>n=l),statistic:p,"onUpdate:statistic":r[2]||(r[2]=l=>p=l),"search-retract":o.value},null,8,["search-form","statistic","search-retract"])]))}})},Symbol.toStringTag,{value:"Module"}));var q=(t=>(t.PAID_MEMBER="PAID_MEMBER",t.ALL_MEMBERS="ALL_MEMBERS",t))(q||{}),X;(t=>{(o=>{o.REDUCE="REDUCE",o.INCREASE="INCREASE"})(t.CHANGE_TYPE||(t.CHANGE_TYPE={})),(o=>{o.INCOME="INCOME",o.EXPENDITURE="EXPENDITURE",o.REFUND="REFUND",o.WITHDRAW="WITHDRAW"})(t.REBATE_TYPE||(t.REBATE_TYPE={}))})(X||(X={}));const{mulHundred:$,divHundred:z,mulTenThousand:Q,divTenThousand:J}=I(),We=()=>{const t=e.ref([]),o=e.ref([]),n=e.reactive({id:"",rebateStatus:!1,rebateUsers:"PAID_MEMBER"}),c=async()=>{const r=await Se();if(r.code!==200)return M.ElMessage.error(r.msg||"获取设置失败");const l=r.data;n.id=l.id||"",n.rebateStatus=l.rebateStatus||!1,n.rebateUsers=l.rebateUsers||"PAID_MEMBER",t.value=Xe(l),o.value=Qe(l)};return{currentMemberRebates:e.computed(()=>n.rebateUsers==="PAID_MEMBER"?t.value:o.value),initialRebates:c,baseRebateConfig:n,onSubmit:async()=>{var u;const r={...n,payRebateUsers:(u=t.value)==null?void 0:u.map(y=>({...y,rebatePaymentPercentage:$(y.rebatePaymentPercentage).toString(),rebatePercentage:$(y.rebatePercentage).toString(),withdrawalThreshold:Q(y.withdrawalThreshold).toString()})),allRebateUsers:o.value.map(y=>({...y,rebatePaymentPercentage:$(y.rebatePaymentPercentage).toString(),rebatePercentage:$(y.rebatePercentage).toString(),withdrawalThreshold:Q(y.withdrawalThreshold).toString()}))},{code:l,data:m,msg:i}=await Te(r);l===200?(M.ElMessage.success(i||"修改成功"),c()):M.ElMessage.error(i||"修改失败")}}},Xe=t=>{var n;if(t.payRebateUsers)return(n=t.payRebateUsers)==null?void 0:n.map(c=>({...c,rebatePaymentPercentage:z(c.rebatePaymentPercentage).toNumber(),rebatePercentage:z(c.rebatePercentage).toNumber(),withdrawalThreshold:J(c.withdrawalThreshold).toNumber()}));const o=[];return Object.keys(t.allMemberRankCode.PAID_MEMBER).forEach(c=>{const p=c;o.push({memberType:"PAID_MEMBER",memberName:t.allMemberRankCode.PAID_MEMBER[p],rankCode:p,rebatePercentage:0,rebatePaymentPercentage:0,withdrawalThreshold:0})}),o},Qe=t=>{var n;if(t.allRebateUsers)return(n=t.allRebateUsers)==null?void 0:n.map(c=>({...c,rebatePaymentPercentage:z(c.rebatePaymentPercentage).toNumber(),rebatePercentage:z(c.rebatePercentage).toNumber(),withdrawalThreshold:J(c.withdrawalThreshold).toNumber()}));const o=[];return Object.keys(t.allMemberRankCode.PAID_MEMBER).forEach(c=>{const p=c;o.push({memberType:"PAID_MEMBER",memberName:t.allMemberRankCode.PAID_MEMBER[p],rankCode:p,rebatePercentage:0,rebatePaymentPercentage:0,withdrawalThreshold:0})}),Object.keys(t.allMemberRankCode.FREE_MEMBER).forEach(c=>{const p=c;o.push({memberType:"FREE_MEMBER",memberName:t.allMemberRankCode.FREE_MEMBER[p],rankCode:p,rebatePercentage:0,rebatePaymentPercentage:0,withdrawalThreshold:0})}),o},Je=t=>(e.pushScopeId("data-v-6c3451fb"),t=t(),e.popScopeId(),t),Ze={style:{display:"flex","justify-content":"space-between","align-items":"center",width:"80%"}},ve=Je(()=>e.createElementVNode("div",{class:"demo-rich-conent",style:{display:"flex",gap:"16px","flex-direction":"column"}},[e.createElementVNode("div",{class:"title"},"消费返利说明"),e.createElementVNode("div",null,[e.createElementVNode("p",{class:"demo-rich-content__name",style:{margin:"0","font-weight":"500"}}," 消费返利属于平台层级的营销活动，通过该营销工具可促进付费会员的购买及续费，同时还可提高用户粘性将用户未来的交易固定在本平台发生的营销神器。平台从各店铺交易获得的【平台服务费】拿出小部分用于消费返利的资金支出(订单完成后才返利)，系统可实现不同会员等级获得不同的返利权益支持您的灵活营销。 "),e.createElementVNode("div",{style:{"text-indent":"1em"}}," 1、返利百分比：是指将平台收到的平台服务费的百分之几用于消费返利，消费返利是以商品为粒度计算的。商品返利金额 = 该商品实收平台服务费 * 商品数 * 返利百分比 "),e.createElementVNode("div",{style:{"text-indent":"1em"}}," 例1: 商品 A 销售价 100 提佣10% 返利百分比80% ， 如用户购买1个商品A可获得返利金额为： (100 *10%) * 1 * 80% = 8 "),e.createElementVNode("div",{style:{"text-indent":"1em"}}," 2、返利支付百分比：用户在使用消费返利余额支付占订单实付金额的百分之几，百分比越大则其实付现金越小 "),e.createElementVNode("div",{style:{"text-indent":"1em"}}," 例2: 商品 A 销售价 100 返利支付百分比80% ， 如用户购买1个商品A 可通过消费余额支付： (100 *80%) * 1 = 80 即其只需支付现金 20 元 即可购买到该商品。 其中实付现金20元计算出来的平台服务费，参与重复返利：（20*10%）*1 *80% = 1.6 "),e.createElementVNode("div",{style:{"text-indent":"1em"}}," 3、重复返利：使用消费返利支付购买商品，也可获得消费返利。重复返利需按【实收现金】计算即需剔除消费返利支付的金额、各项优惠金额 ，可参考例2辅助理解 "),e.createElementVNode("div",{style:{"text-indent":"1em"}}," 4、提现门槛：是指消费返利余额需大于该金额才能提现，可提现金额 = 消费返利余额 - 提现门槛，未达到提现门槛时虽不能提现但可用于购买平台上的商品。 "),e.createElementVNode("div",{style:{"text-indent":"1em"}},"5、积分商品：消费返利支付不能购买积分商品，购买积分也无法获得消费返利")])],-1)),et={class:"name"},tt={class:"name"},at={class:"submit"},ot=e.defineComponent({__name:"rebate-set",setup(t){const{currentMemberRebates:o,initialRebates:n,baseRebateConfig:c,onSubmit:p}=We();return e.onMounted(()=>n()),(x,r)=>{const l=e.resolveComponent("el-radio"),m=e.resolveComponent("el-radio-group"),i=e.resolveComponent("el-form-item"),u=e.resolveComponent("el-icon"),y=e.resolveComponent("el-popover"),d=e.resolveComponent("el-table-column"),f=e.resolveComponent("el-input-number"),V=e.resolveComponent("el-table"),h=e.resolveComponent("el-button"),E=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(E,{model:e.unref(c),"label-width":"120px",style:{position:"relative"}},{default:e.withCtx(()=>[e.createVNode(i,{label:"消费返利"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:e.unref(c).rebateStatus,"onUpdate:modelValue":r[0]||(r[0]=_=>e.unref(c).rebateStatus=_)},{default:e.withCtx(()=>[e.createVNode(l,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1}),e.createVNode(l,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(i,{label:"返利用户"},{default:e.withCtx(()=>[e.createElementVNode("div",Ze,[e.createVNode(m,{modelValue:e.unref(c).rebateUsers,"onUpdate:modelValue":r[1]||(r[1]=_=>e.unref(c).rebateUsers=_)},{default:e.withCtx(()=>[e.createVNode(l,{label:e.unref(q).PAID_MEMBER},{default:e.withCtx(()=>[e.createTextVNode("付费会员")]),_:1},8,["label"]),e.createVNode(l,{label:e.unref(q).ALL_MEMBERS},{default:e.withCtx(()=>[e.createTextVNode("所有会员（含免费会员）")]),_:1},8,["label"])]),_:1},8,["modelValue"]),e.createElementVNode("div",null,[e.createVNode(y,{placement:"top-start",title:"",width:400,trigger:"hover"},{reference:e.withCtx(()=>[e.createVNode(u,null,{default:e.withCtx(()=>[e.createVNode(e.unref(U.QuestionFilled))]),_:1})]),default:e.withCtx(()=>[ve]),_:1})])])]),_:1}),e.createVNode(i,{label:"","label-width":"60"},{default:e.withCtx(()=>[e.createVNode(V,{data:e.unref(o),style:{width:"80%"},"header-row-style":{fontSize:"12px",color:"#909399"},"header-cell-style":{background:"#f6f8fa"},"cell-style":{fontSize:"12px",color:"#333333"}},{default:e.withCtx(()=>[e.createVNode(d,{label:"会员名称",width:"100",align:"center"},{default:e.withCtx(({row:_})=>[e.createElementVNode("div",et,e.toDisplayString(_.memberName),1)]),_:1}),e.createVNode(d,{label:"会员等级",width:"100",align:"center"},{default:e.withCtx(({row:_})=>[e.createElementVNode("div",tt,e.toDisplayString(_.rankCode),1)]),_:1}),e.createVNode(d,{label:"返利百分比(0~100%)",align:"center"},{default:e.withCtx(({row:_})=>[e.createVNode(f,{modelValue:_.rebatePercentage,"onUpdate:modelValue":C=>_.rebatePercentage=C,precision:2,min:0,max:100,controls:!1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(d,{label:"返利支付百分比(0~100%)",align:"center"},{default:e.withCtx(({row:_})=>[e.createVNode(f,{modelValue:_.rebatePaymentPercentage,"onUpdate:modelValue":C=>_.rebatePaymentPercentage=C,precision:2,min:0,max:100,controls:!1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(d,{label:"提现门槛",align:"center"},{default:e.withCtx(({row:_})=>[e.createVNode(f,{modelValue:_.withdrawalThreshold,"onUpdate:modelValue":C=>_.withdrawalThreshold=C,precision:2,min:0,controls:!1},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])]),_:1}),e.createVNode(i,null,{default:e.withCtx(()=>[e.createElementVNode("div",at,[e.createVNode(h,{type:"primary",onClick:e.unref(p)},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1},8,["onClick"])])]),_:1})]),_:1},8,["model"])}}}),kt="",nt=Object.freeze(Object.defineProperty({__proto__:null,default:L(ot,[["__scopeId","data-v-6c3451fb"]])},Symbol.toStringTag,{value:"Module"}));return re});
