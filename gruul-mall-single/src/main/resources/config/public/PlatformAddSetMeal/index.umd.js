(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("vue-router"),require("element-plus"),require("@/composables/useConvert"),require("@/apis/http"),require("@/utils/date"),require("@/components/q-upload/q-upload.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","element-plus","@/composables/useConvert","@/apis/http","@/utils/date","@/components/q-upload/q-upload.vue"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAddSetMeal=V(e.PlatformAddSetMealContext.Vue,e.PlatformAddSetMealContext.VueRouter,e.PlatformAddSetMealContext.ElementPlus,e.PlatformAddSetMealContext.UseConvert,e.PlatformAddSetMealContext.Request,e.PlatformAddSetMealContext.DateUtil,e.PlatformAddSetMealContext.QUpload))})(this,function(e,V,P,O,<PERSON>,j,K){"use strict";var $=document.createElement("style");$.textContent=`@charset "UTF-8";.com[data-v-ff243579]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-ff243579]{width:62px;height:62px}.com__name[data-v-ff243579]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.add[data-v-a3e0f06f]{margin:-20px -15px;height:calc(100vh - 85px);overflow-y:auto;scrollbar-width:none;-ms-overflow-style:none}.add[data-v-a3e0f06f]::-webkit-scrollbar{display:none}.bargaining_amount[data-v-a3e0f06f]{position:relative;width:100%}.bargaining_amount__description[data-v-a3e0f06f]{position:absolute;top:-35px;right:0;width:480px}.title[data-v-a3e0f06f]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-a3e0f06f]{font-size:12px;margin-left:12px;color:#c4c4c4}.use_discount[data-v-a3e0f06f]{display:flex;width:100%}.discount_msg[data-v-a3e0f06f]{display:inline-block;width:400px;flex:1}.rules[data-v-a3e0f06f]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-a3e0f06f]{width:300px;display:flex}.text[data-v-a3e0f06f]{font-size:14px;color:#333}.goodsData[data-v-a3e0f06f]{border:1px solid #ccc}.goods-list[data-v-a3e0f06f]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-a3e0f06f]{display:flex}.goods-list__goods-list__info-name[data-v-a3e0f06f]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-a3e0f06f]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-a3e0f06f]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-a3e0f06f]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-a3e0f06f]{font-size:16px}.ruleform-date[data-v-a3e0f06f]{width:100%;display:flex;align-items:center}.flex[data-v-a3e0f06f]{margin-top:10px;height:50px}.flex-item[data-v-a3e0f06f]{width:40%}.coupon-rules[data-v-a3e0f06f]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-a3e0f06f]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild($);const X={class:"com"},Q={class:"com__name"},W=e.defineComponent({__name:"select-goods-table",props:{productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}},productAttributes:{type:String,default:"MAIN_PRODUCT"}},setup(_,{expose:x}){const g=_,{divTenThousand:b,mulTenThousand:k}=O(),T=e.ref([]);e.watch(()=>g.productList,o=>{const d=a(o);console.log("flatGoodList",d),T.value=u(d)}),e.watch(()=>g.flatGoodList,o=>{T.value=u(o)});function D(o){return o.skuItem.stockType==="LIMITED"?Number(o.skuItem.skuStock):1/0}function a(o,d){if(!o.length)return[];const r=[];return o.forEach(l=>{l.skuIds.forEach((m,p)=>{r.push({productId:l.productId,productName:l.productName,productPic:l.pic,shopId:l.shopId,skuItem:{productId:l.productId,skuId:m,skuName:l.specs[p],skuPrice:l.salePrices[p],skuStock:l.stocks[p],stockType:l.stockTypes[p]},rowTag:0,matchingStock:0,isJoin:!0,matchingPrice:.01})})}),r}function u(o,d){let r=0,l=o.length;for(let m=0;m<l;m++){const p=o[m];m===0&&(p.rowTag=1,r=0),m!==0&&(p.productId===o[m-1].productId?(p.rowTag=0,o[r].rowTag=o[r].rowTag+1):(p.rowTag=1,r=m))}return o}const S=({row:o,column:d,rowIndex:r,columnIndex:l})=>{if(l===0)return{rowspan:o.rowTag,colspan:o.rowTag?1:0}};function B(o){return o.stockType==="UNLIMITED"?"不限购":o.skuStock}function F(){return e.toRaw(T.value).filter(d=>d.isJoin).map(d=>{const{setMealId:r,productId:l,productPic:m,matchingPrice:p,productName:y,matchingStock:s,shopId:h,skuItem:{skuId:U,skuStock:A,skuPrice:q,skuName:N,stockType:c}}=d;return{setMealId:r||"",shopId:h,productId:l,productPic:m,productName:y,productAttributes:g.productAttributes,skuId:U,skuName:N,skuStock:+A,skuPrice:q,stockType:c,matchingPrice:k(p).toString(),matchingStock:s}})}function E(){let o=!0;const d=T.value;if(!d.length)P.ElMessage.warning("请选择商品"),o=!1;else for(let r=0;r<d.length;r++)if(d[r].isJoin&&!d[r].matchingStock){P.ElMessage.warning("商品库存必须大于零"),o=!1;break}return o}return x({getProduct:F,validateProduct:E}),(o,d)=>{const r=e.resolveComponent("el-image"),l=e.resolveComponent("el-table-column"),m=e.resolveComponent("el-input-number"),p=e.resolveComponent("el-switch"),y=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(y,{data:T.value,"span-method":S,"max-height":500},{default:e.withCtx(()=>[e.createVNode(l,{label:"商品信息",width:"215"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",X,[e.createVNode(r,{class:"com__pic",src:s.productPic},null,8,["src"]),e.createElementVNode("div",Q,e.toDisplayString(s.productName),1)])]),_:1}),e.createVNode(l,{label:"规格"},{default:e.withCtx(({row:s})=>[e.createTextVNode(e.toDisplayString(s.skuItem.skuName),1)]),_:1}),e.createVNode(l,{label:"套餐库存"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",null,[e.createVNode(m,{"model-value":+s.matchingStock,min:0,style:{width:"80px"},max:D(s),disabled:g.isEdit,precision:0,controls:!1,"onUpdate:modelValue":h=>s.matchingPrice=h},null,8,["model-value","max","disabled","onUpdate:modelValue"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(B(s.skuItem)),1)]),_:1}),e.createVNode(l,{label:"套餐价（元）"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",null,[e.createVNode(m,{"model-value":+s.matchingPrice,min:.01,style:{width:"80px"},disabled:g.isEdit,precision:2,max:e.unref(b)(s.skuItem.skuPrice).toNumber(),controls:!1,"onUpdate:modelValue":h=>s.matchingPrice=h},null,8,["model-value","disabled","max","onUpdate:modelValue"])]),e.createElementVNode("div",null,"销售价"+e.toDisplayString(e.unref(b)(s.skuItem.skuPrice)),1)]),_:1}),e.createVNode(l,{label:"是否参与"},{default:e.withCtx(({row:s})=>[e.createVNode(p,{modelValue:s.isJoin,"onUpdate:modelValue":h=>s.isJoin=h,size:"large",disabled:g.isEdit},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),me="",z=(_,x)=>{const g=_.__vccOpts||_;for(const[b,k]of x)g[b]=k;return g},H=z(W,[["__scopeId","data-v-ff243579"]]),Z="addon-matching-treasure/setMeal/",v=(_,x)=>J.get({url:`${Z}${_}/${x}`}),M=_=>(e.pushScopeId("data-v-a3e0f06f"),_=_(),e.popScopeId(),_),ee={style:{padding:"40px"},class:"add"},te=M(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),oe={class:"ruleform-date"},ae=M(()=>e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1)),le={class:"use_discount"},ne=M(()=>e.createElementVNode("div",{class:"msg discount_msg"},[e.createTextVNode(" 叠加优惠可能导致实付金额为 "),e.createElementVNode("strong",{style:{color:"red"}},"0"),e.createTextVNode(" (实付金额 = 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1)),se=M(()=>e.createElementVNode("span",{class:"msg"},"自选套餐：主商品+至少1种搭配商品以上 ；固定套餐：主商品+库存不为0的所有搭配商品各1件以上",-1)),re=M(()=>e.createElementVNode("span",{class:"msg"},"主商品：必买商品，此商品详情页展示搭配套餐 搭配商品：用户选择购买",-1)),ie=M(()=>e.createElementVNode("span",{class:"msg"},"是否参与：SKU的粒度设置商品是否参与活动，是(默认)则参与活动，反则否",-1)),de=e.defineComponent({__name:"PlatformAddSetMeal",setup(_){const x=V.useRouter(),g=V.useRoute(),b=new j,k=e.ref(),T=e.ref(),D=e.reactive({form:{setMealId:"",shopId:"",shopName:"",setMealName:"",setMealDescription:"",setMealMainPicture:"",setMealType:"FIXED_COMBINATION",setMealStatus:"NOT_STARTED",startTime:"",endTime:"",stackable:{coupon:!1,vip:!1,full:!1},mainProduct:[],matchingProducts:[]},isEditDisable:!1,fullReductionTime:[],rules:{setMealName:[{required:!0,message:"请输入套餐名称",trigger:"blur"}],setMealDescription:[{required:!0,message:"请输入套餐描述",trigger:"blur"}],product:[{required:!0,message:"请选择商品",trigger:["blur","change"]}],startTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]},{validator:U,trigger:["blur","change"]}],endTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]},{validator:A,trigger:["blur","change"]}],setMealMainPicture:[{required:!0,message:"请添加套餐主图",trigger:["blur","change"]}],setMealType:[{required:!0,message:"请选择套餐类型",trigger:["blur","change"]}]},flatMainGoodList:[],flatMatchingGoodList:[],choosedMainGoods:[],choosedMatchingGoods:[],choosedMainGoodsPopup:!1,choosedMatchingGoodsPopup:!1}),{form:a,isEditDisable:u,rules:S,choosedMainGoodsPopup:B,choosedMatchingGoodsPopup:F,choosedMainGoods:E,choosedMatchingGoods:o,flatMainGoodList:d,flatMatchingGoodList:r}=e.toRefs(D),l=e.ref(),{mulTenThousand:m,divTenThousand:p}=O();y();async function y(c=g.query){if(c.shopId&&c.setMealId){u.value=!0;const{code:t,data:n,msg:f}=await v(c.shopId,c.setMealId);if(t!==200)return P.ElMessage.error(f||"获取活动详情失败");a.value=n,d.value=s(n.mainProduct),r.value=s(n.matchingProducts)}}function s(c){return c.map(t=>{const{productId:n,skuPrice:f,productPic:w,productName:C,skuId:I,skuStock:G,stockType:Y,skuName:R,setMealId:L,matchingPrice:i,matchingStock:ce,productAttributes:fe}=t;return{isJoin:!0,productId:n,productName:C,productPic:w,setMealId:L,matchingPrice:p(i).toString(),matchingStock:ce,productAttributes:fe,skuItem:{productId:n,skuId:I,skuName:R,skuPrice:f,skuStock:G,stockType:Y}}})}function h(c){const t=b.getYMD(new Date),n=b.getYMD(c);return t===n?!1:new Date().getTime()>c.getTime()}function U(c,t,n){t?t&&a.value.endTime?N(new Date(a.value.endTime).getTime(),n,"开始日期和结束日期最少间隔5分钟",new Date(t).getTime(),1e3*60*5):N(new Date(t).getTime(),n,"开始日期必须是一个将来的时间",new Date().getTime(),1e3):n(new Error("请选择活动开始日期"))}function A(c,t,n){t?t&&a.value.startTime?N(new Date(t).getTime(),n,"开始日期和结束日期最少间隔5分钟",new Date(a.value.startTime).getTime(),1e3*60*5):N(new Date(t).getTime(),n,"结束日期最少大当前时间5分钟",new Date().getTime()+1e3,1e3*60*5):n(new Error("请选择活动结束日期"))}function q(c,t=new Date().getTime()){const n=c-t;return(f=1e3)=>n>=f}function N(c,t,n,f,w){q(c,f)(w)?t():t(new Error(n))}return(c,t)=>{const n=e.resolveComponent("el-input"),f=e.resolveComponent("el-form-item"),w=e.resolveComponent("el-date-picker"),C=e.resolveComponent("el-checkbox"),I=e.resolveComponent("el-radio"),G=e.resolveComponent("el-radio-group"),Y=e.resolveComponent("el-form"),R=e.resolveComponent("el-button"),L=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",ee,[te,e.createVNode(Y,{ref_key:"ruleFormRef",ref:l,model:e.unref(a),rules:e.unref(S),"label-width":"auto","inline-message":!1,"label-position":"left"},{default:e.withCtx(()=>[e.createVNode(f,{label:"套餐名称",prop:"setMealName"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:e.unref(a).setMealName,"onUpdate:modelValue":t[0]||(t[0]=i=>e.unref(a).setMealName=i),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"15",placeholder:"限15个字",disabled:e.unref(u)},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(f,{label:"套餐描述",prop:"setMealDescription"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:e.unref(a).setMealDescription,"onUpdate:modelValue":t[1]||(t[1]=i=>e.unref(a).setMealDescription=i),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"40",placeholder:"套餐描述不超过40个字",disabled:e.unref(u)},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(f,{label:"活动日期",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",oe,[e.createVNode(f,{prop:"startTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:e.unref(a).startTime,"onUpdate:modelValue":t[2]||(t[2]=i=>e.unref(a).startTime=i),type:"datetime",disabled:e.unref(u),placeholder:"请选择开始时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":h},null,8,["modelValue","disabled"]),ae]),_:1}),e.createVNode(f,{prop:"endTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:e.unref(a).endTime,"onUpdate:modelValue":t[3]||(t[3]=i=>e.unref(a).endTime=i),disabled:e.unref(u),type:"datetime",placeholder:"请选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":h},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(f,{label:"套餐主图",prop:"setMealMainPicture"},{default:e.withCtx(()=>[e.createVNode(K,{src:e.unref(a).setMealMainPicture,"onUpdate:src":t[4]||(t[4]=i=>e.unref(a).setMealMainPicture=i),width:100,height:100},null,8,["src"])]),_:1}),e.createVNode(f,{label:"叠加优惠"},{default:e.withCtx(()=>[e.createElementVNode("div",le,[e.createVNode(C,{modelValue:e.unref(a).stackable.vip,"onUpdate:modelValue":t[5]||(t[5]=i=>e.unref(a).stackable.vip=i),label:"会员价",disabled:e.unref(u)},null,8,["modelValue","disabled"]),e.createVNode(C,{modelValue:e.unref(a).stackable.coupon,"onUpdate:modelValue":t[6]||(t[6]=i=>e.unref(a).stackable.coupon=i),label:"优惠券",disabled:e.unref(u)},null,8,["modelValue","disabled"]),e.createVNode(C,{modelValue:e.unref(a).stackable.full,"onUpdate:modelValue":t[7]||(t[7]=i=>e.unref(a).stackable.full=i),label:"满减",disabled:e.unref(u)},null,8,["modelValue","disabled"]),ne])]),_:1}),e.createVNode(f,{label:"套餐类型",prop:"setMealType"},{default:e.withCtx(()=>[e.createVNode(G,{modelValue:e.unref(a).setMealType,"onUpdate:modelValue":t[8]||(t[8]=i=>e.unref(a).setMealType=i),class:"ml-4",disabled:e.unref(u)},{default:e.withCtx(()=>[e.createVNode(I,{label:"OPTIONAL_PRODUCT"},{default:e.withCtx(()=>[e.createTextVNode("自选商品套餐")]),_:1}),e.createVNode(I,{label:"FIXED_COMBINATION"},{default:e.withCtx(()=>[e.createTextVNode("固定套餐")]),_:1})]),_:1},8,["modelValue","disabled"]),se]),_:1}),e.createVNode(f,{label:"主商品(限1种商品)",required:""},{default:e.withCtx(()=>[re]),_:1})]),_:1},8,["model","rules"]),e.createVNode(H,{ref_key:"selectMainGoodsTableRef",ref:k,"product-list":e.unref(E),"is-edit":e.unref(u),"flat-good-list":e.unref(d),style:{"margin-bottom":"20px"}},null,8,["product-list","is-edit","flat-good-list"]),e.createVNode(f,{label:"搭配商品（限4种）",required:""},{default:e.withCtx(()=>[ie]),_:1}),e.createVNode(H,{ref_key:"selectMatchingGoodsTableRef",ref:T,"product-list":e.unref(o),"is-edit":e.unref(u),"flat-good-list":e.unref(r),"product-attributes":"MATCHING_PRODUCTS"},null,8,["product-list","is-edit","flat-good-list"]),e.createVNode(L,{justify:"center",style:{"margin-top":"60px"}},{default:e.withCtx(()=>[e.createVNode(R,{round:"",plain:"",onClick:t[9]||(t[9]=i=>e.unref(x).back())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1})]),_:1})])}}}),pe="";return z(de,[["__scopeId","data-v-a3e0f06f"]])});
