(function(e,u){typeof exports=="object"&&typeof module<"u"?module.exports=u(require("vue"),require("vue-router"),require("@vueuse/core"),require("@/apis/http"),require("element-plus"),require("@/composables/useConvert"),require("@/components/pageManage/PageManage.vue"),require("@/store/modules/shopInfo")):typeof define=="function"&&define.amd?define(["vue","vue-router","@vueuse/core","@/apis/http","element-plus","@/composables/useConvert","@/components/pageManage/PageManage.vue","@/store/modules/shopInfo"],u):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopSeckillList=u(e.ShopSeckillListContext.Vue,e.ShopSeckillListContext.VueRouter,e.ShopSeckillListContext.VueUse,e.ShopSeckillListContext.Request,e.ShopSeckillListContext.ElementPlus,e.ShopSeckillListContext.UseConvert,e.ShopSeckillListContext.PageManage,e.ShopSeckillListContext.ShopInfoStore))})(this,function(e,u,B,y,_,C,L,K){"use strict";var V=document.createElement("style");V.textContent=`.seckill-item[data-v-70214b09]{width:966px;height:144px;background:#f9f9f9;margin-bottom:10px;padding:10px;display:flex;justify-content:center;align-items:center}.seckill-item__left[data-v-70214b09]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-around;align-items:flex-start;width:50%;height:100%;font-size:12px;color:#333}.seckill-item__left--title[data-v-70214b09]{font-size:14px}.seckill-item__left--statistical[data-v-70214b09]{width:100%;color:#a9a9a9;display:flex;justify-content:center;align-items:center;padding-right:40px;justify-content:space-between}.seckill-item__center[data-v-70214b09]{width:20%;height:100%}.seckill-item__center--title[data-v-70214b09]{font-size:14px}.seckill-item__right[data-v-70214b09]{width:30%;height:100%;display:flex;justify-content:center;align-items:center}.nots[data-v-70214b09]{color:#2e99f3}.ongoing[data-v-70214b09]{color:#f57373}.hasEnded[data-v-70214b09],.off[data-v-70214b09],.suspended[data-v-70214b09]{color:#a9a9a9}.container[data-v-b904d4c2]{overflow-y:scroll}
`,document.head.appendChild(V);const x="addon-seckill/seckillPromotion/",D=o=>y.get({url:x+"secKillList",params:o}),T=o=>y.del({url:x+`del/${o}`}),I=o=>y.post({url:"addon-seckill/seckillPromotion/syncSecKill",data:o});C();const h=e.computed(()=>o=>{var c;return(c=window==null?void 0:window.permissionList)==null?void 0:c.includes(o)}),S={NOT_STARTED:{title:"未开始",class:"nots"},PROCESSING:{title:"进行中",class:"ongoing"},OVER:{title:"已结束",class:"hasEnded"},ILLEGAL_SELL_OFF:{title:"违规下架",class:"off"}},M={class:"dialog-footer"},z=e.defineComponent({__name:"head-search",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},leftBtnText:{type:String,default:"新增秒杀活动"},leftMsBtnText:{type:String,default:"同步秒杀"}},emits:["update:modelValue","add","search"],setup(o,{emit:c}){const s=e.ref(!1),d=o,l=e.reactive({targetShopId:"",productId:"",productTypeSet:[]}),m=async()=>{const{data:k,code:t}=await I(l);t===200?(s.value=!1,_.ElMessage.success({duration:1e3,message:"同步秒杀成功"})):_.ElMessage.error({duration:1e3,message:"同步秒杀失败"})},p=c,i=B.useVModel(d,"modelValue",p);return(k,t)=>{const n=e.resolveComponent("el-button"),f=e.resolveComponent("el-col"),a=e.resolveComponent("el-date-picker"),g=e.resolveComponent("el-row"),w=e.resolveComponent("el-option"),b=e.resolveComponent("el-select"),E=e.resolveComponent("el-form-item"),P=e.resolveComponent("el-form"),G=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(g,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px",width:"100%"}},{default:e.withCtx(()=>[e.createVNode(f,{span:14},{default:e.withCtx(()=>[e.unref(h)("marketingApp:secondsKill:add")?(e.openBlock(),e.createBlock(n,{key:0,round:"",type:"primary",onClick:t[0]||(t[0]=r=>p("add"))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(d.leftBtnText),1)]),_:1})):e.createCommentVNode("",!0),e.unref(h)("marketingApp:secondsKill:sync")?(e.openBlock(),e.createBlock(n,{key:1,round:"",type:"primary",onClick:t[1]||(t[1]=r=>s.value=!0)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(d.leftMsBtnText),1)]),_:1})):e.createCommentVNode("",!0)]),_:1}),e.createVNode(f,{span:8},{default:e.withCtx(()=>[e.createVNode(a,{modelValue:e.unref(i).date,"onUpdate:modelValue":t[2]||(t[2]=r=>e.unref(i).date=r),"end-placeholder":"结束日期",format:"YYYY/MM/DD HH:mm:ss","range-separator":"-","start-placeholder":"开始日期",style:{width:"300px"},type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss",onChange:t[3]||(t[3]=r=>p("search",r))},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(G,{modelValue:s.value,"onUpdate:modelValue":t[7]||(t[7]=r=>s.value=r),title:"同步店铺秒杀(1.请先确保商品中心的商品已同步,2.秒杀只能同步未开始状态)",center:""},{footer:e.withCtx(()=>[e.createElementVNode("span",M,[e.createVNode(n,{onClick:t[6]||(t[6]=r=>s.value=!1)},{default:e.withCtx(()=>t[8]||(t[8]=[e.createTextVNode("取消")])),_:1}),e.createVNode(n,{type:"primary",onClick:m},{default:e.withCtx(()=>t[9]||(t[9]=[e.createTextVNode("提交")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(P,null,{default:e.withCtx(()=>[e.createVNode(E,{label:"同步的目标店铺"},{default:e.withCtx(()=>[e.createVNode(b,{modelValue:l.shopId,"onUpdate:modelValue":t[4]||(t[4]=r=>l.shopId=r),class:"inputWidth",placeholder:"请选择目标店铺",style:{},disabled:""},{default:e.withCtx(()=>[e.createVNode(w,{label:"所有店铺",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(E,{label:"需要同步的秒杀"},{default:e.withCtx(()=>[e.createVNode(b,{modelValue:l.productId,"onUpdate:modelValue":t[5]||(t[5]=r=>l.productId=r),class:"inputWidth",placeholder:"请选择需要同步的秒杀",style:{},disabled:""},{default:e.withCtx(()=>[e.createVNode(w,{label:"所有秒杀",value:""})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])],64)}}}),j={class:"seckill-item"},$={class:"seckill-item__left"},q={class:"seckill-item__left--title"},A={class:"seckill-item__left--statistical"},R={class:"seckill-item__center"},U={class:"seckill-item__right"},O=e.defineComponent({__name:"seckill-item",props:{secondsKill:{type:Object,default:()=>({})}},emits:["del"],setup(o,{emit:c}){const{divTenThousand:s}=C(),d=c,l=u.useRouter(),m=(p,i)=>{l.push({name:"seckillCreate",query:{secKillId:i,shopId:p}})};return(p,i)=>{const k=e.resolveComponent("el-button"),t=e.resolveComponent("el-button-group");return e.openBlock(),e.createElementBlock("div",j,[e.createElementVNode("div",$,[e.createElementVNode("h1",q,e.toDisplayString(o.secondsKill.secKillName),1),e.createElementVNode("time",null,"活动时间："+e.toDisplayString(o.secondsKill.startTime)+"至"+e.toDisplayString(o.secondsKill.endTime),1),e.createElementVNode("div",null,"活动商品："+e.toDisplayString(o.secondsKill.productNum||0)+"件",1),e.createElementVNode("div",A,[e.createElementVNode("span",null,"参加人数："+e.toDisplayString(o.secondsKill.peopleNum||0),1),e.createElementVNode("span",null,"支付单数："+e.toDisplayString(o.secondsKill.payOrder||0),1),e.createElementVNode("span",null,"应收金额："+e.toDisplayString(o.secondsKill.amountReceivable&&e.unref(s)(o.secondsKill.amountReceivable)||0),1)])]),e.createElementVNode("div",R,[e.createElementVNode("h1",{class:e.normalizeClass([e.unref(S)[o.secondsKill.seckillStatus].class,"seckill-item__center--title"])},e.toDisplayString(e.unref(S)[o.secondsKill.seckillStatus].title),3)]),e.createElementVNode("div",U,[e.createVNode(t,null,{default:e.withCtx(()=>[e.unref(h)("marketingApp:secondsKill:detail")?(e.openBlock(),e.createBlock(k,{key:0,round:"",onClick:i[0]||(i[0]=n=>m(o.secondsKill.shopId,o.secondsKill.id))},{default:e.withCtx(()=>i[2]||(i[2]=[e.createTextVNode(" 查看活动")])),_:1})):e.createCommentVNode("",!0),e.unref(h)("marketingApp:secondsKill:delete")?(e.openBlock(),e.createBlock(k,{key:1,round:"",onClick:i[1]||(i[1]=n=>d("del",o.secondsKill.id))},{default:e.withCtx(()=>i[3]||(i[3]=[e.createTextVNode("删除活动")])),_:1})):e.createCommentVNode("",!0)]),_:1})])])}}}),W="",N=(o,c)=>{const s=o.__vccOpts||o;for(const[d,l]of c)s[d]=l;return s},Y=N(O,[["__scopeId","data-v-70214b09"]]),H={style:{height:"calc(100vh - 220px)"},class:"container"},F=e.defineComponent({__name:"ShopSeckillList",setup(o){const c=u.useRouter(),s=e.ref([]),d=e.reactive({keywords:"",date:""}),l=e.reactive({size:10,current:1,total:0});async function m(){const{date:t}=d,n={startDate:"",endDate:""};Array.isArray(t)&&(n.startDate=t[0],n.endDate=t[1]);const f={...l,...n,shopId:K.useShopInfoStore().shopInfo.id},{code:a,data:g}=await D(f);if(a!==200)return _.ElMessage.error("获取活动列表失败");s.value=g.records,l.current=g.current,l.size=g.size,l.total=g.total}const p=t=>{_.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",callback:async n=>{if(console.log("action",n),n==="cancel")return;const{code:f}=await T(t);if(f!==200){_.ElMessage.error("删除失败");return}_.ElMessage.success("删除成功"),s.value=s.value.filter(a=>a.id!==t),l.total--}})},i=t=>{l.size=t,m()},k=t=>{l.current=t,m()};return(t,n)=>{const f=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(z,{modelValue:d,"onUpdate:modelValue":n[0]||(n[0]=a=>d=a),onAdd:n[1]||(n[1]=a=>e.unref(c).push({name:"seckillCreate"})),onSearch:m},null,8,["modelValue"]),e.createElementVNode("div",H,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value,a=>(e.openBlock(),e.createBlock(Y,{key:a.id,"seconds-kill":a,onDel:p},null,8,["seconds-kill"]))),128))]),e.createVNode(f,{align:"middle",justify:"end"},{default:e.withCtx(()=>[e.createVNode(L,{modelValue:l,"onUpdate:modelValue":n[2]||(n[2]=a=>l=a),"load-init":!0,"page-size":l.size,total:l.total,onReload:m,onHandleSizeChange:i,onHandleCurrentChange:k},null,8,["modelValue","page-size","total"])]),_:1})])}}}),J="";return N(F,[["__scopeId","data-v-b904d4c2"]])});
