(function(e,m){typeof exports=="object"&&typeof module<"u"?module.exports=m(require("vue"),require("element-plus"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/assets/json/data.json"),require("@/utils/Storage"),require("vue-router"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/qszr-core/packages/q-table/QTable","@/assets/json/data.json","@/utils/Storage","vue-router","@/apis/http"],m):(e=typeof globalThis<"u"?globalThis:e||self,e.DeliveryList=m(e.DeliveryListContext.Vue,e.DeliveryListContext.ElementPlus,e.DeliveryListContext.QTableColumn,e.DeliveryListContext.QTable,e.DeliveryListContext.handleGetCompanyName,e.DeliveryListContext.Storage,e.DeliveryListContext.VueRouter,e.DeliveryListContext.Request))})(this,function(e,m,h,S,I,N,L,T){"use strict";var O=document.createElement("style");O.textContent=`.orderIndex-table[data-v-9e08f012]{margin-bottom:100px}.orderIndex-table__img-box[data-v-9e08f012]{width:200px;display:flex;justify-content:flex-start}.orderIndex-table__img[data-v-9e08f012]{border-radius:5px;position:relative}.orderIndex-table__img-mask[data-v-9e08f012]{margin-left:5px;display:flex;flex-direction:column;justify-content:center;align-items:flex-start;font-size:12px;color:#000}.orderIndex-table__img-mask--name[data-v-9e08f012]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.orderIndex-table__img-mask--integral[data-v-9e08f012]{width:100px;display:flex;justify-content:space-between}.orderIndex-table__img-mask--integral[data-v-9e08f012]>:nth-child(1){font-size:12px;color:#000}.is-complete[data-v-9e08f012]{background:#eef1f6}.header-table[data-v-9e08f012]{width:100%;display:flex;justify-content:space-between;align-items:center}.money_text[data-v-9e08f012]{font-size:12px;color:#000}.orderIndex-table[data-v-ba715d6b]{margin-bottom:100px}.orderIndex-table__img-box[data-v-ba715d6b]{width:200px;display:flex;justify-content:flex-start}.orderIndex-table__img[data-v-ba715d6b]{flex-shrink:0;border-radius:5px;position:relative}.orderIndex-table__img-mask[data-v-ba715d6b]{margin-left:5px;display:flex;flex-direction:column;justify-content:center;align-items:flex-start;font-size:12px;color:#000}.orderIndex-table__img-mask--name[data-v-ba715d6b]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.orderIndex-table__img-mask--integral[data-v-ba715d6b]{width:100px;display:flex;justify-content:space-between}.orderIndex-table__img-mask--integral[data-v-ba715d6b]>:nth-child(1){font-size:12px;color:#000}.is-complete[data-v-ba715d6b]{background:#eef1f6}.header-table[data-v-ba715d6b]{width:100%;display:flex;justify-content:space-between;align-items:center}.money_text[data-v-ba715d6b]{font-size:12px;color:#000}.avatar_text_box[data-v-fd0daa28]{display:flex;justify-content:space-between;align-items:flex-start}.send[data-v-fd0daa28]{font-size:14px;color:#333}.DatchDelivery_container[data-v-fd0daa28]{padding:0 25px}.DatchDelivery_container__tabText[data-v-fd0daa28]{color:#000}.DatchDelivery_container__tool[data-v-fd0daa28]{width:1010px;align-items:center;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;margin-left:-14px;z-index:100}
`,document.head.appendChild(O);const z=i=>(e.pushScopeId("data-v-9e08f012"),i=i(),e.popScopeId(),i),R={style:{"margin-right":"36px"}},j={class:"orderIndex-table__img-box"},$={class:"orderIndex-table__img-mask"},U=z(()=>e.createElementVNode("span",{class:"orderIndex-table__img-mask--name"},"商品名称商品名称商1商品名称商品名称商...",-1)),B={class:"orderIndex-table__img-mask--integral"},G={style:{color:"#838383","font-size":"10px"}},M={class:"avatar_text avatar_text__bottom money_text"},F={style:{color:"#2e99f3","margin-right":"10px"}},A={style:{padding:"0 10px 0"},class:"money_text"},X=e.defineComponent({__name:"express-table",props:{tableData:{type:Array,default:()=>[]}},emits:["filterOrderList","express-No","express-company-name"],setup(i,{emit:o}){const _=i,l=I.map(d=>({label:d.companyName,value:d.companyCode,companyType:d.companyType})),g=(d,x)=>{o("express-No",d,x)},b=(d,x)=>{o("express-company-name",d,x)};return(d,x)=>{const s=e.resolveComponent("el-button"),C=e.resolveComponent("el-row"),D=e.resolveComponent("el-image"),w=e.resolveComponent("el-select-v2"),V=e.resolveComponent("el-form-item"),E=e.resolveComponent("el-input");return e.openBlock(),e.createBlock(e.unref(S),{data:_.tableData,class:"orderIndex-table"},{header:e.withCtx(({row:n})=>[e.createElementVNode("div",R,"订单号:"+e.toDisplayString(n.no),1),e.createElementVNode("div",null,"创建时间:"+e.toDisplayString(n.createTime),1),e.createVNode(C,{style:{flex:"1"},justify:"end"},{default:e.withCtx(()=>[e.createVNode(s,{type:"primary",link:"",size:"small",onClick:c=>o("filterOrderList",n)},{default:e.withCtx(()=>[e.createTextVNode("移除")]),_:2},1032,["onClick"])]),_:2},1024)]),default:e.withCtx(()=>[e.createVNode(h,{prop:"name",label:"商品",align:"left"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",j,[e.createVNode(D,{fits:"cover",style:{width:"63px",height:"63px"},shape:"square",size:"large",src:n.image,title:n.productName},null,8,["src","title"]),e.createElementVNode("div",$,[U,e.createElementVNode("div",B,[e.createElementVNode("span",null,"积分"+e.toDisplayString(n.price),1),e.createElementVNode("span",G,"共"+e.toDisplayString(n.num)+"件",1)])])])]),_:1}),e.createVNode(h,{prop:"age",label:"客户"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",M,[e.createElementVNode("span",F,"买家昵称 : "+e.toDisplayString(n.buyerNickname),1),e.createElementVNode("div",A," (收货人："+e.toDisplayString(n.integralOrderReceiverVO.name)+","+e.toDisplayString(n.integralOrderReceiverVO.mobile)+") ",1)])]),_:1}),e.createVNode(h,{prop:"sex",label:"操作",align:"right"},{default:e.withCtx(({row:n})=>[e.createVNode(C,null,{default:e.withCtx(()=>[e.createVNode(V,{label:"物流公司",style:{width:"230px"}},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:n.expressCompanyName,"onUpdate:modelValue":c=>n.expressCompanyName=c,placeholder:"请选择物流公司",options:e.unref(l),onChange:c=>b(c,n)},null,8,["modelValue","onUpdate:modelValue","options","onChange"])]),_:2},1024),e.createVNode(V,{label:"运单号码",style:{width:"230px"}},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:n.expressNo,"onUpdate:modelValue":c=>n.expressNo=c,style:{height:"28px"},maxlength:"40",onChange:c=>g(c,n)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)]),_:2},1024)]),_:1})]),_:1},8,["data"])}}}),ye="",u=(i,o)=>{const _=i.__vccOpts||i;for(const[l,g]of o)_[l]=g;return _},P=u(X,[["__scopeId","data-v-9e08f012"]]),H={class:"orderIndex-table"},W={style:{"margin-right":"36px"}},J={style:{"margin-right":"36px"}},K={class:"orderIndex-table__img-box"},Y={class:"orderIndex-table__img-mask"},Z={class:"orderIndex-table__img-mask--name"},Q={class:"orderIndex-table__img-mask--integral"},ee={style:{color:"#838383","font-size":"10px"}},te={class:"avatar_text avatar_text__bottom money_text"},ae={style:{color:"#2e99f3","margin-right":"10px"}},oe={class:"money_text"},ne=e.defineComponent({__name:"un-express-table",props:{tabData:{type:Array,default:()=>[]}},emits:["filterOrderList"],setup(i,{emit:o}){const _=i;return(l,g)=>{const b=e.resolveComponent("el-button"),d=e.resolveComponent("el-row"),x=e.resolveComponent("el-image");return e.openBlock(),e.createElementBlock("div",H,[e.createVNode(e.unref(S),{data:_.tabData},{header:e.withCtx(({row:s})=>[e.createElementVNode("div",W,"订单号:"+e.toDisplayString(s.no),1),e.createElementVNode("div",J,"创建时间:"+e.toDisplayString(s.createTime),1),e.createVNode(d,{style:{flex:"1"},justify:"end"},{default:e.withCtx(()=>[e.createVNode(b,{type:"primary",link:"",size:"small",onClick:C=>o("filterOrderList",s)},{default:e.withCtx(()=>[e.createTextVNode("移除")]),_:2},1032,["onClick"])]),_:2},1024)]),default:e.withCtx(()=>[e.createVNode(h,{label:"商品",align:"left"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",K,[e.createVNode(x,{fits:"cover",style:{width:"63px",height:"63px"},shape:"square",size:"large",src:s.image,title:s.productName},null,8,["src","title"]),e.createElementVNode("div",Y,[e.createElementVNode("span",Z,e.toDisplayString(s.productName),1),e.createElementVNode("div",Q,[e.createElementVNode("span",null,"积分"+e.toDisplayString(s.price),1),e.createElementVNode("span",ee,"共"+e.toDisplayString(s.num)+"件",1)])])])]),_:1}),e.createVNode(h,{label:"客户",align:"center"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",te,[e.createElementVNode("span",ae,"买家昵称 : "+e.toDisplayString(s.buyerNickname),1),e.createElementVNode("div",oe,"(收货人："+e.toDisplayString(s.integralOrderReceiverVO.name)+","+e.toDisplayString(s.integralOrderReceiverVO.mobile)+")",1)])]),_:1})]),_:1},8,["data"])])}}}),fe="",re=u(ne,[["__scopeId","data-v-ba715d6b"]]),v="addon-integral/integral/"+"order/",le=i=>T.put({url:v+"deliver/",data:i}),se=()=>T.get({url:v+"deliver/batch/undeliver"}),ie={class:"DatchDelivery_container__tool"},de=e.defineComponent({__name:"DeliveryList",setup(i){const o=e.ref([]),_=L.useRouter();e.ref({size:1e3,current:1,total:0});const l=e.reactive({deliverType:"EXPRESS",expressCompany:{logisticsCompanyCode:"",logisticsCompanyName:"",expressNo:""}}),g=[{key:"EXPRESS",value:"手动发货"},{key:"WITHOUT",value:"无需物流发货"}],b=e.reactive({deliverType:[{required:!0,trigger:"blur",message:"请选择"}],addressaddress:[{required:!0,trigger:"change",message:"请选择服务"}],printId:[{required:!0,trigger:"change",message:"请选择"}],logisticsCompanyCode:[{validator(t,a,r,p,y){if(!l.expressCompany.logisticsCompanyCode){r(new Error("请选择物流服务"));return}r()}}]}),d=I.map(t=>({label:t.companyName,value:t.companyCode,companyType:t.companyType}));s();function x(t){const a=o.value.filter(p=>p.no!==t.no);o.value=a,new N().setItem("integralOrderSendGoods",a,24*60*60*1e3)}function s(){o.value=new N().getItem("integralOrderSendGoods")??[],o.value.forEach(t=>{t.expressCompanyName="",t.expressNo=""})}async function C(){const{data:t,code:a}=await se();if(console.log("data",t),a===200){m.ElMessage.success("导入成功"),t.forEach(r=>{r.integralOrderReceiverVO=r.integralOrderReceiver,r.expressCompanyName="",r.expressNo=""}),o.value=t;return}m.ElMessage.error("导入失败")}const D=async()=>{let t;switch(l.deliverType){case"EXPRESS":t=w();break;default:t=V();break}t&&E(t)};function w(){return console.log("abData.value",o.value),o.value.every(a=>a.expressCompanyName&&a.expressNo)?o.value.map(a=>{const{no:r,expressCompanyName:p,expressNo:y}=a;return{integralOrderNo:r,integralOrderDeliverType:l.deliverType,expressCompanyName:p,expressNo:y}}):(m.ElMessage.error({message:"请选择物流服务或填写运单号"}),!1)}const V=()=>o.value.map(a=>{const{no:r}=a;return{integralOrderNo:r,integralOrderDeliverType:l.deliverType}});async function E(t){const{code:a,data:r}=await le(t);if(a!==200)return m.ElMessage.error({message:"发货失败"});new N().setItem("integralOrderSendGoods",[],60*60*24),m.ElMessage.success({message:"发货成功"}),_.replace({name:"integralMall",query:{type:"order"}})}const n=t=>{o.value.forEach(a=>a.expressCompanyName=t)},c=()=>{new N().setItem("integralOrderSendGoods",[],60*60*24),_.replace({name:"integralMall",query:{type:"order"}})},ce=(t,a)=>{const r=o.value.find(p=>p.no===a.no);r&&(r.expressNo=t)},pe=(t,a)=>{const r=o.value.find(p=>p.no===a.no);r&&(r.expressCompanyName=t)};return(t,a)=>{const r=e.resolveComponent("el-radio"),p=e.resolveComponent("el-radio-group"),y=e.resolveComponent("el-form-item"),me=e.resolveComponent("el-select-v2"),k=e.resolveComponent("el-button"),q=e.resolveComponent("el-row"),_e=e.resolveComponent("el-form"),xe=e.resolveComponent("el-popconfirm");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(_e,{ref:"DatchDeliveryFormRef",model:l,rules:b,class:"DatchDelivery_container"},{default:e.withCtx(()=>[e.createVNode(y,{label:"发货方式",prop:"deliverType","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:l.deliverType,"onUpdate:modelValue":a[0]||(a[0]=f=>l.deliverType=f),class:"ml-4",size:"default"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(g,f=>e.createVNode(r,{key:f.key,label:f.key,size:"small"},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(f.value),1)]),_:2},1032,["label"])),64))]),_:1},8,["modelValue"])]),_:1}),e.withDirectives(e.createVNode(y,{label:"物流公司",prop:"logisticsCompanyCode","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(me,{modelValue:l.expressCompany.logisticsCompanyCode,"onUpdate:modelValue":a[1]||(a[1]=f=>l.expressCompany.logisticsCompanyCode=f),options:e.unref(d),placeholder:"请选择物流公司",style:{width:"444px"},onChange:n},null,8,["modelValue","options"])]),_:1},512),[[e.vShow,l.deliverType!=="WITHOUT"]]),e.createVNode(y,null,{default:e.withCtx(()=>[e.createVNode(q,{justify:"end",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(k,{type:"primary",link:"",onClick:C},{default:e.withCtx(()=>[e.createTextVNode("导入全部待发货订单")]),_:1})]),_:1})]),_:1}),e.createVNode(y,null,{default:e.withCtx(()=>[e.withDirectives(e.createVNode(P,{"table-data":o.value,onFilterOrderList:x,onExpressNo:ce,onExpressCompanyName:pe},null,8,["table-data"]),[[e.vShow,l.deliverType==="EXPRESS"]]),e.withDirectives(e.createVNode(re,{"tab-data":o.value,onFilterOrderList:x},null,8,["tab-data"]),[[e.vShow,!["EXPRESS"].includes(l.deliverType)]])]),_:1})]),_:1},8,["model","rules"]),e.createElementVNode("div",ie,[e.createVNode(q,{justify:"center",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(k,{round:"",type:"primary",onClick:D},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1}),e.createVNode(xe,{title:"确定退出批量发货?",onConfirm:c},{reference:e.withCtx(()=>[e.createVNode(k,{round:""},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1})]),_:1})]),_:1})])])}}}),he="";return u(de,[["__scopeId","data-v-fd0daa28"]])});
