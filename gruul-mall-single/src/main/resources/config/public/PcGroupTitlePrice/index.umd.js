(function(e,t){typeof exports=="object"&&typeof module<"u"?module.exports=t(require("vue"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","@/composables/useConvert"],t):(e=typeof globalThis<"u"?globalThis:e||self,e.PcGroupTitlePrice=t(e.PcGroupTitlePriceContext.Vue,e.PcGroupTitlePriceContext.UseConvert))})(this,function(e,t){"use strict";const s={"c-fs-26":"","c-c-e31436":"","fw-700":"","c-ml-14":""},p=e.createElementVNode("span",{"c-fs-16":""},null,-1);return e.defineComponent({__name:"PcGroupTitlePrice",setup(f){const{divTenThousand:n}=t();return(o,u)=>{var r,c,i;return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createTextVNode(" 拼团价 "),e.createElementVNode("span",s,[e.createTextVNode("￥"+e.toDisplayString(e.unref(n)((i=(c=(r=o.priceVal)==null?void 0:r[0])==null?void 0:c.prices)==null?void 0:i[0]).toFixed(2)||e.unref(n)(o.usersPrices.prices).toFixed(2)||"0")+" ",1),p])],64)}}})});
