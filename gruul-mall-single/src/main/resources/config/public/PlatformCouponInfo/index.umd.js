(function(e,_){typeof exports=="object"&&typeof module<"u"?module.exports=_(require("vue"),require("vue-router"),require("element-plus"),require("@vueuse/core"),require("@/utils/date"),require("@/composables/useConvert"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","element-plus","@vueuse/core","@/utils/date","@/composables/useConvert","@/apis/http"],_):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformCouponInfo=_(e.PlatformCouponInfoContext.Vue,e.PlatformCouponInfoContext.VueRouter,e.PlatformCouponInfoContext.ElementPlus,e.PlatformCouponInfoContext.VueUse,e.PlatformCouponInfoContext.DateUtil,e.PlatformCouponInfoContext.UseConvert,e.PlatformCouponInfoContext.Request))})(this,function(e,_,b,D,ne,T,S){"use strict";var N=document.createElement("style");N.textContent=`@charset "UTF-8";.title[data-v-e4502790]{font-size:14px;color:#606266;font-weight:700;margin-bottom:60px}.msg[data-v-e4502790]{font-size:12px;margin-left:12px;color:#c4c4c4}.rules[data-v-e4502790]{display:flex;justify-content:space-between}.rules__input[data-v-e4502790]{width:50%}.period-validity[data-v-e4502790]{width:300px;display:flex}.text[data-v-e4502790]{font-size:14px;color:#333}.goodsData[data-v-e4502790]{border:1px solid #ccc}.goods-list[data-v-e4502790]{width:90%;margin-top:40px;height:360px;border:1px solid transparent}.goods-list__info[data-v-e4502790]{display:flex}.goods-list__goods-list__info-name[data-v-e4502790]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-e4502790]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-e4502790]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-e4502790]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-e4502790]{font-size:16px}.ruleform-date[data-v-e4502790]{width:100%;display:flex}.nav-button[data-v-e4502790]{position:fixed;bottom:50px;left:50%}
`,document.head.appendChild(N);const{divTenThousand:k,mulTenThousand:de}=T(),y={PRICE_DISCOUNT:"无门槛折扣券",PRICE_REDUCE:"无门槛现金券",REQUIRED_PRICE_DISCOUNT:"满折券",REQUIRED_PRICE_REDUCE:"满减券"};var w=(a=>(a.PERIOD="PERIOD",a.IMMEDIATELY="IMMEDIATELY",a))(w||{}),E=(a=>(a.ALL="ALL",a.SHOP_ALL="SHOP_ALL",a.ASSIGNED="ASSIGNED",a.ASSIGNED_NOT="ASSIGNED_NOT",a))(E||{});function g(a){return a?k(a).toString():""}function U(a){const{name:i,days:r,type:p,endDate:t,productIds:c,productType:u,requiredAmount:o,discount:d,amount:n,effectType:f,startDate:x,num:V,limit:h,id:m,shopId:C}=a;return{shopId:C,id:m,name:i,days:r,endDate:t,productType:u,type:p,requiredAmount:g(o).toString(),discount:d,amount:g(n),effectType:f,startDate:x,num:V,limit:h,productIds:c}}const R=e.defineComponent({__name:"select-couppon-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(a,{emit:i}){const r=a,p=D.useVModel(r,"modelValue",i);return(t,c)=>{const u=e.resolveComponent("el-option"),o=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(o,{modelValue:e.unref(p),"onUpdate:modelValue":c[0]||(c[0]=d=>e.isRef(p)?p.value=d:null),placeholder:r.placeholder,style:{width:"150px"},onChange:c[1]||(c[1]=d=>i("change",d))},{default:e.withCtx(()=>[e.renderSlot(t.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Object.values(r.list).length?r.list:e.unref(y),(d,n)=>(e.openBlock(),e.createBlock(u,{key:n,label:d,value:n},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),P=(a,i)=>S.get({url:`addon-coupon/coupon/shop/${a}/${i}`}),s=a=>(e.pushScopeId("data-v-e4502790"),a=a(),e.popScopeId(),a),A={style:{padding:"0 40px"}},B=s(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),q=s(()=>e.createElementVNode("span",{class:"msg"},"优惠券名称不超过5个字",-1)),L={key:0,class:"ruleform-date"},O=s(()=>e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1)),Y={class:"period-validity text"},M={class:"rules"},z=s(()=>e.createElementVNode("span",{style:{"margin-left":"10px"}},"折，无门槛使用",-1)),j={class:"rules"},F=s(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"元，无门槛使用",-1)),G={key:2,class:"rules",style:{"justify-content":"center"}},$=s(()=>e.createElementVNode("span",null,"满",-1)),H=s(()=>e.createElementVNode("span",null,"元,打",-1)),Q=s(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"折",-1)),W={key:3,class:"rules",style:{"justify-content":"center"}},J=s(()=>e.createElementVNode("span",null,"满",-1)),K=s(()=>e.createElementVNode("span",null,"元,减",-1)),X=s(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"元",-1)),Z={class:"goods-list__info"},v={class:"goods-list__goods-list__info-name"},ee={class:"goods-list__goods-list__info-name--name"},te={class:"goods-list__goods-list__info-name--price"},oe=e.defineComponent({__name:"PlatformCouponInfo",setup(a){const i=_.useRouter(),r=_.useRoute(),p=e.ref([]),t=e.ref({name:"",days:"",endDate:"",productType:E.ALL,type:"PRICE_DISCOUNT",requiredAmount:"",discount:"",amount:"",effectType:w.PERIOD,startDate:"",num:"",limit:"",shopId:"",productIds:[]});c();async function c(){if(r.query.id&&r.query.shopId){const{code:u,data:o}=await P(r.query.shopId.toString(),r.query.id.toString());if(u!==200){b.ElMessage.error("获取优惠券信息失败");return}t.value=U(o);return}}return(u,o)=>{const d=e.resolveComponent("el-input"),n=e.resolveComponent("el-form-item"),f=e.resolveComponent("el-radio"),x=e.resolveComponent("el-radio-group"),V=e.resolveComponent("el-row"),h=e.resolveComponent("el-date-picker"),m=e.resolveComponent("el-table-column"),C=e.resolveComponent("el-table"),I=e.resolveComponent("el-image"),le=e.resolveComponent("el-form"),ae=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",A,[B,e.createVNode(le,{"inline-message":!1,model:t.value,"validate-on-rule-change":!1,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(n,{label:"优惠券名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:t.value.name,"onUpdate:modelValue":o[0]||(o[0]=l=>t.value.name=l),modelModifiers:{trim:!0},disabled:"",maxlength:"5",placeholder:"请输入优惠券名称",style:{width:"551px"}},null,8,["modelValue"]),q]),_:1}),e.createVNode(n,{label:"有效时间",prop:"effectType"},{default:e.withCtx(()=>[e.createVNode(V,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:t.value.effectType,"onUpdate:modelValue":o[1]||(o[1]=l=>t.value.effectType=l),class:"ml-4",disabled:""},{default:e.withCtx(()=>[e.createVNode(f,{label:"PERIOD"},{default:e.withCtx(()=>[e.createTextVNode("固定时间")]),_:1}),e.createVNode(f,{label:"IMMEDIATELY"},{default:e.withCtx(()=>[e.createTextVNode("领券立即生效")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(V,{style:{"margin-top":"20px"}},{default:e.withCtx(()=>[t.value.effectType==="PERIOD"?(e.openBlock(),e.createElementBlock("div",L,[e.createVNode(n,{"inline-message":!1,"label-width":"0",prop:"startDate"},{default:e.withCtx(()=>[e.createVNode(h,{modelValue:t.value.startDate,"onUpdate:modelValue":o[2]||(o[2]=l=>t.value.startDate=l),disabled:"",format:"YYYY/MM/DD",placeholder:"请选择开始时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"]),O]),_:1}),e.createVNode(n,{"inline-message":!1,"label-width":"0",prop:"endDate"},{default:e.withCtx(()=>[e.createVNode(h,{modelValue:t.value.endDate,"onUpdate:modelValue":o[3]||(o[3]=l=>t.value.endDate=l),disabled:"",format:"YYYY/MM/DD",placeholder:"请选择结束时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})])):(e.openBlock(),e.createBlock(n,{key:1,"label-width":"0",prop:"days"},{default:e.withCtx(()=>[e.createElementVNode("div",Y,[e.createTextVNode(" 领券当日起 "),e.createVNode(d,{modelValue:t.value.days,"onUpdate:modelValue":o[4]||(o[4]=l=>t.value.days=l),disabled:"",style:{width:"20%",margin:"0 5px"}},null,8,["modelValue"]),e.createTextVNode(" 天内可用 ")])]),_:1}))]),_:1})]),_:1}),e.createVNode(n,{label:"活动规则",prop:"type"},{default:e.withCtx(()=>[e.createVNode(C,{data:[{}],"header-cell-style":{textAlign:"center",fontSize:"14px",color:"#606266"},border:"",style:{width:"90%"}},{default:e.withCtx(()=>[e.createVNode(m,{label:"选择优惠券类型"},{default:e.withCtx(()=>[e.createVNode(n,{"label-width":"0",prop:"type"},{default:e.withCtx(()=>[e.createVNode(R,{modelValue:t.value.type,"onUpdate:modelValue":o[5]||(o[5]=l=>t.value.type=l),list:e.unref(y),disabled:"",placeholder:"全部类型"},null,8,["modelValue","list"])]),_:1})]),_:1}),e.createVNode(m,{label:"优惠券规则",width:"240"},{default:e.withCtx(()=>[t.value.type==="PRICE_DISCOUNT"?(e.openBlock(),e.createBlock(n,{key:0,"label-width":"0",prop:"discount"},{default:e.withCtx(()=>[e.createElementVNode("div",M,[e.createVNode(d,{modelValue:t.value.discount,"onUpdate:modelValue":o[6]||(o[6]=l=>t.value.discount=l),disabled:"",style:{flex:"1"}},null,8,["modelValue"]),z])]),_:1})):e.createCommentVNode("",!0),t.value.type==="PRICE_REDUCE"?(e.openBlock(),e.createBlock(n,{key:1,"label-width":"0",prop:"amount"},{default:e.withCtx(()=>[e.createElementVNode("div",j,[e.createVNode(d,{modelValue:t.value.amount,"onUpdate:modelValue":o[7]||(o[7]=l=>t.value.amount=l),disabled:"",style:{flex:"1"}},null,8,["modelValue"]),F])]),_:1})):e.createCommentVNode("",!0),t.value.type==="REQUIRED_PRICE_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",G,[e.createVNode(n,{"label-width":"0%",prop:"requiredAmount"},{default:e.withCtx(()=>[$,e.createVNode(d,{modelValue:t.value.requiredAmount,"onUpdate:modelValue":o[8]||(o[8]=l=>t.value.requiredAmount=l),disabled:"",style:{width:"60%","margin-left":"5px"}},null,8,["modelValue"])]),_:1}),e.createVNode(n,{"label-width":"0%",prop:"discount"},{default:e.withCtx(()=>[H,e.createVNode(d,{modelValue:t.value.discount,"onUpdate:modelValue":o[9]||(o[9]=l=>t.value.discount=l),disabled:"",style:{width:"50%","margin-left":"5px"}},null,8,["modelValue"]),Q]),_:1})])):e.createCommentVNode("",!0),t.value.type==="REQUIRED_PRICE_REDUCE"?(e.openBlock(),e.createElementBlock("div",W,[e.createVNode(n,{"label-width":"0%",prop:"requiredAmount"},{default:e.withCtx(()=>[J,e.createVNode(d,{modelValue:t.value.requiredAmount,"onUpdate:modelValue":o[10]||(o[10]=l=>t.value.requiredAmount=l),disabled:"",style:{width:"60%","margin-left":"5px"}},null,8,["modelValue"])]),_:1}),e.createVNode(n,{"label-width":"0%",prop:"amount"},{default:e.withCtx(()=>[K,e.createVNode(d,{modelValue:t.value.amount,"onUpdate:modelValue":o[11]||(o[11]=l=>t.value.amount=l),disabled:"",style:{width:"50%","margin-left":"5px"}},null,8,["modelValue"]),X]),_:1})])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(m,{label:"发行量（张）"},{default:e.withCtx(()=>[e.createVNode(n,{"label-width":"0",prop:"num"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:t.value.num,"onUpdate:modelValue":o[12]||(o[12]=l=>t.value.num=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(m,{label:"限领（张）"},{default:e.withCtx(()=>[e.createVNode(n,{"label-width":"0",prop:"num"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:t.value.limit,"onUpdate:modelValue":o[13]||(o[13]=l=>t.value.limit=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),e.createVNode(n,{label:"商品选择",prop:"productType"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:t.value.productType,"onUpdate:modelValue":o[14]||(o[14]=l=>t.value.productType=l),disabled:""},{default:e.withCtx(()=>[e.createVNode(f,{label:"SHOP_ALL"},{default:e.withCtx(()=>[e.createTextVNode("全部商品参与")]),_:1}),e.createVNode(f,{label:"ASSIGNED"},{default:e.withCtx(()=>[e.createTextVNode("指定商品参与")]),_:1}),e.createVNode(f,{label:"ASSIGNED_NOT"},{default:e.withCtx(()=>[e.createTextVNode("指定商品不参与")]),_:1})]),_:1},8,["modelValue"]),e.createElementVNode("div",{class:e.normalizeClass([p.value.length&&"goodsData","goods-list"])},[t.value.productType!=="SHOP_ALL"?(e.openBlock(),e.createBlock(C,{key:0,data:p.value,"header-cell-style":{fontSize:"14px",color:"#606266",background:"#f2f2f2",height:"54px",fontWeight:"normal"},height:"360px",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{label:"商品信息"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",Z,[e.createVNode(I,{"preview-src-list":[l.pic],"preview-teleported":!0,src:l.pic,fit:"fit",style:{width:"60px",height:"60px"}},null,8,["preview-src-list","src"]),e.createElementVNode("div",v,[e.createElementVNode("div",ee,e.toDisplayString(l.name),1),e.createElementVNode("div",te,e.toDisplayString(l.salePrice),1)])])]),_:1}),e.createVNode(m,{label:"操作",width:"80px"})]),_:1},8,["data"])):e.createCommentVNode("",!0)],2)]),_:1})]),_:1},8,["model"])]),e.createVNode(V,{class:"nav-button",justify:"center"},{default:e.withCtx(()=>[e.createVNode(ae,{plain:"",round:"",onClick:o[15]||(o[15]=l=>e.unref(i).back())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1})]),_:1})])}}}),re="";return((a,i)=>{const r=a.__vccOpts||a;for(const[p,t]of i)r[p]=t;return r})(oe,[["__scopeId","data-v-e4502790"]])});
