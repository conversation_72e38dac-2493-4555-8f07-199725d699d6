(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("@vueuse/core"),require("element-plus"),require("@/composables/useConvert"),require("@element-plus/icons-vue"),require("@/components/pageManageS/PageManage.vue"),require("@/apis/http"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","element-plus","@/composables/useConvert","@element-plus/icons-vue","@/components/pageManageS/PageManage.vue","@/apis/http","vue-router"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformSeckillList=V(e.PlatformSeckillListContext.Vue,e.PlatformSeckillListContext.VueUse,e.PlatformSeckillListContext.ElementPlus,e.PlatformSeckillListContext.UseConvert,e.PlatformSeckillListContext.ElementPlusIconsVue,e.PlatformSeckillListContext.PageManage,e.PlatformSeckillListContext.Request,e.PlatformSeckillListContext.VueRouter))})(this,function(e,V,c,L,B,T,y,M){"use strict";L();const w={NOT_STARTED:"未开始",PROCESSING:"进行中",OVER:"已结束",ILLEGAL_SELL_OFF:"违规下架"},D=e.defineComponent({__name:"select-live-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(d,{emit:u}){const p=d,o=V.useVModel(p,"modelValue",u);return(h,l)=>{const m=e.resolveComponent("el-option"),_=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(_,{modelValue:e.unref(o),"onUpdate:modelValue":l[0]||(l[0]=f=>e.isRef(o)?o.value=f:null),placeholder:p.placeholder,style:{width:"150px"},onChange:l[1]||(l[1]=f=>u("change",f))},{default:e.withCtx(()=>[e.renderSlot(h.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Object.values(p.list).length?p.list:e.unref(w),(f,g)=>(e.openBlock(),e.createBlock(m,{key:g,label:f,value:g},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),O=e.defineComponent({__name:"head-search2",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},title:{type:String,default:"新增直播间"}},emits:["update:modelValue","batchDel","search"],setup(d,{emit:u}){const p=d,o=V.useVModel(p,"modelValue",u);return(h,l)=>{const m=e.resolveComponent("el-button"),_=e.resolveComponent("el-col"),f=e.resolveComponent("el-option"),g=e.resolveComponent("el-space"),k=e.resolveComponent("el-input"),N=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(N,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px"}},{default:e.withCtx(()=>[e.createVNode(_,{span:14},{default:e.withCtx(()=>[e.createVNode(m,{plain:"",round:"",onClick:l[0]||(l[0]=r=>u("batchDel"))},{default:e.withCtx(()=>[e.createTextVNode("批量移除")]),_:1})]),_:1}),e.createVNode(_,{span:9},{default:e.withCtx(()=>[e.createVNode(g,null,{default:e.withCtx(()=>[e.createVNode(D,{modelValue:e.unref(o).status,"onUpdate:modelValue":l[1]||(l[1]=r=>e.unref(o).status=r),onChange:l[2]||(l[2]=r=>u("search"))},{default:e.withCtx(()=>[e.createVNode(f,{value:"",label:"全部状态"})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(k,{modelValue:e.unref(o).keywords,"onUpdate:modelValue":l[4]||(l[4]=r=>e.unref(o).keywords=r),placeholder:"输入关键词",style:{width:"55%"}},{append:e.withCtx(()=>[e.createVNode(m,{icon:e.unref(B.Search),onClick:l[3]||(l[3]=r=>u("search"))},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),S="addon-seckill/seckillPromotion/",z=d=>y.get({url:S+"secKillList",params:d}),E=d=>y.del({url:S+"platform/del",data:d}),R=d=>y.put({url:S+`sellOff/${d}`}),F={class:"goods"};return e.defineComponent({__name:"PlatformSeckillList",setup(d){const{divTenThousand:u}=L(),p=e.ref({keywords:"",status:""}),o=e.ref({size:10,current:1}),h=e.ref(0),l=M.useRouter(),m=e.ref([]),_=e.ref([]),f=n=>{c.ElMessageBox.confirm("确定下架该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",callback:async a=>{if(a==="cancel")return;const{code:i,data:C}=await R(n.id);if(i!==200){c.ElMessage.error("下架失败");return}c.ElMessage.success("下架成功");const x=m.value.find(t=>t.id===n.id);x&&(x.seckillStatus="ILLEGAL_SELL_OFF")},type:"warning"})},g=async()=>{if(!_.value.length){c.ElMessage.info("请选择需要删除的商品");return}const n=_.value.map(i=>i.id),{code:s,data:a}=await E(n);if(s!==200){c.ElMessage.error("删除失败");return}c.ElMessage.success("删除成功"),r()},k=n=>{l.push({name:"secondsKillEdit",query:{secKillId:n.id,shopId:n.shopId}})},N=()=>{r()};async function r(){const{status:n,keywords:s}=p.value,a={...o.value,secKillStatus:n,keyword:s},{code:i,data:C}=await z(a);if(i!==200)return c.ElMessage.error("获取活动列表失败");m.value=C.records,o.value.current=C.current,o.value.size=C.size,h.value=C.total}const q=n=>{c.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",callback:async s=>{if(s==="cancel")return;const{code:a}=await E([n.id]);if(a!==200){c.ElMessage.error("删除失败");return}c.ElMessage.success("删除成功"),m.value=m.value.filter(i=>i.id!==n.id),h.value--},type:"warning"})},P=n=>{o.value.size=n,r()},$=n=>{o.value.current=n,r()};return(n,s)=>{const a=e.resolveComponent("el-table-column"),i=e.resolveComponent("el-link"),C=e.resolveComponent("el-row"),x=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(O,{modelValue:p.value,"onUpdate:modelValue":s[0]||(s[0]=t=>p.value=t),onSearch:N,onBatchDel:g},null,8,["modelValue"]),e.createVNode(x,{ref:"multipleTableRef","cell-style":{fontSize:"12px",color:"#333333"},data:m.value,"header-cell-style":{background:"#f6f8fa"},"header-row-style":{fontSize:"12px",color:"#909399"},height:"calc(100vh - 250px)",stripe:"",onSelectionChange:s[1]||(s[1]=t=>_.value=t)},{default:e.withCtx(()=>[e.createVNode(a,{type:"selection",width:"55"}),e.createVNode(a,{label:"店铺名称",width:"160"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",F,e.toDisplayString(t.shopName),1)]),_:1}),e.createVNode(a,{label:"活动名称"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.secKillName),1)]),_:1}),e.createVNode(a,{align:"center",label:"活动时间",width:"250"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",null,e.toDisplayString(t.startTime),1),e.createElementVNode("div",null,e.toDisplayString(t.endTime),1)]),_:1}),e.createVNode(a,{align:"center",label:"参加人数"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.peopleNum||0),1)]),_:1}),e.createVNode(a,{align:"center",label:"应收金额"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,"￥"+e.toDisplayString(t.amountReceivable&&e.unref(u)(t.amountReceivable)||0),1)]),_:1}),e.createVNode(a,{align:"center",label:"状态"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:t.seckillStatus==="ILLEGAL_SELL_OFF"?"#F12F22":""})},e.toDisplayString(e.unref(w)[t.seckillStatus]),5)]),_:1}),e.createVNode(a,{align:"center",fixed:"right",label:"操作",width:"200px"},{default:e.withCtx(({row:t})=>[e.createVNode(C,{justify:"center",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(i,{underline:!1,size:"small",style:{padding:"0 10px"},type:"primary",onClick:b=>k(t)},{default:e.withCtx(()=>[e.createTextVNode(" 查看 ")]),_:2},1032,["onClick"]),["ILLEGAL_SELL_OFF"].includes(t.seckillStatus)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(i,{key:0,underline:!1,size:"small",style:{padding:"0 10px"},type:"primary",onClick:b=>f(t)},{default:e.withCtx(()=>[e.createTextVNode(" 下架 ")]),_:2},1032,["onClick"])),e.createVNode(i,{underline:!1,size:"small",style:{padding:"0 10px"},type:"primary",onClick:b=>q(t)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),e.createVNode(C,{align:"middle",justify:"end"},{default:e.withCtx(()=>[e.createVNode(T,{modelValue:o.value,"onUpdate:modelValue":s[2]||(s[2]=t=>o.value=t),"load-init":!0,total:h.value,onReload:r,onHandleSizeChange:P,onHandleCurrentChange:$},null,8,["modelValue","total"])]),_:1})])}}})});
