(function(e,r){typeof exports=="object"&&typeof module<"u"?module.exports=r(require("vue"),require("@/components/PageManage.vue"),require("element-plus"),require("@/apis/http"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","@/components/PageManage.vue","element-plus","@/apis/http","@/composables/useConvert"],r):(e=typeof globalThis<"u"?globalThis:e||self,e.VipIntegralSubsidiary=r(e.VipIntegralSubsidiaryContext.Vue,e.VipIntegralSubsidiaryContext.PageManageTwo,e.VipIntegralSubsidiaryContext.ElementPlus,e.VipIntegralSubsidiaryContext.Request,e.VipIntegralSubsidiaryContext.UseConvert))})(this,function(e,r,d,u,g){"use strict";const _=i=>u.get({url:"gruul-mall-user/user/integral/detail/info",params:i}),m={key:0};return e.defineComponent({__name:"VipIntegralSubsidiary",props:{properties:{type:Object,required:!0}},setup(i){const p=i;g();const n=e.reactive({current:1,size:10,total:0}),c=e.ref([]),C=t=>{n.size=t,n.current=1,o()},f=t=>{n.current=t,o()},h={DAY_LOGIN:"每日登入",INTEGRAL_PRODUCT_EXCHANGE:"积分商品兑换",DAY_SHARE:"每日分享",INTEGRAL_CLEAR:"积分清空",DAY_SIGN_IN:"每日签到",SYSTEM_RECHARGE:"系统充值",SYSTEM_DEDUCT:"系统扣除",ORDER_CONSUMPTION:"订单消费",ORDER_CANCEL:"订单取消"};e.watch(()=>p.properties.userId,t=>{t&&o()},{immediate:!0});async function o(){const{code:t,data:l}=await _({userId:p.properties.userId,...n});t===200?(c.value=l.records,n.total=l.total):d.ElMessage.error("获取交易明细失败")}function y(t){return t?h[t]:""}return(t,l)=>{const s=e.resolveComponent("el-table-column"),I=e.resolveComponent("el-table"),S=e.resolveComponent("el-tab-pane");return e.openBlock(),e.createBlock(S,{label:"积分明细",name:"theIntegralSubsidiary"},{default:e.withCtx(()=>[e.createVNode(I,{data:c.value,"header-row-style":{background:"#f6f8fa"},"row-style":{height:"60px"},style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(s,{label:"变动类型"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,[e.createTextVNode(e.toDisplayString(y(a.gainIntegralType))+" ",1),a.particulars?(e.openBlock(),e.createElementBlock("span",m,"("+e.toDisplayString(a.particulars)+")",1)):e.createCommentVNode("",!0)])]),_:1}),e.createVNode(s,{label:"积分变动",prop:"createTime"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,e.toDisplayString(a.changeType==="INCREASE"?"+":"-")+" "+e.toDisplayString(a.variationIntegral),1)]),_:1}),e.createVNode(s,{label:"变动时间",prop:"createTime"})]),_:1},8,["data"]),e.createVNode(r,{"page-num":n.current,"page-size":n.size,total:n.total,onHandleSizeChange:C,onHandleCurrentChange:f},null,8,["page-num","page-size","total"])]),_:1})}}})});
