(function(e,F){typeof exports=="object"&&typeof module<"u"?module.exports=F(require("vue"),require("vue-router"),require("decimal.js"),require("element-plus"),require("vue-clipboard3"),require("element-china-area-data"),require("@/components/q-address"),require("@/composables/useConvert"),require("@/components/q-upload/q-upload.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","decimal.js","element-plus","vue-clipboard3","element-china-area-data","@/components/q-address","@/composables/useConvert","@/components/q-upload/q-upload.vue","@/apis/http"],F):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopPurchaseOrderInfo=F(e.ShopPurchaseOrderInfoContext.Vue,e.ShopPurchaseOrderInfoContext.VueRouter,e.ShopPurchaseOrderInfoContext.Decimal,e.ShopPurchaseOrderInfoContext.ElementPlus,e.ShopPurchaseOrderInfoContext.VueClipboard3,e.ShopPurchaseOrderInfoContext.ElementChinaAreaData,e.ShopPurchaseOrderInfoContext.QAddressIndex,e.ShopPurchaseOrderInfoContext.UseConvert,e.ShopPurchaseOrderInfoContext.QUpload,e.ShopPurchaseOrderInfoContext.Request))})(this,function(e,F,O,b,Ie,Te,Se,ke,Oe,ce){"use strict";var _e=document.createElement("style");_e.textContent=`.details__status[data-v-8452c5bc]{display:flex;align-items:center;border:1px solid #d5d5d5}.details__status--steps[data-v-8452c5bc]{padding:20px;margin:20px;width:700px;border-left:1px solid #d5d5d5}.details__status--title[data-v-8452c5bc]{font-size:28px;font-weight:700;color:#515151;width:223px;text-align:center}.details__userInfo[data-v-8452c5bc]{display:flex;margin-bottom:22px;padding:0 30px}.details__userInfo--left[data-v-8452c5bc]{flex:.5}.details__userInfo--left div[data-v-8452c5bc]:nth-of-type(n+2){margin-bottom:11px}.details__userInfo--right[data-v-8452c5bc]{margin-left:30px;flex:.5}.details__userInfo--right div[data-v-8452c5bc]:nth-of-type(n+2){margin-bottom:11px}.details__userInfo--title[data-v-8452c5bc]{font-size:14px;color:#333;font-weight:700;margin:17px 0}.details__table--main[data-v-8452c5bc]{height:300px}.details__table--main .commodity[data-v-8452c5bc]{display:flex;align-items:stretch}.details__table--main .commodity .commodity__info[data-v-8452c5bc]{margin-left:15px;flex:1;overflow:hidden;display:flex;flex-direction:column;justify-content:flex-start;align-items:flex-start;text-align:left}.details__table--main .commodity .commodity__info--title[data-v-8452c5bc]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;width:100%}.details__table--main .commodity .commodity__info--spec[data-v-8452c5bc]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;font-size:.8em;width:100%}.details__subtotal[data-v-8452c5bc]{display:flex;flex-direction:column;align-items:flex-end;width:100%;margin-top:30px;line-height:1.5}.details__subtotal--title[data-v-8452c5bc]{font-size:1.3em;font-weight:600}.details__subtotal .pay-price[data-v-8452c5bc]{font-size:1.2em}.details .text-red[data-v-8452c5bc]{color:red}.proof-img[data-v-8452c5bc]{width:350px;height:350px;object-fit:contain}.copy[data-v-8452c5bc]{margin-left:5px;color:#409eff;cursor:pointer}.logisticsInfo[data-v-7700283f]{display:flex;justify-content:space-between}.logisticsInfo__left[data-v-7700283f]{flex:3}.logisticsInfo__right[data-v-7700283f]{flex:2}.logisticsInfo__timeline-con[data-v-7700283f]{max-height:500px;overflow:auto}.logisticsInfo__title[data-v-7700283f]{font-size:14px;color:#333;font-weight:700;margin:17px 0}.logisticsInfo__text[data-v-7700283f]{margin-bottom:11px}.logisticsInfo__timeline[data-v-7700283f]{margin-top:20px}.logisticsInfo__timeline li[data-v-7700283f]:nth-child(1) .el-timeline-item__timestamp{color:#000}.logisticsInfo__timeline--status[data-v-7700283f]{font-size:13px;font-weight:700;margin-right:10px;color:#838383}.logisticsInfo__timeline--time[data-v-7700283f]{color:#838383}.logisticsInfo__divider[data-v-7700283f]{height:4px;margin:0 -15px;background:#f2f2f2}
`,document.head.appendChild(_e);const Pe=(a={})=>ce.put({url:"addon-supplier/supplier/order/pay",data:a}),we=a=>ce.get({url:`addon-supplier/supplier/order/delivery/${a}`}),De=a=>ce.get({url:`addon-supplier/supplier/order/${a}`}),Be=(a,r)=>ce.get({url:"gruul-mall-freight/logistics/node",params:{companyCode:a,waybillNo:r}}),$e=e.defineComponent({__name:"ShopPurchaseOrderInfo",setup(a){const r=e.ref("basic"),o={basic:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Vt)),delivery:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Mt))},t=e.ref(0),i=F.useRoute(),l=e.ref();async function y(){if(i.query.orderNo){const{code:_,data:n}=await De(i.query.orderNo);l.value=n,t.value=Date.now()}}const f=e.computed(()=>{var _,n;return(n=(_=l==null?void 0:l.value)==null?void 0:_.orderItems)==null?void 0:n.filter(c=>c.packageStatus!=="WAITING_FOR_DELIVER").length});return y(),(_,n)=>{const c=e.resolveComponent("el-tab-pane"),x=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(x,{modelValue:r.value,"onUpdate:modelValue":n[0]||(n[0]=V=>r.value=V)},{default:e.withCtx(()=>[e.createVNode(c,{label:"订单信息",name:"basic"}),f.value&&f.value>0?(e.openBlock(),e.createBlock(c,{key:0,label:"物流信息",name:"delivery"})):e.createCommentVNode("",!0)]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o[r.value]),{key:t.value,order:l.value,reload:y},null,8,["order"]))])}}}),Fe={待支付:1,待审核:2,待发货:2,部分发货:2,待入库:3,已完成:4,已关闭:1},Re={OFFLINE:"线下支付",BALANCE:"余额支付"},{divTenThousand:pe}=ke(),{toClipboard:me}=Ie(),Ae=(a,r)=>{const o=e.ref(!1),t=e.ref(),i=e.ref(null);t.value=a;const l=e.reactive({statusText:"",activeStep:0}),y=l.statusText=qe(a);l.activeStep=Fe[y];const f=async()=>{var C,T;const m=await((C=i.value)==null?void 0:C.getPayOrderFormModel()),{code:h,msg:I}=await Pe({...m,orderNo:(T=t.value)==null?void 0:T.no});h===200?(b.ElMessage.success("支付成功"),o.value=!1,r&&r()):b.ElMessage.error(I||"支付失败")},_=()=>{o.value=!0},n=async m=>{try{await me(m),b.ElMessage.success("复制成功")}catch{b.ElMessage.error("复制失败")}},c=async()=>{var h,I,C,T,p,E,u,P,w,D,N,B,S;const m=`
            收货人姓名：${(C=(I=(h=t==null?void 0:t.value)==null?void 0:h.extra)==null?void 0:I.receiver)==null?void 0:C.name}

            联系人电话：${(E=(p=(T=t==null?void 0:t.value)==null?void 0:T.extra)==null?void 0:p.receiver)==null?void 0:E.mobile}

            收货地址：${Se.AddressFn(Te.regionData,((P=(u=t==null?void 0:t.value)==null?void 0:u.extra)==null?void 0:P.receiver.areaCode)||[])}${(N=(D=(w=t==null?void 0:t.value)==null?void 0:w.extra)==null?void 0:D.receiver)==null?void 0:N.address}

            采购备注：${(S=(B=t==null?void 0:t.value)==null?void 0:B.extra)==null?void 0:S.remark}
        `;try{await me(m),b.ElMessage.success("复制成功")}catch{b.ElMessage.error("复制失败")}},x=e.computed(()=>m=>Me(m)),V=e.computed(()=>m=>Le(m));return{orderDetails:t,stepInfo:l,payTypeMap:Re,divTenThousand:pe,computedCalculateFreight:x,computedCalculateCommodityPrice:V,handlePayOrder:f,showPayDialog:o,handlePay:_,payOrderRef:i,copyOrderNo:n,handleCopyReceiver:c}},qe=a=>{if(!a)return"";if(a.status==="UNPAID")return"待支付";if(a.status==="PAYMENT_AUDIT")return"待审核";const r=["WAITING_FOR_DELIVER","WAITING_FOR_RECEIVE","COMPLETED"],o={WAITING_FOR_DELIVER:"待发货",WAITING_FOR_RECEIVE:"待入库",COMPLETED:"已完成"};if(a.status==="PAID"){let t="COMPLETED";for(const l of a.orderItems){const y=r.findIndex(f=>f===t);if(l.packageStatus==="WAITING_FOR_DELIVER"&&y>0){t="WAITING_FOR_DELIVER";continue}l.packageStatus==="WAITING_FOR_RECEIVE"&&y>1&&(t="WAITING_FOR_RECEIVE")}let i=o[t];return i==="待发货"&&a.orderItems.find(y=>y.packageStatus!=="WAITING_FOR_DELIVER")&&(i="部分发货"),i}return"已关闭"},Me=(a=[])=>a.reduce((r,o)=>r.plus(new O(pe(o.freightPrice))),new O(0)),Le=(a=[])=>a.reduce((r,o)=>r.plus(new O(pe(o.salePrice).mul(new O(o.num)))),new O(0)),je=e.defineComponent({__name:"pay-order",props:{price:{default:()=>new O(0)}},setup(a,{expose:r}){const o=a,t=e.reactive({payType:"",proof:""}),i={payType:[{required:!0,message:"请选择支付方式",trigger:"blur"}],proof:[{validator:(f,_,n)=>{t.payType==="OFFLINE"&&(_||n(new Error("请上传凭证"))),n()},trigger:"change"}]},l=e.ref(null);return r({getPayOrderFormModel:()=>new Promise((f,_)=>{l.value?l.value.validate(n=>{n?f(t):_("valid error")}):_("none instance")})}),(f,_)=>{const n=e.resolveComponent("el-form-item"),c=e.resolveComponent("el-radio"),x=e.resolveComponent("el-radio-group"),V=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(V,{ref_key:"formRef",ref:l,model:t,rules:i},{default:e.withCtx(()=>[e.createVNode(n,{label:"应付款(元)"},{default:e.withCtx(()=>[e.createTextVNode("￥"+e.toDisplayString(o.price),1)]),_:1}),e.createVNode(n,{label:"支付方式",prop:"payType"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:t.payType,"onUpdate:modelValue":_[0]||(_[0]=m=>t.payType=m)},{default:e.withCtx(()=>[e.createVNode(c,{label:"OFFLINE"},{default:e.withCtx(()=>[e.createTextVNode("线下付款")]),_:1}),e.createVNode(c,{label:"BALANCE"},{default:e.withCtx(()=>[e.createTextVNode("店铺余额")]),_:1})]),_:1},8,["modelValue"])]),_:1}),t.payType==="OFFLINE"?(e.openBlock(),e.createBlock(n,{key:0,label:"付款凭证",prop:"proof"},{default:e.withCtx(()=>[e.createVNode(Oe,{src:t.proof,"onUpdate:src":_[1]||(_[1]=m=>t.proof=m),format:{size:1},height:100,width:100},null,8,["src"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model"])}}}),ze=()=>{const a=e.ref(!1),r=e.ref("");return{showProof:a,goToShowProof:t=>{var i,l;r.value=((l=(i=t==null?void 0:t.extra)==null?void 0:i.pay)==null?void 0:l.proof)||"",a.value=!0},currentProof:r}},q=a=>(e.pushScopeId("data-v-8452c5bc"),a=a(),e.popScopeId(),a),We={class:"details"},Ue={class:"details__status"},Ge={class:"details__status--title"},He={class:"details__status--steps"},Qe={class:"details__userInfo"},Ye={class:"details__userInfo--left"},Je=q(()=>e.createElementVNode("div",{class:"details__userInfo--title"},"订单信息",-1)),Ke=q(()=>e.createElementVNode("div",null,"配送方式：快递配送",-1)),Xe={class:"details__userInfo--left"},Ze={class:"details__userInfo--title"},ve=q(()=>e.createElementVNode("span",null,"收货人信息",-1)),et={class:"details__userInfo--right"},tt=q(()=>e.createElementVNode("div",{class:"details__userInfo--title"},"供应商信息",-1)),ot={class:"details__table"},at={class:"commodity"},nt={class:"commodity__info"},st={class:"commodity__info--title"},lt={class:"commodity__info--spec"},rt={class:"details__subtotal"},it=q(()=>e.createElementVNode("div",{class:"details__subtotal--title"},"订单总计",-1)),ct={class:"details__subtotal--line"},dt={class:"details__subtotal--line"},pt={class:"text-red"},_t={class:"details__subtotal--line"},mt={class:"text-red"},ft={class:"details__subtotal--line pay-price"},yt={class:"text-red"},ht={class:"dialog-footer"},gt=["src"],xt={class:"dialog-footer"},Nt=e.defineComponent({__name:"basic",props:{order:{type:Object,default:()=>({})},reload:{type:Object,default:()=>({})}},setup(a){const r=a,{orderDetails:o,stepInfo:t,payTypeMap:i,divTenThousand:l,computedCalculateFreight:y,computedCalculateCommodityPrice:f,handlePayOrder:_,showPayDialog:n,handlePay:c,payOrderRef:x,copyOrderNo:V,handleCopyReceiver:m}=Ae(r.order,r.reload),{showProof:h,goToShowProof:I,currentProof:C}=ze();return(T,p)=>{var R,M,L,j,z,W,U,G,H,Q,Y,J,K,X,Z,v,ee,k,A,te,oe,ae,ne,se,le,d,$,ye,he,ge,xe,Ne,Ve,Ee;const E=e.resolveComponent("el-button"),u=e.resolveComponent("el-step"),P=e.resolveComponent("el-steps"),w=e.resolveComponent("q-address"),D=e.resolveComponent("el-image"),N=e.resolveComponent("el-table-column"),B=e.resolveComponent("el-table"),S=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",We,[e.createElementVNode("div",Ue,[e.createElementVNode("div",Ge,[e.createElementVNode("p",null,e.toDisplayString(e.unref(t).statusText),1),e.unref(t).statusText==="待支付"?(e.openBlock(),e.createBlock(E,{key:0,type:"danger",onClick:e.unref(c)},{default:e.withCtx(()=>[e.createTextVNode("去支付")]),_:1},8,["onClick"])):e.createCommentVNode("",!0)]),e.createElementVNode("div",He,[e.createVNode(P,{active:e.unref(t).activeStep,"align-center":"","finish-status":"finish"},{default:e.withCtx(()=>{var s,g,re,ie,Ce,ue,be;return[e.createVNode(u,{description:(s=e.unref(o))==null?void 0:s.createTime,title:"买家下单"},null,8,["description"]),e.createVNode(u,{description:(re=(g=e.unref(o))==null?void 0:g.timeNodes)==null?void 0:re.payTime,title:"买家付款"},null,8,["description"]),e.createVNode(u,{description:(Ce=(ie=e.unref(o))==null?void 0:ie.timeNodes)==null?void 0:Ce.deliveryTime,title:"卖家发货"},null,8,["description"]),e.createVNode(u,{description:(be=(ue=e.unref(o))==null?void 0:ue.timeNodes)==null?void 0:be.receiveTime,title:"买家收货"},null,8,["description"])]}),_:1},8,["active"])])]),e.createElementVNode("div",Qe,[e.createElementVNode("div",Ye,[Je,e.createElementVNode("div",null,[e.createElementVNode("span",null,"订单编号："+e.toDisplayString((R=e.unref(o))==null?void 0:R.no),1),e.createElementVNode("span",{class:"copy",onClick:p[0]||(p[0]=s=>{var g;return e.unref(V)(((g=e.unref(o))==null?void 0:g.no)||"")})},"复制")]),e.createElementVNode("div",null,"下单时间："+e.toDisplayString((M=e.unref(o))==null?void 0:M.createTime),1),e.createElementVNode("div",null,"支付时间："+e.toDisplayString((j=(L=e.unref(o))==null?void 0:L.timeNodes)==null?void 0:j.payTime),1),e.createElementVNode("div",null,[e.createElementVNode("span",null,"支付方式："+e.toDisplayString(e.unref(i)[(U=(W=(z=e.unref(o))==null?void 0:z.extra)==null?void 0:W.pay)==null?void 0:U.payType]),1),((Q=(H=(G=e.unref(o))==null?void 0:G.extra)==null?void 0:H.pay)==null?void 0:Q.payType)==="OFFLINE"?(e.openBlock(),e.createElementBlock("span",{key:0,class:"copy",onClick:p[1]||(p[1]=s=>{var g,re,ie;return e.unref(I)({extra:{pay:{proof:((ie=(re=(g=e.unref(o))==null?void 0:g.extra)==null?void 0:re.pay)==null?void 0:ie.proof)||""}}})})},"付款凭证")):e.createCommentVNode("",!0)]),Ke]),e.createElementVNode("div",Xe,[e.createElementVNode("div",Ze,[ve,e.createElementVNode("span",{class:"copy",onClick:p[2]||(p[2]=(...s)=>e.unref(m)&&e.unref(m)(...s))},"复制")]),e.createElementVNode("div",null,"收货人姓名："+e.toDisplayString((K=(J=(Y=e.unref(o))==null?void 0:Y.extra)==null?void 0:J.receiver)==null?void 0:K.name),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString((v=(Z=(X=e.unref(o))==null?void 0:X.extra)==null?void 0:Z.receiver)==null?void 0:v.mobile),1),e.createElementVNode("div",null,[e.createTextVNode(" 收货地址： "),e.createVNode(w,{address:(k=(ee=e.unref(o))==null?void 0:ee.extra)==null?void 0:k.receiver.areaCode},null,8,["address"]),e.createTextVNode(" "+e.toDisplayString((oe=(te=(A=e.unref(o))==null?void 0:A.extra)==null?void 0:te.receiver)==null?void 0:oe.address),1)]),e.createElementVNode("div",null,"采购备注："+e.toDisplayString((ne=(ae=e.unref(o))==null?void 0:ae.extra)==null?void 0:ne.remark),1)]),e.createElementVNode("div",et,[tt,e.createElementVNode("div",null,"供应商名称："+e.toDisplayString((le=(se=e.unref(o))==null?void 0:se.extraInfo)==null?void 0:le.supplierName),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString(($=(d=e.unref(o))==null?void 0:d.extraInfo)==null?void 0:$.supplierPhone),1)])]),e.createElementVNode("div",ot,[e.createVNode(B,{data:(ye=e.unref(o))==null?void 0:ye.orderItems,border:"",class:"details__table--main"},{default:e.withCtx(()=>[e.createVNode(N,{align:"center",label:"商品",width:"460"},{default:e.withCtx(({row:s})=>{var g;return[e.createElementVNode("div",at,[e.createVNode(D,{src:s==null?void 0:s.image,fit:"cover",style:{width:"70px",height:"70px"}},null,8,["src"]),e.createElementVNode("div",nt,[e.createElementVNode("span",st,e.toDisplayString(s==null?void 0:s.productName),1),e.createElementVNode("span",lt,e.toDisplayString((g=s==null?void 0:s.specs)==null?void 0:g.join(";")),1)])])]}),_:1}),e.createVNode(N,{align:"center",label:"采购单价"},{default:e.withCtx(({row:s})=>[e.createTextVNode(e.toDisplayString(e.unref(l)(s==null?void 0:s.salePrice)),1)]),_:1}),e.createVNode(N,{align:"center",label:"采购数量",prop:"num"}),e.createVNode(N,{align:"center",label:"采购金额"},{default:e.withCtx(({row:s})=>[e.createTextVNode(e.toDisplayString(e.unref(l)(s==null?void 0:s.salePrice).mul(new e.unref(O)(s.num))),1)]),_:1}),e.createVNode(N,{label:"实际入库量",prop:"used",align:"center"})]),_:1},8,["data"])]),e.createElementVNode("div",rt,[it,e.createElementVNode("div",ct,"采购总数："+e.toDisplayString((ge=(he=e.unref(o))==null?void 0:he.orderItems)==null?void 0:ge.reduce((s,g)=>s+g.num,0)),1),e.createElementVNode("div",dt,[e.createTextVNode(" 商品总价："),e.createElementVNode("span",pt,e.toDisplayString(e.unref(f)((xe=e.unref(o))==null?void 0:xe.orderItems)),1)]),e.createElementVNode("div",_t,[e.createTextVNode(" 运费："),e.createElementVNode("span",mt,e.toDisplayString(e.unref(y)((Ne=e.unref(o))==null?void 0:Ne.orderItems)),1)]),e.createElementVNode("div",ft,[e.createTextVNode(" 采购金额("+e.toDisplayString(((Ve=e.unref(o))==null?void 0:Ve.status)==="PAID"?"已付款":"应付款")+")：",1),e.createElementVNode("span",yt,e.toDisplayString(e.unref(l)((Ee=e.unref(o))==null?void 0:Ee.payAmount))+"元",1)])])]),e.createVNode(S,{modelValue:e.unref(n),"onUpdate:modelValue":p[4]||(p[4]=s=>e.isRef(n)?n.value=s:null),title:"支付订单",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",ht,[e.createVNode(E,{onClick:p[3]||(p[3]=s=>n.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(E,{type:"primary",onClick:e.unref(_)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1},8,["onClick"])])]),default:e.withCtx(()=>{var s;return[e.createVNode(je,{ref_key:"payOrderRef",ref:x,price:e.unref(l)((s=e.unref(o))==null?void 0:s.payAmount)},null,8,["price"])]}),_:1},8,["modelValue"]),e.createVNode(S,{modelValue:e.unref(h),"onUpdate:modelValue":p[7]||(p[7]=s=>e.isRef(h)?h.value=s:null),title:"付款凭证",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",xt,[e.createVNode(E,{onClick:p[5]||(p[5]=s=>h.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(E,{type:"primary",onClick:p[6]||(p[6]=s=>h.value=!1)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("img",{src:e.unref(C),class:"proof-img"},null,8,gt)]),_:1},8,["modelValue"])],64)}}}),zt="",fe=(a,r)=>{const o=a.__vccOpts||a;for(const[t,i]of r)o[t]=i;return o},Vt=Object.freeze(Object.defineProperty({__proto__:null,default:fe(Nt,[["__scopeId","data-v-8452c5bc"]])},Symbol.toStringTag,{value:"Module"})),de=a=>(e.pushScopeId("data-v-7700283f"),a=a(),e.popScopeId(),a),Et={style:{"padding-left":"30px","margin-bottom":"30px"}},Ct=de(()=>e.createElementVNode("div",{style:{color:"#000","font-size":"16px","margin-left":"-10px","font-weight":"700","margin-bottom":"10px"}},"收货人信息",-1)),ut=de(()=>e.createElementVNode("span",null,"收货地址：",-1)),bt={key:0},It={key:1},Tt={class:"logisticsInfo"},St={class:"logisticsInfo__left"},kt={style:{display:"flex","align-items":"center"}},Ot={key:0,style:{"margin-top":"20px"}},Pt=de(()=>e.createElementVNode("div",{class:"logisticsInfo__title"},"物流详情",-1)),wt={class:"logisticsInfo__right"},Dt=de(()=>e.createElementVNode("div",{class:"logisticsInfo__title"},"物流信息",-1)),Bt={key:0},$t={class:"logisticsInfo__text"},Ft={class:"logisticsInfo__text"},Rt={class:"logisticsInfo__text"},At={key:1,style:{"text-align":"center",height:"100px","line-height":"100px"}},qt=e.defineComponent({__name:"delivery",props:{order:{type:Object,default(){return{}}}},setup(a){const r=a,o=F.useRoute(),t=e.ref([]),i=e.ref([]),l=e.ref(0);y();async function y(){const{code:n,data:c}=await we(o.query.orderNo);if(n!==200)return b.ElMessage.error("包裹详情获取失败");c.length&&(i.value=c,f())}const f=async()=>{const n=i.value[l.value];if(n.type!=="WITHOUT")try{const{data:c,code:x,msg:V}=await Be(n.express.expressCompanyCode,n.express.expressNo);if(x!==200)return b.ElMessage.error("包裹详情获取失败");if(!c.data){_(c,n);return}t.value=c.data.reverse()}catch(c){t.value=[{status:"包裹异常",time:n==null?void 0:n.createTime,context:c.msg}]}};function _(n,c){switch(n.returnCode){case"401":t.value=[{status:"包裹异常",time:c.createTime,context:n.message}];break;case"400":t.value=[{status:"包裹异常",time:c.createTime,context:n.message}];break;default:t.value=[{status:"包裹异常",time:c.createTime,context:n.message}];break}}return(n,c)=>{var P,w,D,N,B,S,R,M,L,j,z,W,U,G,H,Q,Y,J,K,X,Z,v,ee;const x=e.resolveComponent("q-address"),V=e.resolveComponent("el-image"),m=e.resolveComponent("el-table-column"),h=e.resolveComponent("el-table"),I=e.resolveComponent("el-row"),C=e.resolveComponent("el-timeline-item"),T=e.resolveComponent("el-timeline"),p=e.resolveComponent("el-scrollbar"),E=e.resolveComponent("el-tab-pane"),u=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",Et,[Ct,e.createElementVNode("div",null,"收货人姓名："+e.toDisplayString(((D=(w=(P=i.value)==null?void 0:P[0])==null?void 0:w.receiver)==null?void 0:D.name)||((S=(B=(N=r.order)==null?void 0:N.extra)==null?void 0:B.receiver)==null?void 0:S.name)),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString(((L=(M=(R=i.value)==null?void 0:R[0])==null?void 0:M.receiver)==null?void 0:L.mobile)||((W=(z=(j=r.order)==null?void 0:j.extra)==null?void 0:z.receiver)==null?void 0:W.mobile)),1),e.createElementVNode("div",null,[ut,(H=(G=(U=i.value)==null?void 0:U[0])==null?void 0:G.receiver)!=null&&H.address?(e.openBlock(),e.createElementBlock("span",bt,e.toDisplayString((J=(Y=(Q=i.value)==null?void 0:Q[0])==null?void 0:Y.receiver)==null?void 0:J.address),1)):(e.openBlock(),e.createElementBlock("span",It,[e.createVNode(x,{address:(X=(K=r.order)==null?void 0:K.extra)==null?void 0:X.receiver.areaCode},null,8,["address"]),e.createElementVNode("span",null,e.toDisplayString((ee=(v=(Z=r.order)==null?void 0:Z.extra)==null?void 0:v.receiver)==null?void 0:ee.address),1)]))])]),e.createVNode(u,{modelValue:l.value,"onUpdate:modelValue":c[0]||(c[0]=k=>l.value=k),type:"card",onTabChange:f},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value,(k,A)=>(e.openBlock(),e.createBlock(E,{key:A,label:"包裹"+(A+1),name:A},{default:e.withCtx(()=>{var te,oe,ae,ne,se,le;return[e.createElementVNode("div",Tt,[e.createElementVNode("div",St,[e.createVNode(h,{data:k.orderItems,border:"",style:{width:"500px"}},{default:e.withCtx(()=>[e.createVNode(m,{label:"商品 ",width:"400px"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",kt,[e.createVNode(V,{src:d==null?void 0:d.image,title:d==null?void 0:d.productName,fits:"cover",shape:"square",size:"large",style:{width:"70px",height:"70px","margin-right":"10px"}},null,8,["src","title"]),e.createElementVNode("div",null,[e.createElementVNode("div",null,e.toDisplayString(d==null?void 0:d.productName),1),e.createElementVNode("div",null,e.toDisplayString(d==null?void 0:d.specs),1)])])]),_:1}),e.createVNode(m,{align:"center",label:"发货数"},{default:e.withCtx(({row:d})=>[e.createTextVNode(e.toDisplayString(d==null?void 0:d.num),1)]),_:1})]),_:2},1032,["data"]),k.type!=="WITHOUT"?(e.openBlock(),e.createElementBlock("div",Ot,[Pt,e.createVNode(p,{ref_for:!0,ref:"scrollbarRef",height:"500px"},{default:e.withCtx(()=>[e.createVNode(T,{class:"logisticsInfo__timeline"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,(d,$)=>(e.openBlock(),e.createBlock(C,{key:d.time+$,color:$===0?"#409eff":" ",timestamp:`${d.context}`,class:"logisticsInfo__timeline--item",style:{"padding-bottom":"42px"}},{default:e.withCtx(()=>[e.createVNode(I,null,{default:e.withCtx(()=>[e.createElementVNode("div",{style:e.normalizeStyle({color:$===0?"#409eff":" "}),class:"logisticsInfo__timeline--status"},e.toDisplayString(d.status),5),e.createElementVNode("div",{style:e.normalizeStyle({color:$===0?"#409eff":" "}),class:"logisticsInfo__timeline--time"},e.toDisplayString(d.time),5)]),_:2},1024)]),_:2},1032,["color","timestamp"]))),128))]),_:2},1024)]),_:2},1536)])):e.createCommentVNode("",!0)]),e.createElementVNode("div",wt,[Dt,k.type!=="WITHOUT"?(e.openBlock(),e.createElementBlock("div",Bt,[e.createElementVNode("div",$t,"收货地址："+e.toDisplayString((oe=(te=i.value[l.value])==null?void 0:te.receiver)==null?void 0:oe.address),1),e.createElementVNode("div",Ft,"物流公司："+e.toDisplayString((ne=(ae=i.value[l.value])==null?void 0:ae.express)==null?void 0:ne.expressCompanyName),1),e.createElementVNode("div",Rt,"物流单号："+e.toDisplayString((le=(se=i.value[l.value])==null?void 0:se.express)==null?void 0:le.expressNo),1)])):(e.openBlock(),e.createElementBlock("div",At,"无需物流"))])])]}),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])],64)}}}),Ut="",Mt=Object.freeze(Object.defineProperty({__proto__:null,default:fe(qt,[["__scopeId","data-v-7700283f"]])},Symbol.toStringTag,{value:"Module"}));return $e});
