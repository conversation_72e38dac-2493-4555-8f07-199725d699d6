(function(e,h){typeof exports=="object"&&typeof module<"u"?module.exports=h(require("vue"),require("@/components/PageManage.vue"),require("element-plus"),require("@/apis/http"),require("@/composables/useConvert"),require("@/store/modules/shopInfo")):typeof define=="function"&&define.amd?define(["vue","@/components/PageManage.vue","element-plus","@/apis/http","@/composables/useConvert","@/store/modules/shopInfo"],h):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopDecorationSeckillSelectGood=h(e.ShopDecorationSeckillSelectGoodContext.Vue,e.ShopDecorationSeckillSelectGoodContext.PageManageTwo,e.ShopDecorationSeckillSelectGoodContext.ElementPlus,e.ShopDecorationSeckillSelectGoodContext.Request,e.ShopDecorationSeckillSelectGoodContext.UseConvert,e.ShopDecorationSeckillSelectGoodContext.ShopInfoStore))})(this,function(e,h,_,C,N,w){"use strict";var y=document.createElement("style");y.textContent=`.title[data-v-1b7ca19b]{font-size:15px;font-weight:700;display:flex;margin-bottom:20px;margin-top:-40px}.digGoods[data-v-1b7ca19b]{border-top:1px solid #d7d7d7;padding-top:10px}.digGoods__box[data-v-1b7ca19b]{background-color:#f2f2f2;padding:10px}.digGoods__box--top[data-v-1b7ca19b]{display:flex;justify-content:space-between}.digGoods__box--content[data-v-1b7ca19b]{margin-top:10px;background-color:#fff;border-radius:5px;display:flex;flex-wrap:wrap;padding:5px}.digGoods__box--content--good[data-v-1b7ca19b]{width:33%;margin-left:2px;margin-bottom:4px;height:80px;border-radius:5px;padding:5px;display:flex}.digGoods__box--content--good--img[data-v-1b7ca19b]{width:65px;height:65px;position:relative}.digGoods__box--content--good--imgShadow[data-v-1b7ca19b]{width:65px;height:65px;position:absolute;background-color:#0009;display:flex;justify-content:center;align-items:center}.digGoods__box--content--good--shopName[data-v-1b7ca19b]{margin-left:10px;font-size:12px;display:flex;flex-direction:column;justify-content:space-between}.digGoods__box--bottom[data-v-1b7ca19b]{display:flex;justify-content:space-between;align-items:center}.serachBtn[data-v-1b7ca19b]{width:32px;height:32px;display:flex;justify-content:center;align-items:center;border:1px solid #dcdfe6;background-color:#fff;cursor:pointer;border-left:none;border-radius:4px}
`,document.head.appendChild(y);const B=d=>C.get({url:`addon-seckill/seckillPromotion/notStartedAndProcessing?size=1000&shopId=${d}`}),D=d=>C.get({url:"addon-seckill/seckillPromotion/secKillProduct",params:d}),z=(d=>(e.pushScopeId("data-v-1b7ca19b"),d=d(),e.popScopeId(),d))(()=>e.createElementVNode("div",{class:"title"},"选择商品",-1)),T={class:"digGoods"},j={class:"digGoods__box"},M={class:"digGoods__box--top"},P={key:0,class:"digGoods__box--content"},q=["onClick"],L=["src"],v={key:0,class:"digGoods__box--content--good--imgShadow"},O={class:"digGoods__box--content--good--shopName"},$={key:1,class:"digGoods__box--content",style:{display:"flex","justify-content":"center","align-items":"center",height:"250px"}},A={class:"digGoods__box--bottom"},F=e.defineComponent({__name:"ShopDecorationSeckillSelectGood",props:{properties:{type:Object,default:{}}},setup(d,{expose:k}){const m=d,x=w.useShopInfoStore(),{divTenThousand:u}=N(),p=e.ref([]),g=e.ref([]),G=e.ref([]),a=e.ref(!1),s=e.shallowReactive({current:1,size:10,total:0,choosedSession:""});e.watch(m.properties,async o=>{o.goodsVisible&&(Q(),g.value=JSON.parse(JSON.stringify(o.pointGoodsList)),W(),await X(),b())},{immediate:!0}),k({tempGoods:g,search:s,goodsList:p,allChecked:a});const V={borderGet:"2px solid #2D8CF0",borderNoGet:"2px solid #f2f2f2"},U=o=>{s.choosedSession=o,b()},H=o=>{o.isCheck=!o.isCheck;const t=g.value;if(t.length>=16)return _.ElMessage.error("最多选择16个商品"),!1;if(o.isCheck)t.push(o);else{const c=t.findIndex(i=>i.productId===o.productId);c!==-1&&t.splice(c,1)}},J=()=>{let o=!1;const t=p.value,c=g.value,i=a.value;if(t.map(l=>{if(i){if(c.length>=16)return o=!0;c.find(r=>r.productId===l.productId)||c.push(l)}return l.isCheck=i}),o)return _.ElMessage.error("自多选择16个商品");i||t.forEach(l=>{const r=c.findIndex(f=>f.productId===l.productId);r!==-1&&c.splice(r,1)})},K=o=>{s.size=o,b()},R=o=>{s.current=o,a.value=!1,b()};async function Q(){s.current=1,s.size=10,a.value=!1}async function W(){let o=!0;p.value.forEach(t=>{const c=I(t.productId);t.isCheck=c,o=o&&c}),a.value=o}async function X(){const{code:o,data:t}=await B(x.getterShopInfo.id);o===200?t.records.length&&(G.value=t.records,s.choosedSession=t.records[0].startTime):_.ElMessage.error("获取场次失败")}async function b(){const{current:o,size:t,choosedSession:c}=s;if(!s.choosedSession)return _.ElMessage.warning("请先选中场次");const{code:i,data:l}=await D({current:o,size:t,startTime:c,shopId:x.getterShopInfo.id});if(i!==200)return _.ElMessage.error("获取商品失败");if(l.records.length){let r=!0;l.records.forEach(f=>{const S=I(f.productId);f.isCheck=S,r=r&&S}),a.value=r,p.value=l.records,s.total=l.total}}function I(o){return g.value.findIndex(t=>t.productId===o)!==-1}return(o,t)=>{const c=e.resolveComponent("el-option"),i=e.resolveComponent("el-option-group"),l=e.resolveComponent("el-select"),r=e.resolveComponent("i-ep-check"),f=e.resolveComponent("el-icon"),S=e.resolveComponent("el-checkbox");return e.openBlock(),e.createElementBlock("div",null,[z,e.createElementVNode("div",T,[e.createElementVNode("div",j,[e.createElementVNode("div",M,[e.createVNode(l,{modelValue:e.unref(s).choosedSession,"onUpdate:modelValue":t[0]||(t[0]=n=>e.unref(s).choosedSession=n),style:{width:"190px"},placeholder:"全部分类"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(G.value,n=>(e.openBlock(),e.createBlock(i,{key:n.startTime},{default:e.withCtx(()=>[e.createVNode(c,{label:n.startTime,value:n.startTime,onClick:E=>U(n.startTime)},null,8,["label","value","onClick"])]),_:2},1024))),128))]),_:1},8,["modelValue"])]),p.value.length>0?(e.openBlock(),e.createElementBlock("div",P,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(p.value,(n,E)=>(e.openBlock(),e.createElementBlock("div",{key:E,class:"digGoods__box--content--good",style:e.normalizeStyle({border:n.isCheck?V.borderGet:V.borderNoGet}),onClick:te=>H(n)},[e.createElementVNode("img",{src:n.productPic,class:"digGoods__box--content--good--img"},null,8,L),n.isCheck?(e.openBlock(),e.createElementBlock("div",v,[e.createVNode(f,{color:"#fff",size:"40px"},{default:e.withCtx(()=>[e.createVNode(r)]),_:1})])):e.createCommentVNode("",!0),e.createElementVNode("div",O,[e.createElementVNode("div",null,e.toDisplayString(n.productName),1),e.createElementVNode("div",null," ￥"+e.toDisplayString(e.unref(u)(n.minPrice))+"-￥"+e.toDisplayString(e.unref(u)(n.maxPrice)),1)])],12,q))),128))])):e.createCommentVNode("",!0),p.value.length===0?(e.openBlock(),e.createElementBlock("div",$," 暂无相关商品信息，请选择其他时段 ")):e.createCommentVNode("",!0),e.createElementVNode("div",A,[e.createVNode(S,{modelValue:a.value,"onUpdate:modelValue":t[1]||(t[1]=n=>a.value=n),style:{"margin-top":"40px"},onChange:J},{default:e.withCtx(()=>[e.createTextVNode("全选")]),_:1},8,["modelValue"]),e.createVNode(h,{"page-size":e.unref(s).size,"page-num":e.unref(s).current,total:e.unref(s).total,onHandleSizeChange:K,onHandleCurrentChange:R},null,8,["page-size","page-num","total"])])])])])}}}),Z="";return((d,k)=>{const m=d.__vccOpts||d;for(const[x,u]of k)m[x]=u;return m})(F,[["__scopeId","data-v-1b7ca19b"]])});
