(function(e,x){typeof exports=="object"&&typeof module<"u"?module.exports=x(require("vue"),require("@vueuse/core"),require("@element-plus/icons-vue"),require("vue-router"),require("@/components/PageManage.vue"),require("element-plus"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@element-plus/icons-vue","vue-router","@/components/PageManage.vue","element-plus","@/apis/http"],x):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformSetMeal=x(e.PlatformSetMealContext.Vue,e.PlatformSetMealContext.VueUse,e.PlatformSetMealContext.ElementPlusIconsVue,e.PlatformSetMealContext.VueRouter,e.PlatformSetMealContext.PageManageTwo,e.PlatformSetMealContext.ElementPlus,e.PlatformSetMealContext.Request))})(this,function(e,x,T,B,L,u,N){"use strict";var M=document.createElement("style");M.textContent=`.origin[data-v-c20339f4]{display:flex;justify-content:start;align-items:center}.origin--name[data-v-c20339f4]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(M);var y=(o=>(o.NOT_STARTED="未开始",o.PROCESSING="进行中",o.OVER="已结束",o.ILLEGAL_SELL_OFF="违规下架",o))(y||{});const z={style:{"margin-bottom":"15px",display:"flex","justify-content":"space-between"}},D=e.defineComponent({__name:"head-operation-p",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0}},emits:["update:modelValue","del","search"],setup(o,{emit:s}){const r=o,i=x.useVModel(r,"modelValue",s);return(_,n)=>{const t=e.resolveComponent("el-button"),c=e.resolveComponent("el-space"),d=e.resolveComponent("el-option"),w=e.resolveComponent("el-select"),b=e.resolveComponent("el-input");return e.openBlock(),e.createElementBlock("div",z,[e.createElementVNode("div",null,[e.createVNode(c,null,{default:e.withCtx(()=>[e.createVNode(t,{round:"",disabled:_.$props.batchDisabled,onClick:n[0]||(n[0]=p=>s("del"))},{default:e.withCtx(()=>[e.createTextVNode("批量删除")]),_:1},8,["disabled"])]),_:1})]),e.createElementVNode("div",null,[e.createVNode(c,null,{default:e.withCtx(()=>[e.createVNode(w,{modelValue:e.unref(i).setMealStatus,"onUpdate:modelValue":n[1]||(n[1]=p=>e.unref(i).setMealStatus=p),placeholder:"",style:{width:"200px"},onChange:n[2]||(n[2]=p=>s("search"))},{default:e.withCtx(()=>[e.createVNode(d,{label:"全部",value:""}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(y),(p,C)=>(e.openBlock(),e.createBlock(d,{key:C,label:p,value:C},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(c,null,{default:e.withCtx(()=>[e.createVNode(b,{modelValue:e.unref(i).keyword,"onUpdate:modelValue":n[4]||(n[4]=p=>e.unref(i).keyword=p),placeholder:"活动名称"},{append:e.withCtx(()=>[e.createVNode(t,{icon:e.unref(T.Search),onClick:n[3]||(n[3]=p=>s("search"))},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})])])}}}),k="addon-matching-treasure/setMeal/",P=o=>N.get({url:k,params:o}),E=o=>N.del({url:k,data:o}),I={class:"origin"},O={class:"origin--name",style:{width:"180px","margin-left":"10px"}},R=e.defineComponent({__name:"set-meal-list",props:{search:{type:Object,default:()=>({})}},setup(o,{expose:s}){const r=o,i=B.useRouter(),_=e.ref([]),n=e.ref(),t=e.reactive({size:10,current:1,total:0}),c=e.ref([]);e.watch(()=>r.search,l=>{d()},{deep:!0});async function d(){const{setMealStatus:l,keyword:m}=r.search,V={...t,...{setMealStatus:l,keyword:m}},{code:g,data:h}=await P(V);if(g!==200)return u.ElMessage.error("获取套餐列表失败");_.value=h.records,t.current=h.current,t.size=h.size,t.total=h.total}const w=l=>{i.push({name:"bundlePriceBaseinfo",query:{setMealId:l.id,shopId:l.shopId}})},b=async(l,m)=>{try{if(!await u.ElMessageBox.confirm("确定删除该套餐?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:V}=await E([{setMealId:l,shopId:m}]);if(V!==200){u.ElMessage.error("删除失败");return}u.ElMessage.success("删除成功"),d()}catch{return}},p=async l=>{const{code:m}=await E(l);if(m!==200){u.ElMessage.error("删除失败");return}u.ElMessage.success("删除成功"),d()},C=l=>{t.size=l,d()},j=l=>{t.current=l,d()};return e.onBeforeMount(()=>{d()}),s({chooseList:c,handleDelBatch:p}),(l,m)=>{const f=e.resolveComponent("el-table-column"),V=e.resolveComponent("el-image"),g=e.resolveComponent("el-button"),h=e.resolveComponent("el-col"),S=e.resolveComponent("el-row"),q=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(q,{ref_key:"multipleTableRef",ref:n,data:_.value,stripe:"",style:e.normalizeStyle({height:"calc(100vh - 280px)"}),"header-row-style":{fontSize:"12px",color:"#909399"},"header-cell-style":{background:"#f6f8fa"},"cell-style":{fontSize:"12px",color:"#333333"},onSelectionChange:m[0]||(m[0]=a=>c.value=a)},{default:e.withCtx(()=>[e.createVNode(f,{type:"selection",width:"55"}),e.createVNode(f,{label:"套餐信息",width:"250px"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",I,[e.createVNode(V,{style:{width:"40px",height:"40px"},src:a.setMealMainPicture},null,8,["src"]),e.createElementVNode("div",O,e.toDisplayString(a.setMealName),1)])]),_:1}),e.createVNode(f,{label:"套餐类型"},{default:e.withCtx(({row:a})=>[e.createTextVNode(e.toDisplayString(a.setMealType==="OPTIONAL_PRODUCT"?"自由搭配":"固定搭配"),1)]),_:1}),e.createVNode(f,{label:"开始时间",width:"180px",align:"center",prop:"startTime"}),e.createVNode(f,{label:"结束时间",width:"180px",align:"center",prop:"endTime"}),e.createVNode(f,{label:"状态",fixed:"right",align:"center"},{default:e.withCtx(({row:a})=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(y)[a.setMealStatus]),1)]),_:1}),e.createVNode(f,{label:"操作",fixed:"right",align:"center"},{default:e.withCtx(({row:a})=>[e.createVNode(S,{justify:"space-between",align:"middle"},{default:e.withCtx(()=>[e.createVNode(h,{span:10},{default:e.withCtx(()=>[e.createVNode(g,{link:"",type:"primary",size:"small",onClick:U=>w(a)},{default:e.withCtx(()=>[e.createTextVNode("查看")]),_:2},1032,["onClick"])]),_:2},1024),e.createVNode(h,{span:10},{default:e.withCtx(()=>[e.createVNode(g,{link:"",type:"primary",size:"small",onClick:U=>b(a.id,a.shopId)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:1})]),_:1},8,["data","style"]),e.createVNode(S,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(L,{modelValue:t,"onUpdate:modelValue":m[1]||(m[1]=a=>t=a),"load-init":!0,"page-size":t.size,total:t.total,onReload:d,onHandleSizeChange:C,onHandleCurrentChange:j},null,8,["modelValue","page-size","total"])]),_:1})],64)}}}),F="",$=((o,s)=>{const r=o.__vccOpts||o;for(const[i,_]of s)r[i]=_;return r})(R,[["__scopeId","data-v-c20339f4"]]);return e.defineComponent({__name:"PlatformSetMeal",setup(o){const s=e.reactive({setMealStatus:"",keyword:""}),r=e.ref(),i=e.computed(()=>r.value?!r.value.chooseList.length:!0),_=async()=>{if(!await u.ElMessageBox.confirm("确定批量删除套餐?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const t=r.value.chooseList.map(c=>({shopId:c.shopId,setMealId:c.id}));r.value.handleDelBatch(t)};return(n,t)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode(D,{modelValue:s,"onUpdate:modelValue":t[0]||(t[0]=c=>s=c),"batch-disabled":i.value,onDel:_},null,8,["modelValue","batch-disabled"]),e.createVNode($,{ref_key:"mealListRef",ref:r,search:s},null,8,["search"])]))}})});
