(function(e,p){typeof exports=="object"&&typeof module<"u"?module.exports=p(require("vue"),require("element-plus"),require("@/apis/http"),require("@/composables/useConvert"),require("lodash")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/apis/http","@/composables/useConvert","lodash"],p):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopPaidMember=p(e.ShopPaidMemberContext.Vue,e.ShopPaidMemberContext.ElementPlus,e.ShopPaidMemberContext.Request,e.ShopPaidMemberContext.UseConvert,e.ShopPaidMemberContext.Lodash))})(this,function(e,p,w,A,z){"use strict";var J=document.createElement("style");<PERSON>.textContent=`.title[data-v-8c01b25b]{font-size:14px;color:#323233;font-weight:700;margin-bottom:20px}.msg[data-v-8c01b25b]{font-size:12px;color:#c4c4c4}.nav-button[data-v-8c01b25b]{width:1010px;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin:0 auto 0 -55px}.level[data-v-dac90691]{font-size:12px;color:#ce732f}.pricing[data-v-dac90691]{width:147px;font-size:12px;color:#838383;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.interests[data-v-dac90691]{width:140px;font-size:12px;color:#333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(J);const F=()=>w.get({url:"addon-member/shop/paid/member/list "}),q=()=>w.get({url:"gruul-mall-user/user/member/rights/usable"}),j=i=>w.post({url:"addon-member/shop/paid/member/save",data:i}),Y=i=>w.post({url:"addon-member/shop/paid/member/update",data:i}),W=i=>w.get({url:`addon-member/shop/paid/member/info?id=${i}`}),K=i=>w.del({url:`addon-member/shop/paid/member/${i}`}),Q=()=>w.get({url:"gruul-mall-shop/shop/group/list"}),X=i=>(e.pushScopeId("data-v-8c01b25b"),i=i(),e.popScopeId(),i),Z={style:{padding:"0 40px"}},v=X(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),ee={style:{width:"450px"}},te={style:{width:"100%"}},oe=e.defineComponent({__name:"EditPayingShopMembers",props:{memberId:{type:String,default:""},memberLevel:{type:Number,default:void 0}},setup(i,{expose:M}){const u=i,{mulHundred:y,divHundred:m,divTenThousand:k,mulTenThousand:T}=A();e.ref(!1);const L=e.ref(),g=e.ref(new Map),D=e.ref([]),E=e.ref([]),V=e.ref(0),x=e.ref(0),a=e.reactive({id:"",shopGroupId:"",paidMemberName:"",paidRuleJson:[],relevancyRightsList:[]}),s={INTEGRAL_MULTIPLE:pe,GOODS_DISCOUNT:me},b={INTEGRAL_MULTIPLE:"积分值为2-9.9倍保留一位小数",GOODS_DISCOUNT:"商品折扣值为0.1-9.9折保留一位小数"},N=[{name:"1月",label:"ONE_MONTH"},{name:"三个月",label:"THREE_MONTH"},{name:"12个月",label:"TWELVE_MONTH"},{name:"3年",label:"THREE_YEAR"},{name:"5年",label:"FIVE_YEAR"}],C=e.reactive({shopGroupId:[{required:!0,message:"请选择店铺组",trigger:"blur"}],paidMemberName:[{required:!0,message:"请输入会员名称",trigger:"blur"}],paidRuleJson:[{validator:R,trigger:"blur"}]});function R(o,t,n){for(let c=0;c<t.length;c++)if(!t[c].effectiveDurationType)return n("请完善付费规则");n()}he(),fe();const B=async()=>{if(!a.paidRuleJson.length)return p.ElMessage.error("请填写付费规则"),Promise.reject("请填写付费规则");const o=L.value;if(await h(o),G()){const t=z.cloneDeep(a);t.relevancyRightsList=Array.from(g.value.values()).map(r=>(r.rightsType==="GOODS_DISCOUNT"?r.extendValue=Number(y(V.value)):r.rightsType==="INTEGRAL_MULTIPLE"?r.extendValue=Number(y(x.value)):delete r.extendValue,r)),t.paidRuleJson=t.paidRuleJson.map(r=>(r.price=Number(T(r.price)),r));const{code:n,msg:c}=u.memberId?await Y(t):await j(t);return n===200?(p.ElMessage.success("保存成功"),Promise.resolve("保存成功")):(p.ElMessage.error(c||"保存失败"),Promise.reject(c||"保存失败"))}else return Promise.reject("校验失败")},O=()=>{a.paidRuleJson.push({price:0,effectiveDurationType:""})},P=o=>{a.paidRuleJson.splice(o,1)},d=o=>{g.value.has(o.memberRightsId)?g.value.delete(o.memberRightsId):g.value.set(o.memberRightsId,o)};function h(o){return o?new Promise((t,n)=>{o==null||o.validate((c,r)=>{c?t("success valid"):n(r)})}):Promise.reject(new Error("no form instance input"))}function G(){const o=[];g.value.forEach(n=>{s[n.rightsType]&&(console.log("tempArr",n.rightsType==="GOODS_DISCOUNT"?V.value:x.value),o.push({type:s[n.rightsType](n.rightsType==="GOODS_DISCOUNT"?V.value:x.value),tips:b[n.rightsType]||""}))});const t=o.filter(n=>!n.type);return t.length&&p.ElMessage.warning(t[0].tips),!t.length}function pe(o){const t=Number(o);return t>=2&&t<=9.9&&$(String(o))}function $(o,t){const n=/^\d+(\.\d?)?$/;return o!==""?n.test(o):!1}function me(o){console.log(o);const t=Number(o);return t>=.1&&t<=9.9&&$(String(o))}async function he(){const{code:o,data:t}=await q();if(o!==200)return p.ElMessage.error("获取会员权益失败");D.value=t,_e()}async function _e(){if(u.memberId){const{code:o,data:t,msg:n}=await W(String(u.memberId));if(o===200&&t){const{id:c,paidMemberName:r,paidRuleJson:I,relevancyRightsList:S,shopGroupId:U}=t;a.id=c,a.paidMemberName=r,a.paidRuleJson=I.map(f=>(f.price=Number(k(f.price)),f)),a.shopGroupId=U,a.relevancyRightsList=S,S.forEach(f=>{f.rightsType==="GOODS_DISCOUNT"?V.value=Number(m(f.extendValue)):f.rightsType==="INTEGRAL_MULTIPLE"&&(x.value=Number(m(f.extendValue))),g.value.set(f.memberRightsId,f)})}else p.ElMessage.error(n||"获取失败")}}async function fe(){const{code:o,data:t}=await Q();if(o!==200)return p.ElMessage.error("获取会员权益失败");E.value=t}return M({handleSubmit:B}),(o,t)=>{const n=e.resolveComponent("el-option"),c=e.resolveComponent("el-select"),r=e.resolveComponent("el-form-item"),I=e.resolveComponent("el-input"),S=e.resolveComponent("el-table-column"),U=e.resolveComponent("el-input-number"),f=e.resolveComponent("el-link"),be=e.resolveComponent("el-table"),ue=e.resolveComponent("el-checkbox"),ge=e.resolveComponent("el-row"),xe=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",Z,[v,e.createVNode(xe,{ref_key:"ruleFormRef",ref:L,model:a,rules:C,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(r,{label:"店铺组",prop:"shopGroupId"},{default:e.withCtx(()=>[e.createVNode(c,{modelValue:a.shopGroupId,"onUpdate:modelValue":t[0]||(t[0]=l=>a.shopGroupId=l),class:"m-2",placeholder:"请选择"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(E.value,l=>(e.openBlock(),e.createBlock(n,{key:l.id,label:l.groupName,value:l.id},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(l.groupName),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(r,{label:"会员等级"},{default:e.withCtx(()=>[e.createTextVNode(" SVIP"+e.toDisplayString(u.memberLevel),1)]),_:1}),e.createVNode(r,{label:"等级名称",prop:"paidMemberName"},{default:e.withCtx(()=>[e.createVNode(I,{modelValue:a.paidMemberName,"onUpdate:modelValue":t[1]||(t[1]=l=>a.paidMemberName=l),modelModifiers:{trim:!0},maxlength:8,minlength:3,placeholder:"请输入等级名称",style:{width:"226px"}},null,8,["modelValue"])]),_:1}),e.createVNode(r,{label:"付费规则",prop:"paidRuleJson",required:""},{default:e.withCtx(()=>[e.withDirectives(e.createElementVNode("div",ee,[e.createVNode(be,{data:a.paidRuleJson,height:a.paidRuleJson.length>4?"220px":o.undef,size:"small",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(S,{label:"有效期"},{default:e.withCtx(({row:l})=>[e.createVNode(c,{modelValue:l.effectiveDurationType,"onUpdate:modelValue":_=>l.effectiveDurationType=_,class:"m-2",placeholder:"请选择"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(N,_=>e.createVNode(n,{key:_.name,label:_.name,value:_.label},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(_.name),1)]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(S,{label:"价格"},{default:e.withCtx(({row:l})=>[e.createVNode(U,{modelValue:l.price,"onUpdate:modelValue":_=>l.price=_,min:.01,precision:2,"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(S,{label:"操作",width:"50px"},{default:e.withCtx(({$index:l})=>[e.createVNode(f,{underline:!1,type:"primary",onClick:_=>P(l)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","height"])],512),[[e.vShow,a.paidRuleJson.length]]),e.createElementVNode("div",te,[e.createVNode(f,{underline:!1,type:"primary",onClick:O},{default:e.withCtx(()=>[e.createTextVNode("添加规则")]),_:1})])]),_:1}),e.createVNode(r,{label:"权益"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(D.value,l=>(e.openBlock(),e.createBlock(ge,{key:l.id,style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(r,{"label-width":"0",style:{margin:"10px 0"}},{default:e.withCtx(()=>[(e.openBlock(),e.createBlock(ue,{key:l.id+g.value.has(l.id),checked:g.value.has(l.id),label:{name:l.rightsName,memberRightsId:l.id,extendValue:0},onChange:_=>d({name:l.rightsName,rightsType:l.rightsType,memberRightsId:l.id,extendValue:0})},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(l.rightsName),1)]),_:2},1032,["checked","label","onChange"])),l.rightsType==="GOODS_DISCOUNT"?(e.openBlock(),e.createBlock(I,{key:0,modelValue:V.value,"onUpdate:modelValue":t[2]||(t[2]=_=>V.value=_),style:{width:"130px",margin:"0 20px"}},{append:e.withCtx(()=>[e.createTextVNode("折")]),_:1},8,["modelValue"])):l.rightsType==="INTEGRAL_MULTIPLE"?(e.openBlock(),e.createBlock(I,{key:1,modelValue:x.value,"onUpdate:modelValue":t[3]||(t[3]=_=>x.value=_),style:{width:"130px",margin:"0 20px"}},{append:e.withCtx(()=>[e.createTextVNode("倍")]),_:1},8,["modelValue"])):e.createCommentVNode("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["model","rules"])])}}}),ye="",H=(i,M)=>{const u=i.__vccOpts||i;for(const[y,m]of M)u[y]=m;return u},le=H(oe,[["__scopeId","data-v-8c01b25b"]]),ne={class:"level"},re={class:"level"},ae={class:"pricing"},ie={key:0},de={key:1},se={key:2},ce=e.defineComponent({__name:"ShopPaidMember",setup(i){const{divTenThousand:M,divHundred:u}=A(),y=e.ref([]),m=e.reactive({id:"",currentMemberLevel:void 0}),k=e.ref(!1),T=e.ref(null);x();const L=async s=>{p.ElMessageBox.confirm("确定需要删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:b,msg:N}=await K(s);b===200&&(p.ElMessage.success("删除成功"),x())})},g=()=>{m.id="",m.currentMemberLevel=y.value.length+1,k.value=!0},D=(s,b)=>{m.id=s,m.currentMemberLevel=b,k.value=!0},E=()=>{k.value=!1,m.id="",m.currentMemberLevel=void 0},V=async()=>{var s;await((s=T==null?void 0:T.value)==null?void 0:s.handleSubmit()),E(),x()};async function x(){const{code:s,data:b,msg:N}=await F();s===200?y.value=b:p.ElMessage.error(N||"获取列表失败")}function a(s){return`${{ONE_MONTH:"1个月",THREE_MONTH:"3个月",TWELVE_MONTH:"12个月",THREE_YEAR:"三年",FIVE_YEAR:"五年"}[s.effectiveDurationType]}${M(s.price)}`}return(s,b)=>{const N=e.resolveComponent("el-button"),C=e.resolveComponent("el-table-column"),R=e.resolveComponent("el-link"),B=e.resolveComponent("el-table"),O=e.resolveComponent("el-dialog"),P=e.resolveComponent("el-tab-pane");return e.openBlock(),e.createBlock(P,{label:"店铺付费会员",name:"PayingShopMember"},{default:e.withCtx(()=>[e.createVNode(N,{round:"",style:{"margin-bottom":"15px"},type:"primary",onClick:g},{default:e.withCtx(()=>[e.createTextVNode("添加等级")]),_:1}),e.createVNode(B,{ref:"multipleTableRef","cell-style":{fontSize:"12px",color:"#333333"},data:y.value,"header-cell-style":{background:"#f6f8fa",fontWeight:400,color:"#333333"},"header-row-style":{fontSize:"12px",color:"#909399"}},{default:e.withCtx(()=>[e.createVNode(C,{align:"center",label:"店铺组"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",ne,e.toDisplayString(d.shopGroupName),1)]),_:1}),e.createVNode(C,{align:"center",label:"会员等级"},{default:e.withCtx(({$index:d})=>[e.createElementVNode("div",re,"SVIP"+e.toDisplayString(d+1),1)]),_:1}),e.createVNode(C,{align:"center",label:"付费会员名称"},{default:e.withCtx(({row:d})=>[e.createElementVNode("span",null,e.toDisplayString(d.paidMemberName),1)]),_:1}),e.createVNode(C,{align:"center",label:"定价",width:"160px"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",ae,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.paidRuleJson,h=>(e.openBlock(),e.createElementBlock("div",{key:h.id},e.toDisplayString(a(h))+"元",1))),128))])]),_:1}),e.createVNode(C,{align:"center",label:"会员权益",width:"150px"},{default:e.withCtx(({row:d})=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.relevancyRightsList,h=>e.withDirectives((e.openBlock(),e.createElementBlock("div",{key:h.id,class:"interests"},[h.rightsType==="GOODS_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",ie,"商品折扣"+e.toDisplayString(e.unref(u)(h.extendValue))+"折",1)):h.rightsType==="INTEGRAL_MULTIPLE"?(e.openBlock(),e.createElementBlock("div",de,"积分"+e.toDisplayString(e.unref(u)(h.extendValue))+"倍",1)):(e.openBlock(),e.createElementBlock("div",se,e.toDisplayString(h.rightsName),1))])),[[e.vShow,d.relevancyRightsList.length]])),128))]),_:1}),e.createVNode(C,{align:"center",fixed:"right",label:"操作",width:"150"},{default:e.withCtx(({row:d,$index:h})=>[e.createVNode(R,{underline:!1,size:"small",style:{padding:"0 5px"},type:"primary",onClick:G=>D(d.id,h+1)},{default:e.withCtx(()=>[e.createTextVNode(" 编辑 ")]),_:2},1032,["onClick"]),y.value.length-1===h?(e.openBlock(),e.createBlock(R,{key:0,underline:!1,size:"small",style:{padding:"0 5px"},type:"primary",onClick:G=>L(d.id)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(O,{modelValue:k.value,"onUpdate:modelValue":b[0]||(b[0]=d=>k.value=d),title:m.id?"编辑会员":"添加会员","destroy-on-close":"",onClose:E},{footer:e.withCtx(()=>[e.createVNode(N,{onClick:E},{default:e.withCtx(()=>[e.createTextVNode("取 消")]),_:1}),e.createVNode(N,{type:"primary",onClick:V},{default:e.withCtx(()=>[e.createTextVNode("保 存")]),_:1})]),default:e.withCtx(()=>[e.createVNode(le,{ref_key:"editMemberRef",ref:T,"member-id":m.id,"member-level":m.currentMemberLevel},null,8,["member-id","member-level"])]),_:1},8,["modelValue","title"])]),_:1})}}}),Ne="";return H(ce,[["__scopeId","data-v-dac90691"]])});
