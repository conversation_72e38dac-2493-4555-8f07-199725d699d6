(function(t,o){typeof exports=="object"&&typeof module<"u"?module.exports=o(require("vue"),require("element-plus"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/apis/http"],o):(t=typeof globalThis<"u"?globalThis:t||self,t.VipDistributionInformation=o(t.VipDistributionInformationContext.Vue,t.VipDistributionInformationContext.ElementPlus,t.VipDistributionInformationContext.Request))})(this,function(t,o,u){"use strict";var p=document.createElement("style");p.textContent=`.color60[data-v-fb68b58b]{color:#606266}
`,document.head.appendChild(p);const d=n=>u.get({url:`addon-distribute/distribute/distributor/distributor/parent/${n}`}),f={class:"f14 color60",style:{"margin-right":"80px"}},_={class:"f14 color60"},m=t.defineComponent({__name:"VipDistributionInformation",props:{properties:{type:Object,required:!0}},setup(n){const s=n,i=t.ref({name:"",createTime:""});t.watch(()=>s.properties.userId,e=>{e&&a(e)},{immediate:!0});async function a(e){const{code:c,data:r,msg:l}=await d(e);if(c!==200)return o.ElMessage.error(l||"获取分销信息失败");r&&(i.value=r)}return(e,c)=>{const r=t.resolveComponent("el-tab-pane");return t.openBlock(),t.createBlock(r,{label:"分销信息",name:"distributionOfInformation"},{default:t.withCtx(()=>[t.createElementVNode("span",f,"邀请人："+t.toDisplayString(i.value.name||"--"),1),t.createElementVNode("span",_,"加入时间："+t.toDisplayString(i.value.createTime||"--"),1)]),_:1})}}}),b="";return((n,s)=>{const i=n.__vccOpts||n;for(const[a,e]of s)i[a]=e;return i})(m,[["__scopeId","data-v-fb68b58b"]])});
