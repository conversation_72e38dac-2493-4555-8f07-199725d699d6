(function(e,U){typeof exports=="object"&&typeof module<"u"?module.exports=U(require("vue"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("element-plus"),require("@/composables/useConvert"),require("@/utils/date"),require("@/utils/http"),require("@/apis/good"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","element-plus","@/composables/useConvert","@/utils/date","@/utils/http","@/apis/good","vue-router"],U):(e=typeof globalThis<"u"?globalThis:e||self,e.GroupForm=U(e.GroupFormContext.Vue,e.GroupFormContext.QChooseGoodsPopup,e.GroupFormContext.ElementPlus,e.GroupFormContext.UseConvert,e.GroupFormContext.DateUtil,e.GroupFormContext.UtilsHttp,e.GroupFormContext.GoodAPI,e.GroupFormContext.VueRouter))})(this,function(e,U,y,j,z,R,K,L){"use strict";var H=document.createElement("style");H.textContent=`.com[data-v-fa2761d6]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-fa2761d6]{width:62px;height:62px}.com__name[data-v-fa2761d6]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.groupForm[data-v-6ed86edc]{padding:0 30px 60px}.groupForm__title[data-v-6ed86edc]{font-size:14px;color:#606266;font-weight:700;margin-bottom:30px}.groupForm__stairs[data-v-6ed86edc]{margin-bottom:16px}.groupForm__stairs--title[data-v-6ed86edc]{font-size:12px;color:#333;font-weight:700}.groupForm__stairs--input[data-v-6ed86edc]{margin:0 7px}.groupForm__btn[data-v-6ed86edc]{height:60px;display:flex;justify-content:center;align-items:center}.tips[data-v-6ed86edc]{font-size:12px;color:#c4c4c4}
`,document.head.appendChild(H);const Q={class:"com"},W={class:"com__name"},X=e.defineComponent({__name:"select-good-table",props:{mode:{type:String,default:"COMMON"},users:{type:Array,default(){return[]}},productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}}},setup(g,{expose:t}){const n=g,{divTenThousand:V,mulTenThousand:T}=j(),_=e.ref([]);e.watch(()=>n.productList,s=>{const k=F(s);_.value=S(k),console.log("tableList.value",_.value)}),e.watch(()=>n.flatGoodList,s=>{_.value=S(s),console.log("tableList.value1",_.value)});function O(s){return s.skuItem.stockType==="LIMITED"?Number(s.skuItem.skuStock):1/0}function F(s,k){if(!s.length)return[];const c=[];return s.forEach(d=>{d.skuIds.forEach((u,p)=>{c.push({productId:d.productId,productName:d.productName,productPic:d.pic,skuItem:{productId:d.productId,skuId:u,skuName:d.specs[p],skuPrice:d.salePrices[p],skuStock:d.stocks[p],stockType:d.stockTypes[p]},rowTag:0,stock:0,prices:[],isJoin:!0})})}),c}function S(s,k){let c=0,d=s.length;for(let u=0;u<d;u++){const p=s[u];u===0&&(p.rowTag=1,c=0),u!==0&&(p.productId===s[u-1].productId?(p.rowTag=0,s[c].rowTag=s[c].rowTag+1):(p.rowTag=1,c=u)),p.prices=p.prices.map(b=>+b)}return s}const $=({row:s,column:k,rowIndex:c,columnIndex:d})=>{if(d===0)return{rowspan:s.rowTag,colspan:s.rowTag?1:0}};function A(s){return s.stockType==="UNLIMITED"?"不限购":s.skuStock}function G(){const s=e.toRaw(_.value),k=[],c=new Map;if(s.length)for(let d=0;d<s.length;d++){if(!s[d].isJoin)continue;const u=s[d],p=u.productId,b={skuId:u.skuItem.skuId,stock:+u.stock,prices:u.prices.map(w=>T(w).toNumber())};if(!c.has(p))c.set(p,k.length),k.push({productId:p,skus:[b]});else{const w=c.get(p);k[w].skus.push(b)}}return k}function B(){let s=!0;const k=_.value;if(!k.length)y.ElMessage.warning("请选择商品"),s=!1;else for(let c=0;c<k.length;c++)if(k[c].isJoin){if(!k[c].stock){y.ElMessage.warning("商品库存必须大于零"),s=!1;break}if(k[c].prices.length!==n.users.length||k[c].prices.some(d=>d===null)){y.ElMessage.warning("拼团价格填写完整"),s=!1;break}}return s}return t({getProduct:G,validateProduct:B}),(s,k)=>{const c=e.resolveComponent("el-image"),d=e.resolveComponent("el-table-column"),u=e.resolveComponent("el-input-number"),p=e.resolveComponent("el-input"),b=e.resolveComponent("el-switch"),w=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(w,{data:_.value,"span-method":$},{default:e.withCtx(()=>[e.createVNode(d,{label:"商品信息",width:"215"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",Q,[e.createVNode(c,{class:"com__pic",src:r.productPic},null,8,["src"]),e.createElementVNode("div",W,e.toDisplayString(r.productName),1)])]),_:1}),e.createVNode(d,{label:"规格"},{default:e.withCtx(({row:r})=>[e.createTextVNode(e.toDisplayString(r.skuItem.skuName),1)]),_:1}),e.createVNode(d,{label:"库存"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[e.createVNode(u,{modelValue:r.stock,"onUpdate:modelValue":h=>r.stock=h,min:0,style:{width:"80px"},max:O(r),disabled:n.isEdit,precision:0,controls:!1},null,8,["modelValue","onUpdate:modelValue","max","disabled"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(A(r.skuItem)),1)]),_:1}),n.mode==="COMMON"?(e.openBlock(),e.createBlock(d,{key:0,label:"拼团价"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[n.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(V)(r.prices[0]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[0],"onUpdate:modelValue":h=>r.prices[0]=h,min:.01,style:{width:"80px"},disabled:n.isEdit,precision:2,max:e.unref(V)(r.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(V)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),n.mode==="STAIRS"&&n.users.length>0?(e.openBlock(),e.createBlock(d,{key:1,label:"第一阶段拼团"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[n.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(V)(r.prices[0]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[0],"onUpdate:modelValue":h=>r.prices[0]=h,min:.01,style:{width:"80px"},disabled:n.isEdit,precision:2,max:e.unref(V)(r.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(V)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),n.mode==="STAIRS"&&n.users.length>1?(e.openBlock(),e.createBlock(d,{key:2,label:"第二阶段拼图"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[n.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(V)(r.prices[1]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[1],"onUpdate:modelValue":h=>r.prices[1]=h,disabled:n.isEdit,min:.01,style:{width:"80px"},max:r.prices[0]-.01,precision:2,controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(V)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),n.mode==="STAIRS"&&n.users.length>2?(e.openBlock(),e.createBlock(d,{key:3,label:"第三阶段拼图"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[n.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(V)(r.prices[2]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[2],"onUpdate:modelValue":h=>r.prices[2]=h,disabled:n.isEdit,min:.01,style:{width:"80px"},precision:2,max:r.prices[1]-.01,controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(V)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(d,{label:"是否参与"},{default:e.withCtx(({row:r})=>[e.createVNode(b,{modelValue:r.isJoin,"onUpdate:modelValue":h=>r.isJoin=h,size:"large",disabled:n.isEdit},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),_e="",J=(g,t)=>{const n=g.__vccOpts||g;for(const[V,T]of t)n[V]=T;return n},Z=J(X,[["__scopeId","data-v-fa2761d6"]]),v=g=>R.http.post({url:"addon-team/team/activity",data:g}),ee=g=>R.http.get({url:`addon-team/team/activity/${g}`}),P=g=>(e.pushScopeId("data-v-6ed86edc"),g=g(),e.popScopeId(),g),te={class:"groupForm"},oe=P(()=>e.createElementVNode("div",{class:"groupForm__title"},"基本信息",-1)),le={key:1},re={class:"groupForm__stairs--title"},ae=P(()=>e.createElementVNode("span",{class:"tips",style:{"margin-left":"8px"}},"商品按下单减库存，请设置未付款订单自动取消时间及时释放库存，可输入3-360分钟",-1)),se=P(()=>e.createElementVNode("div",{class:"tips"}," 开启模拟成团后，拼团有效期内人数未满的团，系统将会以“虚拟用户”凑满人数，使该团拼团成功。你只需要对已付款参团的真实买家发货。建议合理开启，以提高成团率。 ",-1)),de={key:1,class:"tips"},ne=P(()=>e.createElementVNode("span",{class:"tips",style:{"margin-left":"8px"}},[e.createTextVNode(" 叠加优惠可能导致实付金额为 "),e.createElementVNode("i",{style:{color:"red","font-weight":"700","font-style":"unset"}},"0"),e.createTextVNode(" (实付金额= 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1)),ie={class:"groupForm__btn"},ce=e.defineComponent({__name:"GroupForm",setup(g){const t=e.ref({name:"",startTime:"",endTime:"",effectTimeout:0,mode:"COMMON",users:[],payTimeout:0,simulate:!1,huddle:!0,preheat:!1,preheatHours:1,stackable:{vip:!1,coupon:!1,full:!1},products:[]}),n=L.useRoute(),V=L.useRouter(),T=!!n.query.id,_=new z,O=e.ref(),F=e.ref([]),S=e.ref(!1),$=e.ref([]),A=e.ref([]),G=e.ref(),B=e.reactive({maxPrice:"",minPrice:"",activity:{endTime:"",startTime:""},keyword:"",categoryFirstId:""}),s=e.reactive({name:[{required:!0,message:"请输入活动名称",trigger:"blur"},{min:1,max:10,message:"活动名称字数为1-10",trigger:"blur"}],startTime:[{required:!0,message:"请选择日期"},{trigger:"blur",validator:Y}],endTime:[{required:!0,message:"请选择日期"},{trigger:"blur",validator:Y}],effectTimeout:[{validator:pe,trigger:"blur"}],mode:[{required:!0,message:"请选择拼团模式",trigger:"blur"}],users:[{validator:h,trigger:"blur"}],payTimeout:[{validator:me,trigger:"blur"}],simulate:[{required:!0,message:"请选择是否模拟成团",trigger:"blur"}],huddle:[{required:!0,message:"请选择是否凑团",trigger:"blur"}],preheat:[{required:!0,message:"请选择是否预热",trigger:"blur"}]});fe();const k=()=>{t.value.users.length<3?t.value.users.push(0):y.ElMessage.warning("最多添加三个梯队")},c=()=>{t.value.users.pop()},d=a=>{$.value=a.tempGoods},u=()=>{var a;(a=O.value)==null||a.validate(async o=>{if(o&&G.value.validateProduct()){t.value.products=G.value.getProduct();const{code:i,data:m,msg:I}=await v(t.value);i===200?(y.ElMessage.success("创建成功"),b()):y.ElMessage.error(I||"创建失败")}})},p=a=>{a[0].getTime()>=a[1].getTime()&&y.ElMessage.warning("结束时间大于开始时间"),t.value.startTime=_.getYMDHMSs(a[0]),t.value.endTime=_.getYMDHMSs(a[1]),B.activity.startTime=_.getYMDHMSs(a[0]),B.activity.endTime=_.getYMDHMSs(a[1])},b=()=>{V.go(-1)},w=a=>{a==="COMMON"&&(t.value.users=[t.value.users[0]])},r=()=>{if(!t.value.endTime||!t.value.startTime){y.ElMessage.warning("请先选择时间段");return}S.value=!0};function h(a,o,i){t.value.mode==="COMMON"&&(t.value.users.length&&t.value.users[0]>=2?i():i(new Error("参团人数应大于等于两人"))),t.value.mode==="STAIRS"&&(t.value.users.length?ue(t.value.users)?i():i(new Error("阶梯团人数应为递增人数")):i(new Error("请至少添加一项阶梯团人数")))}function pe(a,o,i){o>=15?i():i(new Error("拼团有效时间必须大于等于15分钟"))}function me(a,o,i){o>=3&&o<=369?i():i(new Error("订单关闭时间3-360分钟"))}function ue(a){for(let o=1;o<a.length;o++)if(a[o]<=a[o-1])return!1;return!0}function Y(a,o,i){new Date(t.value.startTime).getTime()>=new Date(t.value.endTime).getTime()?i(new Error("开始时间应小于结束时间")):i()}async function fe(){const a=n.query.id;if(a){const{data:o}=await ee(a);t.value=o,t.value.payTimeout=+o.payTimeout,F.value=[o.startTime,o.endTime],ke(o)}}async function ke(a){const o=a.products.map(f=>f.productId),{code:i,data:m}=await K.doGetRetrieveProduct({productId:o});if(i!==200)return y.ElMessage.error("获取活动商品信息失败");const I=[];for(let f=0;f<a.products.length;f++){const C=m.list.findIndex(x=>x.productId===a.products[f].productId);for(let x=0;x<a.products[C].skus.length;x++){let N={productName:m.list[f].productName,productPic:m.list[f].pic,productId:m.list[f].productId,skuItem:{productId:m.list[f].productId,skuId:"",skuName:"",skuPrice:"",skuStock:"",stockType:"LIMITED"},rowTag:0,stock:0,prices:[],isJoin:!0};const D=a.products[C].skus[x].skuId,E=m.list[f].skuIds.findIndex(l=>l===D);N.skuItem.skuId=m.list[f].skuIds[E],N.skuItem.skuName=m.list[f].specs[E],N.skuItem.skuPrice=m.list[f].salePrices[E],N.skuItem.skuStock=m.list[f].stocks[E],N.skuItem.stockType=m.list[f].stockTypes[E],N.stock=a.products[C].skus[x].stock,N.prices=a.products[C].skus[x].prices,I.push(N)}}A.value=I}function Ve(a){const o=_.getYMD(new Date),i=_.getYMD(a);return o===i?!1:new Date().getTime()>a.getTime()}return(a,o)=>{const i=e.resolveComponent("el-input"),m=e.resolveComponent("el-form-item"),I=e.resolveComponent("el-date-picker"),f=e.resolveComponent("el-radio"),C=e.resolveComponent("el-radio-group"),x=e.resolveComponent("el-button"),N=e.resolveComponent("el-input-number"),D=e.resolveComponent("el-checkbox"),E=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",te,[oe,e.createVNode(E,{ref_key:"ruleFormRef",ref:O,model:t.value,"label-width":"110","label-position":"right",rules:s,disabled:T},{default:e.withCtx(()=>[e.createVNode(m,{label:"活动名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:t.value.name,"onUpdate:modelValue":o[0]||(o[0]=l=>t.value.name=l),placeholder:"限10字",style:{width:"550px"},maxlength:"10"},null,8,["modelValue"])]),_:1}),e.createVNode(m,{label:"活动时间",prop:"startTime"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(I,{modelValue:F.value,"onUpdate:modelValue":o[1]||(o[1]=l=>F.value=l),style:{width:"550px"},type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间","disabled-date":Ve,onChange:p},null,8,["modelValue"])])]),_:1}),e.createVNode(m,{label:"拼团有效时间",prop:"effectTimeout",required:""},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:t.value.effectTimeout,"onUpdate:modelValue":o[2]||(o[2]=l=>t.value.effectTimeout=l),formatter:l=>Number(`${l}`.replace(/[^\d]/g,"")),style:{width:"550px"}},{append:e.withCtx(()=>[e.createTextVNode(" 分钟 ")]),_:1},8,["modelValue","formatter"])]),_:1}),e.createVNode(m,{label:"拼团模式",prop:"mode"},{default:e.withCtx(()=>[e.createVNode(C,{modelValue:t.value.mode,"onUpdate:modelValue":o[3]||(o[3]=l=>t.value.mode=l),onChange:w},{default:e.withCtx(()=>[e.createVNode(f,{label:"COMMON"},{default:e.withCtx(()=>[e.createTextVNode("普通拼团")]),_:1}),e.createVNode(f,{label:"STAIRS"},{default:e.withCtx(()=>[e.createTextVNode("阶梯拼团")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(m,{label:"参团人数",prop:"users",required:""},{default:e.withCtx(()=>[t.value.mode==="COMMON"?(e.openBlock(),e.createBlock(i,{key:0,modelValue:t.value.users[0],"onUpdate:modelValue":o[4]||(o[4]=l=>t.value.users[0]=l),formatter:l=>Number(`${l}`.replace(/[^\d]/g,"")),parser:l=>`${Number(l)>=100?100:l}`,style:{width:"550px"},max:100},{append:e.withCtx(()=>[e.createTextVNode(" 人 ")]),_:1},8,["modelValue","formatter","parser"])):(e.openBlock(),e.createElementBlock("div",le,[e.createVNode(x,{link:"",type:"primary",onClick:k},{default:e.withCtx(()=>[e.createTextVNode("添加拼团阶梯")]),_:1}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value.users,(l,q)=>(e.openBlock(),e.createElementBlock("div",{key:q,class:"groupForm__stairs"},[e.createElementVNode("span",re,"第"+e.toDisplayString(q+1)+"阶段人数",1),e.createVNode(i,{modelValue:t.value.users[q],"onUpdate:modelValue":M=>t.value.users[q]=M,class:"groupForm__stairs--input",style:{width:"450px"},formatter:M=>Number(`${M}`.replace(/[^\d]/g,"")),parser:M=>`${Number(M)>=100?100:M}`,max:100},{append:e.withCtx(()=>[e.createTextVNode(" 人 ")]),_:2},1032,["modelValue","onUpdate:modelValue","formatter","parser"]),q>0?(e.openBlock(),e.createBlock(x,{key:0,type:"primary",link:"",onClick:c},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:1})):e.createCommentVNode("",!0)]))),128))]))]),_:1}),e.createVNode(m,{label:"订单关闭时间",prop:"payTimeout"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:t.value.payTimeout,"onUpdate:modelValue":o[5]||(o[5]=l=>t.value.payTimeout=l),controls:!1,max:360,min:3},null,8,["modelValue"]),ae]),_:1}),e.createVNode(m,{label:"模拟成团",prop:"simulate"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(C,{modelValue:t.value.simulate,"onUpdate:modelValue":o[6]||(o[6]=l=>t.value.simulate=l)},{default:e.withCtx(()=>[e.createVNode(f,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(f,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),se])]),_:1}),e.createVNode(m,{label:"活动预热",prop:"preheat"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(C,{modelValue:t.value.preheat,"onUpdate:modelValue":o[7]||(o[7]=l=>t.value.preheat=l)},{default:e.withCtx(()=>[e.createVNode(f,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(f,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),e.createElementVNode("div",null,[t.value.preheat?(e.openBlock(),e.createBlock(i,{key:0,modelValue:t.value.preheatHours,"onUpdate:modelValue":o[8]||(o[8]=l=>t.value.preheatHours=l),style:{width:"450px"},formatter:l=>Number(`${l}`.replace(/[^\d]/g,"")),parser:l=>`${Number(l)>=24?24:l}`,max:100},{append:e.withCtx(()=>[e.createTextVNode(" 小时 ")]),_:1},8,["modelValue","formatter","parser"])):(e.openBlock(),e.createElementBlock("div",de,"开启后，商品详情展示未开始的拼团活动，但活动开始前用户无法拼团购买。"))])])]),_:1}),e.createVNode(m,{label:"叠加优惠"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:t.value.stackable.vip,"onUpdate:modelValue":o[9]||(o[9]=l=>t.value.stackable.vip=l),label:"会员价"},null,8,["modelValue"]),e.createVNode(D,{modelValue:t.value.stackable.coupon,"onUpdate:modelValue":o[10]||(o[10]=l=>t.value.stackable.coupon=l),label:"优惠券"},null,8,["modelValue"]),e.createVNode(D,{modelValue:t.value.stackable.full,"onUpdate:modelValue":o[11]||(o[11]=l=>t.value.stackable.full=l),label:"满减"},null,8,["modelValue"]),ne]),_:1}),e.createVNode(m,{label:"适用商品"},{default:e.withCtx(()=>[e.createVNode(x,{type:"primary",link:"",onClick:r},{default:e.withCtx(()=>[e.createTextVNode("选择商品")]),_:1})]),_:1})]),_:1},8,["model","rules"]),e.createVNode(U,{modelValue:S.value,"onUpdate:modelValue":o[12]||(o[12]=l=>S.value=l),"search-param":B,"onUpdate:searchParam":o[13]||(o[13]=l=>B=l),onOnConfirm:d},null,8,["modelValue","search-param"]),e.createVNode(Z,{ref_key:"selectGoodsTableRef",ref:G,mode:t.value.mode,users:t.value.users,"product-list":$.value,"is-edit":T,"flat-good-list":A.value},null,8,["mode","users","product-list","flat-good-list"])]),e.createElementVNode("div",ie,[e.createVNode(x,{round:"",onClick:b},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(x,{type:"primary",round:"",disabled:T,onClick:u},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])])}}}),ge="";return J(ce,[["__scopeId","data-v-6ed86edc"]])});
