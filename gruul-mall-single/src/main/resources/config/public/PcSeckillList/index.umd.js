(function(t,i){typeof exports=="object"&&typeof module<"u"?module.exports=i(require("vue"),require("element-plus"),require("@/composables/useConvert"),require("decimal.js"),require("@/apis/http"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/composables/useConvert","decimal.js","@/apis/http","vue-router"],i):(t=typeof globalThis<"u"?globalThis:t||self,t.PcSeckillList=i(t.PcSeckillListContext.Vue,t.PcSeckillListContext.ElementPlus,t.PcSeckillListContext.UseConvert,t.PcSeckillListContext.Decimal,t.PcSeckillListContext.Request,t.PcSeckillListContext.VueRouter))})(this,function(t,i,p,I,E,O){"use strict";var Tt=Object.defineProperty;var Dt=(t,i,p)=>i in t?Tt(t,i,{enumerable:!0,configurable:!0,writable:!0,value:p}):t[i]=p;var G=(t,i,p)=>(Dt(t,typeof i!="symbol"?i+"":i,p),p);var b=document.createElement("style");b.textContent=`.countdown-view[data-v-a4ac5651]{display:inline-flex;color:#000}.countdown-view--item[data-v-a4ac5651]{height:46rpx;line-height:46rpx;width:46rpx;border-radius:50%;font-size:28rpx;text-align:center;color:#fff}.countdown-view--item-pendant[data-v-a4ac5651]{height:46rpx;line-height:46rpx;margin:0 5rpx;color:#fff}.countdown-view--item-day[data-v-a4ac5651]{margin-right:5rpx}.time[data-v-4949b145]:after{display:block;content:"";width:0;height:0;border-style:solid;border-width:0 0 56px 65px;border-color:transparent transparent #fff;position:absolute;left:-65px;top:0}.activeTime[data-v-4949b145]:after{border-color:transparent transparent #e31436}.time[data-v-4949b145]:before{display:block;content:"";width:0;height:0;border-style:solid;border-width:56px 65px 0 0;border-color:#fff transparent transparent;position:absolute;right:-65px;top:0}.activeTime[data-v-4949b145]:before{border-color:#e31436 transparent transparent}
`,document.head.appendChild(b),p();const P={NOT_STARTED:"未开始",PROCESSING:"进行中",OVER:"已结束",ILLEGAL_SELL_OFF:"违规下架"},C={NOT_STARTED:{color:"#333333",text:"即将开始",soldOut:!1},PROCESSING:{color:"#e31436",text:"立即抢购",soldOut:!1},OVER:{color:"#999999",text:"已结束",soldOut:!1},BUY_NOW:{color:"#e31436",text:"立即抢购",soldOut:!1},OUT_OF_STOCK:{color:"#999999",text:"已抢光",soldOut:!0}},B={NOT_STARTED:{text:"距开始"},PROCESSING:{text:"距结束"},OVER:{text:"距结束",soldOut:!1},ILLEGAL_SELL_OFF:{text:"距结束"}},Y=60*60*1e3-1e3;function F(r){let e,o=parseInt((r%864e5/36e5).toString()),n=parseInt((r%(1e3*60*60)/(1e3*60)).toString()),s=parseInt((r%(1e3*60)/1e3).toString());return e=(o<10?"0"+o:o)+":"+(n<10?"0"+n:n)+":"+(s<10?"0"+s:s),e}class ${constructor(e=new Date){G(this,"ms");this.ms=e}getY(e=this.ms){return this.unitReturnDate(e).getFullYear()}getM(e=this.ms){const n=this.unitReturnDate(e).getMonth()+1;return this.formatLength(n)}getD(e=this.ms){const n=this.unitReturnDate(e).getDate();return this.formatLength(n)}getH(e=this.ms){const n=this.unitReturnDate(e).getHours();return this.formatLength(n)}getMin(e=this.ms){const n=this.unitReturnDate(e).getMinutes();return this.formatLength(n)}getS(e=this.ms){const n=this.unitReturnDate(e).getSeconds();return this.formatLength(n)}getYMD(e=this.ms){const o=this.unitReturnDate(e),n=o.getFullYear(),s=o.getMonth()+1,c=o.getDate();return[n,s,c].map(this.formatLength).join("/")}getYMDs(e=this.ms){const o=this.unitReturnDate(e),n=o.getFullYear(),s=o.getMonth()+1,c=o.getDate();return[n,s,c].map(this.formatLength).join("-")}getYMs(e=this.ms){const o=this.unitReturnDate(e),n=o.getFullYear(),s=o.getMonth()+1;return[n,s].map(this.formatLength).join("-")}getMDs(e=this.ms){const o=this.unitReturnDate(e),n=o.getMonth()+1,s=o.getDate();return[n,s].map(this.formatLength).join("-")}getHMS(e=this.ms){const o=this.unitReturnDate(e),n=o.getHours(),s=o.getMinutes(),c=o.getSeconds();return[n,s,c].map(this.formatLength).join(":")}getYMDHMS(e=this.ms){return e=this.unitReturnDate(e),this.getYMD(e)+" "+this.getHMS(e)}getYMDHMSs(e=this.ms){return this.getYMDs(e)+" "+this.getHMS(e)}getLastMonth(e=this.ms,o=30){let n=this.getTime(this.unitReturnDate(e));return n=n-3600*1e3*24*o,this.getYMDs(n)}getLastThreeMonth(e=this.ms,o=90){let n=this.getTime(e);return n=n-3600*1e3*24*o,this.getYMDs(n)}getAddDays(e=this.ms,o=0){let n=this.getTime(this.unitReturnDate(e));n=n+o*24*60*60*1e3;const s=this.getY(n),c=this.getM(n),l=this.getD(n);return[s,c,l].map(this.formatLength).join("-")}getSubtracteDays(e=this.ms,o=0){let n=this.getTime(this.unitReturnDate(e));n=n-o*24*60*60*1e3;const s=this.getY(n),c=this.getM(n),l=this.getD(n);return[s,c,l].map(this.formatLength).join("-")}getTime(e=this.ms){return e.getTime()}getObj(e=this.ms){const o=e,n=o.getFullYear(),s=o.getMonth()+1,c=o.getDate(),l=o.getHours(),d=o.getMinutes(),D=o.getSeconds();return[n,s,c,l,d,D].map(this.formatLength)}formatLength(e){return String(e)[1]?String(e):"0"+e}unitReturnDate(e){return e instanceof Date?e:new Date(e)}isLeapYear(e){return new Date(Number(e),1,29).getDate()===29}MonthToDay(e,o){const n=new Date(Number(e),Number(o),1,0,0,0);return new Date(n.getTime()-1e3).getDate()}}function j(r=0){return r||(r=0),new I(r).div(1e4)}const L="addon-seckill/seckillPromotion/",H=r=>E.get({url:`${L}consumer/sessions`,params:r}),K=r=>E.get({url:`${L}consumer/seckillSessionsProduct`,params:r}),M=r=>(t.pushScopeId("data-v-a4ac5651"),r=r(),t.popScopeId(),r),q={class:"countdown-view"},A={class:"countdown-view--item countdown-view--item-day"},v=M(()=>t.createElementVNode("text",{class:"countdown-view--item-pendant"},"天",-1)),z={class:"countdown-view--item"},U=M(()=>t.createElementVNode("text",{class:"countdown-view--item-pendant"},":",-1)),Q={class:"countdown-view--item"},W=M(()=>t.createElementVNode("text",{class:"countdown-view--item-pendant"},":",-1)),X={class:"countdown-view--item"},J=t.defineComponent({__name:"Pccountdown",props:{startTime:{type:String,default:""},isStart:{type:Boolean,default:!1}},emits:["end"],setup(r,{expose:e,emit:o}){const n=r;let s=null;const c=t.ref(["00","00","00"]),l=new $;t.watch(()=>n.startTime,()=>{d()}),d();function d(){s&&clearInterval(s),c.value=["00","00","00"],D(n.isStart)}t.onBeforeUnmount(()=>{s&&clearInterval(s)});function D(g){if(!n.startTime)return;const u=new Date().getTime(),_=l.getTime(new Date(n.startTime));let S=g?_-u:_+Y-u;S<0||(s=setInterval(()=>{if(S<1e3){o("end"),s&&clearInterval(s);return}S=S-1e3,c.value=F(S).split(":")},1e3))}function f(g){const u=new Date,_=(new Date(g).getTime()-u.getTime())/(1e3*3600*24);return Math.floor(_)<0?"":Math.floor(_)}return e({loadCountdown:d}),(g,u)=>(t.openBlock(),t.createElementBlock("view",q,[t.renderSlot(g.$slots,"default",{timeArr:c.value},()=>[f(r.startTime)?(t.openBlock(),t.createElementBlock(t.Fragment,{key:0},[t.createElementVNode("text",A,t.toDisplayString(f(r.startTime)),1),v],64)):t.createCommentVNode("",!0),t.createElementVNode("text",z,t.toDisplayString(c.value[0]),1),U,t.createElementVNode("text",Q,t.toDisplayString(c.value[1]),1),W,t.createElementVNode("text",X,t.toDisplayString(c.value[2]),1)],!0)]))}}),xt="",N=(r,e)=>{const o=r.__vccOpts||r;for(const[n,s]of e)o[n]=s;return o},Z=N(J,[["__scopeId","data-v-a4ac5651"]]),y=r=>(t.pushScopeId("data-v-4949b145"),r=r(),t.popScopeId(),r),tt=y(()=>t.createElementVNode("img",{src:"https://obs.xiaoqa.cn/gruul/20230112/b0737b03dd6246389ddd8bb7a5739896.jpg"},null,-1)),et=["onClick"],nt={"e-c-f":"","c-mr-2":""},ot={key:1,"c-w-92":"","c-h-31":"","c-lh-31":"","c-c-e31436":"","c-bg-fde9ed":"","b-1":"","c-bc-e31436":"","text-center":"","c-br-16":""},st={flex:"","items-center":"","flex-wrap":"","c-w-1190":"","m-auto":"","c-gap-10":""},rt=["onClick"],ct=["src"],it={"c-mt-11":"","c-ml-15":"","c-c-ff1e32":"","fw-700":"","c-fs-22":""},at=y(()=>t.createElementVNode("span",{"c-fs-16":""},"￥",-1)),lt={"c-mt-6":"","c-ml-15":"","c-ellipsis-2":"","c-w-257":"","c-fs-13":"","c-c-101010":"","c-h-46":"","c-lh-20":""},dt={flex:"","justify-between":"","items-center":""},mt=y(()=>t.createElementVNode("div",null,null,-1)),ut={"c-mt-38":"","c-mb-21":"",flex:"","justify-center":"","c-w-1190":"",ma:""},ht=t.defineComponent({__name:"PcSeckillList",setup(r){const e=O.useRouter(),o=t.ref(0),n=t.ref([]),s=t.ref(),c=t.ref([]),l=t.ref(""),d=t.reactive({startTime:"",size:16,current:1,total:1}),D=t.ref(),f=t.ref(!1);t.onMounted(async()=>{await g(),await u()});async function g(){const{code:a,data:h,msg:w}=await H({shopId:l.value?l.value:""});if(a!==200)return i.ElMessage.error(w||"秒杀活动获取失败");n.value=h.slice(0,3),s.value=h[0]}async function u(){var x;if(!((x=s.value)!=null&&x.startTime))return;d.startTime=s.value.startTime;const{code:a,data:h,msg:w}=await K({...d,shopId:l.value?l.value:""});if(a!==200)return i.ElMessage.error(w||"获取商品列表失败");d.total=h.total,c.value=h.records}const _=(a,h)=>{e.push({path:"/detail",query:{productId:a,shopId:h}})},S=a=>{d.current=a,u()};function gt(a){return a.split(" ")[1],a.split(" ")[1].substring(0,5)}const pt=()=>{i.ElMessageBox.alert("本次秒杀活动结束啦，看看最新的秒杀活动吧~","",{confirmButtonText:"前往查看",center:!0,showClose:!1}).then(async()=>{await g(),await u()})},ft=()=>{f.value=!f.value},_t=(a,h)=>{s.value=a,u(),o.value=h};return(a,h)=>{const w=t.resolveComponent("el-carousel-item"),x=t.resolveComponent("el-carousel"),St=t.resolveComponent("el-affix"),wt=t.resolveComponent("el-pagination");return t.openBlock(),t.createElementBlock(t.Fragment,null,[t.createVNode(x,{height:"298px","c-mb-18":""},{default:t.withCtx(()=>[t.createVNode(w,{style:{"background-color":"#99a9bf"}},{default:t.withCtx(()=>[tt]),_:1})]),_:1}),t.createVNode(St,{offset:0,onChange:ft},{default:t.withCtx(()=>[t.createElementVNode("div",{flex:"",ma:"","cursor-pointer":"","c-mb-8":"",class:t.normalizeClass(f.value?"c-w-1190":"c-w-1062")},[(t.openBlock(!0),t.createElementBlock(t.Fragment,null,t.renderList(n.value,(m,T)=>{var k,R,V;return t.openBlock(),t.createElementBlock("div",{"c-h-56":"","c-lh-56":"","c-fs-14":"","fw-700":"",relative:"",flex:"","items-center":"","justify-center":"",class:t.normalizeClass([o.value===T?"activeTime c-bg-e31436":"c-bg-ffffff",f.value?"c-w-397":"c-w-354 time"]),onClick:kt=>_t(m,T)},[t.createElementVNode("span",{"c-fs-20":"","c-mr-11":"",class:t.normalizeClass(o.value===T?"e-c-f":"e-c-3")},t.toDisplayString(gt(m.startTime)),3),o.value===T?(t.openBlock(),t.createElementBlock(t.Fragment,{key:0},[t.createElementVNode("span",nt,t.toDisplayString(((k=s.value)==null?void 0:k.secKillStatus)&&t.unref(B)[s.value.secKillStatus].text),1),t.createVNode(Z,{ref_for:!0,ref_key:"countdownRef",ref:D,"start-time":(R=s.value)==null?void 0:R.startTime,"is-start":((V=s.value)==null?void 0:V.secKillStatus)==="NOT_STARTED",onEnd:pt},null,8,["start-time","is-start"])],64)):(t.openBlock(),t.createElementBlock("div",ot,t.toDisplayString(t.unref(P)[m.secKillStatus]),1))],10,et)}),256))],2)]),_:1}),t.createElementVNode("div",st,[(t.openBlock(!0),t.createElementBlock(t.Fragment,null,t.renderList(c.value,(m,T)=>(t.openBlock(),t.createElementBlock("div",{key:T,"c-w-290":"","c-h-432":"","bg-white":"","text-left":"","c-pt-27":"","c-mb-11":"",onClick:k=>_(m.productId,m.shopId)},[t.createElementVNode("img",{src:m.productPic,"c-w-234":"","c-h-234":"","m-auto":"","display-block":""},null,8,ct),t.createElementVNode("div",it,[at,t.createTextVNode(" "+t.toDisplayString(t.unref(j)(m.secKillPrice)),1)]),t.createElementVNode("div",lt,t.toDisplayString(m.productName),1),t.createElementVNode("div",dt,[mt,t.createElementVNode("div",{"c-mr-7":"","c-w-94":"","c-h-39":"","c-lh-39":"","e-c-f":"","text-center":"","c-br-50":"",style:t.normalizeStyle({background:t.unref(C)[m.stockStatus].color})},t.toDisplayString(t.unref(C)[m.stockStatus].text),5)])],8,rt))),128))]),t.createElementVNode("div",ut,[t.createVNode(wt,{background:"",layout:"prev, pager, next",total:+d.total,"current-page":d.current,onCurrentChange:S},null,8,["total","current-page"])])],64)}}}),Mt="";return N(ht,[["__scopeId","data-v-4949b145"]])});
