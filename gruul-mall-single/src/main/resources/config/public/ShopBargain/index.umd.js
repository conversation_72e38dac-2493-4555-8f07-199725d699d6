(function(e,b){typeof exports=="object"&&typeof module<"u"?module.exports=b(require("vue"),require("@vueuse/core"),require("vue-router"),require("@/composables/useConvert"),require("@/components/pageManage/PageManage.vue"),require("element-plus"),require("@/store/modules/shopInfo"),require("@element-plus/icons-vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","vue-router","@/composables/useConvert","@/components/pageManage/PageManage.vue","element-plus","@/store/modules/shopInfo","@element-plus/icons-vue","@/apis/http"],b):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopBargain=b(e.ShopBargainContext.Vue,e.ShopBargainContext.VueUse,e.ShopBargainContext.VueRouter,e.ShopBargainContext.UseConvert,e.ShopBargainContext.PageManage,e.ShopBargainContext.ElementPlus,e.ShopBargainContext.ShopInfoStore,e.ShopBargainContext.ElementPlusIconsVue,e.ShopBargainContext.Request))})(this,function(e,b,x,N,B,C,L,D,V){"use strict";var S=document.createElement("style");S.textContent=`.column_bargain[data-v-b7f1aa66]{width:966px;height:144px;background:#f9f9f9;margin-bottom:10px;padding:10px;display:flex;justify-content:center;align-items:center}.column_bargain__left[data-v-b7f1aa66]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-around;align-items:flex-start;width:50%;height:100%;font-size:12px;color:#333}.column_bargain__left--title[data-v-b7f1aa66]{font-size:14px}.column_bargain__left--statistical[data-v-b7f1aa66]{width:100%;color:#a9a9a9;display:flex;justify-content:center;align-items:center;padding-right:40px;justify-content:space-between}.column_bargain__center[data-v-b7f1aa66]{width:20%;height:100%}.column_bargain__center--title[data-v-b7f1aa66]{font-size:14px}.column_bargain__right[data-v-b7f1aa66]{width:30%;height:100%;display:flex;justify-content:center;align-items:center}.nots[data-v-b7f1aa66]{color:#2e99f3}.ongoing[data-v-b7f1aa66]{color:#f57373}.hasEnded[data-v-b7f1aa66],.off[data-v-b7f1aa66],.suspended[data-v-b7f1aa66]{color:#a9a9a9}.container[data-v-cfc5d699]{overflow-y:scroll}.bargain_origin[data-v-d61ef3cd]{display:flex;justify-content:center;align-items:center}.bargain_origin--name[data-v-d61ef3cd]{width:120px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.bargain_origin--shop_name[data-v-d61ef3cd]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(S);const O=e.defineComponent({__name:"ShopBargain",setup(a){const p=e.reactive({activeName:"bargainList",tabsList:[{label:"砍价列表",name:"bargainList"},{label:"砍价订单",name:"bargainOrder"}]}),{activeName:d,tabsList:c}=e.toRefs(p),o={bargainList:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Q)),bargainOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>te))},_=n=>{d.value=n};return(n,m)=>{const i=e.resolveComponent("el-tab-pane"),r=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(r,{modelValue:e.unref(d),"onUpdate:modelValue":m[0]||(m[0]=s=>e.isRef(d)?d.value=s:null),class:"demo-tabs",onTabChange:_},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(c),s=>(e.openBlock(),e.createBlock(i,{key:s.label,label:s.label,name:s.name},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o[e.unref(d)])))])}}}),z=e.defineComponent({__name:"selectType",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Array,default:()=>[{label:"1",value:"2"}]}},emits:["update:modelValue","change"],setup(a,{emit:p}){const d=a,c=b.useVModel(d,"modelValue",p);return(o,_)=>{const n=e.resolveComponent("el-option"),m=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(m,{modelValue:e.unref(c),"onUpdate:modelValue":_[0]||(_[0]=i=>e.isRef(c)?c.value=i:null),placeholder:d.placeholder,style:{width:"150px"},onChange:_[1]||(_[1]=i=>p("change",i))},{default:e.withCtx(()=>[e.renderSlot(o.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.list,(i,r)=>(e.openBlock(),e.createBlock(n,{key:r,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),R=e.defineComponent({__name:"head-search",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},leftBtnText:{type:String,default:"leftBtnText"}},emits:["update:modelValue","leftBtnClick","search"],setup(a,{emit:p}){const d=a,c=[{value:"NOT_STARTED",label:"未开始"},{value:"PROCESSING",label:"进行中"},{value:"OVER",label:"已结束"},{value:"ILLEGAL_SELL_OFF",label:"违规下架"}],o=b.useVModel(d,"modelValue",p);return(_,n)=>{const m=e.resolveComponent("el-button"),i=e.resolveComponent("el-option"),r=e.resolveComponent("el-col"),s=e.resolveComponent("el-date-picker"),f=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(f,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px",width:"100%"}},{default:e.withCtx(()=>[e.createVNode(r,{span:14},{default:e.withCtx(()=>[e.createVNode(m,{round:"",type:"primary",onClick:n[0]||(n[0]=t=>p("leftBtnClick"))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(d.leftBtnText),1)]),_:1}),e.createVNode(z,{modelValue:e.unref(o).status,"onUpdate:modelValue":n[1]||(n[1]=t=>e.unref(o).status=t),style:{"margin-left":"15px"},list:c,onChange:n[2]||(n[2]=t=>p("search"))},{default:e.withCtx(()=>[e.createVNode(i,{label:"全部状态",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(r,{span:8},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:e.unref(o).date,"onUpdate:modelValue":n[3]||(n[3]=t=>e.unref(o).date=t),style:{width:"300px"},format:"YYYY/MM/DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:n[4]||(n[4]=t=>p("search",t))},null,8,["modelValue"])]),_:1})]),_:1})}}}),I={class:"column_bargain"},A={class:"column_bargain__left"},j={class:"column_bargain__left--title"},$={class:"column_bargain__left--statistical"},M={class:"column_bargain__center"},P={class:"column_bargain__right"},q=e.defineComponent({__name:"column",props:{item:{type:Object,required:!0}},emits:["del"],setup(a,{emit:p}){const{divTenThousand:d}=N(),c=x.useRouter(),o={NOT_STARTED:{title:"未开始",class:"nots"},PROCESSING:{title:"进行中",class:"ongoing"},OVER:{title:"已结束",class:"hasEnded"},ILLEGAL_SELL_OFF:{title:"违规下架",class:"off"}},_=m=>{const{id:i,shopId:r}=m;c.push({name:"bargainBaseinfo",query:{activityId:i,shopId:r}})},n=e.computed(()=>m=>Number(m)===0?"全部":m+"件");return(m,i)=>{const r=e.resolveComponent("el-button"),s=e.resolveComponent("el-button-group");return e.openBlock(),e.createElementBlock("div",I,[e.createElementVNode("div",A,[e.createElementVNode("h1",j,e.toDisplayString(a.item.name),1),e.createElementVNode("time",null,"活动时间："+e.toDisplayString(a.item.startTime)+"至"+e.toDisplayString(a.item.endTime),1),e.createElementVNode("div",null,"活动商品："+e.toDisplayString(n.value(a.item.productNum)),1),e.createElementVNode("div",$,[e.createElementVNode("span",null,"参加人数："+e.toDisplayString(a.item.peopleNum||0),1),e.createElementVNode("span",null,"支付单数："+e.toDisplayString(a.item.payOrder||0),1),e.createElementVNode("span",null,"应收金额："+e.toDisplayString(a.item.amountReceivable&&e.unref(d)(a.item.amountReceivable)||0),1)])]),e.createElementVNode("div",M,[e.createElementVNode("h1",{class:e.normalizeClass(["column_bargain__center--title",o[a.item.status].class])},e.toDisplayString(o[a.item.status].title),3)]),e.createElementVNode("div",P,[e.createVNode(s,null,{default:e.withCtx(()=>[e.createVNode(r,{round:"",onClick:i[0]||(i[0]=f=>_(a.item))},{default:e.withCtx(()=>[e.createTextVNode(" 查看活动 ")]),_:1}),e.createVNode(r,{round:"",onClick:i[1]||(i[1]=f=>p("del",a.item))},{default:e.withCtx(()=>[e.createTextVNode("删除活动")]),_:1})]),_:1})])])}}}),le="",w=(a,p)=>{const d=a.__vccOpts||a;for(const[c,o]of p)d[c]=o;return d},U=w(q,[["__scopeId","data-v-b7f1aa66"]]),k="addon-bargain/bargain/",F="addon-bargain/bargainOrder/",G=a=>V.get({url:k,params:a}),H=a=>V.del({url:k,data:a}),Y=a=>V.get({url:F,params:a}),J={BARGAINING:"砍价中",FAILED_TO_BARGAIN:"砍价失败",SUCCESSFUL_BARGAIN:"砍价成功"},K=e.defineComponent({__name:"BargainList",setup(a){const p=e.reactive({keyword:"",date:"",status:""}),d=x.useRouter(),c=e.ref([]),o=e.reactive({size:10,current:1,total:0});async function _(){const{date:r,status:s,keyword:f}=p,t={startTime:"",endTime:""};Array.isArray(r)&&r.length===2&&(t.startTime=r[0],t.endTime=r[1]);const u={...o,keyword:f,...t,shopId:L.useShopInfoStore().shopInfo.id,status:s},{code:y,data:h}=await G(u);if(y!==200)return C.ElMessage.error("获取活动列表失败");c.value=h.records,o.current=h.current,o.size=h.size,o.total=h.total}const n=async r=>{try{if(!await C.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{shopId:f,id:t}=r,{code:u,data:y}=await H([{activityId:t,shopId:f}]);if(u!==200){C.ElMessage.error("删除失败");return}C.ElMessage.success("删除成功"),c.value=c.value.filter(h=>h.id!==r.id),o.total--}catch{}},m=r=>{o.size=r,_()},i=r=>{o.current=r,_()};return(r,s)=>{const f=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(R,{modelValue:p,"onUpdate:modelValue":s[0]||(s[0]=t=>p=t),"left-btn-text":"新增砍价活动",onSearch:_,onLeftBtnClick:s[1]||(s[1]=t=>e.unref(d).push({name:"bargainBaseinfo"}))},null,8,["modelValue"]),e.createElementVNode("div",{style:e.normalizeStyle({height:"calc(100vh - 280px)"}),class:"container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value,t=>(e.openBlock(),e.createBlock(U,{key:t.id,item:t,onDel:n},null,8,["item"]))),128))],4),e.createVNode(f,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:o,"onUpdate:modelValue":s[2]||(s[2]=t=>o=t),"load-init":!0,"page-size":o.size,total:o.total,onReload:_,onHandleSizeChange:m,onHandleCurrentChange:i},null,8,["modelValue","page-size","total"])]),_:1})],64)}}}),re="",Q=Object.freeze(Object.defineProperty({__proto__:null,default:w(K,[["__scopeId","data-v-cfc5d699"]])},Symbol.toStringTag,{value:"Module"})),W={class:"bargain_origin"},X={class:"bargain_origin--name",style:{width:"100px","margin-left":"10px"}},Z={class:"bargain_origin"},v={class:"bargain_origin--shop_name",style:{width:"100px","margin-left":"10px"}},ee=e.defineComponent({__name:"BargainOrder",setup(a){const p=e.reactive({form:{keyword:""},bargainOrderList:[],chooseList:[]}),d=x.useRouter(),{form:c,bargainOrderList:o,chooseList:_}=e.toRefs(p),n=e.reactive({size:10,current:1,total:0}),{divTenThousand:m}=N();async function i(){const{code:f,data:t,msg:u}=await Y({...n,keyword:c.value.keyword});if(f!==200){C.ElMessage.error(u||"获取砍价活动详情");return}o.value=t.records,n.size=t.size,n.total=t.total,n.current=t.current}const r=f=>{n.size=f,i()},s=f=>{n.current=f,i()};return(f,t)=>{const u=e.resolveComponent("el-button"),y=e.resolveComponent("el-input"),h=e.resolveComponent("el-form-item"),ne=e.resolveComponent("el-form"),E=e.resolveComponent("el-image"),g=e.resolveComponent("el-table-column"),ae=e.resolveComponent("el-col"),T=e.resolveComponent("el-row"),oe=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(ne,{model:e.unref(c),"label-width":"60px"},{default:e.withCtx(()=>[e.createVNode(h,{label:"发起人"},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:e.unref(c).keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>e.unref(c).keyword=l),placeholder:"输入关键词",class:"input-with-select",style:{width:"200px"}},{append:e.withCtx(()=>[e.createVNode(u,{icon:e.unref(D.Search),onClick:i},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),e.createVNode(oe,{ref:"multipleTableRef",data:e.unref(o),stripe:"",style:e.normalizeStyle({height:"calc(100vh - 280px)"}),"header-row-style":{fontSize:"12px",color:"#909399"},"header-cell-style":{background:"#f6f8fa"},"cell-style":{fontSize:"12px",color:"#333333"},onSelectionChange:t[1]||(t[1]=l=>_.value=l)},{default:e.withCtx(()=>[e.createVNode(g,{label:"发起人",width:"180px"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",W,[e.createVNode(E,{style:{width:"40px",height:"40px"},fit:"",src:l.userHeadPortrait},null,8,["src"]),e.createElementVNode("div",X,e.toDisplayString(l.userNickname),1)])]),_:1}),e.createVNode(g,{label:"商品信息",width:"180px"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",Z,[e.createVNode(E,{style:{width:"40px",height:"40px"},fit:"",src:l.productPic},null,8,["src"]),e.createElementVNode("div",v,e.toDisplayString(l.productName),1)])]),_:1}),e.createVNode(g,{label:"实售价"},{default:e.withCtx(({row:l})=>[e.createElementVNode("span",null,e.toDisplayString(l.salePrice&&e.unref(m)(l.salePrice)),1)]),_:1}),e.createVNode(g,{label:"底价"},{default:e.withCtx(({row:l})=>[e.createElementVNode("span",null,e.toDisplayString(l.floorPrice&&e.unref(m)(l.floorPrice)),1)]),_:1}),e.createVNode(g,{label:"砍价人数",align:"center"},{default:e.withCtx(({row:l})=>[e.createElementVNode("span",null,e.toDisplayString(l.bargainingPeople),1)]),_:1}),e.createVNode(g,{label:"发布砍价时间",width:"200px",align:"center"},{default:e.withCtx(({row:l})=>[e.createElementVNode("span",null,e.toDisplayString(l.createTime),1)]),_:1}),e.createVNode(g,{label:"结束时间",width:"200px",align:"center"},{default:e.withCtx(({row:l})=>[e.createElementVNode("span",null,e.toDisplayString(l.endTime),1)]),_:1}),e.createVNode(g,{label:"状态",fixed:"right",width:"120",align:"center"},{default:e.withCtx(({row:l})=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(J)[l.bargainStatus]),1)]),_:1}),e.createVNode(g,{label:"操作",fixed:"right",width:"120",align:"center"},{default:e.withCtx(({row:l})=>[e.createVNode(T,{justify:"space-between",align:"middle"},{default:e.withCtx(()=>[e.createVNode(ae,{span:10},{default:e.withCtx(()=>[e.createVNode(u,{link:"",type:"primary",size:"small",onClick:()=>e.unref(d).push({name:"BargainHelpInfo",query:{id:l.id}})},{default:e.withCtx(()=>[e.createTextVNode("帮砍记录")]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:1})]),_:1},8,["data","style"]),e.createVNode(T,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:n,"onUpdate:modelValue":t[2]||(t[2]=l=>n=l),"load-init":!0,"page-size":n.size,total:n.total,onReload:i,onHandleSizeChange:r,onHandleCurrentChange:s},null,8,["modelValue","page-size","total"])]),_:1})],64)}}}),se="",te=Object.freeze(Object.defineProperty({__proto__:null,default:w(ee,[["__scopeId","data-v-d61ef3cd"]])},Symbol.toStringTag,{value:"Module"}));return O});
