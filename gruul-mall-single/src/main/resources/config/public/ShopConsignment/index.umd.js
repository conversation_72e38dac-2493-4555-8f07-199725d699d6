(function(e,R){typeof exports=="object"&&typeof module<"u"?module.exports=R(require("vue"),require("@/components/pageManage/PageManage.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/MCard.vue"),require("lodash"),require("@/composables/useConvert"),require("element-plus"),require("vue-router"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/components/pageManage/PageManage.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/MCard.vue","lodash","@/composables/useConvert","element-plus","vue-router","@/apis/http"],R):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopConsignment=R(e.ShopConsignmentContext.Vue,e.ShopConsignmentContext.PageManage,e.ShopConsignmentContext.QTable,e.ShopConsignmentContext.QTableColumn,e.ShopConsignmentContext.MCard,e.ShopConsignmentContext.Lodash,e.ShopConsignmentContext.UseConvert,e.ShopConsignmentContext.ElementPlus,e.ShopConsignmentContext.VueRouter,e.ShopConsignmentContext.Request))})(this,function(e,R,A,E,G,F,L,T,Q,k){"use strict";var z=document.createElement("style");z.textContent=`.preview[data-v-e0f064d8]{line-height:1.5rem}.preview__image span[data-v-e0f064d8]{vertical-align:top}.commodity-info[data-v-ba05f0f6]{display:flex;align-items:center}.commodity-info img[data-v-ba05f0f6]{width:60px;height:60px}.commodity-info span[data-v-ba05f0f6]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}[data-v-ba05f0f6] .el-form-item .el-form-item{margin-top:8px}.tools[data-v-fe42f1ae]{padding:15px 0}.commodity-info[data-v-fe42f1ae]{display:flex;align-items:center}.commodity-info img[data-v-fe42f1ae]{width:60px;height:60px}.commodity-info span[data-v-fe42f1ae]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}img[data-v-0c3708f5]{vertical-align:top}.good[data-v-ef42aa8e]{padding:0 30px 0 45px;box-sizing:border-box;font-size:12px;font-family:Microsoft YaHei,Microsoft YaHei-Normal;font-weight:400;color:#000}.good__info[data-v-ef42aa8e]{margin-bottom:24px}.good__img[data-v-ef42aa8e]{display:flex;justify-content:flex-start;align-items:flex-start}.good__spec[data-v-ef42aa8e]{width:758px}.b[data-v-ef42aa8e]{font-size:12px;color:#000}.tools[data-v-d9c0f592]{padding:15px 0}.commodity-info[data-v-d9c0f592]{display:flex;align-items:center}.commodity-info img[data-v-d9c0f592]{width:60px;height:60px}.commodity-info span[data-v-d9c0f592]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.settings__title[data-v-069d8820]{margin-bottom:8px}.settings__container[data-v-069d8820]{padding:0 15px}.settings__container--line[data-v-069d8820]{line-height:1.3}.settings__container .text-red[data-v-069d8820]{color:red}.settings__form[data-v-069d8820]{margin-top:30px;display:flex}.settings__form>span[data-v-069d8820]{margin-right:15px;margin-top:8px}
`,document.head.appendChild(z);const v=e.defineComponent({__name:"ShopConsignment",setup(o){const i=e.ref("goodSource"),l={goodSource:e.defineAsyncComponent(()=>Promise.resolve().then(()=>We)),shipped:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ht)),settings:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Dt))},c=[{name:"goodSource",label:"货源"},{name:"shipped",label:"已铺货"},{name:"settings",label:"代销设置"}];return(a,V)=>{const s=e.resolveComponent("el-tab-pane"),g=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(g,{modelValue:i.value,"onUpdate:modelValue":V[0]||(V[0]=m=>i.value=m),style:{"margin-top":"15px"}},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(c,(m,r)=>e.createVNode(s,{key:r,label:m.label,name:m.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(l[i.value])))],64)}}}),ee=o=>k.get({url:"addon-supplier/supplier/manager/product/getSupplyList",params:{...o}}),j=(o={})=>k.get({url:"gruul-mall-shop/shop/info/getSupplierInfo",params:o}),te=(o,i)=>k.get({url:`gruul-mall-storage/storage/shop/${o}/product/${i}`}),H=()=>k.get({url:"gruul-mall-addon-platform/platform/shop/signing/category/choosable/list"}),oe=o=>k.get({url:"gruul-mall-goods/goods/product/category",params:o}),Y=()=>k.get({url:"gruul-mall-goods/consignment/config"}),ne=o=>k.post({url:"gruul-mall-goods/consignment/config",data:o}),le=o=>k.get({url:"gruul-mall-goods/consignment/pave/goods",params:o}),ae=o=>k.post({url:"gruul-mall-goods/consignment/pave/goods",data:o}),se=o=>k.put({url:`gruul-mall-goods/consignment/product/update/status/${o}`}),ie={style:{background:"#f9f9f9"}},re=e.defineComponent({__name:"search",emits:["changeShow","search"],setup(o,{emit:i}){const l=e.ref(!1),c=e.ref([]),a=e.ref([]);e.watch(()=>l.value,r=>i("changeShow",r));const V=async()=>{const{data:r,code:n}=await H();a.value=r};e.onMounted(()=>V());const s=e.reactive({supplierId:"",platformCategoryParentId:"",supplierGoodsName:""}),g=()=>{const r=F.cloneDeep(s);r.platformCategoryParentId=Array.isArray(r.platformCategoryParentId)?r.platformCategoryParentId.pop():"",i("search",r)},m=async(r="")=>{var _;const n=await j({supplierName:r});n.data&&((_=n.data)!=null&&_.length)&&(c.value=n.data)};return(r,n)=>{const _=e.resolveComponent("el-option"),d=e.resolveComponent("el-select"),t=e.resolveComponent("el-form-item"),f=e.resolveComponent("el-col"),u=e.resolveComponent("el-cascader"),N=e.resolveComponent("el-input"),C=e.resolveComponent("el-row"),b=e.resolveComponent("el-button"),P=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",ie,[e.createVNode(G,{modelValue:l.value,"onUpdate:modelValue":n[3]||(n[3]=h=>l.value=h)},{default:e.withCtx(()=>[e.createVNode(P,{ref:"form",model:s,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(C,null,{default:e.withCtx(()=>[e.createVNode(f,{span:8},{default:e.withCtx(()=>[e.createVNode(t,{label:"供应商"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:s.supplierId,"onUpdate:modelValue":n[0]||(n[0]=h=>s.supplierId=h),remote:"",filterable:"",clearable:"","remote-method":m,placeholder:"请选择供应商"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value,h=>(e.openBlock(),e.createBlock(_,{key:h.id,value:h.id,label:h.name},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(f,{span:8},{default:e.withCtx(()=>[e.createVNode(t,{label:"平台类目"},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:s.platformCategoryParentId,"onUpdate:modelValue":n[1]||(n[1]=h=>s.platformCategoryParentId=h),clearable:"",style:{width:"62.5%"},options:a.value,placeholder:"请选择平台类目","show-all-levels":"",props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"}},null,8,["modelValue","options"])]),_:1})]),_:1}),e.createVNode(f,{span:8},{default:e.withCtx(()=>[e.createVNode(t,{label:"商品名称"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:s.supplierGoodsName,"onUpdate:modelValue":n[2]||(n[2]=h=>s.supplierGoodsName=h),placeholder:"请输入商品名称",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(t,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(b,{class:"from_btn",type:"primary",round:"",onClick:g},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),ce=o=>(e.pushScopeId("data-v-e0f064d8"),o=o(),e.popScopeId(),o),de={class:"preview"},pe={class:"preview__name"},me={class:"preview__image"},_e=ce(()=>e.createElementVNode("span",null,"商品图片：",-1)),fe=e.defineComponent({__name:"preview",props:{commodityItem:{type:Object,default:()=>({}),required:!0}},setup(o){const{divTenThousand:i}=L();return(l,c)=>{var g,m;const a=e.resolveComponent("el-image"),V=e.resolveComponent("el-table-column"),s=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",de,[e.createElementVNode("div",pe,"商品名称："+e.toDisplayString(o.commodityItem.productName),1),e.createElementVNode("div",me,[_e,e.createVNode(a,{src:(m=(g=o.commodityItem.albumPics)==null?void 0:g.split(","))==null?void 0:m.shift(),style:{width:"80px",height:"80px"}},null,8,["src"])]),e.createVNode(s,{data:o.commodityItem.storageSkus},{default:e.withCtx(()=>[e.createVNode(V,{label:"商品规格"},{default:e.withCtx(({row:r})=>{var n,_;return[e.createTextVNode(e.toDisplayString(((n=o.commodityItem.storageSkus)==null?void 0:n.length)===1?"单规格":(_=r==null?void 0:r.specs)==null?void 0:_.join(";")),1)]}),_:1}),e.createVNode(V,{label:"供货价"},{default:e.withCtx(({row:r})=>[e.createTextVNode(e.toDisplayString(e.unref(i)(r==null?void 0:r.salePrice)),1)]),_:1}),e.createVNode(V,{label:"库存"},{default:e.withCtx(({row:r})=>[e.createTextVNode(e.toDisplayString((r==null?void 0:r.stockType)==="UNLIMITED"?"无限库存":r==null?void 0:r.stock),1)]),_:1})]),_:1},8,["data"])])}}}),It="",B=(o,i)=>{const l=o.__vccOpts||o;for(const[c,a]of i)l[c]=a;return l},ge=B(fe,[["__scopeId","data-v-e0f064d8"]]),{divTenThousand:K,mulTenThousand:O}=L(),he={expandTrigger:"hover",label:"name",value:"id"};function W(o,i){const l=o===3;for(let c=0;c<i.length;){const a=i[c];if(l){a.disabled=!1,c++;continue}const V=a.children||a.secondCategoryVos||a.categoryThirdlyVos;delete a.secondCategoryVos,delete a.categoryThirdlyVos;const s=!V||V.length===0;if(a.disabled=s,s){i.splice(c,1);continue}if(W(o+1,V),V.length===0){i.splice(c,1);continue}a.children=V,c++}return i}const Ve=o=>{const i=e.ref(null),l=e.reactive({shopCategory:"",consignmentPriceSetting:{type:"UNIFY",sale:0,scribe:0},unifyPriceSetting:{type:"",sale:0,scribe:0}}),c={shopCategory:{required:!0,message:"请选择商家分类",type:"array",trigger:"change"},"consignmentPriceSetting.scribe":{validator:(n,_,d)=>{l.consignmentPriceSetting.type==="RATE"&&(_>100&&d(new Error("划线价比率不能大于100%")),d())},trigger:"blur"},"consignmentPriceSetting.sale":{validator:(n,_,d)=>{l.consignmentPriceSetting.type==="RATE"&&(_>100&&d(new Error("销售价比率不能大于100%")),d())},trigger:"blur"}},a=e.computed(()=>n=>{const _=((n==null?void 0:n.salePrices)||[]).map(f=>Number(f)),d=Math.min(..._),t=Math.max(..._);return d===t?t/1e4:`${d/1e4}~${t/1e4}`}),V=e.computed(()=>n=>{let _=0,d=!1;return n==null||n.forEach(t=>{_+=Number(t.stock),t.stockType==="LIMITED"&&(d=!0)}),d?_:"无限库存"}),s=e.ref([]);async function g(){const{code:n,data:_}=await oe({current:1,size:500});if(n!==200){T.ElMessage.error("获取店铺分类失败");return}s.value=W(1,_.records)}const m=async()=>{const{data:n,code:_}=await Y();_===200&&n&&(l.unifyPriceSetting.sale=K(n==null?void 0:n.sale).toNumber(),l.unifyPriceSetting.scribe=K(n==null?void 0:n.scribe).toNumber(),l.unifyPriceSetting.type=n==null?void 0:n.type)},r=()=>new Promise((n,_)=>{var t,f,u;if(i.value){const N={shopCategory:{one:(t=l.shopCategory)==null?void 0:t[0],two:(f=l.shopCategory)==null?void 0:f[1],three:(u=l.shopCategory)==null?void 0:u[2]},shopProductKeys:o.value.map(b=>({shopId:b.shopId,productId:b.id}))},C={};l.consignmentPriceSetting.type==="UNIFY"?(C.type=l.unifyPriceSetting.type,C.sale=O(l.unifyPriceSetting.sale).toString(),C.scribe=O(l.unifyPriceSetting.scribe).toString()):(C.type=l.consignmentPriceSetting.type,C.sale=O(l.consignmentPriceSetting.sale).toString(),C.scribe=O(l.consignmentPriceSetting.scribe).toString()),N.consignmentPriceSetting=C,n(N)}else _("valid error")});return m(),g(),{formRefs:i,formModel:l,formRules:c,shopCategoryList:s,shopCascaderProps:he,computedSalePrice:a,computedSuplier:V,buildData:r}},ue=e.createElementVNode("span",null,"销售价 = 供货价 + （供货价 *  ",-1),ye=e.createElementVNode("span",null," % ）",-1),Ne=e.createElementVNode("span",null,"划线价 = 销售价 + （销售价 *  ",-1),Ce=e.createElementVNode("span",null," % ）",-1),be=e.createElementVNode("span",null,"销售价 = 供货价 +  ",-1),xe=e.createElementVNode("span",null," 元",-1),Se=e.createElementVNode("span",null,"划线价 = 销售价 +  ",-1),we=e.createElementVNode("span",null," 元",-1),Ee=e.defineComponent({__name:"batch-unify-settings",props:{type:{default:""},sale:{default:0},scribe:{default:0}},setup(o){return(i,l)=>{const c=e.resolveComponent("el-input-number"),a=e.resolveComponent("el-form-item");return i.type==="RATE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(a,{prop:"sale"},{default:e.withCtx(()=>[ue,e.createVNode(c,{"model-value":i.sale,controls:!1,precision:2,disabled:""},null,8,["model-value"]),ye]),_:1}),e.createVNode(a,{prop:"scribe"},{default:e.withCtx(()=>[Ne,e.createVNode(c,{"model-value":i.scribe,controls:!1,precision:2,disabled:""},null,8,["model-value"]),Ce]),_:1})],64)):i.type==="REGULAR"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createVNode(a,{prop:"sale"},{default:e.withCtx(()=>[be,e.createVNode(c,{"model-value":i.sale,controls:!1,precision:2,disabled:""},null,8,["model-value"]),xe]),_:1}),e.createVNode(a,{prop:"scribe"},{default:e.withCtx(()=>[Se,e.createVNode(c,{"model-value":i.scribe,controls:!1,precision:2,disabled:""},null,8,["model-value"]),we]),_:1})],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[],64))}}}),D=o=>(e.pushScopeId("data-v-ba05f0f6"),o=o(),e.popScopeId(),o),ke={class:"price-settings"},Pe=D(()=>e.createElementVNode("span",null,"销售价 = 供货价 + （供货价 *  ",-1)),Te=D(()=>e.createElementVNode("span",null," % ）",-1)),$e=D(()=>e.createElementVNode("span",null,"划线价 = 销售价 + （销售价 *  ",-1)),De=D(()=>e.createElementVNode("span",null," % ）",-1)),Ie=D(()=>e.createElementVNode("span",null,"销售价 = 供货价 +  ",-1)),Be=D(()=>e.createElementVNode("span",null," 元",-1)),Re=D(()=>e.createElementVNode("span",null,"划线价 = 销售价 +  ",-1)),Le=D(()=>e.createElementVNode("span",null," 元",-1)),Me={class:"commodity-info"},Ue=["src"],Fe=e.defineComponent({__name:"batch-distrubution",props:{batchItems:{type:Object,required:!0}},setup(o,{expose:i}){const l=o,c=e.computed({get(){return l.batchItems},set(d){}}),{formModel:a,formRefs:V,formRules:s,shopCategoryList:g,shopCascaderProps:m,computedSalePrice:r,computedSuplier:n,buildData:_}=Ve(c);return i({buildData:_}),(d,t)=>{const f=e.resolveComponent("el-cascader"),u=e.resolveComponent("el-form-item"),N=e.resolveComponent("el-radio"),C=e.resolveComponent("el-radio-group"),b=e.resolveComponent("el-input-number"),P=e.resolveComponent("el-form"),h=e.resolveComponent("el-table-column"),x=e.resolveComponent("el-link"),S=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(P,{ref_key:"formRefs",ref:V,model:e.unref(a),rules:e.unref(s)},{default:e.withCtx(()=>[e.createVNode(u,{label:"店铺类目",prop:"shopCategory"},{default:e.withCtx(()=>[e.createVNode(f,{ref:"shopCategoryRef",modelValue:e.unref(a).shopCategory,"onUpdate:modelValue":t[0]||(t[0]=p=>e.unref(a).shopCategory=p),clearable:"",style:{width:"62.5%"},class:"inputWidth",options:e.unref(g),props:e.unref(m),placeholder:"请选择店铺分类","show-all-levels":""},null,8,["modelValue","options","props"])]),_:1}),e.createVNode(u,{label:"价格设置"},{default:e.withCtx(()=>[e.createElementVNode("div",ke,[e.createVNode(C,{modelValue:e.unref(a).consignmentPriceSetting.type,"onUpdate:modelValue":t[1]||(t[1]=p=>e.unref(a).consignmentPriceSetting.type=p)},{default:e.withCtx(()=>[e.createVNode(N,{label:"UNIFY"},{default:e.withCtx(()=>[e.createTextVNode("统一设价")]),_:1}),e.createVNode(N,{label:"RATE"},{default:e.withCtx(()=>[e.createTextVNode("按比例设价")]),_:1}),e.createVNode(N,{label:"REGULAR"},{default:e.withCtx(()=>[e.createTextVNode("固定金额设价")]),_:1})]),_:1},8,["modelValue"]),e.unref(a).consignmentPriceSetting.type==="UNIFY"?(e.openBlock(),e.createBlock(Ee,{key:0,sale:e.unref(a).unifyPriceSetting.sale,scribe:e.unref(a).unifyPriceSetting.scribe,type:e.unref(a).unifyPriceSetting.type},null,8,["sale","scribe","type"])):e.unref(a).consignmentPriceSetting.type==="RATE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createVNode(u,{prop:"consignmentPriceSetting.sale"},{default:e.withCtx(()=>[Pe,e.createVNode(b,{modelValue:e.unref(a).consignmentPriceSetting.sale,"onUpdate:modelValue":t[2]||(t[2]=p=>e.unref(a).consignmentPriceSetting.sale=p),controls:!1,precision:2},null,8,["modelValue"]),Te]),_:1}),e.createVNode(u,{prop:"consignmentPriceSetting.scribe"},{default:e.withCtx(()=>[$e,e.createVNode(b,{modelValue:e.unref(a).consignmentPriceSetting.scribe,"onUpdate:modelValue":t[3]||(t[3]=p=>e.unref(a).consignmentPriceSetting.scribe=p),controls:!1,precision:2},null,8,["modelValue"]),De]),_:1})],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[e.createVNode(u,{prop:"consignmentPriceSetting.sale"},{default:e.withCtx(()=>[Ie,e.createVNode(b,{modelValue:e.unref(a).consignmentPriceSetting.sale,"onUpdate:modelValue":t[4]||(t[4]=p=>e.unref(a).consignmentPriceSetting.sale=p),controls:!1,precision:2},null,8,["modelValue"]),Be]),_:1}),e.createVNode(u,{prop:"consignmentPriceSetting.scribe"},{default:e.withCtx(()=>[Re,e.createVNode(b,{modelValue:e.unref(a).consignmentPriceSetting.scribe,"onUpdate:modelValue":t[5]||(t[5]=p=>e.unref(a).consignmentPriceSetting.scribe=p),controls:!1,precision:2},null,8,["modelValue"]),Le]),_:1})],64))])]),_:1})]),_:1},8,["model","rules"]),e.createVNode(S,{data:c.value},{default:e.withCtx(()=>[e.createVNode(h,{label:"商品名称",align:"left"},{default:e.withCtx(({row:p})=>{var w,I;return[e.createElementVNode("div",Me,[e.createElementVNode("img",{src:(I=(w=p.albumPics)==null?void 0:w.split(","))==null?void 0:I.shift()},null,8,Ue),e.createElementVNode("span",null,e.toDisplayString(p.productName),1)])]}),_:1}),e.createVNode(h,{label:"供货价",align:"center"},{default:e.withCtx(({row:p})=>[e.createTextVNode(" ￥"+e.toDisplayString(e.unref(r)(p)),1)]),_:1}),e.createVNode(h,{label:"库存",align:"center",width:"100"},{default:e.withCtx(({row:p})=>[e.createTextVNode(e.toDisplayString(e.unref(n)(p==null?void 0:p.storageSkus)),1)]),_:1}),e.createVNode(h,{label:"操作",width:"80"},{default:e.withCtx(({$index:p})=>[e.createVNode(x,{type:"danger",onClick:()=>c.value.splice(p,1)},{default:e.withCtx(()=>[e.createTextVNode("移除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])],64)}}}),Rt="",Oe=B(Fe,[["__scopeId","data-v-ba05f0f6"]]),qe=()=>{const o=e.ref(),i=e.ref(!1);return{previewData:o,handlePreviewData:c=>{o.value=c,i.value=!0},showPreviewDialog:i}},Ae=()=>{const o=Q.useRouter(),i=e.ref("calc(100vh - 350px)"),l=e.ref({}),c=e.reactive({page:{current:1,size:10},total:0}),a=e.ref([]),V=e.ref([]),s=d=>{i.value=d?"calc(100vh - 450px)":"calc(100vh - 350px)"},g=e.computed(()=>d=>{const t=((d==null?void 0:d.salePrices)||[]).map(N=>Number(N)),f=Math.min(...t),u=Math.max(...t);return f===u?u/1e4:`${f/1e4}~${u/1e4}`}),m=e.computed(()=>d=>{let t=0,f=!1;return d==null||d.forEach(u=>{t+=Number(u.stock),u.stockType==="LIMITED"&&(f=!0)}),f?t:"无限库存"}),r=(d={})=>{l.value=d,c.page.current=1,n()},n=async()=>{const d=await ee({...c.page,sellType:"CONSIGNMENT",...l.value});d.data&&(c.total=Number(d.data.total),a.value=d.data.records)};return{tableHeight:i,pagination:c,tableList:a,multiSelect:V,changeShow:s,computedSalePrice:g,computedSuplier:m,handleSearch:r,initList:n,handleDistribution:d=>{o.push({path:"/goods/consignment/distribution",query:{shopId:d.shopId,id:d.id}})}}},Ge=()=>{const o=e.ref(null),i=e.ref(!1),l=e.ref([]);return{batchDistrubutionRef:o,showBatchDistributionDialog:i,checkedDistributuion:l,openBatchDistributionDialog:a=>{if(a.length===0)return T.ElMessage.error({message:"请选择需要铺货的信息"});l.value=F.cloneDeep(a),i.value=!0}}},ze={class:"tools"},je={class:"commodity-info"},He=["src"],Ye={class:"dialog-footer"},Ke=e.defineComponent({__name:"index",setup(o){const{previewData:i,showPreviewDialog:l,handlePreviewData:c}=qe(),{tableHeight:a,pagination:V,tableList:s,multiSelect:g,changeShow:m,computedSalePrice:r,computedSuplier:n,handleSearch:_,initList:d,handleDistribution:t}=Ae(),{showBatchDistributionDialog:f,openBatchDistributionDialog:u,batchDistrubutionRef:N,checkedDistributuion:C}=Ge(),b=async()=>{var p;const P=await((p=N.value)==null?void 0:p.buildData()),{code:h,success:x,msg:S}=await ae(P);h===200&&x?(T.ElMessage.success({message:S||"批量铺货成功"}),d(),f.value=!1):T.ElMessage.error({message:S||"批量铺货失败"})};return(P,h)=>{const x=e.resolveComponent("el-button"),S=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(re,{onChangeShow:e.unref(m),onSearch:e.unref(_)},null,8,["onChangeShow","onSearch"]),e.createElementVNode("div",ze,[e.createVNode(x,{type:"primary",onClick:h[0]||(h[0]=p=>e.unref(u)(e.unref(g)))},{default:e.withCtx(()=>[e.createTextVNode("一键铺货")]),_:1})]),e.createVNode(e.unref(A),{checkedItem:e.unref(g),"onUpdate:checkedItem":h[1]||(h[1]=p=>e.isRef(g)?g.value=p:null),data:e.unref(s),style:e.normalizeStyle({height:e.unref(a),overflowY:"auto"}),selection:!0,class:"table"},{default:e.withCtx(()=>[e.createVNode(E,{label:"供应商",align:"center",width:"130",prop:"supplierName"}),e.createVNode(E,{label:"商品名称",align:"left"},{default:e.withCtx(({row:p})=>{var w,I;return[e.createElementVNode("div",je,[e.createElementVNode("img",{src:(I=(w=p.albumPics)==null?void 0:w.split(","))==null?void 0:I.shift()},null,8,He),e.createElementVNode("span",null,e.toDisplayString(p.productName),1)])]}),_:1}),e.createVNode(E,{label:"供货价",align:"center",width:"150"},{default:e.withCtx(({row:p})=>[e.createTextVNode(" ￥"+e.toDisplayString(e.unref(r)(p)),1)]),_:1}),e.createVNode(E,{label:"供应商库存",align:"center",width:"100"},{default:e.withCtx(({row:p})=>[e.createTextVNode(e.toDisplayString(e.unref(n)(p==null?void 0:p.storageSkus)),1)]),_:1}),e.createVNode(E,{label:"操作",fixed:"right",width:"150"},{default:e.withCtx(({row:p})=>[e.createElementVNode("div",null,[e.createVNode(x,{link:"",type:"primary",size:"small",onClick:w=>e.unref(c)(p)},{default:e.withCtx(()=>[e.createTextVNode("查看")]),_:2},1032,["onClick"]),e.createVNode(x,{link:"",type:"primary",size:"small",onClick:w=>e.unref(t)(p)},{default:e.withCtx(()=>[e.createTextVNode("一键铺货")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["checkedItem","data","style"]),e.createVNode(R,{modelValue:e.unref(V).page,"onUpdate:modelValue":h[2]||(h[2]=p=>e.unref(V).page=p),"load-init":"",total:e.unref(V).total,onReload:e.unref(d)},null,8,["modelValue","total","onReload"]),e.createVNode(S,{modelValue:e.unref(l),"onUpdate:modelValue":h[3]||(h[3]=p=>e.isRef(l)?l.value=p:null),title:"商品详情",width:"650px"},{default:e.withCtx(()=>[e.createVNode(ge,{"commodity-item":e.unref(i)},null,8,["commodity-item"])]),_:1},8,["modelValue"]),e.createVNode(S,{modelValue:e.unref(f),"onUpdate:modelValue":h[5]||(h[5]=p=>e.isRef(f)?f.value=p:null),title:"一键铺货",width:"750px","destroy-on-close":"","close-on-click-modal":!1},{footer:e.withCtx(()=>[e.createElementVNode("span",Ye,[e.createVNode(x,{onClick:h[4]||(h[4]=p=>f.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(x,{type:"primary",onClick:b},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(Oe,{ref_key:"batchDistrubutionRef",ref:N,"batch-items":e.unref(C)},null,8,["batch-items"])]),_:1},8,["modelValue"])],64)}}}),Ft="",We=Object.freeze(Object.defineProperty({__proto__:null,default:B(Ke,[["__scopeId","data-v-fe42f1ae"]])},Symbol.toStringTag,{value:"Module"})),Xe={style:{background:"#f9f9f9"}},Je=e.defineComponent({__name:"search",emits:["changeShow","search"],setup(o,{emit:i}){const l=e.ref(!1),c=e.ref([]),a=e.ref([]);e.watch(()=>l.value,r=>i("changeShow",r));const V=async()=>{const{data:r,code:n}=await H();a.value=r};e.onMounted(()=>V());const s=e.reactive({supplierId:"",platformCategoryParentId:"",supplierGoodsName:""}),g=()=>{const r=F.cloneDeep(s);r.platformCategoryParentId=Array.isArray(r.platformCategoryParentId)?r.platformCategoryParentId.pop():"",i("search",r)},m=async(r="")=>{var _;const n=await j({supplierName:r});n.data&&((_=n.data)!=null&&_.length)&&(c.value=n.data)};return(r,n)=>{const _=e.resolveComponent("el-option"),d=e.resolveComponent("el-select"),t=e.resolveComponent("el-form-item"),f=e.resolveComponent("el-col"),u=e.resolveComponent("el-cascader"),N=e.resolveComponent("el-input"),C=e.resolveComponent("el-row"),b=e.resolveComponent("el-button"),P=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",Xe,[e.createVNode(G,{modelValue:l.value,"onUpdate:modelValue":n[3]||(n[3]=h=>l.value=h)},{default:e.withCtx(()=>[e.createVNode(P,{ref:"form",model:s,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(C,null,{default:e.withCtx(()=>[e.createVNode(f,{span:8},{default:e.withCtx(()=>[e.createVNode(t,{label:"供应商"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:s.supplierId,"onUpdate:modelValue":n[0]||(n[0]=h=>s.supplierId=h),remote:"",filterable:"",clearable:"","remote-method":m,placeholder:"请选择供应商"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value,h=>(e.openBlock(),e.createBlock(_,{key:h.id,value:h.id,label:h.name},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(f,{span:8},{default:e.withCtx(()=>[e.createVNode(t,{label:"平台类目"},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:s.platformCategoryParentId,"onUpdate:modelValue":n[1]||(n[1]=h=>s.platformCategoryParentId=h),clearable:"",style:{width:"62.5%"},options:a.value,placeholder:"请选择平台类目","show-all-levels":"",props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"}},null,8,["modelValue","options"])]),_:1})]),_:1}),e.createVNode(f,{span:8},{default:e.withCtx(()=>[e.createVNode(t,{label:"商品名称"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:s.supplierGoodsName,"onUpdate:modelValue":n[2]||(n[2]=h=>s.supplierGoodsName=h),placeholder:"请输入商品名称",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(t,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(b,{class:"from_btn",type:"primary",round:"",onClick:g},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),{divTenThousand:q}=L(),Ze=()=>{const o=e.ref([]),i=e.ref([]),l=e.ref("calc(100vh - 350px)"),c=e.reactive({page:{current:1,size:10},total:0}),a=e.ref({status:""}),V=t=>{l.value=t?"calc(100vh - 450px)":"calc(100vh - 350px)"},s=(t={})=>{a.value={...t,status:a.value.status},c.page.current=1,m()},g=e.computed(()=>t=>{let f=0,u=!1;return t==null||t.forEach(N=>{f+=Number(N.stock),N.stockType==="LIMITED"&&(u=!0)}),u?f:"无限库存"}),m=async()=>{const t=await le({...c.page,...a.value});t.data&&(c.total=Number(t.data.total),o.value=t.data.records)},r=e.computed(()=>t=>{var C;const f=(C=(t==null?void 0:t.salePrices)||[])==null?void 0:C.map(b=>parseInt(b)),u=Math.min(...f),N=Math.max(...f);return u===N?q(u):`${q(u)}~${q(N)}`}),n=t=>{a.value.status=t,c.page.current=1,m()},_=t=>{T.ElMessageBox.confirm("确认上架当前商品").then(()=>{se(t).then(({code:f,msg:u})=>{f===200?(T.ElMessage.success({message:u||"上架成功"}),m()):T.ElMessage.error({message:u||"上架失败"})})})};let d;return(t=>{t.SELL_ON="已上架",t.SELL_OFF="已下架",t.PLATFORM_SELL_OFF="违规下架"})(d||(d={})),{searchCondition:a,tableList:o,multiSelect:i,changeShow:V,handleSearch:s,initList:m,pagination:c,tableHeight:l,computedSalePrice:r,computedSuplierStock:g,changeStatus:n,handleSaleOn:_,StatusMap:d}},Qe=()=>{const o=e.ref(),i=e.ref(!1);return{currentRow:o,showPreviewDialog:i,openPreviewDialog:c=>{o.value=c,i.value=!0}}},ve=()=>{const o=e.ref(!1),i=e.ref();return{violationReason:i,showViolationReason:o,openViolationReasonDialog:c=>{i.value=c,o.value=!0}}},et={style:{"line-height":"30px"}},tt=["src"],ot=e.defineComponent({__name:"violation-reason",props:{productViolation:{type:Object,default:()=>({})}},setup(o){const i=o,l={PROHIBITED:"违禁品",COUNTERFEIT:"假冒伪劣",EXCESSIVE_PLATFORM_INTERVENTION:"平台介入率太高",TITLE_IRREGULARITY:"标题有问题",OTHER:"其他"},c=e.computed(()=>{var a,V,s;return{...i.productViolation,violationType:l[(a=i.productViolation)==null?void 0:a.violationType],violationEvidence:(s=(V=i.productViolation)==null?void 0:V.violationEvidence)==null?void 0:s.split(",")}});return(a,V)=>{const s=e.resolveComponent("el-col"),g=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",et,[e.createVNode(g,{gutter:8},{default:e.withCtx(()=>[e.createVNode(s,{span:12},{default:e.withCtx(()=>[e.createElementVNode("div",null,"检查员："+e.toDisplayString(c.value.rummager),1)]),_:1}),e.createVNode(s,{span:12},{default:e.withCtx(()=>[e.createElementVNode("div",null,"检查时间："+e.toDisplayString(c.value.examineDateTime),1)]),_:1})]),_:1}),e.createElementVNode("div",null,"类型："+e.toDisplayString(c.value.violationType),1),e.createElementVNode("div",null,"原因："+e.toDisplayString(c.value.violationExplain),1),e.createElementVNode("div",null,[e.createTextVNode(" 相关证据： "),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value.violationEvidence,(m,r)=>(e.openBlock(),e.createElementBlock("img",{key:r,src:m,class:"violation-evidence"},null,8,tt))),128))])])}}}),zt="",nt=B(ot,[["__scopeId","data-v-0c3708f5"]]),X=o=>(e.pushScopeId("data-v-ef42aa8e"),o=o(),e.popScopeId(),o),lt={class:"good"},at={class:"good__info"},st={class:"good__info"},it={class:"good__img"},rt=X(()=>e.createElementVNode("text",null,"商品图片：",-1)),ct=X(()=>e.createElementVNode("div",null,"规格：",-1)),dt=e.defineComponent({__name:"preview",props:{currentProduct:{type:Object,default:()=>({})}},setup(o){const i=o,{divTenThousand:l}=L(),c=e.ref([]),a=e.ref([]),V=async()=>{const{data:s}=await te(i.currentProduct.supplierId,i.currentProduct.id);c.value=(s==null?void 0:s.skus)||[],a.value=(s==null?void 0:s.specGroups)||[]};return e.onMounted(()=>V()),(s,g)=>{var _,d;const m=e.resolveComponent("el-image"),r=e.resolveComponent("el-table-column"),n=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",lt,[e.createElementVNode("div",at,"店铺名称："+e.toDisplayString((_=i.currentProduct)==null?void 0:_.supplierName),1),e.createElementVNode("div",st,"商品名称："+e.toDisplayString((d=i.currentProduct)==null?void 0:d.name),1),e.createElementVNode("div",it,[rt,e.createVNode(m,{style:{width:"100px",height:"100px"},src:i.currentProduct.pic,"preview-src-list":[i.currentProduct.pic]},null,8,["src","preview-src-list"])]),ct,e.createVNode(n,{data:c.value,height:"350",stripe:"","header-row-style":{"font-size":"12px",color:"#000000"}},{default:e.withCtx(()=>[a.value.length?(e.openBlock(),e.createBlock(r,{key:0,label:"规格",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString((t==null?void 0:t.specs)&&(t==null?void 0:t.specs.join("-"))),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(r,{label:"sku图",align:"center"},{default:e.withCtx(({row:t})=>[e.createVNode(m,{src:t==null?void 0:t.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e.createVNode(r,{label:"实售价(元)",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.salePrice&&e.unref(l)(t.salePrice)),1)]),_:1}),e.createVNode(r,{prop:"originalPrice",label:"指导价(元)",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.price&&e.unref(l)(t.price)),1)]),_:1}),e.createVNode(r,{prop:"weight",label:"重量(kg)",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.weight),1)]),_:1})]),_:1},8,["data"])])}}}),jt="",pt=B(dt,[["__scopeId","data-v-ef42aa8e"]]),mt={class:"commodity-info"},_t=["src"],ft={class:"commodity-info__main"},gt=e.defineComponent({__name:"index",setup(o){const{tableList:i,changeShow:l,searchCondition:c,computedSalePrice:a,handleSearch:V,pagination:s,initList:g,multiSelect:m,tableHeight:r,computedSuplierStock:n,changeStatus:_,handleSaleOn:d,StatusMap:t}=Ze(),{violationReason:f,showViolationReason:u,openViolationReasonDialog:N}=ve(),{currentRow:C,showPreviewDialog:b,openPreviewDialog:P}=Qe();return(h,x)=>{const S=e.resolveComponent("el-tab-pane"),p=e.resolveComponent("el-tabs"),w=e.resolveComponent("el-button"),I=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(Je,{onChangeShow:e.unref(l),onSearch:e.unref(V)},null,8,["onChangeShow","onSearch"]),e.createVNode(p,{"model-value":e.unref(c).status,"onUpdate:modelValue":e.unref(_)},{default:e.withCtx(()=>[e.createVNode(S,{name:"",label:"全部"}),e.createVNode(S,{name:"SELL_ON",label:"已上架"}),e.createVNode(S,{name:"SELL_OFF",label:"已下架"}),e.createVNode(S,{name:"PLATFORM_SELL_OFF",label:"违规下架"})]),_:1},8,["model-value","onUpdate:modelValue"]),e.createVNode(e.unref(A),{checkedItem:e.unref(m),"onUpdate:checkedItem":x[0]||(x[0]=y=>e.isRef(m)?m.value=y:null),data:e.unref(i),style:e.normalizeStyle({height:e.unref(r),overflowY:"auto"}),selection:!0,class:"table"},{default:e.withCtx(()=>[e.createVNode(E,{label:"供应商",align:"center",width:"130",prop:"supplierName"}),e.createVNode(E,{label:"商品名称",align:"left"},{default:e.withCtx(({row:y})=>{var M,U;return[e.createElementVNode("div",mt,[e.createElementVNode("img",{src:(U=(M=y.pic)==null?void 0:M.split(","))==null?void 0:U.shift()},null,8,_t),e.createElementVNode("div",ft,[e.createElementVNode("span",null,e.toDisplayString(y.name),1),e.createElementVNode("span",null,e.toDisplayString(e.unref(a)(y)),1)])])]}),_:1}),e.createVNode(E,{label:"供应商库存",align:"center",width:"100"},{default:e.withCtx(({row:y})=>[e.createTextVNode(e.toDisplayString(e.unref(n)(y==null?void 0:y.storageSkus)),1)]),_:1}),e.createVNode(E,{label:"状态",align:"center",width:"100"},{default:e.withCtx(({row:y})=>[e.createTextVNode(e.toDisplayString(e.unref(t)[y==null?void 0:y.status]),1)]),_:1}),e.createVNode(E,{label:"操作",fixed:"right",width:"150"},{default:e.withCtx(({row:y})=>[e.createElementVNode("div",null,[e.createVNode(w,{link:"",type:"primary",size:"small",onClick:M=>e.unref(P)(y)},{default:e.withCtx(()=>[e.createTextVNode("查看 ")]),_:2},1032,["onClick"]),y!=null&&y.deleted||(y==null?void 0:y.status)==="SELL_OFF"?(e.openBlock(),e.createBlock(w,{key:0,link:"",type:"primary",size:"small",onClick:M=>e.unref(d)(y==null?void 0:y.id)},{default:e.withCtx(()=>[e.createTextVNode("上架")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),(y==null?void 0:y.status)==="PLATFORM_SELL_OFF"?(e.openBlock(),e.createBlock(w,{key:1,link:"",type:"primary",size:"small",onClick:M=>{var U;return e.unref(N)((U=y==null?void 0:y.extra)==null?void 0:U.productViolation)}},{default:e.withCtx(()=>[e.createTextVNode("违规原因")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["checkedItem","data","style"]),e.createVNode(R,{modelValue:e.unref(s).page,"onUpdate:modelValue":x[1]||(x[1]=y=>e.unref(s).page=y),"load-init":"",total:e.unref(s).total,onReload:e.unref(g)},null,8,["modelValue","total","onReload"]),e.createVNode(I,{modelValue:e.unref(b),"onUpdate:modelValue":x[2]||(x[2]=y=>e.isRef(b)?b.value=y:null),title:"查看",width:"1000px","destroy-on-close":"","close-on-click-modal":!1},{default:e.withCtx(()=>[e.createVNode(pt,{"current-product":e.unref(C)},null,8,["current-product"])]),_:1},8,["modelValue"]),e.createVNode(I,{modelValue:e.unref(u),"onUpdate:modelValue":x[3]||(x[3]=y=>e.isRef(u)?u.value=y:null),title:"违规原因",width:"600px","destroy-on-close":"","close-on-click-modal":!1},{default:e.withCtx(()=>[e.createVNode(nt,{"product-violation":e.unref(f)},null,8,["product-violation"])]),_:1},8,["modelValue"])],64)}}}),Ht="",ht=Object.freeze(Object.defineProperty({__proto__:null,default:B(gt,[["__scopeId","data-v-d9c0f592"]])},Symbol.toStringTag,{value:"Module"})),{divTenThousand:J,mulTenThousand:Z}=L(),Vt=()=>{const o=e.reactive({type:"RATE",sale:0,scribe:0}),i=()=>{o.sale=0,o.scribe=0},l=e.ref(null),c={type:{required:!0,message:"请选择代销类型",trigger:"change"},scribe:{validator:(s,g,m)=>{o.type==="RATE"&&(g>100&&m(new Error("划线价比率不能大于100%")),m()),m()},trigger:"blur"},sale:{validator:(s,g,m)=>{o.type==="RATE"&&(g>100&&m(new Error("销售价比率不能大于100%")),m()),m()},trigger:"blur"}},a=()=>{var s;(s=l.value)==null||s.validate(async g=>{if(g){const m=F.cloneDeep(o);m.sale=Z(o.sale).toNumber(),m.scribe=Z(o.scribe).toNumber();const{code:r,success:n,msg:_}=await ne(m);r===200&&n?T.ElMessage.success({message:_||"更新代销设置信息成功"}):T.ElMessage.error({message:_||"更新代销设置信息失败"})}})};return(async()=>{const{data:s,code:g}=await Y();g===200&&s&&(o.sale=J(s==null?void 0:s.sale).toNumber(),o.scribe=J(s==null?void 0:s.scribe).toNumber(),o.type=s==null?void 0:s.type)})(),{formRef:l,rules:c,formModel:o,handleChangeType:i,handleSubmitSettingData:a}},$=o=>(e.pushScopeId("data-v-069d8820"),o=o(),e.popScopeId(),o),ut={class:"settings"},yt=e.createStaticVNode('<div class="settings__title" data-v-069d8820>代销设置说明</div><div class="settings__container" data-v-069d8820><p class="settings__container--line" data-v-069d8820> 1、设价方式： 统一设价 、自定义设价 ； 价格优先级：<span class="text-red" data-v-069d8820>自定义设价 &gt; 统一设价</span></p><p class="settings__container--line" data-v-069d8820>2、自定义设价：在上架(铺货) 或 编辑 时可对具体的某个商品进行自定义价格设定</p><p class="settings__container--line" data-v-069d8820>3、统一设价：统一设价为默认方案，其优先级低于自定义设价；即有自定义价格则以自定义价格为准</p><p class="settings__container--line" data-v-069d8820> 4、无论何种设价方式，系统均可实现随供货价的调整而<b class="text-red" data-v-069d8820>自动调整</b>代销商品的划线价、销售价 (划线价 &gt;= 销售价) ，避免亏损 </p></div>',2),Nt={class:"settings__form"},Ct=$(()=>e.createElementVNode("span",{class:"setting_form--title"},"统一设价",-1)),bt=$(()=>e.createElementVNode("span",null,"销售价 = 供货价 + （供货价 *  ",-1)),xt=$(()=>e.createElementVNode("span",null," % ）",-1)),St=$(()=>e.createElementVNode("span",null,"划线价 = 销售价 + （销售价 *  ",-1)),wt=$(()=>e.createElementVNode("span",null," % ）",-1)),Et=$(()=>e.createElementVNode("span",null,"销售价 = 供货价 +  ",-1)),kt=$(()=>e.createElementVNode("span",null," 元",-1)),Pt=$(()=>e.createElementVNode("span",null,"划线价 = 销售价 +  ",-1)),Tt=$(()=>e.createElementVNode("span",null," 元",-1)),$t=e.defineComponent({__name:"index",setup(o){const{rules:i,formModel:l,formRef:c,handleChangeType:a,handleSubmitSettingData:V}=Vt();return(s,g)=>{const m=e.resolveComponent("el-radio"),r=e.resolveComponent("el-radio-group"),n=e.resolveComponent("el-form-item"),_=e.resolveComponent("el-input-number"),d=e.resolveComponent("el-button"),t=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",ut,[yt,e.createElementVNode("div",Nt,[Ct,e.createVNode(t,{ref_key:"formRef",ref:c,model:e.unref(l),rules:e.unref(i)},{default:e.withCtx(()=>[e.createVNode(n,null,{default:e.withCtx(()=>[e.createVNode(r,{modelValue:e.unref(l).type,"onUpdate:modelValue":g[0]||(g[0]=f=>e.unref(l).type=f),onChange:e.unref(a)},{default:e.withCtx(()=>[e.createVNode(m,{label:"RATE"},{default:e.withCtx(()=>[e.createTextVNode("按比例设价")]),_:1}),e.createVNode(m,{label:"REGULAR"},{default:e.withCtx(()=>[e.createTextVNode("固定金额设价")]),_:1})]),_:1},8,["modelValue","onChange"])]),_:1}),e.unref(l).type==="RATE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(n,{prop:"sale"},{default:e.withCtx(()=>[bt,e.createVNode(_,{modelValue:e.unref(l).sale,"onUpdate:modelValue":g[1]||(g[1]=f=>e.unref(l).sale=f),controls:!1,precision:2},null,8,["modelValue"]),xt]),_:1}),e.createVNode(n,{prop:"scribe"},{default:e.withCtx(()=>[St,e.createVNode(_,{modelValue:e.unref(l).scribe,"onUpdate:modelValue":g[2]||(g[2]=f=>e.unref(l).scribe=f),controls:!1,precision:2},null,8,["modelValue"]),wt]),_:1})],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createVNode(n,{prop:"sale"},{default:e.withCtx(()=>[Et,e.createVNode(_,{modelValue:e.unref(l).sale,"onUpdate:modelValue":g[3]||(g[3]=f=>e.unref(l).sale=f),controls:!1,precision:2},null,8,["modelValue"]),kt]),_:1}),e.createVNode(n,{prop:"scribe"},{default:e.withCtx(()=>[Pt,e.createVNode(_,{modelValue:e.unref(l).scribe,"onUpdate:modelValue":g[4]||(g[4]=f=>e.unref(l).scribe=f),controls:!1,precision:2},null,8,["modelValue"]),Tt]),_:1})],64)),e.createVNode(n,null,{default:e.withCtx(()=>[e.createVNode(d,{type:"primary",onClick:e.unref(V)},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])])])}}}),Wt="",Dt=Object.freeze(Object.defineProperty({__proto__:null,default:B($t,[["__scopeId","data-v-069d8820"]])},Symbol.toStringTag,{value:"Module"}));return v});
