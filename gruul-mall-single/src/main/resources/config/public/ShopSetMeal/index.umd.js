(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("vue-router"),require("@vueuse/core"),require("@element-plus/icons-vue"),require("@/components/PageManage.vue"),require("element-plus"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@vueuse/core","@element-plus/icons-vue","@/components/PageManage.vue","element-plus","@/apis/http"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopSetMeal=V(e.ShopSetMealContext.Vue,e.ShopSetMealContext.VueRouter,e.ShopSetMealContext.VueUse,e.ShopSetMealContext.ElementPlusIconsVue,e.ShopSetMealContext.PageManageTwo,e.ShopSetMealContext.ElementPlus,e.ShopSetMealContext.Request))})(this,function(e,V,T,B,L,u,b){"use strict";var M=document.createElement("style");M.textContent=`.origin[data-v-c20339f4]{display:flex;justify-content:start;align-items:center}.origin--name[data-v-c20339f4]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(M);var w=(t=>(t.NOT_STARTED="未开始",t.PROCESSING="进行中",t.OVER="已结束",t.ILLEGAL_SELL_OFF="违规下架",t))(w||{});const z={style:{"margin-bottom":"15px",display:"flex","justify-content":"space-between"}},D=e.defineComponent({__name:"head-operation",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0}},emits:["update:modelValue","add","del","search"],setup(t,{emit:s}){const c=t,p=T.useVModel(c,"modelValue",s);return(_,o)=>{const n=e.resolveComponent("el-button"),i=e.resolveComponent("el-space"),l=e.resolveComponent("el-option"),h=e.resolveComponent("el-select"),N=e.resolveComponent("el-input");return e.openBlock(),e.createElementBlock("div",z,[e.createElementVNode("div",null,[e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(n,{type:"primary",round:"",onClick:o[0]||(o[0]=d=>s("add"))},{default:e.withCtx(()=>[e.createTextVNode("新增套餐")]),_:1})]),_:1}),e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(n,{round:"",disabled:_.$props.batchDisabled,onClick:o[1]||(o[1]=d=>s("del"))},{default:e.withCtx(()=>[e.createTextVNode("批量删除")]),_:1},8,["disabled"])]),_:1})]),e.createElementVNode("div",null,[e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(h,{modelValue:e.unref(p).setMealStatus,"onUpdate:modelValue":o[2]||(o[2]=d=>e.unref(p).setMealStatus=d),placeholder:"",style:{width:"200px"},onChange:o[3]||(o[3]=d=>s("search"))},{default:e.withCtx(()=>[e.createVNode(l,{label:"全部",value:""}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(w),(d,y)=>(e.openBlock(),e.createBlock(l,{key:y,label:d,value:y},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(N,{modelValue:e.unref(p).keyword,"onUpdate:modelValue":o[5]||(o[5]=d=>e.unref(p).keyword=d),placeholder:"活动名称"},{append:e.withCtx(()=>[e.createVNode(n,{icon:e.unref(B.Search),onClick:o[4]||(o[4]=d=>s("search"))},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})])])}}}),S="addon-matching-treasure/setMeal/",I=t=>b.get({url:S,params:t}),k=t=>b.del({url:S,data:t}),O={class:"origin"},R={class:"origin--name",style:{width:"180px","margin-left":"10px"}},$=e.defineComponent({__name:"set-meal-list",props:{search:{type:Object,default:()=>({})}},setup(t,{expose:s}){const c=t,p=V.useRouter(),_=e.ref([]),o=e.ref(),n=e.reactive({size:10,current:1,total:0}),i=e.ref([]);e.watch(()=>c.search,a=>{l()},{deep:!0});async function l(){const{setMealStatus:a,keyword:m}=c.search,C={...n,...{setMealStatus:a,keyword:m}},{code:g,data:x}=await I(C);if(g!==200)return u.ElMessage.error("获取套餐列表失败");_.value=x.records,n.current=x.current,n.size=x.size,n.total=x.total}const h=a=>{p.push({name:"bundlePriceBaseinfo",query:{setMealId:a.id,shopId:a.shopId}})},N=async(a,m)=>{try{if(!await u.ElMessageBox.confirm("确定删除该套餐?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:C}=await k([{setMealId:a,shopId:m}]);if(C!==200){u.ElMessage.error("删除失败");return}u.ElMessage.success("删除成功"),l()}catch{return}},d=async a=>{const{code:m}=await k(a);if(m!==200){u.ElMessage.error("删除失败");return}u.ElMessage.success("删除成功"),l()},y=a=>{n.size=a,l()},q=a=>{n.current=a,l()};return e.onBeforeMount(()=>{l()}),s({chooseList:i,handleDelBatch:d}),(a,m)=>{const f=e.resolveComponent("el-table-column"),C=e.resolveComponent("el-image"),g=e.resolveComponent("el-button"),x=e.resolveComponent("el-col"),E=e.resolveComponent("el-row"),U=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(U,{ref_key:"multipleTableRef",ref:o,data:_.value,stripe:"",style:e.normalizeStyle({height:"calc(100vh - 280px)"}),"header-row-style":{fontSize:"12px",color:"#909399"},"header-cell-style":{background:"#f6f8fa"},"cell-style":{fontSize:"12px",color:"#333333"},onSelectionChange:m[0]||(m[0]=r=>i.value=r)},{default:e.withCtx(()=>[e.createVNode(f,{type:"selection",width:"55"}),e.createVNode(f,{label:"套餐信息",width:"250px"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",O,[e.createVNode(C,{style:{width:"40px",height:"40px"},src:r.setMealMainPicture},null,8,["src"]),e.createElementVNode("div",R,e.toDisplayString(r.setMealName),1)])]),_:1}),e.createVNode(f,{label:"套餐类型"},{default:e.withCtx(({row:r})=>[e.createTextVNode(e.toDisplayString(r.setMealType==="OPTIONAL_PRODUCT"?"自由搭配":"固定搭配"),1)]),_:1}),e.createVNode(f,{label:"开始时间",width:"180px",align:"center",prop:"startTime"}),e.createVNode(f,{label:"结束时间",width:"180px",align:"center",prop:"endTime"}),e.createVNode(f,{label:"状态",fixed:"right",align:"center"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(w)[r.setMealStatus]),1)]),_:1}),e.createVNode(f,{label:"操作",fixed:"right",align:"center"},{default:e.withCtx(({row:r})=>[e.createVNode(E,{justify:"space-between",align:"middle"},{default:e.withCtx(()=>[e.createVNode(x,{span:10},{default:e.withCtx(()=>[e.createVNode(g,{link:"",type:"primary",size:"small",onClick:P=>h(r)},{default:e.withCtx(()=>[e.createTextVNode("查看")]),_:2},1032,["onClick"])]),_:2},1024),e.createVNode(x,{span:10},{default:e.withCtx(()=>[e.createVNode(g,{link:"",type:"primary",size:"small",onClick:P=>N(r.id,r.shopId)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:1})]),_:1},8,["data","style"]),e.createVNode(E,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(L,{modelValue:n,"onUpdate:modelValue":m[1]||(m[1]=r=>n=r),"load-init":!0,"page-size":n.size,total:n.total,onReload:l,onHandleSizeChange:y,onHandleCurrentChange:q},null,8,["modelValue","page-size","total"])]),_:1})],64)}}}),F="",j=((t,s)=>{const c=t.__vccOpts||t;for(const[p,_]of s)c[p]=_;return c})($,[["__scopeId","data-v-c20339f4"]]);return e.defineComponent({__name:"ShopSetMeal",setup(t){const s=e.reactive({setMealStatus:"",keyword:""}),c=e.ref(),p=V.useRouter(),_=e.computed(()=>c.value?!c.value.chooseList.length:!0),o=async()=>{try{if(!await u.ElMessageBox.confirm("确定批量删除套餐?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const l=c.value.chooseList.map(h=>({shopId:h.shopId,setMealId:h.id}));c.value.handleDelBatch(l)}catch(i){return i}},n=()=>p.push({name:"bundlePriceBaseinfo"});return(i,l)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode(D,{modelValue:s,"onUpdate:modelValue":l[0]||(l[0]=h=>s=h),"batch-disabled":_.value,onAdd:n,onDel:o},null,8,["modelValue","batch-disabled"]),e.createVNode(j,{ref_key:"mealListRef",ref:c,search:s},null,8,["search"])]))}})});
