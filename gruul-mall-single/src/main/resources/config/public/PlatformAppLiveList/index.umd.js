(function(e,k){typeof exports=="object"&&typeof module<"u"?module.exports=k(require("vue"),require("lodash"),require("@/components/MCard.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("element-plus"),require("@/components/pageManage/PageManage.vue"),require("@/apis/http"),require("@/components/q-upload/q-upload.vue"),require("@element-plus/icons-vue")):typeof define=="function"&&define.amd?define(["vue","lodash","@/components/MCard.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","element-plus","@/components/pageManage/PageManage.vue","@/apis/http","@/components/q-upload/q-upload.vue","@element-plus/icons-vue"],k):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAppLiveList=k(e.PlatformAppLiveListContext.Vue,e.PlatformAppLiveListContext.Lodash,e.PlatformAppLiveListContext.MCard,e.PlatformAppLiveListContext.QTable,e.PlatformAppLiveListContext.QTableColumn,e.PlatformAppLiveListContext.ElementPlus,e.PlatformAppLiveListContext.PageManage,e.PlatformAppLiveListContext.Request,e.PlatformAppLiveListContext.QUpload,e.PlatformAppLiveListContext.ElementPlusIconsVue))})(this,function(e,k,q,P,E,u,z,O,M,j){"use strict";var U=document.createElement("style");U.textContent=`.el-row[data-v-d0d80bc4]{margin-bottom:8px}.el-row img+img[data-v-d0d80bc4]{margin-left:10px;width:50px;height:50px;vertical-align:top;object-fit:cover}.table[data-v-4b126293]{overflow:auto}.table-info[data-v-4b126293]{display:flex;align-items:center;width:350px;overflow:hidden}.table-info img[data-v-4b126293]{width:50px;height:50px;border-radius:10px;flex-shrink:0}.table-info__wrapper[data-v-4b126293]{margin-left:15px;overflow:hidden;flex:1}.table-info__wrapper--name .label[data-v-4b126293]{display:inline-block;line-height:22px;padding:0 3px;border-radius:4px;color:#fff;margin-right:5px}.table-info__wrapper--name .label.in-live[data-v-4b126293]{background-color:#085fdb}.table-info__wrapper--name .label.foretell[data-v-4b126293]{background-color:#81b337}.table-info__wrapper--name .label.finish[data-v-4b126293]{background-color:#000}.table-info__wrapper--name .label.violation[data-v-4b126293]{background-color:#fd0505}.table-anchor[data-v-4b126293]{text-align:center;line-height:22px}.table-performance[data-v-4b126293]{width:100%;line-height:22px}.ellipsis[data-v-4b126293]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.el-link+.el-link[data-v-4b126293]{margin-left:5px}.table[data-v-7e4044f6]{overflow:auto}.table-info[data-v-7e4044f6]{display:flex;width:350px;overflow:hidden}.table-info img[data-v-7e4044f6]{width:50px;height:50px;border-radius:10px;flex-shrink:0}.table-info__wrapper[data-v-7e4044f6]{margin-left:15px;overflow:hidden;flex:1}.table-info__wrapper--name span+span[data-v-7e4044f6]{margin-left:5px}.table-performance[data-v-7e4044f6]{width:100%;line-height:22px}.ellipsis[data-v-7e4044f6]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
`,document.head.appendChild(U);const Y=e.defineComponent({__name:"PlatformAppLiveList",setup(C){const n=e.ref("room"),c={room:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Ce)),anchor:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Oe))};return(t,m)=>{const a=e.resolveComponent("el-tab-pane"),o=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(o,{modelValue:n.value,"onUpdate:modelValue":m[0]||(m[0]=s=>n.value=s),class:"demo-tabs"},{default:e.withCtx(()=>[e.createVNode(a,{label:"直播间",name:"room"}),e.createVNode(a,{label:"主播管理",name:"anchor"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(c[n.value])))])}}}),W={style:{background:"#f9f9f9"}},J=e.defineComponent({__name:"room-search",emits:["changeShow","onSearchParams"],setup(C,{emit:n}){const c=e.ref(!1),t=e.reactive({liveTitle:"",anchorNickname:"",createTime:"",liveId:"",phone:"",shopName:""});e.watch(()=>c.value,a=>{n("changeShow",a)});const m=()=>{const a=k.cloneDeep(t);n("onSearchParams",a)};return(a,o)=>{const s=e.resolveComponent("el-input"),p=e.resolveComponent("el-form-item"),d=e.resolveComponent("el-col"),_=e.resolveComponent("el-date-picker"),N=e.resolveComponent("el-row"),h=e.resolveComponent("el-button"),g=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",W,[e.createVNode(q,{modelValue:c.value,"onUpdate:modelValue":o[6]||(o[6]=V=>c.value=V)},{default:e.withCtx(()=>[e.createVNode(g,{ref:"form",model:t,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(N,null,{default:e.withCtx(()=>[e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"直播主题"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.liveTitle,"onUpdate:modelValue":o[0]||(o[0]=V=>t.liveTitle=V),placeholder:"请输入直播主题"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"主播昵称"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.anchorNickname,"onUpdate:modelValue":o[1]||(o[1]=V=>t.anchorNickname=V),placeholder:"请输入主播昵称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"直播时间"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:t.createTime,"onUpdate:modelValue":o[2]||(o[2]=V=>t.createTime=V),"end-placeholder":"结束日期","range-separator":"-","start-placeholder":"开始日期",style:{width:"224px"},type:"daterange","unlink-panels":"","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"直播ID"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.liveId,"onUpdate:modelValue":o[3]||(o[3]=V=>t.liveId=V),placeholder:"请输入直播ID"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"手机号"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.phone,"onUpdate:modelValue":o[4]||(o[4]=V=>t.phone=V),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"店铺名称"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.shopName,"onUpdate:modelValue":o[5]||(o[5]=V=>t.shopName=V),placeholder:"请输入店铺名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(p,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(h,{class:"from_btn",round:"",type:"primary",onClick:m},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),K=C=>O.get({url:"addon-live/manager/live/list",params:C}),X=(C,n)=>O.get({url:`addon-live/manager/ban/reason/${C}/${n}`}),Z=C=>O.get({url:"addon-live/manager/anchor/list",params:C}),R=C=>O.put({url:"addon-live/manager/platform/update/anchor",data:C}),Q={class:"reason"},v=["src"],ee=e.defineComponent({__name:"violation-reason",props:{id:{type:String,default:""},type:{type:String,default:""}},setup(C){const n=C,c={YELLOW_INVOLVEMENT:"涉黄",DRUG_RELATED:"涉毒",SENSITIVE_TOPIC:"敏感话题",OTHER:"其它"},t=e.ref({qualityInspector:"",sourceId:"",shopId:"",type:"",categoryTypes:"",reason:"",relevantEvidence:"",createTime:""});e.watch(n,a=>{m()}),m();async function m(){const{code:a,data:o}=await X(n.id,n.type);if(a!==200)return u.ElMessage.error("获取直播间列表失败");o.relevantEvidence=o.relevantEvidence.split(","),o.categoryTypes=o.categoryTypes.map(s=>c[s]).join(","),t.value=o}return(a,o)=>{const s=e.resolveComponent("el-col"),p=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",Q,[e.createVNode(p,{gutter:8},{default:e.withCtx(()=>[e.createVNode(s,{span:12},{default:e.withCtx(()=>[e.createTextVNode("检查员："+e.toDisplayString(t.value.qualityInspector),1)]),_:1}),e.createVNode(s,{span:12},{default:e.withCtx(()=>[e.createTextVNode("检查时间："+e.toDisplayString(t.value.createTime),1)]),_:1})]),_:1}),e.createVNode(p,{gutter:8},{default:e.withCtx(()=>[e.createVNode(s,{span:24},{default:e.withCtx(()=>[e.createTextVNode("类型："+e.toDisplayString(t.value.categoryTypes),1)]),_:1})]),_:1}),e.createVNode(p,{gutter:8},{default:e.withCtx(()=>[e.createVNode(s,{span:24},{default:e.withCtx(()=>[e.createTextVNode("原因："+e.toDisplayString(t.value.reason),1)]),_:1})]),_:1}),e.createVNode(p,{gutter:8},{default:e.withCtx(()=>[e.createTextVNode(" 相关证据： "),e.createVNode(s,{span:24},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value.relevantEvidence,d=>(e.openBlock(),e.createElementBlock("img",{key:d,src:d,style:{width:"60px",height:"60px"}},null,8,v))),128))]),_:1})]),_:1})])}}}),De="",A=(C,n)=>{const c=C.__vccOpts||C;for(const[t,m]of n)c[t]=m;return c},H=A(ee,[["__scopeId","data-v-d0d80bc4"]]),F=e.defineComponent({__name:"prohibitLive",setup(C,{expose:n}){const c=e.reactive({categoryTypes:[],reason:"",relevantEvidence:""}),t=e.ref([]),m={categoryTypes:[{required:!0,message:"请选择类型",trigger:"change"}],reason:[{required:!0,message:"请输入原因",trigger:"change"}],relevantEvidence:[{required:!0,message:"请选择证据",trigger:"change"}]},a=e.ref(null),o=()=>new Promise((d,_)=>{a.value?a.value.validate(N=>{N?d(c):_("valid error")}):_("form instance not found")}),s=d=>{t.value.splice(d,1),c.relevantEvidence=t.value.join(",")},p=(d,_)=>{t.value.push(d),c.relevantEvidence=t.value.join(",")};return n({getprohibitModel:o}),(d,_)=>{const N=e.resolveComponent("el-checkbox"),h=e.resolveComponent("el-checkbox-group"),g=e.resolveComponent("el-form-item"),V=e.resolveComponent("el-input"),S=e.resolveComponent("el-icon"),B=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(B,{ref_key:"formRef",ref:a,model:c,rules:m},{default:e.withCtx(()=>[e.createVNode(g,{label:"类型(多选)",prop:"categoryTypes"},{default:e.withCtx(()=>[e.createVNode(h,{modelValue:c.categoryTypes,"onUpdate:modelValue":_[0]||(_[0]=w=>c.categoryTypes=w)},{default:e.withCtx(()=>[e.createVNode(N,{label:"YELLOW_INVOLVEMENT"},{default:e.withCtx(()=>[e.createTextVNode("涉黄")]),_:1}),e.createVNode(N,{label:"DRUG_RELATED"},{default:e.withCtx(()=>[e.createTextVNode("涉毒")]),_:1}),e.createVNode(N,{label:"SENSITIVE_TOPIC"},{default:e.withCtx(()=>[e.createTextVNode("敏感话题")]),_:1}),e.createVNode(N,{label:"OTHER"},{default:e.withCtx(()=>[e.createTextVNode("其它")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(g,{label:"原因",prop:"reason"},{default:e.withCtx(()=>[e.createVNode(V,{modelValue:c.reason,"onUpdate:modelValue":_[1]||(_[1]=w=>c.reason=w),placeholder:"20字以内"},null,8,["modelValue"])]),_:1}),e.createVNode(g,{label:"相关证据（最多5张图片）","label-width":"120px",prop:"relevantEvidence"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,(w,y)=>(e.openBlock(),e.createElementBlock("div",{key:w,style:{position:"relative","margin-right":"20px"}},[e.createVNode(M,{src:t.value[y],"onUpdate:src":T=>t.value[y]=T,format:{size:1},height:100,width:100},null,8,["src","onUpdate:src"]),w?(e.openBlock(),e.createBlock(S,{key:0,color:"#7f7f7f",size:"20px",style:{position:"absolute",right:"-5px",top:"-5px",background:"#fff","border-radius":"50%"},onClick:T=>s(y)},{default:e.withCtx(()=>[e.createVNode(e.unref(j.CircleClose))]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]))),128)),e.withDirectives(e.createVNode(M,{format:{size:1},height:100,width:100,onChange:p},null,512),[[e.vShow,t.value.length<=4]])]),_:1})]),_:1},8,["model"])}}}),te={class:"table-info"},oe={style:{"margin-right":"20px"}},ae=["src"],ne={class:"table-info__wrapper"},le={class:"table-info__wrapper--name"},re={key:0,class:"label in-live"},ie={key:1,class:"label foretell"},ce={key:2,class:"label finish"},se={key:3,class:"label violation"},de={class:"table-info__wrapper--description"},pe={class:"table-info__wrapper--description ellipsis"},me={class:"table-anchor"},_e={class:"table-anchor__info"},fe={class:"table-anchor__info"},he={class:"dialog-footer"},Ve=e.defineComponent({__name:"index",setup(C){const n=e.ref([]),c=e.ref("calc(100vh - 310px)"),t=e.ref(""),m=e.ref({liveTitle:"",anchorNickname:"",createTime:"",liveId:"",phone:"",shopName:"",beginTime:"",endTime:""}),a=e.reactive({size:10,current:1,total:0}),o=e.ref(!1),s=[{name:"",label:"全部"},{name:"PROCESSING",label:"直播中"},{name:"NOT_STARTED",label:"预告"},{name:"OVER",label:"已结束"},{name:"ILLEGAL_SELL_OFF",label:"违规下播"}],p=e.ref({id:""}),d=e.ref({id:""}),_=e.ref(!1),N=e.ref();h();async function h(){Array.isArray(m.value.createTime)&&(m.value.beginTime=m.value.createTime[0],m.value.endTime=m.value.createTime[1],m.value.createTime="");const x={...a,...m.value,status:t.value},{code:l,data:f}=await K(x);if(l!==200)return u.ElMessage.error("获取直播间列表失败");n.value=f.records,a.current=f.current,a.size=f.size,a.total=f.total}const g=x=>{a.size=x,h()},V=x=>{a.current=x,h()},S=x=>{m.value=x,a.current=1,h()},B=()=>{a.current=1,h()},w=x=>{c.value=x?"calc(100vh - 480px)":"calc(100vh - 320px)"},y=x=>{o.value=!0,p.value.id=x},T=x=>{_.value=!0,d.value.id=x},$=async()=>{var b;const x=await((b=N.value)==null?void 0:b.getprohibitModel()),{code:l,data:f}=await R({...x,type:"LIVE",isEnable:!1,sourceId:d.value.id});if(l!==200)return u.ElMessage.error("违规下播失败");h(),_.value=!1};return(x,l)=>{const f=e.resolveComponent("el-tab-pane"),b=e.resolveComponent("el-tabs"),D=e.resolveComponent("el-link"),L=e.resolveComponent("el-dialog"),I=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(J,{onOnSearchParams:S,onChangeShow:w}),e.createVNode(b,{modelValue:t.value,"onUpdate:modelValue":l[0]||(l[0]=r=>t.value=r),onTabChange:B},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(s,r=>e.createVNode(f,{key:r.name,label:r.label,name:r.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),e.createVNode(e.unref(P),{data:n.value,style:e.normalizeStyle({height:c.value}),class:"table"},{default:e.withCtx(()=>[e.createVNode(E,{label:"直播信息",width:"350"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",te,[e.createElementVNode("div",oe,e.toDisplayString(r.shopName),1),e.createElementVNode("img",{src:r.pic},null,8,ae),e.createElementVNode("div",ne,[e.createElementVNode("div",le,[r.status==="PROCESSING"?(e.openBlock(),e.createElementBlock("span",re,"直播中")):r.status==="NOT_STARTED"?(e.openBlock(),e.createElementBlock("span",ie,"预告")):r.status==="OVER"?(e.openBlock(),e.createElementBlock("span",ce,"已结束")):r.status==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createElementBlock("span",se,"违规下播")):e.createCommentVNode("",!0),e.createElementVNode("span",null,e.toDisplayString(r.liveTitle),1)]),e.createElementVNode("div",de,"直播ID："+e.toDisplayString(r.id),1),e.createElementVNode("div",pe,e.toDisplayString(r.liveSynopsis),1)])])]),_:1}),e.createVNode(E,{align:"center",label:"主播昵称",width:"350"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",me,[e.createElementVNode("div",_e,e.toDisplayString(r.anchor.anchorNickname),1),e.createElementVNode("div",fe,e.toDisplayString(r.anchor.phone),1)])]),_:1}),e.createVNode(E,{label:"操作",prop:"action",width:"150"},{default:e.withCtx(({row:r})=>[r.status==="PROCESSING"?(e.openBlock(),e.createBlock(D,{key:0,type:"danger",onClick:i=>T(r.id)},{default:e.withCtx(()=>[e.createTextVNode("违规下播")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),r.status==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createBlock(D,{key:1,type:"primary",onClick:i=>y(r.id)},{default:e.withCtx(()=>[e.createTextVNode("违禁原因 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data","style"]),e.createVNode(z,{"page-num":a.current,"page-size":a.size,total:a.total,onHandleSizeChange:g,onHandleCurrentChange:V},null,8,["page-num","page-size","total"]),e.createVNode(L,{modelValue:o.value,"onUpdate:modelValue":l[1]||(l[1]=r=>o.value=r),title:"违禁原因",width:"550px"},{default:e.withCtx(()=>[e.createVNode(H,{id:p.value.id,type:"LIVE"},null,8,["id"])]),_:1},8,["modelValue"]),e.createVNode(L,{modelValue:_.value,"onUpdate:modelValue":l[3]||(l[3]=r=>_.value=r),"close-on-click-modal":!1,"destroy-on-close":"",title:"违禁下播",width:"550px"},{footer:e.withCtx(()=>[e.createElementVNode("span",he,[e.createVNode(I,{onClick:l[2]||(l[2]=r=>_.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(I,{type:"primary",onClick:$},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(F,{ref_key:"prohibitLiveRef",ref:N},null,512)]),_:1},8,["modelValue"])])}}}),Re="",Ce=Object.freeze(Object.defineProperty({__proto__:null,default:A(Ve,[["__scopeId","data-v-4b126293"]])},Symbol.toStringTag,{value:"Module"})),xe={style:{background:"#f9f9f9"}},ge=e.defineComponent({__name:"anchor-search",emits:["changeShow","onSearchParams"],setup(C,{emit:n}){const c=e.ref(!1),t=e.reactive({anchorNickname:"",id:"",phone:""});e.watch(()=>c.value,a=>{n("changeShow",a)});const m=()=>{const a=k.cloneDeep(t);n("onSearchParams",a)};return(a,o)=>{const s=e.resolveComponent("el-input"),p=e.resolveComponent("el-form-item"),d=e.resolveComponent("el-col"),_=e.resolveComponent("el-row"),N=e.resolveComponent("el-button"),h=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",xe,[e.createVNode(q,{modelValue:c.value,"onUpdate:modelValue":o[3]||(o[3]=g=>c.value=g)},{default:e.withCtx(()=>[e.createVNode(h,{ref:"form",model:t,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(_,null,{default:e.withCtx(()=>[e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"主播昵称"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.anchorNickname,"onUpdate:modelValue":o[0]||(o[0]=g=>t.anchorNickname=g),placeholder:"请输入主播昵称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"手机号"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.phone,"onUpdate:modelValue":o[1]||(o[1]=g=>t.phone=g),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(d,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"主播ID"},{default:e.withCtx(()=>[e.createVNode(s,{modelValue:t.id,"onUpdate:modelValue":o[2]||(o[2]=g=>t.id=g),placeholder:"请输入主播ID"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(p,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(N,{class:"from_btn",round:"",type:"primary",onClick:m},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Ne={class:"table-info"},be={style:{"margin-right":"20px"}},we=["src"],ye={class:"table-info__wrapper"},ke={class:"table-info__wrapper--name"},Ee={class:"table-info__wrapper--description"},ue={class:"table-info__wrapper--description ellipsis"},Te={key:0},Le={key:1},Se={key:2},Be={class:"dialog-footer"},Ie=e.defineComponent({__name:"index",setup(C){const n=e.reactive({size:10,current:1,total:0}),c=e.ref("calc(100vh - 380px)"),t=e.ref(""),m=[{name:"",label:"全部"},{name:"NORMAL",label:"启用"},{name:"FORBIDDEN",label:"禁用"},{name:"VIOLATION",label:"违规禁播"}],a=e.ref([]),o=e.ref(!1),s=e.ref({id:""}),p=e.ref({id:""}),d=e.ref({id:"",anchorNickname:"",phone:""}),_=e.ref(!1),N=e.ref();h();async function h(){const l={...n,...d.value,status:t.value},{code:f,data:b}=await Z(l);if(f!==200)return u.ElMessage.error("获取直播间列表失败");a.value=b.records,n.current=b.current,n.size=b.size,n.total=b.total}const g=l=>{n.size=l,h()},V=l=>{n.current=l,h()},S=l=>{d.value=l,n.current=1,h()},B=()=>{n.current=1,h()},w=l=>{o.value=!0,s.value.id=l},y=l=>{_.value=!0,p.value.id=l},T=l=>{c.value=l?"calc(100vh - 480px)":"calc(100vh - 380px)"},$=async()=>{var b;const l=await((b=N.value)==null?void 0:b.getprohibitModel()),{code:f}=await R({...l,type:"ANCHOR",isEnable:!1,sourceId:p.value.id});if(f!==200)return u.ElMessage.error("禁播失败");h(),_.value=!1},x=async l=>{const{code:f,data:b}=await R({type:"ANCHOR",isEnable:!0,sourceId:l});if(f!==200)return u.ElMessage.error("恢复直播失败");h()};return(l,f)=>{const b=e.resolveComponent("el-tab-pane"),D=e.resolveComponent("el-tabs"),L=e.resolveComponent("el-link"),I=e.resolveComponent("el-dialog"),r=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(ge,{onOnSearchParams:S,onChangeShow:T}),e.createVNode(D,{modelValue:t.value,"onUpdate:modelValue":f[0]||(f[0]=i=>t.value=i),onTabChange:B},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(m,i=>e.createVNode(b,{key:i.name,label:i.label,name:i.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),e.createVNode(e.unref(P),{data:a.value,style:e.normalizeStyle({height:c.value}),class:"table"},{default:e.withCtx(()=>[e.createVNode(E,{label:"主播信息",width:"300"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",Ne,[e.createElementVNode("div",be,e.toDisplayString(i.shopName),1),e.createElementVNode("img",{src:i.anchorIcon},null,8,we),e.createElementVNode("div",ye,[e.createElementVNode("div",ke,[e.createElementVNode("span",null,e.toDisplayString(i.anchorNickname),1),e.createElementVNode("span",null,e.toDisplayString(i.phone),1)]),e.createElementVNode("div",Ee,"主播ID："+e.toDisplayString(i.id),1),e.createElementVNode("div",ue,e.toDisplayString(i.anchorSynopsis),1)])])]),_:1}),e.createVNode(E,{align:"center",label:"状态"},{default:e.withCtx(({row:i})=>[i.status==="NORMAL"?(e.openBlock(),e.createElementBlock("span",Te,"启用")):i.status==="FORBIDDEN"?(e.openBlock(),e.createElementBlock("span",Le,"禁用")):i.status==="VIOLATION"?(e.openBlock(),e.createElementBlock("span",Se,"违规禁播")):e.createCommentVNode("",!0)]),_:1}),e.createVNode(E,{label:"操作",prop:"action",width:"150"},{default:e.withCtx(({row:i})=>[i.status==="VIOLATION"?(e.openBlock(),e.createBlock(L,{key:0,style:{"margin-right":"20px"},type:"primary",onClick:G=>x(i.id)},{default:e.withCtx(()=>[e.createTextVNode("恢复直播 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),i.status==="VIOLATION"?(e.openBlock(),e.createBlock(L,{key:1,type:"primary",onClick:G=>w(i.id)},{default:e.withCtx(()=>[e.createTextVNode("禁播原因")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),i.status!=="VIOLATION"?(e.openBlock(),e.createBlock(L,{key:2,type:"danger",onClick:G=>y(i.id)},{default:e.withCtx(()=>[e.createTextVNode("违规禁播")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data","style"]),e.createVNode(z,{"page-num":n.current,"page-size":n.size,total:n.total,onHandleSizeChange:g,onHandleCurrentChange:V},null,8,["page-num","page-size","total"]),e.createVNode(I,{modelValue:o.value,"onUpdate:modelValue":f[1]||(f[1]=i=>o.value=i),title:"违禁原因",width:"550px"},{default:e.withCtx(()=>[e.createVNode(H,{id:s.value.id,type:"ANCHOR"},null,8,["id"])]),_:1},8,["modelValue"]),e.createVNode(I,{modelValue:_.value,"onUpdate:modelValue":f[3]||(f[3]=i=>_.value=i),"close-on-click-modal":!1,"destroy-on-close":"",title:"违禁下播",width:"550px"},{footer:e.withCtx(()=>[e.createElementVNode("span",Be,[e.createVNode(r,{onClick:f[2]||(f[2]=i=>_.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(r,{type:"primary",onClick:$},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(F,{ref_key:"prohibitLiveRef",ref:N},null,512)]),_:1},8,["modelValue"])])}}}),$e="",Oe=Object.freeze(Object.defineProperty({__proto__:null,default:A(Ie,[["__scopeId","data-v-7e4044f6"]])},Symbol.toStringTag,{value:"Module"}));return Y});
