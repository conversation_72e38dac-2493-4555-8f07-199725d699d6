(function(e,x){typeof exports=="object"&&typeof module<"u"?module.exports=x(require("vue"),require("element-plus"),require("@/composables/useConvert"),require("@/apis/good"),require("@/utils/http"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/composables/useConvert","@/apis/good","@/utils/http","vue-router"],x):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformGroupDetail=x(e.PlatformGroupDetailContext.Vue,e.PlatformGroupDetailContext.ElementPlus,e.PlatformGroupDetailContext.UseConvert,e.PlatformGroupDetailContext.GoodAPI,e.PlatformGroupDetailContext.UtilsHttp,e.PlatformGroupDetailContext.VueRouter))})(this,function(e,x,E,B,S,U){"use strict";var w=document.createElement("style");w.textContent=`.com[data-v-c7a542c1]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-c7a542c1]{width:62px;height:62px}.com__name[data-v-c7a542c1]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.groupForm[data-v-17c96b73]{padding:0 30px 60px}.groupForm__title[data-v-17c96b73]{font-size:14px;color:#606266;font-weight:700;margin-bottom:30px}.groupForm__stairs[data-v-17c96b73]{margin-bottom:16px}.groupForm__stairs--title[data-v-17c96b73]{font-size:12px;color:#333;font-weight:700}.groupForm__stairs--input[data-v-17c96b73]{margin:0 7px}.groupForm__btn[data-v-17c96b73]{width:1010px;position:fixed;left:292px;bottom:10px;height:60px;display:flex;justify-content:center;align-items:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin:0 auto}.tips[data-v-17c96b73]{font-size:12px;color:#c4c4c4}
`,document.head.appendChild(w);const P={class:"com"},D={class:"com__name"},M=e.defineComponent({__name:"select-good-table",props:{mode:{type:String,default:"COMMON"},users:{type:Array,default(){return[]}},productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}}},setup(k,{expose:h}){const o=k,{divTenThousand:m,mulTenThousand:b}=E(),N=e.ref([]);e.watch(()=>o.productList,l=>{const i=f(l);N.value=s(i)}),e.watch(()=>o.flatGoodList,l=>{N.value=s(l)});function I(l){return l.skuItem.stockType==="LIMITED"?Number(l.skuItem.skuStock):1/0}function f(l,i){if(!l.length)return[];const r=[];return l.forEach(d=>{d.skuIds.forEach((t,n)=>{r.push({productId:d.productId,productName:d.productName,productPic:d.pic,skuItem:{productId:d.productId,skuId:t,skuName:d.specs[n],skuPrice:d.salePrices[n],skuStock:d.stocks[n],stockType:d.stockTypes[n]},rowTag:0,stock:0,prices:[],isJoin:!0})})}),r}function s(l,i){let r=0,d=l.length;for(let t=0;t<d;t++){const n=l[t];t===0&&(n.rowTag=1,r=0),t!==0&&(n.productId===l[t-1].productId?(n.rowTag=0,l[r].rowTag=l[r].rowTag+1):(n.rowTag=1,r=t)),n.prices=n.prices.map(u=>+u),n.stock=+n.stock}return l}const _=({row:l,column:i,rowIndex:r,columnIndex:d})=>{if(d===0)return{rowspan:l.rowTag,colspan:l.rowTag?1:0}};function c(l){return l.stockType==="UNLIMITED"?"不限购":l.skuStock}function y(){const l=e.toRaw(N.value),i=[],r=new Map;if(l.length)for(let d=0;d<l.length;d++){if(!l[d].isJoin)continue;const t=l[d],n=t.productId,u={skuId:t.skuItem.skuId,stock:+t.stock,prices:t.prices.map(g=>b(g).toNumber())};if(!r.has(n))r.set(n,i.length),i.push({productId:n,skus:[u]});else{const g=r.get(n);i[g].skus.push(u)}}return i}function p(){let l=!0;const i=N.value;if(!i.length)x.ElMessage.warning("请选择商品"),l=!1;else for(let r=0;r<i.length;r++)if(i[r].isJoin){if(!i[r].stock){x.ElMessage.warning("商品库存必须大于零"),l=!1;break}if(i[r].prices.length!==o.users.length||i[r].prices.some(d=>d===null)){x.ElMessage.warning("拼团价格填写完整"),l=!1;break}}return l}return h({getProduct:y,validateProduct:p}),(l,i)=>{const r=e.resolveComponent("el-image"),d=e.resolveComponent("el-table-column"),t=e.resolveComponent("el-input-number"),n=e.resolveComponent("el-input"),u=e.resolveComponent("el-switch"),g=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(g,{data:N.value,"span-method":_},{default:e.withCtx(()=>[e.createVNode(d,{label:"商品信息",width:"215"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",P,[e.createVNode(r,{class:"com__pic",src:a.productPic},null,8,["src"]),e.createElementVNode("div",D,e.toDisplayString(a.productName),1)])]),_:1}),e.createVNode(d,{label:"规格"},{default:e.withCtx(({row:a})=>[e.createTextVNode(e.toDisplayString(a.skuItem.skuName),1)]),_:1}),e.createVNode(d,{label:"库存"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,[e.createVNode(t,{modelValue:a.stock,"onUpdate:modelValue":V=>a.stock=V,min:0,style:{width:"80px"},max:I(a),disabled:o.isEdit,precision:0,controls:!1},null,8,["modelValue","onUpdate:modelValue","max","disabled"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(c(a.skuItem)),1)]),_:1}),o.mode==="COMMON"?(e.openBlock(),e.createBlock(d,{key:0,label:"拼团价"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,[o.isEdit?(e.openBlock(),e.createBlock(n,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(m)(a.prices[0]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(t,{key:1,modelValue:a.prices[0],"onUpdate:modelValue":V=>a.prices[0]=V,min:.01,style:{width:"80px"},disabled:o.isEdit,precision:2,max:e.unref(m)(a.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(m)(a.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),o.mode==="STAIRS"&&o.users.length>0?(e.openBlock(),e.createBlock(d,{key:1,label:"第一阶段拼团"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,[o.isEdit?(e.openBlock(),e.createBlock(n,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(m)(a.prices[0]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(t,{key:1,modelValue:a.prices[0],"onUpdate:modelValue":V=>a.prices[0]=V,min:.01,style:{width:"80px"},disabled:o.isEdit,precision:2,max:e.unref(m)(a.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(m)(a.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),o.mode==="STAIRS"&&o.users.length>1?(e.openBlock(),e.createBlock(d,{key:2,label:"第二阶段拼图"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,[o.isEdit?(e.openBlock(),e.createBlock(n,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(m)(a.prices[1]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(t,{key:1,modelValue:a.prices[1],"onUpdate:modelValue":V=>a.prices[1]=V,disabled:o.isEdit,min:.01,style:{width:"80px"},max:a.prices[0]-.01,precision:2,controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(m)(a.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),o.mode==="STAIRS"&&o.users.length>2?(e.openBlock(),e.createBlock(d,{key:3,label:"第三阶段拼图"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,[o.isEdit?(e.openBlock(),e.createBlock(n,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(m)(a.prices[2]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(t,{key:1,modelValue:a.prices[2],"onUpdate:modelValue":V=>a.prices[2]=V,disabled:o.isEdit,min:.01,style:{width:"80px"},precision:2,max:a.prices[1]-.01,controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(m)(a.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(d,{label:"是否参与"},{default:e.withCtx(({row:a})=>[e.createVNode(u,{modelValue:a.isJoin,"onUpdate:modelValue":V=>a.isJoin=V,size:"large",disabled:o.isEdit},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),H="",T=(k,h)=>{const o=k.__vccOpts||k;for(const[m,b]of h)o[m]=b;return o},G=T(M,[["__scopeId","data-v-c7a542c1"]]),F=(k,h)=>S.http.get({url:`addon-team/team/activity/${h}`,params:{shopId:k}}),C=k=>(e.pushScopeId("data-v-17c96b73"),k=k(),e.popScopeId(),k),$={class:"groupForm"},O=C(()=>e.createElementVNode("div",{class:"groupForm__title"},"基本信息",-1)),q={key:1},A={class:"groupForm__stairs--title"},R=C(()=>e.createElementVNode("span",{class:"tips",style:{"margin-left":"8px"}},"商品按下单减库存，请设置未付款订单自动取消时间及时释放库存，可输入3-360分钟",-1)),L=C(()=>e.createElementVNode("div",{class:"tips"}," 开启模拟成团后，拼团有效期内人数未满的团，系统将会以“虚拟用户”凑满人数，使该团拼团成功。你只需要对已付款参团的真实买家发货。建议合理开启，以提高成团率。 ",-1)),z=C(()=>e.createElementVNode("div",{class:"tips"},"开启凑团后，活动商品详情页展示未成团的团列表，买家可以任选一个团参团，提升成团率。",-1)),J={key:1,class:"tips"},j=e.defineComponent({__name:"PlatformGroupDetail",setup(k){const h=U.useRoute(),o=e.ref({name:"",startTime:"",endTime:"",effectTimeout:0,mode:"COMMON",users:[],payTimeout:0,simulate:!1,huddle:!1,preheat:!1,preheatHours:1,stackable:{vip:!1,coupon:!1,full:!1},products:[]}),m=e.ref([]),b=e.ref([]);N();async function N(){const f=h.query.activityId,s=h.query.shopId,{code:_,data:c}=await F(s,f);if(_!==200)return x.ElMessage.error("获取表单失败");o.value=c,b.value=[c.startTime,c.endTime],I(c)}async function I(f){const s=f.products.map(p=>p.productId),{code:_,data:c}=await B.doGetRetrieveProduct({productId:s});if(_!==200)return x.ElMessage.error("获取活动商品信息失败");const y=[];for(let p=0;p<f.products.length;p++){const l=c.list.findIndex(i=>i.productId===f.products[p].productId);for(let i=0;i<f.products[l].skus.length;i++){let r={productName:c.list[p].productName,productPic:c.list[p].pic,productId:c.list[p].productId,skuItem:{productId:c.list[p].productId,skuId:"",skuName:"",skuPrice:"",skuStock:"",stockType:"LIMITED"},rowTag:0,stock:0,prices:[],isJoin:!0};const d=f.products[l].skus[i].skuId,t=c.list[p].skuIds.findIndex(n=>n===d);r.skuItem.skuId=c.list[p].skuIds[t],r.skuItem.skuName=c.list[p].specs[t],r.skuItem.skuPrice=c.list[p].salePrices[t],r.skuItem.skuStock=c.list[p].stocks[t],r.skuItem.stockType=c.list[p].stockTypes[t],r.stock=f.products[l].skus[i].stock,r.prices=f.products[l].skus[i].prices,y.push(r)}}m.value=y}return(f,s)=>{const _=e.resolveComponent("el-input"),c=e.resolveComponent("el-form-item"),y=e.resolveComponent("el-date-picker"),p=e.resolveComponent("el-radio"),l=e.resolveComponent("el-radio-group"),i=e.resolveComponent("el-input-number"),r=e.resolveComponent("el-checkbox"),d=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",$,[O,e.createVNode(d,{ref:"ruleFormRef",model:o.value,"label-width":"110","label-position":"right",disabled:""},{default:e.withCtx(()=>[e.createVNode(c,{label:"活动名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:o.value.name,"onUpdate:modelValue":s[0]||(s[0]=t=>o.value.name=t),placeholder:"限10字",style:{width:"550px"},maxlength:"10"},null,8,["modelValue"])]),_:1}),e.createVNode(c,{label:"活动时间",prop:"startTime"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(y,{modelValue:b.value,"onUpdate:modelValue":s[1]||(s[1]=t=>b.value=t),style:{width:"550px"},type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间"},null,8,["modelValue"])])]),_:1}),e.createVNode(c,{label:"拼团有效时间",prop:"effectTimeout",required:""},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:o.value.effectTimeout,"onUpdate:modelValue":s[2]||(s[2]=t=>o.value.effectTimeout=t),formatter:t=>Number(`${t}`.replace(/[^\d]/g,"")),style:{width:"550px"}},{append:e.withCtx(()=>[e.createTextVNode(" 分钟 ")]),_:1},8,["modelValue","formatter"])]),_:1}),e.createVNode(c,{label:"拼团模式",prop:"mode"},{default:e.withCtx(()=>[e.createVNode(l,{modelValue:o.value.mode,"onUpdate:modelValue":s[3]||(s[3]=t=>o.value.mode=t)},{default:e.withCtx(()=>[e.createVNode(p,{label:"COMMON"},{default:e.withCtx(()=>[e.createTextVNode("普通拼团")]),_:1}),e.createVNode(p,{label:"STAIRS"},{default:e.withCtx(()=>[e.createTextVNode("阶梯拼团")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(c,{label:"参团人数",prop:"users",required:""},{default:e.withCtx(()=>[o.value.mode==="COMMON"?(e.openBlock(),e.createBlock(_,{key:0,modelValue:o.value.users[0],"onUpdate:modelValue":s[4]||(s[4]=t=>o.value.users[0]=t),formatter:t=>Number(`${t}`.replace(/[^\d]/g,"")),parser:t=>`$ ${Number(t)>=100?100:t}`,style:{width:"550px"},max:100},{append:e.withCtx(()=>[e.createTextVNode(" 人 ")]),_:1},8,["modelValue","formatter","parser"])):(e.openBlock(),e.createElementBlock("div",q,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value.users,(t,n)=>(e.openBlock(),e.createElementBlock("div",{key:n,class:"groupForm__stairs"},[e.createElementVNode("span",A,"第"+e.toDisplayString(n+1)+"阶段人数",1),e.createVNode(_,{modelValue:o.value.users[n],"onUpdate:modelValue":u=>o.value.users[n]=u,class:"groupForm__stairs--input",style:{width:"450px"},formatter:u=>Number(`${u}`.replace(/[^\d]/g,"")),parser:u=>`${Number(u)>=100?100:u}`,max:100},{append:e.withCtx(()=>[e.createTextVNode(" 人 ")]),_:2},1032,["modelValue","onUpdate:modelValue","formatter","parser"])]))),128))]))]),_:1}),e.createVNode(c,{label:"订单关闭时间",prop:"payTimeout"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.value.payTimeout,"onUpdate:modelValue":s[5]||(s[5]=t=>o.value.payTimeout=t),controls:!1,max:360,min:3},null,8,["modelValue"]),R]),_:1}),e.createVNode(c,{label:"模拟成团",prop:"simulate"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(l,{modelValue:o.value.simulate,"onUpdate:modelValue":s[6]||(s[6]=t=>o.value.simulate=t)},{default:e.withCtx(()=>[e.createVNode(p,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(p,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),L])]),_:1}),e.createVNode(c,{label:"凑团模式",prop:"huddle"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(l,{modelValue:o.value.huddle,"onUpdate:modelValue":s[7]||(s[7]=t=>o.value.huddle=t)},{default:e.withCtx(()=>[e.createVNode(p,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(p,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),z])]),_:1}),e.createVNode(c,{label:"活动预热",prop:"preheat"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(l,{modelValue:o.value.preheat,"onUpdate:modelValue":s[8]||(s[8]=t=>o.value.preheat=t)},{default:e.withCtx(()=>[e.createVNode(p,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(p,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),e.createElementVNode("div",null,[o.value.preheat?(e.openBlock(),e.createBlock(_,{key:0,modelValue:o.value.preheatHours,"onUpdate:modelValue":s[9]||(s[9]=t=>o.value.preheatHours=t),style:{width:"450px"},formatter:t=>Number(`${t}`.replace(/[^\d]/g,"")),parser:t=>`${Number(t)>=24?24:t}`,max:100},{append:e.withCtx(()=>[e.createTextVNode(" 小时 ")]),_:1},8,["modelValue","formatter","parser"])):(e.openBlock(),e.createElementBlock("div",J,"开启后，商品详情展示未开始的拼团活动，但活动开始前用户无法拼团购买。"))])])]),_:1}),e.createVNode(c,{label:"叠加优惠"},{default:e.withCtx(()=>[e.createVNode(r,{modelValue:o.value.stackable.vip,"onUpdate:modelValue":s[10]||(s[10]=t=>o.value.stackable.vip=t),label:"会员价"},null,8,["modelValue"]),e.createVNode(r,{modelValue:o.value.stackable.coupon,"onUpdate:modelValue":s[11]||(s[11]=t=>o.value.stackable.coupon=t),label:"优惠券"},null,8,["modelValue"]),e.createVNode(r,{modelValue:o.value.stackable.full,"onUpdate:modelValue":s[12]||(s[12]=t=>o.value.stackable.full=t),label:"满减"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),e.createVNode(G,{ref:"selectGoodsTableRef",mode:o.value.mode,users:o.value.users,"is-edit":!0,"flat-good-list":m.value},null,8,["mode","users","flat-good-list"])])}}}),K="";return T(j,[["__scopeId","data-v-17c96b73"]])});
