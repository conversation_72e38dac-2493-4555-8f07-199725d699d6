(function(e,w){typeof exports=="object"&&typeof module<"u"?module.exports=w(require("vue"),require("@/utils/http"),require("@/apis/http"),require("decimal.js")):typeof define=="function"&&define.amd?define(["vue","@/utils/http","@/apis/http","decimal.js"],w):(e=typeof globalThis<"u"?globalThis:e||self,e.PcGroupOrder=w(e.PcGroupOrderContext.Vue,e.PcGroupOrderContext.UtilsHttp,e.PcGroupOrderContext.Request))})(this,function(e,w,S){"use strict";var T=document.createElement("style");T.textContent=`[data-v-ee093e9f] .el-card__body{padding:0}em[data-v-ee093e9f],i[data-v-ee093e9f]{font-style:normal}.xianshi[data-v-ee093e9f]{display:block!important}.all[data-v-ee093e9f]{display:none;width:100%;height:100%;background-color:#10101080;position:fixed;top:0;left:0;z-index:999999}.all .el-card[data-v-ee093e9f]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);z-index:9999999;width:520px;border-radius:8px;background-color:#fff}.all .el-card .title[data-v-ee093e9f]{height:44px;line-height:44px;border-bottom:1px solid #bbb}.all .el-card .title span[data-v-ee093e9f]{margin-left:24px;float:left;font-size:14px;font-weight:700}.all .el-card .title i[data-v-ee093e9f]{margin:6px 14px 0;float:right;cursor:pointer}.all .el-card .line[data-v-ee093e9f]{margin:24px 0 14px}.all .el-card .line i[data-v-ee093e9f]{display:inline-block;width:106px;border-top:1px dashed rgba(10,10,10,.3);transform:translateY(-4px)}.all .el-card .line em[data-v-ee093e9f]{width:96px;height:24px;font-size:16px;font-weight:700;display:inline-block;margin-left:35px;margin-right:30px}.all .el-card p[data-v-ee093e9f]{display:inline-block;height:17px;font-size:12px;font-weight:700;margin-bottom:37px}.all .el-card p i[data-v-ee093e9f]{margin:0 5px;color:#ee1e38}.all .el-card .photo[data-v-ee093e9f]{margin-bottom:34px}.all .el-card .photo .user-img[data-v-ee093e9f]{display:inline-flex}.all .el-card .photo .user-img i[data-v-ee093e9f]{padding:2px 5px;background-color:#f7c945;color:#fff;font-weight:700;border-radius:4px;font-size:12px;left:-12px;top:-11px;position:absolute;z-index:9}.all .el-card .photo span[data-v-ee093e9f]{width:58px;height:58px;border-radius:50%;box-sizing:border-box;margin-left:29px;border:1px dashed #bbb}.all .el-card .photo span[data-v-ee093e9f]:first-of-type{border:4px solid #f7c945;margin-left:0}.all .el-card .photo span img[data-v-ee093e9f]{border-radius:50%;width:100%;height:100%}.all .el-card .rule[data-v-ee093e9f]{border-top:1px solid #bbb;padding:24px 0}.all .el-card .rule p[data-v-ee093e9f]{font-size:14px;width:100%;padding-right:66px;margin-bottom:16px;text-align:right;color:#101010}.all .el-card .rule p[data-v-ee093e9f]:last-child{margin-bottom:0}.all .el-card .rule p span[data-v-ee093e9f]{font-weight:500!important;margin-left:66px;float:left;text-align:justify;text-align-last:justify;text-justify:inter-word;display:block;width:70px;height:20px;color:#a7a7a7}
`,document.head.appendChild(T);const V={WAITING_FOR_DELIVER:{desp:"待发货",steps:2},WAITING_FOR_RECEIVE:{desp:"等待收货",color:"#71B247",track:!0,Confirmreceipt:!0,steps:3},BUYER_WAITING_FOR_COMMENT:{desp:"交易成功",color:"#ccc",track:!0,evaluate:!0,steps:4},SYSTEM_WAITING_FOR_COMMENT:{desp:"交易成功",color:"#ccc",track:!0,evaluate:!0,steps:4},BUYER_COMMENTED_COMPLETED:{desp:"交易成功",color:"#ccc",track:!0,steps:4},SYSTEM_COMMENTED_COMPLETED:{desp:"交易成功",color:"#ccc",track:!0,steps:4}},D=a=>S.get({url:`addon-team/team/activity/order/summary?teamNo=${a}`}),I=a=>S.get({url:`addon-team/team/activity/order/users?size=300&teamNo=${a}`}),i=a=>(e.pushScopeId("data-v-ee093e9f"),a=a(),e.popScopeId(),a),O=i(()=>e.createElementVNode("span",null,"我的拼团",-1)),G=[i(()=>e.createElementVNode("svg",{t:"1686211043354",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"8090",width:"24",height:"24"},[e.createElementVNode("path",{d:"M576 512l277.333 277.333-64 64L512 576 234.667 853.333l-64-64L448 512 170.667 234.667l64-64L512 448l277.333-277.333 64 64L576 512z",fill:"#101010","p-id":"8091"})],-1))],z={key:0},F=i(()=>e.createElementVNode("div",{class:"line"},[e.createElementVNode("i"),e.createElementVNode("em",null,"邀请好友参团"),e.createElementVNode("i")],-1)),L={key:1,style:{height:"93px","line-height":"93px","margin-left":"125px",color:"#de3224"}},P={key:2,style:{height:"93px","line-height":"93px","margin-left":"55px",color:"#de3224"}},j={class:"photo"},A={class:"user-img",style:{transform:"translateY(-5px)"}},R=i(()=>e.createElementVNode("i",null,"团长",-1)),q=["src"],v=[i(()=>e.createElementVNode("img",null,null,-1))],Y={class:"rule"},U=i(()=>e.createElementVNode("span",null,"商品名称：",-1)),$={style:{display:"inline-block",width:"300px","white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"}},W=i(()=>e.createElementVNode("p",null,[e.createElementVNode("span",null,"拼团规则："),e.createTextVNode("参团人数不足，系统自动退款")],-1)),H=e.defineComponent({__name:"PcMyTeam",props:{isShow:{type:Boolean,default:!1},teamNo:{type:Object,required:!0},records:{type:Object,required:!0}},emits:["isShowFalse"],setup(a,{emit:r}){var _,x,N,E;const t=a,m=()=>{r("isShowFalse",!1)},f=e.computed(()=>{var o,d,p,n,y,k;return(o=t.teamNo)!=null&&o.totalNum&&((p=(d=t.records)==null?void 0:d.records)!=null&&p.length)&&((n=t.teamNo)==null?void 0:n.totalNum)-((k=(y=t.records)==null?void 0:y.records)==null?void 0:k.length)||0}),h=e.ref(new Date((_=t.teamNo)==null?void 0:_.openTime).getTime()+Number((x=t.teamNo)==null?void 0:x.effectTimeout)*60*1e3||new Date),g=e.ref(new Date((N=t.teamNo)==null?void 0:N.openTime).getTime()+Number((E=t.teamNo)==null?void 0:E.effectTimeout)*60*1e3||new Date),l=e.ref(new Date().getTime()),s=e.computed(()=>{if(h.value.toString()<l.value.toString())return"00";let o=+g.value-l.value;return o<0?"00":Math.floor(o%(1e3*60*60*24)/(1e3*60*60)).toString().padStart(2,"0")}),c=e.computed(()=>{if(h.value.toString()<l.value.toString())return"00";let o=+g.value-l.value;return o<0?"00":Math.floor(o%(1e3*60*60)/(1e3*60)).toString().padStart(2,"0")}),u=e.computed(()=>{if(h.value.toString()<l.value.toString())return"00";let o=+g.value-l.value;return o<0?"00":Math.floor(o%(1e3*60)/1e3).toString().padStart(2,"0")});return e.onMounted(()=>{setInterval(()=>{l.value=new Date().getTime()},1e3)}),(o,d)=>{const p=e.resolveComponent("el-card");return e.openBlock(),e.createElementBlock("div",{class:e.normalizeClass(t.isShow===!0?"all xianshi":"all")},[e.createVNode(p,{class:"el-card"},{default:e.withCtx(()=>{var n,y,k,b,B,C;return[e.createElementVNode("div",{class:"title"},[O,e.createElementVNode("i",{onClick:m},G)]),((n=t.teamNo)==null?void 0:n.status)==="ING"?(e.openBlock(),e.createElementBlock("div",z,[F,e.createElementVNode("p",null,[e.createTextVNode(" 仅剩"),e.createElementVNode("i",null,e.toDisplayString(((y=t.teamNo)==null?void 0:y.totalNum)-t.teamNo.currentNum||0)+"个",1),e.createTextVNode("名额"),e.createElementVNode("i",null,e.toDisplayString(s.value)+":"+e.toDisplayString(c.value)+":"+e.toDisplayString(u.value),1),e.createTextVNode("后结束 ")])])):((k=t.teamNo)==null?void 0:k.status)==="FAIL"?(e.openBlock(),e.createElementBlock("div",L," 真可惜拼团未能成功！！！ ")):((b=t.teamNo)==null?void 0:b.status)==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",P," 拼团成功！！！ ")):e.createCommentVNode("",!0),e.createElementVNode("div",j,[e.createElementVNode("div",A,[R,(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList((B=t.records)==null?void 0:B.records,(M,J)=>(e.openBlock(),e.createElementBlock("span",{key:J},[e.createElementVNode("img",{src:t.teamNo.commanderAvatar,alt:""},null,8,q)]))),128))]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,M=>(e.openBlock(),e.createElementBlock("div",{key:M,class:"user-img"},v))),128))]),e.createElementVNode("div",Y,[e.createElementVNode("p",null,[U,e.createElementVNode("em",$,e.toDisplayString((C=t.teamNo)==null?void 0:C.productName),1)]),W])]}),_:1})],2)}}}),Z="",ee="",K=((a,r)=>{const t=a.__vccOpts||a;for(const[m,f]of r)t[m]=f;return t})(H,[["__scopeId","data-v-ee093e9f"]]);return e.defineComponent({__name:"PcGroupOrder",props:{properties:{type:Object,default:()=>{}}},setup(a){const r=a;e.reactive({current:1,size:10});const t=e.ref(!1),m=e.ref(0),f=e.ref(),h=e.ref(),g=async s=>{t.value=!0;const c=await D(s);c.code===200&&(f.value=c.data);const{data:u,code:_}=await I(s);h.value=u,m.value=Date.now()},l=s=>{t.value=s};return(s,c)=>{var u,_,x,N,E,o;return e.openBlock(),e.createElementBlock(e.Fragment,null,[!e.unref(V)[(_=(u=r.properties)==null?void 0:u.shop)==null?void 0:_.shopOrderItems[0].packageStatus].Confirmreceipt&&!e.unref(V)[(N=(x=r.properties)==null?void 0:x.shop)==null?void 0:N.shopOrderItems[0].packageStatus].evaluate&&((E=r.properties)==null?void 0:E.order.type)==="TEAM"||((o=r.properties)==null?void 0:o.order.status)==="TEAMING"?(e.openBlock(),e.createElementBlock("div",{key:0,"c-w-87":"","c-h-24":"","c-lh-24":"","c-c-E31436":"","b-1":"","c-bc-E31436":"","c-mt-4":"","c-mb-6":"","cursor-pointer":"",onClick:c[0]||(c[0]=d=>{var p,n;return g((n=(p=r.properties)==null?void 0:p.order)==null?void 0:n.extra.teamNo)})}," 我的拼团 ")):e.createCommentVNode("",!0),t.value?(e.openBlock(),e.createBlock(K,{key:m.value,"is-show":t.value,"team-no":f.value,records:h.value,onIsShowFalse:l},null,8,["is-show","team-no","records"])):e.createCommentVNode("",!0)],64)}}})});
