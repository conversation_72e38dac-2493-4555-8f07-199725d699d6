(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("vue-router"),require("@/components/MCard.vue"),require("@element-plus/icons-vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("element-plus"),require("@/utils/Storage"),require("@/components/q-tooltip/q-tooltip.vue"),require("@/apis/shops"),require("@/composables/useConvert"),require("@/components/q-btn/q-dropdown-btn.vue"),require("@/components/pageManage/PageManage.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/MCard.vue","@element-plus/icons-vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","element-plus","@/utils/Storage","@/components/q-tooltip/q-tooltip.vue","@/apis/shops","@/composables/useConvert","@/components/q-btn/q-dropdown-btn.vue","@/components/pageManage/PageManage.vue"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformSupplierList=V(e.PlatformSupplierListContext.Vue,e.PlatformSupplierListContext.VueRouter,e.PlatformSupplierListContext.MCard,e.PlatformSupplierListContext.ElementPlusIconsVue,e.PlatformSupplierListContext.QTable,e.PlatformSupplierListContext.QTableColumn,e.PlatformSupplierListContext.ElementPlus,e.PlatformSupplierListContext.Storage,e.PlatformSupplierListContext.QTooltip,e.PlatformSupplierListContext.ShopAPI,e.PlatformSupplierListContext.UseConvert,e.PlatformSupplierListContext.QDropdownBtn,e.PlatformSupplierListContext.PageManage))})(this,function(e,V,M,P,z,R,f,q,B,k,A,$,U){"use strict";var O=document.createElement("style");O.textContent=`.shop__search-visible[data-v-80ad3646]{padding:20px 20px 0}.row_balance{display:flex;width:100%;flex-direction:column;align-items:center}.row_balance .col_row_balance+.col_row_balance{margin-top:5px}.header{font-size:12px;color:#838383;height:46px;line-height:46px;border-radius:10px 10px 0 0;display:flex;justify-content:space-between;align-items:center;width:100%}.mr-20{margin-right:20px}.shop{height:68px;font-size:12px;width:100%;padding-left:10px;display:flex;align-items:center}.shop__title{width:130px;margin-left:10px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.shop__content{height:60px;display:flex;flex-direction:column;justify-content:space-between}.shop__content--name{display:flex;align-items:center;font-weight:700;font-size:1.3em;line-height:20px}.shop__content--phone{font-size:1.2em}.tag__autarky{flex-shrink:0;margin-right:5px;padding:0 5px;background-color:#fd0505;color:#fff;border-radius:4px;font-size:.7em}.tag__optimize{flex-shrink:0;margin-right:5px;padding:0 5px;background-color:#7728f5;color:#fff;border-radius:4px;font-size:.8em}.btn{color:#2e99f3;cursor:pointer}.q-table{height:calc(100vh - 430px);overflow:auto;transition:height .5s}.up-table{height:calc(100vh - 330px);overflow:auto}.btns[data-v-f1e5d074]{display:flex;justify-content:center;align-items:center;justify-content:flex-start}.down[data-v-f1e5d074]{height:18px}.mr-20[data-v-f1e5d074]{margin-right:20px}.mb-15[data-v-f1e5d074]{margin-bottom:15px}.group[data-v-f1e5d074]{position:relative}.group__placeholder[data-v-f1e5d074]{position:absolute;right:-2px;z-index:999}.group__placeholder[data-v-f1e5d074]:hover{color:#337ecc!important}.text-center[data-v-f1e5d074]{margin-bottom:5px}.q-table[data-v-f1e5d074]{height:calc(100vh - 470px)}.up-table[data-v-f1e5d074]{height:calc(100vh - 315px)}
`,document.head.appendChild(O);const j={style:{background:"#f9f9f9"}},F=e.defineComponent({__name:"search",emits:["searchParams","showChange"],setup(E,{emit:x}){V.useRoute();const t=e.reactive({no:"",name:"",status:"",shopType:"",extractionType:""}),o=e.ref(!1),g=()=>{x("searchParams",t)};return e.watch(()=>o.value,c=>{x("showChange",c)}),(c,n)=>{const C=e.resolveComponent("el-input"),u=e.resolveComponent("el-form-item"),l=e.resolveComponent("el-col"),r=e.resolveComponent("el-option"),d=e.resolveComponent("el-select"),m=e.resolveComponent("el-row"),b=e.resolveComponent("el-button"),w=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",j,[e.createVNode(w,{model:t},{default:e.withCtx(()=>[e.createVNode(M,{modelValue:o.value,"onUpdate:modelValue":n[5]||(n[5]=p=>o.value=p)},{default:e.withCtx(()=>[e.createVNode(m,{gutter:20},{default:e.withCtx(()=>[e.createVNode(l,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{label:"供应商ID"},{default:e.withCtx(()=>[e.createVNode(C,{modelValue:t.no,"onUpdate:modelValue":n[0]||(n[0]=p=>t.no=p),maxlength:"20",placeholder:"请填写店铺ID"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(l,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{label:"供应商名称"},{default:e.withCtx(()=>[e.createVNode(C,{modelValue:t.name,"onUpdate:modelValue":n[1]||(n[1]=p=>t.name=p),maxlength:"20",placeholder:"请填写店铺名称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(l,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{label:"状态"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:t.status,"onUpdate:modelValue":n[2]||(n[2]=p=>t.status=p),placeholder:"请选择"},{default:e.withCtx(()=>[e.createVNode(r,{label:"全部",value:" "}),e.createVNode(r,{label:"正常",value:"NORMAL"}),e.createVNode(r,{label:"禁用",value:"FORBIDDEN"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(m,{gutter:20},{default:e.withCtx(()=>[e.createVNode(l,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{label:"提佣类型"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:t.extractionType,"onUpdate:modelValue":n[3]||(n[3]=p=>t.extractionType=p),placeholder:"请选择"},{default:e.withCtx(()=>[e.createVNode(r,{label:"全部",value:""}),e.createVNode(r,{label:"类目提佣",value:"CATEGORY_EXTRACTION"}),e.createVNode(r,{label:"订单金额提佣",value:"ORDER_SALES_EXTRACTION"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(l,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{label:"供应商类型"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:t.shopType,"onUpdate:modelValue":n[4]||(n[4]=p=>t.shopType=p),placeholder:"请选择"},{default:e.withCtx(()=>[e.createVNode(r,{label:"全部",value:""}),e.createVNode(r,{label:"自营",value:"SELF_OWNED"}),e.createVNode(r,{label:"优选",value:"PREFERRED"}),e.createVNode(r,{label:"普通",value:"ORDINARY"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(m,{gutter:20},{default:e.withCtx(()=>[e.createVNode(l,{span:3},{default:e.withCtx(()=>[e.createVNode(b,{r:"",round:"",type:"primary",onClick:g},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["model"])])}}}),ie="",I=(E,x)=>{const t=E.__vccOpts||E;for(const[o,g]of x)t[o]=g;return t},H=I(F,[["__scopeId","data-v-80ad3646"]]),J={class:"header"},W={class:"header__content"},X={class:"mr-20"},G={class:"shop"},Y={class:"shop__content"},Q={class:"shop__content--name"},K={key:0,class:"tag__autarky"},Z={key:1,class:"tag__optimize"},v={class:"shop__content--phone"},ee={class:"shop__content--address"},te={class:"col_row_balance"},ae={class:"col_row_balance"},oe=[e.createElementVNode("span",null,"已拒绝",-1)],ne={class:"btn"},se=e.defineComponent({__name:"supplier-table",props:{tableList:{type:Array,default(){return[]}}},setup(E,{expose:x}){const t=E,o=e.inject("parentTabChangeHandle"),g=e.inject("parentTabChoose"),c=e.inject("parentShowChangeStatus"),n=V.useRouter(),{divTenThousand:C}=A(),u=e.ref([]);e.watch(t,s=>{u.value=s.tableList},{immediate:!0,deep:!0});const l=e.ref([]),r=(s,i)=>{k.doShopAudit(s,i).then(h=>{if(h.code!==200){f.ElMessage.error("更新");return}f.ElMessage.success("已更新"),o("UNDER_REVIEW")})},d=(s,i)=>{const h=i==="NORMAL";k.doChangeStatus([s],h)},m=async(s,i)=>{f.ElMessageBox.confirm("确定要删除该供应商吗，此操作不可逆?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const h=C(s.shopBalance.uncompleted).toNumber(),S=C(s.shopBalance.undrawn).toNumber();if(h||S){f.ElMessage.warning({message:"该供应商存在未（提现/结算）金额，禁止删除",duration:4e3});return}const{code:D,msg:L}=await k.doDelShop([s.id]);if(D===200){f.ElMessage({type:"success",message:"删除成功"}),o(i||" ");return}f.ElMessage({type:"error",message:L})}).catch(()=>{})},b=()=>{if(!l.value.length)return f.ElMessage.error("请勾选列表");f.ElMessageBox.confirm("确定要删除该供应商吗，此操作不可逆?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const s=l.value.map(S=>S.id),{code:i,success:h}=await k.doDelShop(s);i===200&&h&&(g.value!=="REJECT"?o(" "):o("REJECT"),f.ElMessage({type:"success",message:"删除成功"}))}).catch(()=>{})},w=async s=>{const i=s==="NORMAL";if(!_())return f.ElMessage.error("请勾选列表"),!1;const h=l.value.map(L=>L.id);if(s==="refusedTo")return r(h.join(","),!1);const{code:S,success:D}=await k.doChangeStatus(h,i);return S===200&&D===!0&&f.ElMessage.success("操作成功"),!0},p=()=>{};function _(s){let i=!0;return l.value.length||(i=!1),i}const T=(s,i)=>{new q().setItem("SHOPITEM",s,60*60*2),n.push({path:"/supplier/editSupplier",query:{shopId:s.id,type:i}})},N=s=>{new q().setItem("SHOPITEM",s,60*60*2),n.push({path:"/supplier/previewSupplier",query:{shopId:s.id}})};return x({changeStatus:d,deleteShop:m,batchChangeStatus:w,batchDeleteShop:b}),(s,i)=>{const h=e.resolveComponent("el-link"),S=e.resolveComponent("el-avatar"),D=e.resolveComponent("el-row"),L=e.resolveComponent("el-switch");return e.openBlock(),e.createBlock(e.unref(z),{"checked-item":l.value,"onUpdate:checkedItem":i[0]||(i[0]=a=>l.value=a),class:e.normalizeClass([{"up-table":!e.unref(c)},"q-table"]),data:u.value,selection:!0},{header:e.withCtx(({row:a})=>[e.createElementVNode("div",J,[e.createElementVNode("div",W,[e.createElementVNode("span",X,"供应商ID："+e.toDisplayString(a.no),1),e.createElementVNode("span",null,"申请时间："+e.toDisplayString(a.createTime),1)]),e.createVNode(h,{underline:!1,class:"mr-20",style:{"font-size":"12px"},type:"primary",onClick:y=>N(a)},{default:e.withCtx(()=>[e.createTextVNode(" 查看 ")]),_:2},1032,["onClick"])])]),default:e.withCtx(()=>[e.createVNode(R,{label:"供应商信息"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",G,[e.createVNode(S,{icon:e.unref(P.Shop),size:60,src:a.logo,shape:"square",style:{"vertical-align":"middle",margin:"0 10px 0 0"}},null,8,["icon","src"]),e.createElementVNode("div",Y,[e.createElementVNode("div",Q,[a.shopType==="SELF_OWNED"?(e.openBlock(),e.createElementBlock("span",K,"自营")):a.shopType==="PREFERRED"?(e.openBlock(),e.createElementBlock("span",Z,"优选 ")):e.createCommentVNode("",!0),e.createVNode(B,{content:a.name,width:"160px"},null,8,["content"])]),e.createElementVNode("div",v,e.toDisplayString(a.contractNumber),1),e.createElementVNode("div",ee,[e.createVNode(B,{content:a.address,style:{"margin-left":"0"},width:"auto"},null,8,["content"])])])])]),_:1}),e.createVNode(R,{align:"left",label:"供应商余额",width:"250"},{default:e.withCtx(({row:a})=>[e.createVNode(D,{class:"row_balance",onClick:p},{default:e.withCtx(()=>[e.createElementVNode("div",te,"待提现:"+e.toDisplayString(e.unref(C)(a.shopBalance.undrawn)),1),e.createElementVNode("div",ae,"待结算:"+e.toDisplayString(e.unref(C)(a.shopBalance.uncompleted)),1)]),_:2},1024)]),_:1}),e.createVNode(R,{label:"提佣类型",width:"130"},{default:e.withCtx(({row:a})=>[e.createTextVNode(e.toDisplayString(a.extractionType==="CATEGORY_EXTRACTION"?"类目抽佣":a.extractionType==="ORDER_SALES_EXTRACTION"?"订单金额提佣":""),1)]),_:1}),e.createVNode(R,{label:"状态",prop:"status",width:"50"},{default:e.withCtx(({row:a})=>[a.status==="FORBIDDEN"||a.status==="NORMAL"?(e.openBlock(),e.createBlock(L,{key:0,modelValue:a.status,"onUpdate:modelValue":y=>a.status=y,"active-value":"NORMAL","inactive-value":"FORBIDDEN",onChange:y=>d(a.id,a.status)},null,8,["modelValue","onUpdate:modelValue","onChange"])):e.createCommentVNode("",!0),e.withDirectives(e.createElementVNode("div",null,oe,512),[[e.vShow,a.status==="REJECT"]])]),_:1}),e.createVNode(R,{label:"操作",width:"130"},{default:e.withCtx(({row:a})=>[e.withDirectives(e.createElementVNode("div",ne,[e.createVNode(h,{underline:!1,class:"mr-20",style:{"font-size":"12px"},type:"primary",onClick:y=>T(a,"EDIT")},{default:e.withCtx(()=>[e.createTextVNode("编辑 ")]),_:2},1032,["onClick"]),e.createVNode(h,{underline:!1,style:{"font-size":"12px"},type:"danger",onClick:y=>m(a)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])],512),[[e.vShow,a.status==="FORBIDDEN"||a.status==="NORMAL"]]),e.withDirectives(e.createElementVNode("div",null,[e.createVNode(h,{underline:!1,class:"mr-20",style:{"font-size":"12px"},type:"primary",onClick:y=>r(a.id,!0)},{default:e.withCtx(()=>[e.createTextVNode("通过 ")]),_:2},1032,["onClick"]),e.createVNode(h,{underline:!1,style:{"font-size":"12px"},type:"danger",onClick:y=>r(a.id,!1)},{default:e.withCtx(()=>[e.createTextVNode("拒绝 ")]),_:2},1032,["onClick"])],512),[[e.vShow,a.status==="UNDER_REVIEW"]]),e.withDirectives(e.createElementVNode("div",null,[e.createVNode(h,{underline:!1,style:{"font-size":"12px"},type:"danger",onClick:y=>m(a,"REJECT")},{default:e.withCtx(()=>[e.createTextVNode("删除 ")]),_:2},1032,["onClick"])],512),[[e.vShow,a.status==="REJECT"]])]),_:1})]),_:1},8,["checked-item","class","data"])}}}),pe="",le={class:"mb-15 btns"},re=e.defineComponent({__name:"list-part",props:{searchParams:{type:Object,default(){return{}}},currentTabChoose:{type:String,required:!0}},setup(E,{expose:x}){const t=E,o=e.reactive({size:20,current:1,total:0}),g=e.ref([]),c=e.ref();e.ref();const n=e.ref([{label:"删除",name:"Delete"},{label:"启用",name:"NORMAL"},{label:"禁用",name:"FORBIDDEN"}]);e.watch(()=>t.currentTabChoose,_=>{switch(_){case"REJECT":n.value=[{label:"Delete",name:"删除"}];break;case"UNDER_REVIEW":n.value=[{label:"refusedTo",name:"拒绝"}];break;default:n.value=[{name:"Delete",label:"删除"},{name:"NORMAL",label:"启用"},{name:"FORBIDDEN",label:"禁用"}];break}},{immediate:!0});const C=V.useRouter(),u=()=>{C.push({name:"addsupplier"})};l(t.searchParams),x({initList:l});async function l(_){const T=Object.assign(_,o,{shopModes:"SUPPLIER"}),{data:N}=await k.doGetShopList(T);g.value=N.records,o.current=+N.current,o.total=+N.total,o.size=+N.size}const r=_=>{switch(_){case"Delete":d();break;case"NORMAL":m("NORMAL");break;case"FORBIDDEN":m("FORBIDDEN");break;default:m("refusedTo");break}},d=()=>{c.value.batchDeleteShop()},m=async _=>{if(t.currentTabChoose==="REJECT")return f.ElMessage.error("该商户已被拒绝");await c.value.batchChangeStatus(_)&&l(Object.assign(t.searchParams,{status:t.currentTabChoose}))},b=()=>{l(t.searchParams)},w=_=>{o.size=_,l(Object.assign(t.searchParams,{status:t.currentTabChoose}))},p=_=>{o.current=_,l(Object.assign(t.searchParams,{status:t.currentTabChoose}))};return(_,T)=>{const N=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",le,[e.createVNode(N,{class:"mr-20",round:"",type:"primary",onClick:u},{default:e.withCtx(()=>[e.createTextVNode("添加供应商")]),_:1}),t.currentTabChoose==="REJECT"?(e.openBlock(),e.createBlock(N,{key:0,class:"mr-20",round:"",type:"primary",onClick:T[0]||(T[0]=s=>r("Delete"))},{default:e.withCtx(()=>[e.createTextVNode(" 批量删除 ")]),_:1})):e.createCommentVNode("",!0),t.currentTabChoose===" "?(e.openBlock(),e.createBlock($,{key:1,option:n.value,title:"批量操作",onRightClick:r},null,8,["option"])):e.createCommentVNode("",!0)]),e.createVNode(se,{ref_key:"shopTableRef",ref:c,"table-list":g.value,onRefresh:b},null,8,["table-list"]),e.createVNode(U,{"page-num":o.current,"page-size":o.size,total:o.total,onHandleSizeChange:w,onHandleCurrentChange:p},null,8,["page-num","page-size","total"])],64)}}}),he="",ce=I(re,[["__scopeId","data-v-f1e5d074"]]);return e.defineComponent({__name:"PlatformSupplierList",setup(E){const x=e.ref(),t=e.ref(),o=V.useRoute(),g=e.reactive({searchParams:{no:"",name:"",status:o.query.name||""}}),c=e.ref(o.query.name||""),n=e.ref(!1),C=()=>{x.value=c.value,t.value.initList({status:c.value})},u=d=>{n.value=d},l=d=>{c.value=d,t.value.initList({status:d})};e.provide("parentTabChangeHandle",l),e.provide("parentTabChoose",c),e.provide("parentShowChangeStatus",n);const r=async d=>{if(c.value!==""){t.value.initList({...d,status:c.value});return}t.value.initList(d)};return(d,m)=>{const b=e.resolveComponent("el-tab-pane"),w=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(H,{onSearchParams:r,onShowChange:u}),e.createVNode(w,{modelValue:c.value,"onUpdate:modelValue":m[0]||(m[0]=p=>c.value=p),class:"tabs",onTabChange:C},{default:e.withCtx(()=>[e.createVNode(b,{label:"供应商列表",name:""}),e.createVNode(b,{label:"待审核",name:"UNDER_REVIEW"}),e.createVNode(b,{label:"已拒绝",name:"REJECT"})]),_:1},8,["modelValue"]),e.createVNode(ce,{ref_key:"listPartRef",ref:t,"current-tab-choose":c.value,"search-params":g.searchParams},null,8,["current-tab-choose","search-params"])])}}})});
