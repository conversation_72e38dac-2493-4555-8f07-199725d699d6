(function(e,n){typeof exports=="object"&&typeof module<"u"?module.exports=n(require("vue"),require("element-plus"),require("vue-router"),require("@/libs/validate"),require("@/apis/http"),require("@/components/slide-captcha/SliderCaptcha.vue")):typeof define=="function"&&define.amd?define(["vue","element-plus","vue-router","@/libs/validate","@/apis/http","@/components/slide-captcha/SliderCaptcha.vue"],n):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopStoreList=n(e.ShopStoreListContext.Vue,e.ShopStoreListContext.ElementPlus,e.ShopStoreListContext.VueRouter,e.ShopStoreListContext.LibsValidate,e.ShopStoreListContext.Request,e.ShopStoreListContext.SliderCaptcha))})(this,function(e,n,M,$,V,I){"use strict";var B=document.createElement("style");B.textContent=`.addsalesclerk__phone[data-v-b774117e]{display:flex;align-items:center;margin-bottom:15px}.addsalesclerk__phone--input[data-v-b774117e]{margin-left:35px}.addsalesclerk__code[data-v-b774117e]{display:flex;align-items:center}.addsalesclerk__code--title[data-v-b774117e]{width:72px;text-align:right}
`,document.head.appendChild(B);const O=e.defineComponent({__name:"ShopStoreList",setup(l){const a={storeList:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Q)),salesclerkList:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ce))},d=e.ref("storeList");return(p,i)=>{const m=e.resolveComponent("el-tab-pane"),_=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(_,{modelValue:d.value,"onUpdate:modelValue":i[0]||(i[0]=C=>d.value=C)},{default:e.withCtx(()=>[e.createVNode(m,{label:"门店列表",name:"storeList"}),e.createVNode(m,{label:"店员列表",name:"salesclerkList"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(a[d.value])))])}}}),L=l=>V.get({url:"addon-shop-store/store/list",params:l}),T=(l,a)=>V.del({url:`addon-shop-store/store/del/${l}/${a}`}),A=(l,a)=>V.put({url:`addon-shop-store/store/update/${l}`,data:a}),F=l=>V.post({url:"addon-shop-store/assistant/issue",data:l}),R=l=>V.get({url:"addon-shop-store/assistant/list",data:l}),P=(l,a)=>V.put({url:`addon-shop-store/assistant/set/store/${l}`,params:a}),U=l=>V.del({url:`addon-shop-store/assistant/del/${l}`}),q=l=>{const{captchaTrack:a,form:d,id:p,smsType:i}=l;return V.post({url:`gruul-mall-uaa/uaa/auth/captcha/sms/${i}`,data:{captchaTrack:a,form:d,id:p}})},y=e.computed(()=>l=>{var a,d;return console.log((a=window==null?void 0:window.permissionList)==null?void 0:a.includes(l),"是否包含"),(d=window==null?void 0:window.permissionList)==null?void 0:d.includes(l)}),j={style:{display:"flex","justify-content":"space-between","margin-bottom":"13px"}},z={class:"ellipsis"},H={class:"ellipsis"},G=["onClick"],X=["onClick"],J=["onClick"],K=["onClick"],Q=Object.freeze(Object.defineProperty({__proto__:null,default:e.defineComponent({__name:"storeList",setup(l){const a=M.useRouter(),d=e.ref([]),p=e.reactive([{value:"",label:"全部"},{value:"NORMAL",label:"正常"},{value:"SHOP_FORBIDDEN",label:"店铺禁用"},{value:"PLATFORM_FORBIDDEN",label:"平台禁用"}]),i=e.ref("");m();async function m(){const{code:c,data:r}=await L({size:5,status:i.value});c===200&&r?d.value=r.records:n.ElMessage.error("获取列表失败")}const _=(c,r,h)=>{a.push({path:"/shop/store/AddStore",query:{shopId:c,id:r,lookType:h}})},C=(c,r)=>{n.ElMessageBox.confirm("确定删除该门店吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:h,msg:x}=await T(c,r);h===200?(n.ElMessage.success("删除成功"),m()):n.ElMessage.error(x||"删除失败")})},k=async(c,r,h)=>{h=h==="NORMAL"?"SHOP_FORBIDDEN":"NORMAL";const{code:x,msg:E}=await A(h,[{shopId:c,ids:[r]}]);x===200?(n.ElMessage.success("修改状态成功"),m()):n.ElMessage.error(E||"修改状态失败")};return(c,r)=>{const h=e.resolveComponent("el-button"),x=e.resolveComponent("el-option"),E=e.resolveComponent("el-select"),N=e.resolveComponent("el-table-column"),S=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",j,[e.createElementVNode("div",null,[e.unref(y)("shop:store:add")?(e.openBlock(),e.createBlock(h,{key:0,round:"",type:"primary",onClick:r[0]||(r[0]=o=>e.unref(a).push("/shop/store/AddStore"))},{default:e.withCtx(()=>r[2]||(r[2]=[e.createTextVNode("添加门店")])),_:1})):e.createCommentVNode("",!0)]),e.createVNode(E,{modelValue:i.value,"onUpdate:modelValue":r[1]||(r[1]=o=>i.value=o),class:"m-2",placeholder:"全部状态",size:"small",onChange:m},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(p,o=>(e.openBlock(),e.createBlock(x,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),e.createVNode(S,{data:d.value,"header-cell-style":{"background-color":"#F6F8FA","font-weight":"bold",color:"#515151"},"empty-text":"暂无数据~",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(N,{label:"门店名称"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",z,e.toDisplayString(o.storeName),1)]),_:1}),e.createVNode(N,{label:"地址",width:"270px"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",H,e.toDisplayString(o.detailedAddress),1)]),_:1}),e.createVNode(N,{label:"负责人手机号"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",null,e.toDisplayString(o.functionaryPhone),1)]),_:1}),e.createVNode(N,{label:"负责人姓名"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",null,e.toDisplayString(o.functionaryName),1)]),_:1}),e.createVNode(N,{label:"状态"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",null,e.toDisplayString(o.status==="NORMAL"?"正常":o.status==="SHOP_FORBIDDEN"?"店铺禁用":"平台禁用"),1)]),_:1}),e.createVNode(N,{align:"center",label:"操作",width:"200px"},{default:e.withCtx(({row:o})=>[e.unref(y)("shop:store:detail")?(e.openBlock(),e.createElementBlock("span",{key:0,style:{color:"#2e99f3",cursor:"pointer",margin:"0 10px"},onClick:g=>_(o.shopId,o.id,"OnlyLook")},"查看",8,G)):e.createCommentVNode("",!0),o.status==="NORMAL"&&e.unref(y)("shop:store:edit")?(e.openBlock(),e.createElementBlock("span",{key:1,style:{color:"#2e99f3",cursor:"pointer"},onClick:g=>_(o.shopId,o.id,"Edite")},"编辑",8,X)):e.createCommentVNode("",!0),o.status!=="PLATFORM_FORBIDDEN"&&e.unref(y)("shop:store:status:change")?(e.openBlock(),e.createElementBlock("span",{key:2,style:{color:"#2e99f3",cursor:"pointer",margin:"0 10px"},onClick:g=>k(o.shopId,o.id,o.status)},e.toDisplayString(o.status==="SHOP_FORBIDDEN"?"开启":"关闭"),9,J)):e.createCommentVNode("",!0),e.unref(y)("shop:store:delete")?(e.openBlock(),e.createElementBlock("span",{key:3,style:{color:"#2e99f3",cursor:"pointer"},onClick:g=>C(o.shopId,o.id)},"删除",8,K)):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"])])}}})},Symbol.toStringTag,{value:"Module"})),W={style:{display:"flex","justify-content":"space-between","margin-bottom":"13px"}},Y={class:"ellipsis"},Z={class:"ellipsis"},v=["onClick"],ee=["onClick"],te={class:"addsalesclerk__phone"},oe={class:"addsalesclerk__phone--input"},se={class:"addsalesclerk__code"},le={class:"dialog-footer"},ne={class:"addsalesclerk__phone"},ae={class:"addsalesclerk__phone--input"},re={class:"addsalesclerk__code"},de={class:"dialog-footer"},ie=e.defineComponent({__name:"salesclerkList",setup(l){const a=e.ref([]),d=e.ref([]),p=e.ref(!1),i=e.ref(!1),m=e.ref(!1),_=e.ref(120),C=e.ref(null),k=e.ref({assistantPhone:"",assistantPhoneCode:""}),c=e.ref({shopAssistantId:"",storeId:""}),r=e.ref("");e.onMounted(()=>{g(),pe()});const h=()=>{n.ElMessage.success("验证码已发送"),_.value-=1,m.value=!1,C.value=setInterval(()=>{if(_.value-=1,_.value<=0){_.value=120,C.value&&(clearInterval(C.value),C.value=null);return}},999)},x=()=>{if(!k.value.assistantPhone)return n.ElMessage.error("请填写手机号");if(!$.REGEX_MOBILE(k.value.assistantPhone))return n.ElMessage.error("请填写正确的手机号");m.value=!0},E=async()=>{const{code:f,msg:t}=await F(k.value);f===200?(n.ElMessage.success("添加成功"),g(),k.value={assistantPhone:"",assistantPhoneCode:""}):n.ElMessage.error(t||"添加失败"),clearInterval(C.value),C.value=null,_.value=120,p.value=!1},N=(f,t,u)=>{r.value=f,c.value.shopAssistantId=t,c.value.storeId=u,i.value=!0},S=async()=>{const{code:f,msg:t}=await P(c.value.shopAssistantId,{storeId:c.value.storeId});f===200?(i.value=!1,n.ElMessage.success("添加成功"),g()):n.ElMessage.error(t||"添加失败")},o=async f=>{n.ElMessageBox.confirm("确定删除该店员吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:t,msg:u}=await U(f);t===200?(n.ElMessage.success("删除成功"),g()):n.ElMessage.error(u||"删除失败")})};async function g(){const{code:f,data:t}=await R();f===200&&t?a.value=t:n.ElMessage.error("获取列表失败"),p.value=!1}async function pe(){const{code:f,data:t}=await L({size:5});f===200&&t?d.value=t.records:n.ElMessage.error("获取列表失败")}return(f,t)=>{const u=e.resolveComponent("el-button"),b=e.resolveComponent("el-table-column"),me=e.resolveComponent("el-table"),w=e.resolveComponent("el-input"),D=e.resolveComponent("el-dialog"),_e=e.resolveComponent("el-option"),fe=e.resolveComponent("el-select");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",W,[e.unref(y)("shop:assistant:add")?(e.openBlock(),e.createBlock(u,{key:0,round:"",type:"primary",onClick:t[0]||(t[0]=s=>p.value=!0)},{default:e.withCtx(()=>t[9]||(t[9]=[e.createTextVNode(" 添加店员")])),_:1})):e.createCommentVNode("",!0),t[10]||(t[10]=e.createElementVNode("div",{style:{color:"#8f8f95"}},"店员手机号用于门店自提订单（门店移动端）的核销",-1))]),e.createVNode(me,{data:a.value,"header-cell-style":{"background-color":"#F6F8FA","font-weight":"bold",color:"#515151"},"empty-text":"暂无数据~",style:{width:"100%",height:"calc(100vh - 210px)"}},{default:e.withCtx(()=>[e.createVNode(b,{align:"center",label:"店员手机号"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",Y,e.toDisplayString(s.assistantPhone),1)]),_:1}),e.createVNode(b,{align:"center",label:"所属门店"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",Z,e.toDisplayString(s.storeName?s.storeName:"尚未关联门店"),1)]),_:1}),e.createVNode(b,{align:"center",label:"操作"},{default:e.withCtx(({row:s})=>[e.unref(y)("shop:assistant:edit")?(e.openBlock(),e.createElementBlock("span",{key:0,style:{color:"#2e99f3",cursor:"pointer","margin-right":"20px"},onClick:ue=>N(s.assistantPhone,s.id,s.storeId)},"编辑",8,v)):e.createCommentVNode("",!0),e.unref(y)("shop:assistant:delete")?(e.openBlock(),e.createElementBlock("span",{key:1,style:{color:"#2e99f3",cursor:"pointer"},onClick:ue=>o(s.id)},"删除",8,ee)):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(D,{modelValue:p.value,"onUpdate:modelValue":t[4]||(t[4]=s=>p.value=s),center:"",title:"店员",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",le,[e.createVNode(u,{round:"",onClick:t[3]||(t[3]=s=>p.value=!1)},{default:e.withCtx(()=>t[14]||(t[14]=[e.createTextVNode("取消")])),_:1}),e.createVNode(u,{round:"",type:"primary",onClick:E},{default:e.withCtx(()=>t[15]||(t[15]=[e.createTextVNode(" 保存 ")])),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("div",te,[t[12]||(t[12]=e.createTextVNode(" 店员手机号 ")),e.createElementVNode("div",oe,[e.createVNode(w,{modelValue:k.value.assistantPhone,"onUpdate:modelValue":t[1]||(t[1]=s=>k.value.assistantPhone=s),maxlength:"11",placeholder:"请输入电话号码",style:{width:"200px"}},null,8,["modelValue"]),_.value===120?(e.openBlock(),e.createBlock(u,{key:0,plain:"",onClick:x},{default:e.withCtx(()=>t[11]||(t[11]=[e.createTextVNode("获取验证码")])),_:1})):(e.openBlock(),e.createBlock(u,{key:1,disabled:"",link:""},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(_.value),1)]),_:1}))])]),e.createElementVNode("div",se,[t[13]||(t[13]=e.createElementVNode("div",{class:"addsalesclerk__code--title"},"验证码",-1)),e.createVNode(w,{modelValue:k.value.assistantPhoneCode,"onUpdate:modelValue":t[2]||(t[2]=s=>k.value.assistantPhoneCode=s),maxlength:"6",placeholder:"请输入验证码",style:{width:"200px","margin-left":"35px"}},null,8,["modelValue"])])]),_:1},8,["modelValue"]),e.createVNode(D,{modelValue:i.value,"onUpdate:modelValue":t[7]||(t[7]=s=>i.value=s),center:"",title:"所属门店",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",de,[e.createVNode(u,{round:"",onClick:t[6]||(t[6]=s=>i.value=!1)},{default:e.withCtx(()=>t[18]||(t[18]=[e.createTextVNode("取消")])),_:1}),e.createVNode(u,{round:"",type:"primary",onClick:S},{default:e.withCtx(()=>t[19]||(t[19]=[e.createTextVNode(" 保存 ")])),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("div",ne,[t[16]||(t[16]=e.createTextVNode(" 店员手机号 ")),e.createElementVNode("div",ae,e.toDisplayString(r.value),1)]),e.createElementVNode("div",re,[t[17]||(t[17]=e.createElementVNode("div",{class:"addsalesclerk__code--title"},"所属门店",-1)),e.createVNode(fe,{modelValue:c.value.storeId,"onUpdate:modelValue":t[5]||(t[5]=s=>c.value.storeId=s),placeholder:"选择门店",style:{"margin-left":"35px"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.value,s=>(e.openBlock(),e.createBlock(_e,{key:s.id,label:s.storeName,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"]),e.createVNode(I,{modelValue:m.value,"onUpdate:modelValue":t[8]||(t[8]=s=>m.value=s),"do-submit":e.unref(q),"get-form":()=>k.value.assistantPhone,scale:1,smsType:"SHOP_STORE_FOUND",onSuccess:h},null,8,["modelValue","do-submit","get-form"])],64)}}}),he="",ce=Object.freeze(Object.defineProperty({__proto__:null,default:((l,a)=>{const d=l.__vccOpts||l;for(const[p,i]of a)d[p]=i;return d})(ie,[["__scopeId","data-v-b774117e"]])},Symbol.toStringTag,{value:"Module"}));return O});
