(function(e,w){typeof exports=="object"&&typeof module<"u"?module.exports=w(require("vue"),require("vue-router"),require("@/utils/date"),require("decimal.js"),require("@/store/modules/shopInfo"),require("element-plus"),require("@vueuse/core"),require("@/composables/useConvert"),require("@/apis/http"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/utils/date","decimal.js","@/store/modules/shopInfo","element-plus","@vueuse/core","@/composables/useConvert","@/apis/http","@/components/q-choose-goods-popup/q-choose-goods-popup.vue"],w):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopSeckillInfo=w(e.ShopSeckillInfoContext.Vue,e.ShopSeckillInfoContext.VueRouter,e.ShopSeckillInfoContext.DateUtil,e.ShopSeckillInfoContext.Decimal,e.ShopSeckillInfoContext.ShopInfoStore,e.ShopSeckillInfoContext.ElementPlus,e.ShopSeckillInfoContext.VueUse,e.ShopSeckillInfoContext.UseConvert,e.ShopSeckillInfoContext.Request,e.ShopSeckillInfoContext.QChooseGoodsPopup))})(this,function(e,w,O,Ce,R,h,W,z,H,Q){"use strict";var B=document.createElement("style");B.textContent=`@charset "UTF-8";.title[data-v-b51a8d26]{font-size:14px;color:#323233;font-weight:700;margin-bottom:20px}.msg[data-v-b51a8d26]{font-size:12px;color:#c4c4c4}.nav-button[data-v-b51a8d26]{width:1010px;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin:0 auto 0 -55px}.goods-list[data-v-b51a8d26]{width:100%;height:300px;overflow-x:scroll}.goods-list__info[data-v-b51a8d26]{width:100%;display:flex}.goods-list__goods-list__info-name[data-v-b51a8d26]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-b51a8d26]{width:260px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-b51a8d26]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-b51a8d26]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.table-msg[data-v-b51a8d26]{font-size:12px;color:#838383}
`,document.head.appendChild(B);const{divTenThousand:J,mulTenThousand:A}=z();function X(a){const l=new Map,d=[];return a.forEach(p=>{var G;const{productPic:k,productWidePic:T,productName:N,productId:s}=p,{skuName:f,skuPrice:V,skuId:o,seckillPrice:g,seckillStock:x,seckillLimit:I,actualPaidPrice:_,skuStock:v,stockType:P}=p.sku,Y={productName:N,productId:s,productPic:k,productWidePic:T,secKillProductSkus:[{skuName:(f==null?void 0:f.join(""))||"",seckillPrice:A(g).toNumber(),skuId:o,stockType:P,skuStock:v,actualPaidPrice:V,seckillStock:x,seckillLimit:I}]};l.get(s)?d.find(U=>U.productId===s)?(G=d.find(U=>U.productId===s))==null||G.secKillProductSkus.push({skuName:(f==null?void 0:f.join(""))||"",seckillPrice:A(g).toNumber(),skuId:o,skuStock:v,actualPaidPrice:V,seckillStock:x,stockType:P,seckillLimit:I}):h.ElMessage.info("商品不存在"):(l.set(s,s),d.push(Y))}),d}function Z(a,l){const d=[];return a.forEach(p=>{const{productId:k,productName:T,productPic:N,productWidePic:s}=p;p.secKillProductSkus.forEach(f=>{const{actualPaidPrice:V,seckillPrice:o,seckillStock:g,skuId:x,skuName:I,skuStock:_,stockType:v,limitType:P}=f,Y={productId:k,productName:T,productPic:N,productWidePic:s,shopId:l,sku:{limitType:P,productId:k,stockType:v,seckillLimit:"",seckillPrice:J(o).toNumber(),seckillStock:Number(g),skuId:x,skuName:(I==null?void 0:I.split(" "))||[],skuPrice:V,actualPaidPrice:V,skuStock:_}};d.push(Y)})}),d}let q=[],$=0;const j=a=>{q=[],$=0;for(let l=0;l<a.length;l++)l===0?(q.push(1),$=0):a[l].productId===a[l-1].productId?(q[$]+=1,q.push(0)):(q.push(1),$=l)},ee=({row:a,column:l,rowIndex:d,columnIndex:p})=>{if(p===0){const k=q[d],T=k>0?1:0;return{rowspan:k,colspan:T}}},L="addon-seckill/seckillPromotion/",te=(a,l=!1)=>H.post({url:L+"edit",data:a,params:{update:l}}),oe=(a,l)=>H.get({url:L+`${a}/${l}`}),le=(a,l,d)=>H.get({url:L+`secKillProduct/${a}/${l}`,params:d}),F=a=>(e.pushScopeId("data-v-b51a8d26"),a=a(),e.popScopeId(),a),se={style:{padding:"0 40px"}},ie=F(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),ae=F(()=>e.createElementVNode("span",{class:"msg"},"活动名称不超过5个字",-1)),ne={style:{width:"240px"}},re={style:{width:"240px"}},de={class:"goods-list__info"},ce={style:{width:"60px",height:"60px"}},pe={class:"goods-list__goods-list__info-name"},ue={class:"goods-list__goods-list__info-name--name"},me={class:"table-msg"},fe={class:"table-msg"},he={class:"table-msg"},ke={class:"nav-button"},_e=e.defineComponent({__name:"ShopSeckillInfo",setup(a){const l=e.ref(!1),d=w.useRouter(),p=w.useRoute().query.secKillId,{divTenThousand:k}=z();new O().getYMDs();const T=e.ref(),N=e.reactive({maxPrice:"",minPrice:"",activity:{endTime:"",startTime:""},keyword:"",categoryFirstId:""}),s=e.ref([]),f=e.ref(!1),V=R.useShopInfoStore().shopInfo.id,o=e.ref({id:"",date:"",seckillId:"",time:"",shopId:"",secKillName:"",seckillStatus:"NOT_STARTED",startTime:"",endTime:"",orderClosingTime:15,deductionType:!1,applyTypes:[],secKillProducts:[]}),g=e.reactive({current:1,pages:1,size:10}),x=e.ref(),I=e.reactive({secKillName:[{required:!0,message:"请输入活动名称",trigger:"blur"}],orderClosingTime:[{required:!0,message:"请输入订单关闭时间",trigger:"blur"}],date:[{required:!1,message:"请选择活动时间",trigger:["blur","change"]}],startTime:[{required:!1,message:"请选择活动时段",trigger:["blur","change"]}],endTime:[{required:!1,message:"请选择活动时段",trigger:["blur","change"]}]}),_=e.computed(()=>!!w.useRoute().query.secKillId);e.onMounted(()=>{U()});async function v(n=!1){n?n&&g.current<g.pages&&(g.current++,s.value=s.value.concat(await P()),j(s.value)):(g.current=1,s.value=await P(),j(s.value))}async function P(){const{code:n,data:i,msg:u}=await le(V,p,g);if(n!==200)return h.ElMessage.error(u||"获取秒杀商品信息失败"),[];g.pages=i.pages;const c=i.records;return Z(c,o.value.shopId)}const Y=async n=>{try{if(_.value){d.back();return}if(!n||(await n.validate(),!G()))return;l.value=!0;const{date:i,time:u,endTime:c,secKillName:m,startTime:S,orderClosingTime:C,deductionType:E,applyTypes:y,secKillProducts:M,seckillStatus:D}=o.value,K={shopId:s.value[0].shopId,startTime:`${S}`,secKillName:m,endTime:`${c}`,orderClosingTime:C,shopName:R.useShopInfoStore().shopInfo.name,deductionType:E,applyTypes:y,secKillProducts:X(s.value),seckillStatus:D},b=!!(p&&!_.value);b&&(K.seckillId=p);const{code:t,msg:r}=await te(K,b);if(r!=null&&r.includes("需要是一个将来的时间")){h.ElMessage.error("活动时间段需要是一个将来的时间"),l.value=!1;return}if(t!==200){h.ElMessage.error(r||`${b?"编辑":"保存"}失败`),l.value=!1;return}l.value=!1,h.ElMessage.success(`${b?"编辑":"保存"}成功`),xe(),d.push("/marketingApp/secondsKill")}catch{l.value=!1}};function G(){return o.value.startTime&&o.value.startTime,o.value.orderClosingTime?s.value.length?s.value.every(n=>n.sku.seckillPrice)?s.value.every(n=>n.sku.seckillStock)?!0:(h.ElMessage.info("请输入秒杀库存"),!1):(h.ElMessage.info("请输入秒杀价"),!1):(h.ElMessage.info("请选择适用商品"),!1):(h.ElMessage.info("订单关闭时间3-360分钟"),!1)}async function U(){if(p){const{code:n,data:i}=await oe(V,p);if(n!==200){h.ElMessage.error("获取秒杀信息失败");return}const{deductionType:u,endTime:c,id:m,orderClosingTime:S,secKillName:C,startTime:E,applyTypes:y,shopId:M,seckillStatus:D}=i;o.value.deductionType=u,o.value.id=m,o.value.orderClosingTime=S,o.value.secKillName=C,o.value.startTime=E,o.value.endTime=c,o.value.applyTypes=y||[],o.value.shopId=M,o.value.seckillStatus=D,v()}}W.useEventListener(x,"scroll",n=>{x.value&&p&&x.value.scrollTop+x.value.clientHeight+1>=x.value.scrollHeight&&v(!0)});const ge=n=>{var u;const i=[];for(let c=0;c<n.tempGoods.length;c++){const m=n.tempGoods[c],{productId:S,productName:C,pic:E,widePic:y,shopId:M,stocks:D,salePrices:K,stockTypes:b,specs:t}=m;if((u=m==null?void 0:m.skuIds)!=null&&u.length)for(let r=0;r<m.skuIds.length;r++){const Se=m.skuIds[r],be=K[r],Ne=D[r],Ve=b[r],Ie=t[r].split(" ");i.push({sku:{seckillPrice:1,seckillStock:1,seckillLimit:0,actualPaidPrice:0,productId:S,skuId:Se,skuPrice:be,skuStock:Ne,stockType:Ve,skuName:Ie},productId:S,productName:C,productPic:E,productWidePic:y,shopId:M})}}s.value=i,j(s.value),f.value=!1},xe=()=>{T.value&&(T.value.resetFields(),o.value={date:"",seckillId:"",time:"",shopId:"",secKillName:"",seckillStatus:"NOT_STARTED",startTime:"",endTime:"",orderClosingTime:3,deductionType:!1,applyTypes:[],secKillProducts:[]},l.value=!1,s.value=[])},ye=async n=>{if(await h.ElMessageBox.confirm("确定移除该商品?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})){const u=s.value.findIndex(c=>c.sku.skuId===n);u!==-1&&(s.value.splice(u,1),j(s.value))}},Te=()=>{if(o.value.endTime&&o.value.startTime){const{date:n,startTime:i,endTime:u}=o.value;N.activity.startTime=`${i}`,N.activity.endTime=`${u}`,f.value=!0;return}return h.ElMessage.warning("请选择活动时间")};return(n,i)=>{const u=e.resolveComponent("el-input"),c=e.resolveComponent("el-form-item"),m=e.resolveComponent("el-date-picker"),S=e.resolveComponent("el-row"),C=e.resolveComponent("el-link"),E=e.resolveComponent("el-image"),y=e.resolveComponent("el-table-column"),M=e.resolveComponent("el-input-number"),D=e.resolveComponent("el-table"),K=e.resolveComponent("el-form"),b=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",se,[ie,e.createVNode(K,{ref_key:"ruleFormRef",ref:T,model:o.value,rules:I,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(c,{label:"活动名称",prop:"secKillName"},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:o.value.secKillName,"onUpdate:modelValue":i[0]||(i[0]=t=>o.value.secKillName=t),modelModifiers:{trim:!0},disabled:_.value,maxlength:5,minlength:3,placeholder:"请输入活动名称",style:{width:"60%","margin-right":"15px"}},null,8,["modelValue","disabled"]),ae]),_:1}),e.createVNode(c,{label:"活动时间",required:""},{default:e.withCtx(()=>[e.createVNode(S,{style:{display:"flex",width:"100%"}},{default:e.withCtx(()=>[e.createElementVNode("div",ne,[e.createVNode(c,{"label-width":"0px",prop:"startTime"},{default:e.withCtx(()=>[e.createVNode(m,{type:"datetime",modelValue:o.value.startTime,"onUpdate:modelValue":i[1]||(i[1]=t=>o.value.startTime=t),disabled:_.value,format:"YYYY-MM-DD HH:mm:ss",placeholder:"开始时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})]),e.createElementVNode("div",re,[e.createVNode(c,{"label-width":"0px",prop:"endTime"},{default:e.withCtx(()=>[e.createVNode(m,{type:"datetime",modelValue:o.value.endTime,"onUpdate:modelValue":i[2]||(i[2]=t=>o.value.endTime=t),disabled:_.value,format:"YYYY-MM-DD HH:mm:ss",placeholder:"结束时间","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})])]),_:1})]),_:1}),e.createVNode(c,{label:"适用商品",required:""},{default:e.withCtx(()=>[e.createVNode(S,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(C,{disabled:_.value,underline:!1,type:"primary",onClick:Te},{default:e.withCtx(()=>[e.createTextVNode("选择商品 ")]),_:1},8,["disabled"])]),_:1}),s.value.length?(e.openBlock(),e.createElementBlock("div",{key:0,ref_key:"goodsDataRef",ref:x,class:"goods-list"},[e.createVNode(D,{"cell-style":{height:"80px"},data:s.value,"header-cell-style":{fontSize:"14px",color:"#606266",background:"#f2f2f2",height:"54px",fontWeight:"normal"},"span-method":e.unref(ee),border:""},{default:e.withCtx(()=>[e.createVNode(y,{label:"商品信息"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",de,[e.createElementVNode("div",ce,[e.createVNode(E,{"preview-src-list":[t.productPic],"preview-teleported":!0,src:t.productPic,fit:"cover",style:{width:"60px",height:"60px"}},null,8,["preview-src-list","src"])]),e.createElementVNode("div",pe,[e.createElementVNode("div",ue,e.toDisplayString(t.productName),1)])])]),_:1}),e.createVNode(y,{align:"center",label:"规格",width:"100px"},{default:e.withCtx(({row:t})=>{var r;return[e.createElementVNode("div",me,e.toDisplayString(((r=t.sku.skuName)==null?void 0:r.join(""))||"-"),1)]}),_:1}),e.createVNode(y,{align:"center",label:"秒杀价（元）",width:"120px"},{default:e.withCtx(({row:t})=>[e.createVNode(M,{modelValue:t.sku.seckillPrice,"onUpdate:modelValue":r=>t.sku.seckillPrice=r,controls:!1,disabled:_.value,max:e.unref(k)(t.sku.skuPrice).toNumber(),min:.01,precision:2,style:{width:"80%"}},null,8,["modelValue","onUpdate:modelValue","disabled","max"]),e.createElementVNode("div",fe,"实售价：￥"+e.toDisplayString(e.unref(k)(t.sku.skuPrice).toNumber()),1)]),_:1}),e.createVNode(y,{align:"center",label:"秒杀库存",width:"120px"},{default:e.withCtx(({row:t})=>[e.createVNode(M,{modelValue:t.sku.seckillStock,"onUpdate:modelValue":r=>t.sku.seckillStock=r,controls:!1,disabled:_.value,max:t.sku.stockType==="UNLIMITED"?1/0:parseInt(t.sku.skuStock),min:0,precision:0,style:{width:"80%"}},null,8,["modelValue","onUpdate:modelValue","disabled","max"]),e.createElementVNode("div",he,"库存："+e.toDisplayString(t.sku.stockType==="UNLIMITED"?"不限库存":t.sku.skuStock),1)]),_:1}),e.createVNode(y,{label:"操作",width:"80px"},{default:e.withCtx(({row:t})=>[e.createVNode(C,{disabled:_.value,underline:!1,type:"primary",onClick:r=>ye(t.sku.skuId)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data","span-method"])],512)):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["model","rules"]),e.createElementVNode("div",ke,[e.createVNode(b,{plain:"",round:"",onClick:i[3]||(i[3]=t=>n.$router.back())},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(b,{loading:l.value,round:"",type:"primary",onClick:i[4]||(i[4]=t=>Y(T.value))},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1},8,["loading"])])]),e.createVNode(Q,{modelValue:f.value,"onUpdate:modelValue":i[5]||(i[5]=t=>f.value=t),"search-param":N,"onUpdate:searchParam":i[6]||(i[6]=t=>N=t),"point-goods-list":s.value,onOnConfirm:ge},null,8,["modelValue","search-param","point-goods-list"])])}}}),we="";return((a,l)=>{const d=a.__vccOpts||a;for(const[p,k]of l)d[p]=k;return d})(_e,[["__scopeId","data-v-b51a8d26"]])});
