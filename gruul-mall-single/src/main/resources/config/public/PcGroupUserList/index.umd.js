(function(e,u){typeof exports=="object"&&typeof module<"u"?module.exports=u(require("vue"),require("@/composables/useConvert"),require("@/utils/http"),require("@/apis/http"),require("decimal.js"),require("vue-router"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@/composables/useConvert","@/utils/http","@/apis/http","decimal.js","vue-router","element-plus"],u):(e=typeof globalThis<"u"?globalThis:e||self,e.PcGroupUserList=u(e.PcGroupUserListContext.Vue,e.PcGroupUserListContext.UseConvert,e.PcGroupUserListContext.UtilsHttp,e.PcGroupUserListContext.Request,e.PcGroupUserListContext.Decimal,e.PcGroupUserListContext.VueRouter,e.PcGroupUserListContext.ElementPlus))})(this,function(e,u,q,j,lt,H,K){"use strict";var at=Object.defineProperty;var nt=(e,u,q)=>u in e?at(e,u,{enumerable:!0,configurable:!0,writable:!0,value:q}):e[u]=q;var T=(e,u,q)=>(nt(e,typeof u!="symbol"?u+"":u,q),q);var W=document.createElement("style");W.textContent=`.active[data-v-08cf2ab5]{border:2px solid red}.disable[data-v-08cf2ab5]{border:2px dashed #efefef}[data-v-3e12721e] .el-card__body{padding:0}[data-v-3e12721e] .m-4{margin:0}.yhq-p[data-v-3e12721e]{width:500px;height:20px;overflow:hidden;position:absolute;z-index:4;left:60px}.yhq-p[data-v-3e12721e]:hover{height:auto}.yhq-p span[data-v-3e12721e]{margin:2px 0}li[data-v-3e12721e]{list-style:none}em[data-v-3e12721e],i[data-v-3e12721e]{font-style:normal}.fl[data-v-3e12721e]{float:left}.flr[data-v-3e12721e]{float:right}.tank[data-v-3e12721e]{display:inline!important}.all[data-v-3e12721e]{width:100%;height:100%;background-color:#10101080;position:fixed;top:0;left:0;z-index:999999;display:none}.box-card[data-v-3e12721e]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);z-index:9999999;width:833px;border-radius:8px;background-color:#fff}.box-card .title[data-v-3e12721e]{height:44px;border-bottom:1px solid #bbb;padding:12px 16px 12px 24px;display:flex;font-size:14px;color:#333}.box-card .title .xianshi[data-v-3e12721e]{display:inline!important}.box-card .title h4[data-v-3e12721e]{flex:1;margin:auto;display:none}.box-card .title .h4-copy[data-v-3e12721e]{flex:1;margin:auto;font-weight:400;color:#000;display:none}.box-card .title .h4-copy span[data-v-3e12721e]{display:inline-block;width:24px;height:24px;border-radius:50%;overflow:hidden}.box-card .title .h4-copy span img[data-v-3e12721e]{width:100%;height:100%}.box-card .title .h4-copy>i[data-v-3e12721e]{display:inline-block;width:80px;height:20px;margin:0 30px 0 10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.box-card .title .h4-copy em[data-v-3e12721e]{display:inline-block;width:110px;height:20px;vertical-align:top;margin-top:4px;font-weight:700}.box-card .title .h4-copy em i[data-v-3e12721e]{margin:0 5px;color:#e31436}.box-card .title li[data-v-3e12721e]{width:245px;list-style:none;margin:0 auto;position:relative;color:#333}.box-card .title li i[data-v-3e12721e]{color:#ee1e32}.box-card .title li>span[data-v-3e12721e]{display:inline-block;width:18px;height:18px;border-radius:50px;border:2px solid #fff;overflow:hidden}.box-card .title li>span img[data-v-3e12721e]{width:100%;height:100%}.box-card .title li>span[data-v-3e12721e]:nth-of-type(1){position:absolute;left:0;z-index:999}.box-card .title li>span[data-v-3e12721e]:nth-of-type(2){position:absolute;top:0;left:12px;z-index:199}.box-card .title li>span[data-v-3e12721e]:nth-of-type(3){position:absolute;top:0;left:24px;z-index:100}.box-card .title li>span[data-v-3e12721e]:nth-of-type(4){position:absolute;top:0;left:34px;z-index:99}.box-card .title li i[data-v-3e12721e]{margin-left:9 px;display:inline-block}.box-card .title>i[data-v-3e12721e]{display:inline-block;width:24px;height:24px;overflow:hidden}.box-card .title-copy[data-v-3e12721e]{height:44px;border-bottom:1px solid #bbb;padding:12px 16px 12px 24px;display:flex;font-size:14px}.box-card .title-copy h4[data-v-3e12721e]{flex:1;margin:auto;font-weight:400;color:#000}.box-card .title-copy h4 span[data-v-3e12721e]{display:inline-block;width:24px;height:24px;border-radius:50%;overflow:hidden}.box-card .title-copy h4 span img[data-v-3e12721e]{width:100%;height:100%}.box-card .title-copy h4>i[data-v-3e12721e]{display:inline-block;width:80px;height:20px;margin:0 30px 0 10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.box-card .title-copy h4 em[data-v-3e12721e]{line-height:39px;display:inline-block;width:90px;height:20px}.box-card .title-copy h4 em>i[data-v-3e12721e]{font-weight:700;margin:0 5px;color:#e31436}.box-card .shop[data-v-3e12721e]{margin:0 0 0 41px;height:160px}.box-card .shop .img[data-v-3e12721e]{width:149px;height:149px;overflow:hidden;border:1px solid #d5d5d5;margin-right:18px}.box-card .shop .img img[data-v-3e12721e]{width:100%;height:100%}.box-card .shop .info[data-v-3e12721e]{text-align:left;max-width:70%}.box-card .shop .info li[data-v-3e12721e]{list-style:none;margin-bottom:24px}.box-card .shop .info li[data-v-3e12721e]:nth-of-type(1){font-weight:400;margin-top:5px;font-size:14px;color:#ee1e32}.box-card .shop .info li:nth-of-type(1) span[data-v-3e12721e]{font-weight:700;font-size:25px}.box-card .shop .info li:nth-of-type(1) em[data-v-3e12721e]{font-size:20px}.box-card .shop .info li:nth-of-type(2) span[data-v-3e12721e]{font-size:12px;margin-right:9px;display:inline-block;padding:2px 5px;background-color:#ee1e32;color:#fff}.box-card .shop .info li[data-v-3e12721e]:nth-of-type(3){font-size:12px}.box-card .shop .info li:nth-of-type(3) span[data-v-3e12721e]{margin-right:9px;display:inline-block;padding:2px 5px;border:1px solid #ee1e32;color:#ee1e32}.box-card .shop .info li:nth-of-type(3) i[data-v-3e12721e]{color:#333;margin-right:23px}.box-card .shop .info li[data-v-3e12721e]:last-child{margin:0}.box-card .shop .info h4[data-v-3e12721e]{width:43px;height:20px;text-align:justify;text-align-last:justify;text-justify:inter-word;font-size:12px;color:#999;font-weight:400;display:inline-block;margin-right:27px}.box-card .shop .info p[data-v-3e12721e]{display:inline-block}.box-card .specification li[data-v-3e12721e]{text-align:left;font-size:12px}.box-card .specification li span[data-v-3e12721e]{display:inline-block;width:43px;height:20px;font-size:12px;color:#999;text-align:justify;text-align-last:justify;text-justify:inter-word;margin:0 23px 25px 52px}.box-card .specification li[data-v-3e12721e]:nth-of-type(1){margin-top:20px}.box-card .specification li:nth-of-type(1) i[data-v-3e12721e]{font-size:12px;color:#333;margin-left:9px}.box-card .specification li:nth-of-type(2) em[data-v-3e12721e]{display:inline-block;padding:8px 12px}.box-card .specification li:nth-of-type(3) em[data-v-3e12721e],.box-card .specification li:nth-of-type(4) em[data-v-3e12721e]{display:inline-block;padding:8px 12px}.box-card .specification li:nth-of-type(5)>i[data-v-3e12721e]{margin-left:12px;color:#333}.box-card .specification li:nth-of-type(5)>i>em[data-v-3e12721e]{margin:0;color:#ee1e32}.box-card .specification li:nth-of-type(5) em[data-v-3e12721e]{color:#999;margin-left:10px}.box-card .specification li:nth-of-type(6) i[data-v-3e12721e]{display:inline-block;font-weight:700;line-height:23px;vertical-align:top;margin-right:7px}.box-card .specification li:nth-of-type(6) em[data-v-3e12721e]{margin-right:20px}.box-card .specification li:nth-of-type(7) i[data-v-3e12721e]{margin-top:2px}.box-card .specification li:nth-of-type(7) em[data-v-3e12721e]{margin-left:7px;margin-right:20px}.box-card .specification li .xuxian[data-v-3e12721e]{border:1.5px dashed #efefef!important;color:#efefef!important}.box-card .specification li .xuxian em[data-v-3e12721e]{color:#efefef!important}.box-card .specification li .hongxian[data-v-3e12721e]{border:1.5px solid #e31436!important;position:relative}.box-card .specification li .hongxian i[data-v-3e12721e]{position:absolute;bottom:0;right:0;display:inline-block;width:0;height:0;border:10px solid #e31436;border-top:10px solid transparent;border-left:10px solid transparent;z-index:9999}.box-card .specification li .hongxian span[data-v-3e12721e]{display:inline-block;position:absolute;bottom:-34px;right:-55px;display:inline!important;z-index:9999}.box-card .specification li .every[data-v-3e12721e]{display:inline-block;border:1.5px solid #e0e0e0;margin-right:7px}.box-card .specification li .every .img[data-v-3e12721e]{width:38px;height:38px;overflow:hidden;margin:2px 3px}.box-card .specification li .every .img img[data-v-3e12721e]{width:100%;height:100%}.box-card .specification li .every em[data-v-3e12721e]{text-align:right;display:inline-block;width:37px;height:20px;margin:13px 15px 8px;color:#333}.box-card .specification li .every span[data-v-3e12721e]{display:none}.unity[data-v-089e4cdc]{color:#101010}.unity .left[data-v-089e4cdc]{overflow:hidden;float:left;width:333px;height:148px}.unity .left ul li[data-v-089e4cdc]{list-style:none;height:24px;line-height:24px;position:relative;margin-bottom:15px}.unity .left ul li>span[data-v-089e4cdc]{display:inline-block;width:18px;height:18px;border-radius:50px;border:2px solid #fff;overflow:hidden;transform:translateY(3px)}.unity .left ul li>span img[data-v-089e4cdc]{width:100%;height:100%;transform:translateY(-4px)}.unity .left ul li>span[data-v-089e4cdc]:nth-of-type(1){position:absolute;left:0;z-index:999}.unity .left ul li>span[data-v-089e4cdc]:nth-of-type(2){position:absolute;top:0;left:12px;z-index:199}.unity .left ul li>span[data-v-089e4cdc]:nth-of-type(3){position:absolute;top:0;left:24px;z-index:100}.unity .left ul li>span[data-v-089e4cdc]:nth-of-type(4){position:absolute;top:0;left:34px;z-index:99}.unity .left ul li h3[data-v-089e4cdc]{display:inline-block;font-size:14px}.unity .left ul li h3 span[data-v-089e4cdc]{color:#e31436}.unity .left ul li .btn[data-v-089e4cdc]{margin-right:30px;font-size:12px;text-align:center;border-radius:4px;color:#fff;float:right;width:70px;height:24px;background-color:#e31436;cursor:pointer}.unity .right[data-v-089e4cdc]{overflow:hidden;width:351px;height:148px;float:right}.unity .right ul li[data-v-089e4cdc]{height:24px;line-height:24px;font-size:14px;margin-bottom:15px}.unity .right ul li span[data-v-089e4cdc]{display:inline-block;height:24px;width:24px;overflow:hidden;border-radius:50%}.unity .right ul li span img[data-v-089e4cdc]{width:100%;height:100%}.unity .right ul li h3[data-v-089e4cdc]{height:20px;display:inline-block;transform:translateY(-4px);margin-left:10px;font-size:14px;width:66px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.unity .right ul li h4[data-v-089e4cdc]{display:inline-block;transform:translateY(-7px);margin-left:65px;margin-right:21px}.unity .right ul li h4 span[data-v-089e4cdc]{transform:translateY(7px);color:#e31436}.unity .right ul li .btn[data-v-089e4cdc]{font-size:12px;text-align:center;border-radius:4px;color:#fff;float:right;width:70px;height:24px;background-color:#e31436;cursor:pointer}.unity h1[data-v-089e4cdc]{height:21px;font-size:16px;margin-top:10px;margin-bottom:15px}
`,document.head.appendChild(W);const re=(n,o)=>j.get({url:`gruul-mall-goods/api/product/get/${n}`,headers:{"Shop-Id":o}}),X=(n,o)=>j.get({url:`addon-team/team/product/${n}/${o}`}),se=(n,o)=>j.get({url:`gruul-mall-storage/storage/shop/${o}/product/${n}`}),ce=n=>j.get({url:`gruul-mall-shop/shop/info/base/${n}`}),de=(n,o,t,i)=>j.get({url:`addon-team/team/product/${n}/${o}/orders?current=${t}&size=${i}`});class pe{constructor(o){T(this,"vertex",[]);T(this,"quantity",0);T(this,"vertexRange",[]);this.vertex=o,this.quantity=o.length,this.init()}init(){this.vertexRange=Array.from({length:this.quantity*this.quantity})}setVertexRange(o,t){const i=this.vertex.indexOf(o);t.forEach(l=>{const p=this.vertex.indexOf(l);this.vertexRange[i*this.quantity+p]=1})}getVertexColumn(o){const t=this.vertex.indexOf(o),i=[];return this.vertex.forEach((l,p)=>{i.push(this.vertexRange[t+this.quantity*p])}),i}getColumnTotal(o){const t=o.map(l=>this.getVertexColumn(l)),i=[];return this.vertex.forEach((l,p)=>{const E=t.map(_=>_[p]).reduce((_,v)=>(_+=v||0,_),0);i.push(E)}),i}getIntersection(o){return this.getColumnTotal(o).map((i,l)=>i>=o.length&&this.vertex[l]).filter(Boolean)}getUnion(o){return o=this.getColumnTotal(o),o.map((t,i)=>t&&this.vertex[i]).filter(Boolean)}}class Q extends pe{constructor(t,i){super(t.reduce((l,p)=>[...l,...p.list],[]));T(this,"commoditySpecs",[]);T(this,"normGroup",[]);this.commoditySpecs=t,this.normGroup=i,this.initMatrix(),this.initSimilarVertex()}initMatrix(){this.normGroup.forEach(t=>{this.applyCommodity(t.specs)})}initSimilarVertex(){const t=this.getUnion(this.vertex);this.commoditySpecs.forEach(i=>{const l=[];i.list.forEach(p=>{t.indexOf(p)>-1&&l.push(p)}),this.applyCommodity(l)})}querySpecsOptions(t){return t.some(Boolean)?t=this.getIntersection(t.filter(Boolean)):t=this.getUnion(this.vertex),t}applyCommodity(t){t.forEach(i=>{this.setVertexRange(i,t)})}}const he={flex:"","c-mt-13":""},me={"c-w-43":"","e-tj":"","c-mr-23":"","c-mt-11":""},fe={key:0,flex:"","items-center":"","flex-wrap":"","c-w-630":""},xe=["onClick"],ue=["src"],ge={"e-c-3":"","c-lh-42":""},ye={key:1,flex:"","items-center":"","flex-wrap":"","c-w-630":""},be=["onClick"],_e=e.defineComponent({__name:"good-spec",props:{skuGroup:{type:Object,default(){return{}}},currentChoose:{type:Array,default(){return[]}},imgBoolean:{type:Boolean,default:!0}},emits:["chooseSpec"],setup(n,{emit:o}){const t=n,i=e.shallowReactive({commoditySpecs:[],optionSpec:[],chooseSpec:[],normClass:new Q([],[])});let l={};e.watch(t.skuGroup,m=>{var d;if(m){const c=JSON.parse(JSON.stringify(m)),f=v(c.specGroups),a=w(c.skus),g=new Q(f,a);i.commoditySpecs=f,l=(d=i.commoditySpecs[0])==null?void 0:d.list.map(I=>{var C;return{name:I,img:(C=t.skuGroup.skus.find($=>$.specs[0]===I))==null?void 0:C.image}}),i.normClass=g,!t.currentChoose.length&&c.skus.length>0?i.chooseSpec=c.skus[0].specs:t.currentChoose.length&&c.skus.length>0?i.chooseSpec=JSON.parse(JSON.stringify(t.currentChoose)):i.chooseSpec=Array.from({length:f.length}),i.optionSpec=g.querySpecsOptions(i.chooseSpec)}},{immediate:!0});const p=(m,d,c)=>{const{commoditySpecs:f,chooseSpec:a,normClass:g,optionSpec:y}=e.toRefs(i);a.value[c]===d||!m||(a.value[c]=a.value[c]===d?"":d,a.value=a.value.slice(),y.value=g.value.querySpecsOptions(a.value.slice()),a.value.filter(Boolean).length===f.value.length&&o("chooseSpec",E()))};function E(){return e.toRaw(t.skuGroup.skus).filter(c=>!!_(c.specs,i.chooseSpec))}function _(m,d){if(m.length!==d.length)return!1;for(let c=0;c<m.length;c++)if(m[c]!==d[c])return!1;return!0}function v(m){return m.map(d=>({title:d.name,list:d.children.map(c=>c.name)}))}function w(m){return m.map(d=>({id:d.id,specs:d.specs}))}return(m,d)=>(e.openBlock(),e.createElementBlock("div",null,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(i).commoditySpecs,(c,f)=>(e.openBlock(),e.createElementBlock("div",{key:f,class:"norm"},[e.createElementVNode("div",he,[e.createElementVNode("div",me,e.toDisplayString(c.title),1),f===0?(e.openBlock(),e.createElementBlock("div",fe,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(l),(a,g)=>(e.openBlock(),e.createElementBlock("div",{key:g,"c-lh-42":"","b-2":"","c-bc-e0e0e0":"",flex:"","items-center":"","c-mr-7":"","c-mb-5":"","c-pr-5":"",class:e.normalizeClass({active:e.unref(i).chooseSpec.indexOf(a.name)!==-1,disable:e.unref(i).optionSpec.indexOf(a.name)<0}),onClick:y=>p(e.unref(i).optionSpec.indexOf(a.name)>-1,a.name,f)},[t.imgBoolean?(e.openBlock(),e.createElementBlock("img",{key:0,src:a.img,"c-w-38":"","c-h-38":"","c-mr-9":""},null,8,ue)):e.createCommentVNode("",!0),e.createElementVNode("div",ge,e.toDisplayString(a.name),1)],10,xe))),128))])):(e.openBlock(),e.createElementBlock("div",ye,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.list,(a,g)=>(e.openBlock(),e.createElementBlock("div",{key:g,"c-lh-34":"","b-2":"","c-bc-e0e0e0":"","c-mr-7":"","c-mt-5":"","e-c-3":"","c-pl-15":"","c-pr-15":"",class:e.normalizeClass({active:e.unref(i).chooseSpec.indexOf(a)>-1,disable:e.unref(i).optionSpec.indexOf(a)<=0}),onClick:y=>p(e.unref(i).optionSpec.indexOf(a)>-1,a,f)},e.toDisplayString(a),11,be))),128))]))])]))),128))]))}}),rt="",J=(n,o)=>{const t=n.__vccOpts||n;for(const[i,l]of o)t[i]=l;return t},ke=J(_e,[["__scopeId","data-v-08cf2ab5"]]);class we{constructor(o={}.VITE_LOCAL_STORAGE_KEY_PREFIX){T(this,"name");this.name=o,this.init()}init(){Object.keys(localStorage).forEach(o=>{o.startsWith(this.name)&&this.hasItem(o.replace(this.name,""))})}setItem(o,t,i){t===void 0&&(t=null);const l=this.name+o,p={value:t};i&&(p.time=i*1e3+new Date().getTime()),localStorage.setItem(l,JSON.stringify(p))}getItem(o){const t=localStorage.getItem(this.name+o);return this.hasItem(o)&&t?JSON.parse(t).value:null}removeItem(o){localStorage.removeItem(this.name+o)}clear(){Object.keys(localStorage).forEach(o=>{o.startsWith(this.name)&&localStorage.removeItem(o)})}getLength(){return Object.keys(localStorage).length}hasItem(o){const t=this.name+o,i=localStorage.getItem(t);if(i){const l=JSON.parse(i).time;return new Date().getTime()>l?(localStorage.removeItem(t),!1):!0}else return!1}}const L=n=>(e.pushScopeId("data-v-3e12721e"),n=n(),e.popScopeId(),n),Ne={class:"title"},Ve={key:0},Se={key:1},Ee={key:2,class:"xianshi"},ve={key:3,class:"h4-copy xianshi"},Ce=["src"],Be=[L(()=>e.createElementVNode("svg",{t:"1686128889407",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"9052",width:"24",height:"24"},[e.createElementVNode("path",{d:"M576 512l277.333 277.333-64 64L512 576 234.667 853.333l-64-64L448 512 170.667 234.667l64-64L512 448l277.333-277.333 64 64L576 512z",fill:"#101010","p-id":"9053"})],-1))],Le={class:"shop"},Ie={class:"img fl"},$e=["src"],ze={class:"info fl"},Ge=L(()=>e.createElementVNode("h4",null,"拼团价",-1)),Oe=L(()=>e.createElementVNode("li",null,[e.createElementVNode("div",{style:{position:"relative",display:"flex"}})],-1)),Pe={class:"specification"},Te=L(()=>e.createElementVNode("span",null,"送至",-1)),qe={class:"m-4",style:{display:"inline-block"}},Ue={style:{color:"#333"}},De={style:{padding:"0 0 20px 50px"}},Fe=L(()=>e.createElementVNode("span",null,"数量",-1)),je={key:0},Ae=L(()=>e.createElementVNode("span",null,null,-1)),Re={key:1},Me=L(()=>e.createElementVNode("span",null,null,-1)),Ye=e.defineComponent({__name:"PcGroupPopUp",props:{isShow:{type:Boolean,default:!1},skuList:{type:Object,default:()=>({})},group:{type:Number,default:()=>0},areaCode:{type:Array,default:()=>[]},freight:{type:String,default:()=>""},itemList:{type:Object,default:()=>({})},limitNum:{type:String,default:()=>""},pricesPrice:{type:String,default:()=>""},productId:{type:String,default:()=>""},shop:{type:String,default:()=>""},regionDatas:{type:Object,default:()=>({})}},emits:["closeFn","update:isShow","update:areaCode","priceValue"],setup(n,{emit:o}){const t=n,{divTenThousand:i}=u(),l=e.computed({get(){return t.isShow},set(s){o("update:isShow")}}),p=e.computed({get(){return t.areaCode},set(s){o("update:areaCode")}}),E=()=>{o("closeFn",!1)},_=s=>{o("priceValue",s)},v=e.ref(),w=e.ref("");let m=e.ref(),d=e.ref();const c=e.ref(),f=async()=>{var x,S,G,U;const s=await X(t.shop,t.productId);c.value=s.data.skus,w.value=s.data.activityId;const r=e.ref();r.value=t.pricesPrice,m.value=i(r.value).toFixed(2),d.value=(U=(G=(S=(x=s.data)==null?void 0:x.skus)==null?void 0:S[0])==null?void 0:G.stock)==null?void 0:U[0]},a=e.ref(),g=s=>{var r;if(s)return a.value=(r=c.value)==null?void 0:r.filter(x=>x.skuId===s),_(a.value),a.value},y=e.ref(),I=e.ref(),C=e.ref(""),$=e.ref(),ee=async()=>{var x,S;const s=await se(t.productId,t.shop);y.value=(x=s.data.skus)==null?void 0:x[0],I.value=s.data,g((S=s.data.skus)==null?void 0:S[0].id);const{data:r}=await re(t.productId,t.shop);C.value=r==null?void 0:r.pic,$.value=r},h=e.ref({shopId:"",shopLogo:"",shopName:"",newTips:"",status:""}),k=async()=>{const{code:s,data:r,msg:x}=await ce(t.shop);if(s!==200)return K.ElMessage.error(x||"未获取店铺信息");h.value.shopId=r.id,h.value.shopName=r.name,h.value.shopLogo=r.logo},N=e.ref(),b=s=>{var r;N.value=s[0],console.log(s[0]),g((r=s==null?void 0:s[0])==null?void 0:r.id)},V=()=>{var F,te,oe,ie,ae,ne,le;if(+((F=a.value)==null?void 0:F[0].stock)==0)return K.ElMessage("该规格商品库存不足");const s="TEAM",{id:r,image:x,price:S,salePrice:G,productId:U,secKillPrice:B,weight:O}=N.value||y.value,{name:P,id:R,freightTemplateId:M,pic:Y}=$.value,D={skuId:r,image:x,pic:Y,price:S,salePrice:G,productId:U,productName:P,id:R,freightTemplateId:M,weight:O};D.salePrice=+t.pricesPrice,(ie=(oe=(te=a.value)==null?void 0:te[0])==null?void 0:oe.prices)!=null&&ie[0]&&(D.salePrice=(le=(ne=(ae=a.value)==null?void 0:ae[0])==null?void 0:ne.prices)==null?void 0:le[0]),new we().setItem("commodityInfo",[Object.assign(h.value,w.value,{orderType:s},{products:[{...D,num:z.value}]})],60*60*2);const A=e.ref();t.itemList.totalNum?A.value=t.itemList.totalNum:A.value=t.itemList.item},z=e.ref(1);return e.onMounted(()=>{ee(),f(),k()}),(s,r)=>{const x=e.resolveComponent("el-cascader"),S=e.resolveComponent("el-input-number"),G=e.resolveComponent("el-button"),U=e.resolveComponent("el-dialog");return e.openBlock(),e.createBlock(U,{modelValue:l.value,"onUpdate:modelValue":r[2]||(r[2]=B=>l.value=B),class:"box-card",width:"800px","show-close":!1,"close-on-click-modal":!1},{header:e.withCtx(()=>{var B,O,P;return[e.createElementVNode("div",Ne,[n.group===1?(e.openBlock(),e.createElementBlock("h3",Ve,"我要开团")):(e.openBlock(),e.createElementBlock("h3",Se,"快速成团")),n.group===1?(e.openBlock(),e.createElementBlock("h4",Ee,[e.createElementVNode("li",null,[e.createElementVNode("i",null,e.toDisplayString(n.itemList.item),1),e.createTextVNode(" 人团 拼团价 "),e.createElementVNode("i",null,"¥"+e.toDisplayString(e.unref(i)((P=(O=(B=a.value)==null?void 0:B[0])==null?void 0:O.prices)==null?void 0:P[0]).toFixed(2)),1)])])):(e.openBlock(),e.createElementBlock("div",ve,[e.createElementVNode("span",null,[e.createElementVNode("img",{src:t.itemList.commanderAvatar,alt:""},null,8,Ce)]),e.createElementVNode("i",null,e.toDisplayString(t.itemList.commanderNickname),1),e.createElementVNode("em",null,[e.createTextVNode("还差"),e.createElementVNode("i",null,e.toDisplayString(+t.itemList.totalNum-+t.itemList.currentNum)+"人",1),e.createTextVNode("拼成")])])),e.createElementVNode("i",{style:{cursor:"pointer"},onClick:E},Be)])]}),default:e.withCtx(()=>{var B,O,P,R,M,Y,D,A;return[e.createElementVNode("div",Le,[e.createElementVNode("div",Ie,[e.createElementVNode("img",{src:((B=N.value)==null?void 0:B.image)||((O=v.value)==null?void 0:O.image)||y.value.image||C.value,alt:""},null,8,$e)]),e.createElementVNode("ul",ze,[e.createElementVNode("li",null,[Ge,e.createElementVNode("p",null,[e.createTextVNode(" ￥"),e.createElementVNode("span",null,e.toDisplayString(e.unref(i)((M=(R=(P=a.value)==null?void 0:P[0])==null?void 0:R.prices)==null?void 0:M[0]).toFixed(2)),1)])]),Oe])]),e.createElementVNode("ul",Pe,[e.createElementVNode("li",null,[Te,e.createElementVNode("div",qe,[e.createVNode(x,{modelValue:p.value,"onUpdate:modelValue":r[0]||(r[0]=F=>p.value=F),style:{width:"250px",height:"34px",border:"1px solid #bbb"},placeholder:"请选择省/市/区",options:n.regionDatas,filterable:""},null,8,["modelValue","options"])]),e.createElementVNode("i",Ue,e.toDisplayString(t.freight?`快递：${Number(t.freight).toFixed(2)}`:"所选地区免运费"),1)]),e.createElementVNode("li",De,[e.createVNode(ke,{"sku-group":I.value,"img-boolean":!1,onChooseSpec:b},null,8,["sku-group"])]),e.createElementVNode("li",null,[Fe,e.createVNode(S,{modelValue:z.value,"onUpdate:modelValue":r[1]||(r[1]=F=>z.value=F),min:1,max:+((D=(Y=a.value)==null?void 0:Y[0])==null?void 0:D.stock)||1,size:"small"},null,8,["modelValue","max"]),e.createElementVNode("em",null,"剩余库存："+e.toDisplayString((A=a.value)==null?void 0:A[0].stock),1)]),n.group===1?(e.openBlock(),e.createElementBlock("li",je,[Ae,e.createVNode(G,{color:"#ee1e32",disabled:!e.unref(d),plain:"",style:{width:"146px",height:"40px",transform:"translateY(-20px)"},onClick:V},{default:e.withCtx(()=>[e.createTextVNode("立即开团")]),_:1},8,["disabled"])])):(e.openBlock(),e.createElementBlock("li",Re,[Me,e.createVNode(G,{color:"#ee1e32",disabled:!e.unref(d),plain:"",style:{width:"146px",height:"40px",transform:"translateY(-20px)"},onClick:V},{default:e.withCtx(()=>[e.createTextVNode("立即拼团")]),_:1},8,["disabled"])]))])]}),_:1},8,["modelValue"])}}}),ct="",Je=J(Ye,[["__scopeId","data-v-3e12721e"]]),Z=n=>(e.pushScopeId("data-v-089e4cdc"),n=n(),e.popScopeId(),n),He={class:"unity"},Ke={class:"left"},We=Z(()=>e.createElementVNode("h1",null,"我要开团",-1)),Xe=["onClick"],Qe={class:"right"},Ze=Z(()=>e.createElementVNode("h1",null,"快速拼团",-1)),et=["src"],tt={"text-center":""},ot=["onClick"],it=e.defineComponent({__name:"PcGroupUserList",props:{properties:{type:Object,default:()=>{}}},emits:["priceValueFn"],setup(n,{emit:o}){const t=n,{divTenThousand:i}=u(),l=H.useRoute();H.useRouter();const p=e.ref(l.query.shopId),E=e.ref(l.query.productId),_=e.ref([]),v=e.ref("0"),w=e.ref({users:"",prices:""});(async()=>{var k,N,b,V;const h=await X(p.value,E.value);v.value=(k=h.data.skus)==null?void 0:k[0].stock,w.value.users=h.data.users,w.value.prices=(b=(N=h.data.skus)==null?void 0:N[0].prices)==null?void 0:b[0],_.value=(V=h.data.skus)==null?void 0:V[0].prices})();const d=e.ref(!1),c=h=>{d.value=h},f=e.ref(),a=e.ref(),g=(h,k)=>{f.value=h,d.value=!0,a.value=k},y=e.ref(),I=h=>{console.log("val",t.properties.skuList),y.value=h};e.watch(()=>y.value,()=>{console.log("priceVal",y.value),o("priceValueFn",y.value)});const C=e.ref(),$=e.reactive({size:4,current:1});return(async()=>{var k;const h=await de(l.query.shopId,l.query.productId,$.current,$.size);C.value=(k=h.data)==null?void 0:k.records})(),(h,k)=>{var N;return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",He,[e.createElementVNode("div",Ke,[We,e.createElementVNode("ul",null,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(w.value.users,(b,V)=>{var z,s,r;return e.openBlock(),e.createElementBlock("li",{key:V},[e.createElementVNode("h3",null,[e.createElementVNode("span",null,e.toDisplayString(b),1),e.createTextVNode(" 人拼团价 "),e.createElementVNode("span",null,"¥ "+e.toDisplayString(e.unref(i)((r=(s=(z=y.value)==null?void 0:z[0])==null?void 0:s.prices)==null?void 0:r[0]).toFixed(2)),1)]),e.createElementVNode("div",{class:"btn",onClick:x=>g(1,{item:b})},"立即开团",8,Xe)])}),128))])]),e.createElementVNode("div",Qe,[Ze,e.createElementVNode("ul",null,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(C.value,(b,V)=>(e.openBlock(),e.createElementBlock("li",{key:V},[e.createElementVNode("span",null,[e.createElementVNode("img",{src:b.commanderAvatar,alt:""},null,8,et)]),e.createElementVNode("h3",null,e.toDisplayString(b.commanderNickname),1),e.createElementVNode("h4",null,[e.createTextVNode(" 还差"),e.createElementVNode("span",tt,e.toDisplayString(+b.totalNum-+b.currentNum),1),e.createTextVNode("人拼成 ")]),e.createElementVNode("div",{class:"btn",onClick:z=>g(0,b)},"去拼团",8,ot)]))),128))])])]),e.createVNode(Je,{"is-show":d.value,group:f.value,"region-datas":(N=t.properties)==null?void 0:N.regionDatas,"sku-list":t.properties.skuList,"area-code":t.properties.areaCode,"item-list":a.value,"limit-num":v.value,freight:t.properties.freight,"prices-price":w.value.prices,shop:p.value,"product-id":E.value,onCloseFn:c,onPriceValue:I},null,8,["is-show","group","region-datas","sku-list","area-code","item-list","limit-num","freight","prices-price","shop","product-id"])],64)}}}),dt="";return J(it,[["__scopeId","data-v-089e4cdc"]])});
