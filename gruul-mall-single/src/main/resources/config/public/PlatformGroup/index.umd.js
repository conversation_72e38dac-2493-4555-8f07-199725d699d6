(function(e,C){typeof exports=="object"&&typeof module<"u"?module.exports=C(require("vue"),require("@vueuse/core"),require("@element-plus/icons-vue"),require("@/components/PageManage.vue"),require("@/utils/http"),require("vue-router"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@element-plus/icons-vue","@/components/PageManage.vue","@/utils/http","vue-router","element-plus"],C):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformGroup=C(e.PlatformGroupContext.Vue,e.PlatformGroupContext.VueUse,e.PlatformGroupContext.ElementPlusIconsVue,e.PlatformGroupContext.PageManageTwo,e.PlatformGroupContext.UtilsHttp,e.PlatformGroupContext.VueR<PERSON>er,e.PlatformGroupContext.ElementPlus))})(this,function(e,C,b,B,g,S,d){"use strict";const T=e.defineComponent({__name:"select-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Array,default:()=>[{label:"1",value:"2"}]}},emits:["update:modelValue","change"],setup(p,{emit:s}){const i=p,m=C.useVModel(i,"modelValue",s);return(f,c)=>{const l=e.resolveComponent("el-option"),_=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(_,{modelValue:e.unref(m),"onUpdate:modelValue":c[0]||(c[0]=u=>e.isRef(m)?m.value=u:null),placeholder:i.placeholder,style:{width:"150px"},onChange:c[1]||(c[1]=u=>s("change"))},{default:e.withCtx(()=>[e.renderSlot(f.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.list,(u,x)=>(e.openBlock(),e.createBlock(l,{key:x,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),D=e.defineComponent({__name:"head-search-p",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},selectOptions:{type:Object,default(){return{}}}},emits:["update:modelValue","batchDel","search"],setup(p,{emit:s}){const i=p,m=[{value:"OPENING",label:"未开始"},{value:"OPEN",label:"进行中"},{value:"FINISHED",label:"已结束"},{value:"VIOLATION",label:"违规下架"}],f=C.useVModel(i,"modelValue",s);return(c,l)=>{const _=e.resolveComponent("el-button"),u=e.resolveComponent("el-col"),x=e.resolveComponent("el-option"),N=e.resolveComponent("el-space"),w=e.resolveComponent("el-input"),h=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(h,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px"}},{default:e.withCtx(()=>[e.createVNode(u,{span:14},{default:e.withCtx(()=>[e.createVNode(_,{round:"",plain:"",onClick:l[0]||(l[0]=V=>s("batchDel"))},{default:e.withCtx(()=>[e.createTextVNode("批量移除")]),_:1})]),_:1}),e.createVNode(u,{span:9},{default:e.withCtx(()=>[e.createVNode(N,null,{default:e.withCtx(()=>[e.createVNode(T,{modelValue:e.unref(f).status,"onUpdate:modelValue":l[1]||(l[1]=V=>e.unref(f).status=V),list:m,onChange:l[2]||(l[2]=V=>s("search"))},{default:e.withCtx(()=>[e.createVNode(x,{label:"全部状态",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(w,{modelValue:e.unref(f).keywords,"onUpdate:modelValue":l[4]||(l[4]=V=>e.unref(f).keywords=V),placeholder:"输入关键词",style:{width:"55%"}},{append:e.withCtx(()=>[e.createVNode(_,{icon:e.unref(b.Search),onClick:l[3]||(l[3]=V=>s("search"))},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),M=p=>g.http.get({url:"addon-team/team/activity",params:p},{isMock:!1}),E=p=>g.http.post({url:"addon-team/team/activity/delete/batch",data:p}),O=(p,s)=>g.http.post({url:`addon-team/team/activity/${p}/${s}/violate`}),G={class:"goods"};return e.defineComponent({__name:"PlatformGroup",setup(p){const s=e.ref({keywords:"",status:""}),i=e.ref({size:10,current:1}),m=e.ref(0),f=S.useRouter(),c=e.ref([]),l=e.ref([]),_={OPENING:"未开始",PREHEAT:"预热中",OPEN:"进行中",FINISHED:"已结束",VIOLATION:"违规下架"},u=async o=>{try{if(!await d.ElMessageBox.confirm("确定下架该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:n,data:y}=await O(o.shopId,o.id);if(n!==200){d.ElMessage.error("下架失败");return}d.ElMessage.success("下架成功");const a=c.value.find(k=>k.id===o.id);a&&(a.status="VIOLATION")}catch{}},x=async()=>{if(!l.value.length){d.ElMessage.info("请选择需要删除的活动");return}if(!await d.ElMessageBox.confirm("确定删除活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const r=l.value.map(a=>({shopId:a.shopId,activityId:a.id})),{code:n,data:y}=await E(r);if(n!==200){d.ElMessage.error("删除失败");return}d.ElMessage.success("删除成功"),h()},N=o=>{f.push({name:"groupDetail",query:{activityId:o.id,shopId:o.shopId}})},w=()=>{h()};async function h(){const{status:o,keywords:r}=s.value,n={...i.value,status:o,keyword:r},{code:y,data:a}=await M(n);if(y!==200)return d.ElMessage.error("获取活动列表失败");c.value=a.records,i.value.current=a.current,i.value.size=a.size,m.value=a.total}const V=async o=>{try{if(!await d.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:n,data:y}=await E([{shopId:o.shopId,activityId:o.id}]);if(n!==200){d.ElMessage.error("删除失败");return}d.ElMessage.success("删除成功"),c.value=c.value.filter(a=>a.id!==o.id),m.value--}catch{}},z=o=>{i.value.size=o,h()},$=o=>{i.value.current=o,h()};return e.onBeforeMount(()=>{h()}),(o,r)=>{const n=e.resolveComponent("el-table-column"),y=e.resolveComponent("el-link"),a=e.resolveComponent("el-row"),k=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(D,{modelValue:s.value,"onUpdate:modelValue":r[0]||(r[0]=t=>s.value=t),onBatchDel:x,onSearch:w},null,8,["modelValue"]),e.createVNode(k,{ref:"multipleTableRef",data:c.value,stripe:"",height:"calc(100vh - 250px)","header-row-style":{fontSize:"12px",color:"#909399"},"header-cell-style":{background:"#f6f8fa"},"cell-style":{fontSize:"12px",color:"#333333"},onSelectionChange:r[1]||(r[1]=t=>l.value=t)},{default:e.withCtx(()=>[e.createVNode(n,{type:"selection",width:"55"}),e.createVNode(n,{label:"店铺名称",width:"160"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",G,e.toDisplayString(t.shopName),1)]),_:1}),e.createVNode(n,{label:"活动名称",width:"150"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.name),1)]),_:1}),e.createVNode(n,{label:"活动时间",align:"center",width:"250"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",null,e.toDisplayString(t.startTime),1),e.createElementVNode("div",null,e.toDisplayString(t.endTime),1)]),_:1}),e.createVNode(n,{label:"参加人数",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.users||0),1)]),_:1}),e.createVNode(n,{label:"成团订单数",align:"center",width:"150"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.orders||0),1)]),_:1}),e.createVNode(n,{label:"状态",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:t.status==="VIOLATION"?"#F12F22":""})},e.toDisplayString(_[t.status]),5)]),_:1}),e.createVNode(n,{label:"操作",align:"center",width:"200px",fixed:"right"},{default:e.withCtx(({row:t})=>[e.createVNode(a,{justify:"center",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(y,{style:{padding:"0 10px"},underline:!1,type:"primary",size:"small",onClick:I=>N(t)},{default:e.withCtx(()=>[e.createTextVNode(" 查看 ")]),_:2},1032,["onClick"]),["VIOLATION","FINISHED"].includes(t.status)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(y,{key:0,style:{padding:"0 10px"},underline:!1,type:"primary",size:"small",onClick:I=>u(t)},{default:e.withCtx(()=>[e.createTextVNode(" 下架 ")]),_:2},1032,["onClick"])),e.createVNode(y,{style:{padding:"0 10px"},underline:!1,type:"primary",size:"small",onClick:I=>V(t)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),e.createVNode(a,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:i.value,"onUpdate:modelValue":r[2]||(r[2]=t=>i.value=t),"load-init":!0,total:m.value,onHandleSizeChange:z,onHandleCurrentChange:$,onReload:h},null,8,["modelValue","total"])]),_:1})])}}})});
