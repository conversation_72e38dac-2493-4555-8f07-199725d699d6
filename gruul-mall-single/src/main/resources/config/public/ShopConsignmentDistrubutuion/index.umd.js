(function(e,S){typeof exports=="object"&&typeof module<"u"?module.exports=S(require("vue"),require("@/apis/http"),require("element-plus"),require("decimal.js"),require("vue-router"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","@/apis/http","element-plus","decimal.js","vue-router","@/composables/useConvert"],S):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopConsignmentDistrubutuion=S(e.ShopConsignmentDistrubutuionContext.Vue,e.ShopConsignmentDistrubutuionContext.Request,e.ShopConsignmentDistrubutuionContext.ElementPlus,e.ShopConsignmentDistrubutuionContext.Decimal,e.ShopConsignmentDistrubutuionContext.VueR<PERSON><PERSON>,e.ShopConsignmentDistrubutuionContext.UseConvert))})(this,function(e,S,q,p,W,H){"use strict";var K=document.createElement("style");K.textContent=`.distribution__title[data-v-a0bc49e7]{padding:15px 0}.distribution__container[data-v-a0bc49e7]{padding:0 20px}.distribution__btns[data-v-a0bc49e7]{margin:30px auto 0;width:30%;display:flex;justify-content:space-between;align-items:center}
`,document.head.appendChild(K);var R=(t=>(t.UNLIMITED="不限购",t.PRODUCT_LIMITED="商品限购",t.SKU_LIMITED="规格限购",t))(R||{});const J=(t,o)=>S.get({url:`gruul-mall-storage/storage/shop/${t}/product/${o}`}),O=()=>S.get({url:"gruul-mall-addon-platform/platform/shop/signing/category/choosable/list"}),Q=t=>S.get({url:"gruul-mall-goods/goods/product/category",params:t}),X=()=>S.get({url:"gruul-mall-goods/consignment/config"}),Z=(t,o)=>S.get({url:`addon-supplier/supplier/manager/product/get/${o}/${t}`}),v=t=>S.post({url:"gruul-mall-goods/consignment/single/pave/goods",data:t}),{divTenThousand:B,mulTenThousand:$}=H(),ee={name:"",saleDescribe:"",platformCategoryId:[],categoryId:"",providerId:"",widePic:"",distributionMode:[],videoUrl:"",albumPics:"",productType:"REAL_PRODUCT",specGroups:[],platformCategory:{one:null,two:null,three:null},shopCategory:{one:null,two:null,three:null},skus:[{id:"",image:"",initSalesVolume:0,limitNum:0,limitType:"UNLIMITED",price:0,productId:"",initStock:0,salePrice:0,shopId:"",stockType:"UNLIMITED",weight:0,specs:[]}],productParameters:[],productAttributes:[],serviceIds:[],detail:"",freightTemplateId:"0",status:"SELL_ON",brandId:""},te=()=>{const t=W.useRoute(),o=W.useRouter(),a=e.ref(null),c=e.ref([]),d=e.ref([]),r=e.reactive({name:"",shopCategory:"",saleDescribe:"",consignmentPriceSetting:{type:"UNIFY",sale:0,scribe:0},unifyPriceSetting:{type:"",sale:"",scribe:""}}),P={name:{required:!0,message:"请输入商品名称",trigger:"blur"},shopCategory:{required:!0,message:"请选择店铺类目",trigger:"change",type:"array"}},U={expandTrigger:"hover",label:"name",value:"id"},D=e.ref(ee),m=e.ref([]),u=e.ref([]);async function y(){const{code:s,data:h,success:i}=await O();if(s!==200){q.ElMessage.error("获取平台分类失败");return}m.value=Y(1,h==null?void 0:h.map(f=>({...f,id:f.categoryId})))}async function V(){const{code:s,data:h}=await Q({current:1,size:500});if(s!==200){q.ElMessage.error("获取店铺分类失败");return}u.value=Y(1,h.records)}const l=async()=>{var f,_,x,E,T,b;const s=t.query.shopId,h=t.query.id,{data:i}=await Z(h,s);r.name=i==null?void 0:i.name,D.value.platformCategoryId=[(_=(f=i==null?void 0:i.extra)==null?void 0:f.platformCategory)==null?void 0:_.one,(E=(x=i==null?void 0:i.extra)==null?void 0:x.platformCategory)==null?void 0:E.two,(b=(T=i==null?void 0:i.extra)==null?void 0:T.platformCategory)==null?void 0:b.three]},C=async()=>{const{data:s,code:h}=await X();h===200&&s&&(r.unifyPriceSetting.sale=B(s==null?void 0:s.sale).toString(),r.unifyPriceSetting.scribe=B(s==null?void 0:s.scribe).toString(),r.unifyPriceSetting.type=s==null?void 0:s.type)};e.watch(()=>r.consignmentPriceSetting,()=>z(r,c),{deep:!0});const A=async()=>{var f;const s=t.query.shopId,h=t.query.id,{data:i}=await J(s,h);c.value=(f=i==null?void 0:i.skus)==null?void 0:f.map(_=>{var x,E;return{..._,salePrice:(x=B(_.salePrice))==null?void 0:x.toString(),price:(E=B(_.price))==null?void 0:E.toString()}}),d.value=(i==null?void 0:i.specGroups)||[],z(r,c)},F=async()=>{l(),y(),V();try{await C()}finally{A()}},w=()=>{o.back()},j=()=>{var s;(s=a.value)==null||s.validate(async h=>{var i,f,_,x,E;if(h){const T={shopCategory:{one:(i=r==null?void 0:r.shopCategory)==null?void 0:i[0],two:(f=r==null?void 0:r.shopCategory)==null?void 0:f[1],three:(_=r==null?void 0:r.shopCategory)==null?void 0:_[2]},shopProductKey:{shopId:(x=t.query)==null?void 0:x.shopId,productId:(E=t.query)==null?void 0:E.id},name:r.name,saleDescribe:r.saleDescribe},b={};r.consignmentPriceSetting.type==="UNIFY"?(b.type=r.unifyPriceSetting.type,b.sale=$(r.unifyPriceSetting.sale).toString(),b.scribe=$(r.unifyPriceSetting.scribe).toString()):(b.type=r.consignmentPriceSetting.type,b.sale=$(r.consignmentPriceSetting.sale).toString(),b.scribe=$(r.consignmentPriceSetting.scribe).toString()),T.consignmentPriceSetting=b;const{code:G,success:M,msg:L}=await v(T);G===200&&M?(q.ElMessage.success({message:L||"铺货完成"}),w()):q.ElMessage.error({message:L})}})};return F(),{formRef:a,distributionFormModel:r,distributionFormRules:P,platformCategoryList:m,currentComodityInfo:D,shopCascaderProps:U,shopCategoryList:u,distributionSkus:c,divTenThousand:B,specGroups:d,cancelDistribution:w,handleConfirmDistribution:j}};function z(t,o){var a,c,d,r,P,U,D;((a=t==null?void 0:t.consignmentPriceSetting)==null?void 0:a.type)==="UNIFY"?((c=t==null?void 0:t.unifyPriceSetting)==null?void 0:c.type)==="RATE"?o.value=(d=o.value)==null?void 0:d.map(m=>{var u,y,V,l;return{...m,actualSalePrice:(y=new p(m.salePrice))==null?void 0:y.mul(new p(1).add(new p(((u=t==null?void 0:t.unifyPriceSetting)==null?void 0:u.sale)||0).div(100))).toString(),actualPrice:(l=new p(m.price))==null?void 0:l.mul(new p(1).add(new p(((V=t==null?void 0:t.unifyPriceSetting)==null?void 0:V.scribe)||0).div(100))).toString()}}):o.value=(r=o.value)==null?void 0:r.map(m=>{var u,y,V,l;return{...m,actualSalePrice:(y=new p(m.salePrice))==null?void 0:y.add(new p(((u=t==null?void 0:t.unifyPriceSetting)==null?void 0:u.sale)||0)).toString(),actualPrice:(l=new p(m.price))==null?void 0:l.add(new p(((V=t==null?void 0:t.unifyPriceSetting)==null?void 0:V.scribe)||0)).toString()}}):((P=t==null?void 0:t.consignmentPriceSetting)==null?void 0:P.type)==="RATE"?o.value=(U=o.value)==null?void 0:U.map(m=>{var u,y,V,l;return{...m,actualSalePrice:(y=new p(m.salePrice))==null?void 0:y.mul(new p(1).add(new p((u=t==null?void 0:t.consignmentPriceSetting)==null?void 0:u.sale).div(100))).toString(),actualPrice:(l=new p(m.price))==null?void 0:l.mul(new p(1).add(new p((V=t==null?void 0:t.consignmentPriceSetting)==null?void 0:V.scribe).div(100))).toString()}}):o.value=(D=o.value)==null?void 0:D.map(m=>{var u,y,V,l;return{...m,actualSalePrice:(y=new p(m.salePrice))==null?void 0:y.add(new p((u=t==null?void 0:t.consignmentPriceSetting)==null?void 0:u.sale)).toString(),actualPrice:(l=new p(m.price))==null?void 0:l.add(new p((V=t==null?void 0:t.consignmentPriceSetting)==null?void 0:V.scribe)).toString()}})}function Y(t,o){const a=t===3;for(let c=0;c<o.length;){const d=o[c];if(a){d.disabled=!1,c++;continue}const r=d.children||d.secondCategoryVos||d.categoryThirdlyVos;delete d.secondCategoryVos,delete d.categoryThirdlyVos;const P=!r||r.length===0;if(d.disabled=P,P){o.splice(c,1);continue}if(Y(t+1,r),r.length===0){o.splice(c,1);continue}d.children=r,c++}return o}const ne=e.createElementVNode("span",null,"销售价 = 供货价 + （供货价 *  ",-1),oe=e.createElementVNode("span",null," % ）",-1),re=e.createElementVNode("span",null,"划线价 = 销售价 + （销售价 *  ",-1),le=e.createElementVNode("span",null," % ）",-1),ie=e.createElementVNode("span",null,"销售价 = 供货价 +  ",-1),se=e.createElementVNode("span",null," 元",-1),ae=e.createElementVNode("span",null,"划线价 = 销售价 +  ",-1),ce=e.createElementVNode("span",null," 元",-1),de=e.defineComponent({__name:"batch-unify-settings",props:{type:{default:""},sale:{default:0},scribe:{default:0}},setup(t){return(o,a)=>{const c=e.resolveComponent("el-input-number"),d=e.resolveComponent("el-form-item");return o.type==="RATE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(d,{prop:"sale"},{default:e.withCtx(()=>[ne,e.createVNode(c,{"model-value":o.sale,controls:!1,precision:2,disabled:""},null,8,["model-value"]),oe]),_:1}),e.createVNode(d,{prop:"scribe"},{default:e.withCtx(()=>[re,e.createVNode(c,{"model-value":o.scribe,controls:!1,precision:2,disabled:""},null,8,["model-value"]),le]),_:1})],64)):o.type==="REGULAR"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createVNode(d,{prop:"sale"},{default:e.withCtx(()=>[ie,e.createVNode(c,{"model-value":o.sale,controls:!1,precision:2,disabled:""},null,8,["model-value"]),se]),_:1}),e.createVNode(d,{prop:"scribe"},{default:e.withCtx(()=>[ae,e.createVNode(c,{"model-value":o.scribe,controls:!1,precision:2,disabled:""},null,8,["model-value"]),ce]),_:1})],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[],64))}}}),N=t=>(e.pushScopeId("data-v-a0bc49e7"),t=t(),e.popScopeId(),t),pe={class:"distribution"},me=N(()=>e.createElementVNode("div",{class:"distribution__title"},"基础信息",-1)),_e={class:"distribution__container"},ge=N(()=>e.createElementVNode("div",{class:"distribution__title"},"单规格信息",-1)),ue={class:"distribution__container"},ye=N(()=>e.createElementVNode("div",{class:"distribution__title"},"价格设置",-1)),he={class:"distribution__container"},Ve={class:"price-settings"},fe=N(()=>e.createElementVNode("span",null,"销售价 = 供货价 + （供货价 *  ",-1)),be=N(()=>e.createElementVNode("span",null," % ）",-1)),Ce=N(()=>e.createElementVNode("span",null,"划线价 = 销售价 + （销售价 *  ",-1)),Ne=N(()=>e.createElementVNode("span",null," % ）",-1)),Se=N(()=>e.createElementVNode("span",null,"销售价 = 供货价 +  ",-1)),Pe=N(()=>e.createElementVNode("span",null," 元",-1)),xe=N(()=>e.createElementVNode("span",null,"划线价 = 销售价 +  ",-1)),Ee=N(()=>e.createElementVNode("span",null," 元",-1)),we={key:0,class:"distribution__container--actual"},Ie={class:"distribution__btns"},Te=e.defineComponent({__name:"ShopConsignmentDistrubutuion",setup(t){const{distributionFormModel:o,distributionSkus:a,distributionFormRules:c,platformCategoryList:d,shopCascaderProps:r,currentComodityInfo:P,shopCategoryList:U,specGroups:D,formRef:m,cancelDistribution:u,handleConfirmDistribution:y}=te();return(V,l)=>{const C=e.resolveComponent("el-form-item"),A=e.resolveComponent("el-input"),F=e.resolveComponent("el-cascader"),w=e.resolveComponent("el-col"),j=e.resolveComponent("el-row"),s=e.resolveComponent("el-radio"),h=e.resolveComponent("el-radio-group"),i=e.resolveComponent("el-input-number"),f=e.resolveComponent("el-image"),_=e.resolveComponent("el-table-column"),x=e.resolveComponent("el-table"),E=e.resolveComponent("el-form"),T=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",pe,[e.createVNode(E,{ref_key:"formRef",ref:m,model:e.unref(o),rules:e.unref(c)},{default:e.withCtx(()=>{var b,G,M,L;return[me,e.createElementVNode("div",_e,[e.createVNode(C,{label:"商品类型"},{default:e.withCtx(()=>[e.createTextVNode("实物商品")]),_:1}),e.createVNode(C,{label:"销售方式"},{default:e.withCtx(()=>[e.createTextVNode("代销商品（采购商品可供各店铺采购，代销商品可以让各店铺帮您将商品销售出去；）")]),_:1}),e.createVNode(C,{label:"商品名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(A,{modelValue:e.unref(o).name,"onUpdate:modelValue":l[0]||(l[0]=n=>e.unref(o).name=n),placeholder:"请输入商品名称"},null,8,["modelValue"])]),_:1}),e.createVNode(C,{label:"平台类目"},{default:e.withCtx(()=>[e.createVNode(F,{ref:"platformCategoryRef",modelValue:e.unref(P).platformCategoryId,"onUpdate:modelValue":l[1]||(l[1]=n=>e.unref(P).platformCategoryId=n),clearable:"",class:"inputWidth",style:{width:"100%"},options:e.unref(d),props:e.unref(r),placeholder:"请选择平台类目","show-all-levels":"",disabled:""},null,8,["modelValue","options","props"])]),_:1}),e.createVNode(C,{label:"店铺类目",prop:"shopCategory"},{default:e.withCtx(()=>[e.createVNode(F,{ref:"shopCategoryRef",modelValue:e.unref(o).shopCategory,"onUpdate:modelValue":l[2]||(l[2]=n=>e.unref(o).shopCategory=n),clearable:"",style:{width:"100%"},class:"inputWidth",options:e.unref(U),props:e.unref(r),placeholder:"请选择店铺类目","show-all-levels":""},null,8,["modelValue","options","props"])]),_:1}),e.createVNode(C,{label:"卖点描述",prop:"saleDescribe"},{default:e.withCtx(()=>[e.createVNode(A,{modelValue:e.unref(o).saleDescribe,"onUpdate:modelValue":l[3]||(l[3]=n=>e.unref(o).saleDescribe=n),class:"inputWidth",placeholder:"请填写卖点描述",maxlength:"60"},null,8,["modelValue"])]),_:1})]),e.unref(a).length===1?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[ge,e.createElementVNode("div",ue,[e.createVNode(j,{gutter:8},{default:e.withCtx(()=>[e.createVNode(w,{span:6},{default:e.withCtx(()=>[e.createTextVNode("规格类型：单规格")]),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var n,g;return[e.createTextVNode("供货价："+e.toDisplayString((g=(n=e.unref(a))==null?void 0:n[0])==null?void 0:g.salePrice),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var n,g,I,k;return[e.createTextVNode("销量（注水）："+e.toDisplayString(Number(((g=(n=e.unref(a))==null?void 0:n[0])==null?void 0:g.initSalesVolume)||0)+Number(((k=(I=e.unref(a))==null?void 0:I[0])==null?void 0:k.salesVolume)||0)),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var n,g;return[e.createTextVNode("重量："+e.toDisplayString((g=(n=e.unref(a))==null?void 0:n[0])==null?void 0:g.weight),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var n,g,I,k;return[e.createTextVNode("库存数："+e.toDisplayString(((g=(n=e.unref(a))==null?void 0:n[0])==null?void 0:g.stockType)==="UNLIMITED"?"无限库存":(k=(I=e.unref(a))==null?void 0:I[0])==null?void 0:k.stock),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var n,g;return[e.createTextVNode("限购类型："+e.toDisplayString(e.unref(R)[(g=(n=e.unref(a))==null?void 0:n[0])==null?void 0:g.limitType]),1)]}),_:1})]),_:1})])],64)):e.createCommentVNode("",!0),ye,e.createElementVNode("div",he,[e.createElementVNode("div",Ve,[e.createVNode(h,{modelValue:e.unref(o).consignmentPriceSetting.type,"onUpdate:modelValue":l[4]||(l[4]=n=>e.unref(o).consignmentPriceSetting.type=n)},{default:e.withCtx(()=>[e.createVNode(s,{label:"UNIFY"},{default:e.withCtx(()=>[e.createTextVNode("统一设价")]),_:1}),e.createVNode(s,{label:"RATE"},{default:e.withCtx(()=>[e.createTextVNode("按比例设价")]),_:1}),e.createVNode(s,{label:"REGULAR"},{default:e.withCtx(()=>[e.createTextVNode("固定金额设价")]),_:1})]),_:1},8,["modelValue"]),e.unref(o).consignmentPriceSetting.type==="UNIFY"?(e.openBlock(),e.createBlock(de,{key:0,sale:+e.unref(o).unifyPriceSetting.sale,scribe:+e.unref(o).unifyPriceSetting.scribe,type:e.unref(o).unifyPriceSetting.type},null,8,["sale","scribe","type"])):e.unref(o).consignmentPriceSetting.type==="RATE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createVNode(C,{prop:"consignmentPriceSetting.sale"},{default:e.withCtx(()=>[fe,e.createVNode(i,{modelValue:e.unref(o).consignmentPriceSetting.sale,"onUpdate:modelValue":l[5]||(l[5]=n=>e.unref(o).consignmentPriceSetting.sale=n),controls:!1,precision:2,max:100},null,8,["modelValue"]),be]),_:1}),e.createVNode(C,{prop:"consignmentPriceSetting.scribe"},{default:e.withCtx(()=>[Ce,e.createVNode(i,{modelValue:e.unref(o).consignmentPriceSetting.scribe,"onUpdate:modelValue":l[6]||(l[6]=n=>e.unref(o).consignmentPriceSetting.scribe=n),controls:!1,precision:2,max:100},null,8,["modelValue"]),Ne]),_:1})],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[e.createVNode(C,{prop:"consignmentPriceSetting.sale"},{default:e.withCtx(()=>[Se,e.createVNode(i,{modelValue:e.unref(o).consignmentPriceSetting.sale,"onUpdate:modelValue":l[7]||(l[7]=n=>e.unref(o).consignmentPriceSetting.sale=n),controls:!1,precision:2},null,8,["modelValue"]),Pe]),_:1}),e.createVNode(C,{prop:"consignmentPriceSetting.scribe"},{default:e.withCtx(()=>[xe,e.createVNode(i,{modelValue:e.unref(o).consignmentPriceSetting.scribe,"onUpdate:modelValue":l[8]||(l[8]=n=>e.unref(o).consignmentPriceSetting.scribe=n),controls:!1,precision:2},null,8,["modelValue"]),Ee]),_:1})],64))]),e.unref(a).length===1?(e.openBlock(),e.createElementBlock("div",we,[e.createElementVNode("span",null,"销售价："+e.toDisplayString((G=(b=e.unref(a))==null?void 0:b[0])==null?void 0:G.actualSalePrice),1),e.createElementVNode("span",null,"划线价："+e.toDisplayString((L=(M=e.unref(a))==null?void 0:M[0])==null?void 0:L.actualPrice),1)])):(e.openBlock(),e.createBlock(x,{key:1,data:e.unref(a)},{default:e.withCtx(()=>[e.createVNode(_,{fixed:"left",width:"150",label:"规格图"},{default:e.withCtx(({row:n})=>[e.createVNode(f,{src:n==null?void 0:n.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(D),(n,g)=>(e.openBlock(),e.createBlock(_,{key:g,width:"150",label:n==null?void 0:n.name},{default:e.withCtx(({row:I})=>{var k;return[e.createTextVNode(e.toDisplayString(Array.isArray(I.specs)?(k=I.specs)==null?void 0:k[g]:I.specs.name),1)]}),_:2},1032,["label"]))),128)),e.createVNode(_,{width:"150",label:"供货价",prop:"salePrice"}),e.createVNode(_,{width:"150",label:"划线价",prop:"actualPrice"}),e.createVNode(_,{width:"150",label:"销售价",prop:"actualSalePrice"}),e.createVNode(_,{width:"150",label:"库存"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString((n==null?void 0:n.stockType)==="UNLIMITED"?"无限库存":n==null?void 0:n.stock),1)]),_:1}),e.createVNode(_,{width:"150",label:"限购类型"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(e.unref(R)[n==null?void 0:n.limitType]),1)]),_:1}),e.createVNode(_,{width:"150",label:"规格限购"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString((n==null?void 0:n.limitType)!=="UNLIMITED"&&(n==null?void 0:n.limitNum)),1)]),_:1}),e.createVNode(_,{width:"150",label:"重量",prop:"weight"}),e.createVNode(_,{width:"150",label:"销量（注水）"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(Number((n==null?void 0:n.initSalesVolume)||0)+Number((n==null?void 0:n.salesVolume)||0)),1)]),_:1})]),_:1},8,["data"]))])]}),_:1},8,["model","rules"]),e.createElementVNode("div",Ie,[e.createVNode(T,{onClick:e.unref(u)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1},8,["onClick"]),e.createVNode(T,{type:"primary",onClick:e.unref(y)},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1},8,["onClick"])])])}}}),ke="";return((t,o)=>{const a=t.__vccOpts||t;for(const[c,d]of o)a[c]=d;return a})(Te,[["__scopeId","data-v-a0bc49e7"]])});
