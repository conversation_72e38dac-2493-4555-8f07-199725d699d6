(function(e,T){typeof exports=="object"&&typeof module<"u"?module.exports=T(require("vue"),require("@/apis/http"),require("element-plus"),require("@/composables/useConvert"),require("vue-router"),require("@/components/q-input-number/q-input-number.vue"),require("@/utils/date")):typeof define=="function"&&define.amd?define(["vue","@/apis/http","element-plus","@/composables/useConvert","vue-router","@/components/q-input-number/q-input-number.vue","@/utils/date"],T):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAddBargain=T(e.PlatformAddBargainContext.Vue,e.PlatformAddBargainContext.Request,e.PlatformAddBargainContext.ElementPlus,e.PlatformAddBargainContext.UseConvert,e.PlatformAddBargainContext.VueRouter,e.PlatformAddBargainContext.QInputNumber,e.PlatformAddBargainContext.DateUtil))})(this,function(e,T,C,J,F,j,O){"use strict";var z=document.createElement("style");z.textContent=`@charset "UTF-8";.com[data-v-a8c112a5]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-a8c112a5]{width:62px;height:62px}.com__name[data-v-a8c112a5]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.add[data-v-52566479]{margin:-20px -15px;height:calc(100vh - 85px);overflow-y:auto;scrollbar-width:none;-ms-overflow-style:none}.add[data-v-52566479]::-webkit-scrollbar{display:none}.bargaining_amount[data-v-52566479]{position:relative;width:100%}.bargaining_amount__description[data-v-52566479]{position:absolute;top:-35px;right:0;width:480px}.title[data-v-52566479]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-52566479]{font-size:12px;margin-left:12px;color:#c4c4c4}.use_discount[data-v-52566479]{display:flex;width:100%}.discount_msg[data-v-52566479]{display:inline-block;width:400px;flex:1}.rules[data-v-52566479]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-52566479]{width:300px;display:flex}.text[data-v-52566479]{font-size:14px;color:#333}.goodsData[data-v-52566479]{border:1px solid #ccc}.goods-list[data-v-52566479]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-52566479]{display:flex}.goods-list__goods-list__info-name[data-v-52566479]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-52566479]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-52566479]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-52566479]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-52566479]{font-size:16px}.ruleform-date[data-v-52566479]{width:100%;display:flex;align-items:center}.flex[data-v-52566479]{margin-top:10px;height:50px}.flex-item[data-v-52566479]{width:40%}.coupon-rules[data-v-52566479]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-52566479]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild(z);const $="addon-bargain/bargain/",K=_=>T.post({url:$,data:_}),W=_=>T.get({url:$+`${_.shopId}/${_.activityId}`}),X={class:"com"},Q={class:"com__name"},Z=e.defineComponent({__name:"select-goods-table",props:{productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}}},setup(_,{expose:E}){const V=_,{divTenThousand:x,mulTenThousand:h}=J(),y=e.ref([]);e.watch(()=>V.productList,a=>{const p=U(a);console.log("flatGoodList",p),y.value=o(p)}),e.watch(()=>V.flatGoodList,a=>{y.value=o(a)});function M(a){return a.skuItem.stockType==="LIMITED"?Number(a.skuItem.skuStock):1/0}function U(a,p){if(!a.length)return[];const s=[];return a.forEach(n=>{n.skuIds.forEach((f,u)=>{s.push({productId:n.productId,productName:n.productName,productPic:n.pic,skuItem:{productId:n.productId,skuId:f,skuName:n.specs[u],skuPrice:n.salePrices[u],skuStock:n.stocks[u],stockType:n.stockTypes[u]},rowTag:0,stock:0,isJoin:!0,floorPrice:.01})})}),s}function o(a,p){let s=0,n=a.length;for(let f=0;f<n;f++){const u=a[f];f===0&&(u.rowTag=1,s=0),f!==0&&(u.productId===a[f-1].productId?(u.rowTag=0,a[s].rowTag=a[s].rowTag+1):(u.rowTag=1,s=f))}return a}const c=({row:a,column:p,rowIndex:s,columnIndex:n})=>{if(n===0)return{rowspan:a.rowTag,colspan:a.rowTag?1:0}};function q(a){return a.stockType==="UNLIMITED"?"不限购":a.skuStock}function I(){return e.toRaw(y.value).filter(p=>p.isJoin).map(p=>{const{productId:s,productPic:n,floorPrice:f,productName:u,stock:P,skuItem:{skuId:D,skuStock:d,skuPrice:b,skuName:m,stockType:t}}=p;return{activityId:"",productId:s,productPic:n,floorPrice:h(f).toString(),productName:u,stock:P,skuId:D,skuStock:+d,skuPrice:b,skuName:m,stockType:t}})}function R(){let a=!0;const p=y.value;if(!p.length)C.ElMessage.warning("请选择商品"),a=!1;else for(let s=0;s<p.length;s++)if(p[s].isJoin&&!p[s].stock){C.ElMessage.warning("商品库存必须大于零"),a=!1;break}return a}return E({getProduct:I,validateProduct:R}),(a,p)=>{const s=e.resolveComponent("el-image"),n=e.resolveComponent("el-table-column"),f=e.resolveComponent("el-input"),u=e.resolveComponent("el-input-number"),P=e.resolveComponent("el-switch"),D=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(D,{data:y.value,"span-method":c,"max-height":500},{default:e.withCtx(()=>[e.createVNode(n,{label:"商品信息",width:"215"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",X,[e.createVNode(s,{class:"com__pic",src:d.productPic},null,8,["src"]),e.createElementVNode("div",Q,e.toDisplayString(d.productName),1)])]),_:1}),e.createVNode(n,{label:"规格"},{default:e.withCtx(({row:d})=>[e.createTextVNode(e.toDisplayString(d.skuItem.skuName),1)]),_:1}),e.createVNode(n,{label:"砍价低价（元）"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",null,[V.isEdit?(e.openBlock(),e.createBlock(f,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(x)(d.floorPrice).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:d.floorPrice,"onUpdate:modelValue":b=>d.floorPrice=b,min:.01,style:{width:"80px"},disabled:V.isEdit,precision:2,max:e.unref(x)(d.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(x)(d.skuItem.skuPrice)),1)]),_:1}),e.createVNode(n,{label:"砍价库存"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",null,[e.createVNode(u,{"model-value":+d.stock,min:0,style:{width:"80px"},max:M(d),disabled:V.isEdit,precision:0,controls:!1,"onUpdate:modelValue":b=>d.stock=b},null,8,["model-value","max","disabled","onUpdate:modelValue"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(q(d.skuItem)),1)]),_:1}),e.createVNode(n,{label:"是否参与"},{default:e.withCtx(({row:d})=>[e.createVNode(P,{modelValue:d.isJoin,"onUpdate:modelValue":b=>d.isJoin=b,size:"large",disabled:V.isEdit},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),Ve="",H=(_,E)=>{const V=_.__vccOpts||_;for(const[x,h]of E)V[x]=h;return V},v=H(Z,[["__scopeId","data-v-a8c112a5"]]),g=_=>(e.pushScopeId("data-v-52566479"),_=_(),e.popScopeId(),_),ee={style:{padding:"40px"},class:"add"},te=g(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),oe={class:"ruleform-date"},ae=g(()=>e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1)),le=g(()=>e.createElementVNode("span",{class:"msg"},"砍到底价所需人数",-1)),ne=g(()=>e.createElementVNode("span",{class:"msg"},"砍价有效期是指从用户发起砍价到砍价截止的时间",-1)),re=g(()=>e.createElementVNode("span",{class:"msg"},"是否用户发起砍价的同时为自己砍1次价",-1)),de=g(()=>e.createElementVNode("span",{class:"msg"},"新用户是指未在本店铺购买过商品的用户",-1)),ie=g(()=>e.createElementVNode("span",null,"活动前",-1)),se=g(()=>e.createElementVNode("span",null,"开启活动预热",-1)),ce=g(()=>e.createElementVNode("span",{class:"msg"},"预热阶段用户可看到砍价活动，但活动开始前无法发起砍价",-1)),me={class:"use_discount"},pe=g(()=>e.createElementVNode("div",{class:"msg discount_msg"},[e.createTextVNode(" 叠加优惠可能导致实付金额为 "),e.createElementVNode("strong",{style:{color:"red"}},"0"),e.createTextVNode(" (实付金额 = 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1)),fe={class:"bargaining_amount"},ue=g(()=>e.createElementVNode("div",null,[e.createElementVNode("p",{class:"msg"},"a.固定砍价 =（原价 - 砍价底价）/砍价人数"),e.createElementVNode("p",{class:"msg"}," b.随机砍价：最低砍价金额 = 1 ，最高砍价金额 = (原价 - 砍价底价) * 100 / 砍价到底人数 * 2 单位：分，最后一人砍完剩余价格 ")],-1)),_e=g(()=>e.createElementVNode("span",{class:"msg"},"是否参与：SKU的粒度设置商品是否参与活动，是(默认)则参与活动，反则否",-1)),ge=e.defineComponent({__name:"PlatformAddBargain",setup(_){const E=F.useRouter(),V=F.useRoute(),x=new O,h=e.ref(),y=e.reactive({form:{shopId:"",shopName:"",name:"",startTime:"",endTime:"",bargainingPeople:0,bargainValidityPeriod:5,isSelfBargain:!1,userType:"NEW_USER",activityPreheat:0,stackable:{coupon:!1,vip:!1,full:!1},status:"ILLEGAL_SELL_OFF",helpCutAmount:"FIX_BARGAIN",bargainProducts:[],productNum:0},isEditDisable:!1,fullReductionTime:[],rules:{name:[{required:!0,message:"请输入活动名称",trigger:"blur"}],product:[{required:!0,message:"请选择商品",trigger:["blur","change"]}],startTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]},{validator:P,trigger:["blur","change"]}],endTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]},{validator:D,trigger:["blur","change"]}]},chooseGoodsPopup:!1}),M=e.ref([]),U=e.ref([]),{form:o,isEditDisable:c,rules:q}=e.toRefs(y),I=e.ref(),R={79111:"砍价商品在相同时间段内已存在",79112:"砍价活动不存在",79113:"当前商品不是砍价商品"};a();async function a(m=V.query){if(m.shopId&&m.activityId){c.value=!0;const{code:t,data:i,msg:r}=await W(m);if(t!==200)return C.ElMessage.error(r||"获取活动详情失败");o.value=i,o.value.bargainProducts=i.bargainActivityProducts,U.value=p(i.bargainActivityProducts)}}function p(m){return m.map(t=>{const{activityId:i,productId:r,skuPrice:k,productPic:Y,productName:N,skuId:B,skuStock:w,stock:G,skuName:A,floorPrice:S,stockType:L}=t;return{floorPrice:S,isJoin:!0,productId:r,productName:N,productPic:Y,stock:G,skuItem:{productId:r,skuId:B,skuName:A,skuPrice:k,skuStock:w,stockType:L}}})}const s=async()=>{if(!(!I.value||!await I.value.validate())&&h.value&&h.value.validateProduct()){o.value.bargainProducts=h.value.getProduct(),o.value.productNum=o.value.bargainProducts.length;const{code:t,data:i,msg:r}=await K(o.value);if(t===200)return n();if([79111,79112,79113].includes(t))return C.ElMessage.error(R[t]||"添加活动失败");C.ElMessage.error(r||"添加活动失败")}};function n(){C.ElMessage.success("添加活动成功"),E.push({name:"bargainIndex"})}function f(m){const t=x.getYMD(new Date),i=x.getYMD(m);return t===i?!1:new Date().getTime()>m.getTime()}const u=async()=>{};function P(m,t,i){t?t&&o.value.endTime?b(new Date(o.value.endTime).getTime(),i,"开始日期和结束日期最少间隔5分钟",new Date(t).getTime(),1e3*60*5):b(new Date(t).getTime(),i,"开始日期必须是一个将来的时间",new Date().getTime(),1e3):i(new Error("请选择活动开始日期"))}function D(m,t,i){t?t&&o.value.startTime?b(new Date(t).getTime(),i,"开始日期和结束日期最少间隔5分钟",new Date(o.value.startTime).getTime(),1e3*60*5):b(new Date(t).getTime(),i,"结束日期最少大当前时间5分钟",new Date().getTime()+1e3,1e3*60*5):i(new Error("请选择活动结束日期"))}function d(m,t=new Date().getTime()){const i=m-t;return(r=1e3)=>i>=r}function b(m,t,i,r,k){d(m,r)(k)?t():t(new Error(i))}return(m,t)=>{const i=e.resolveComponent("el-input"),r=e.resolveComponent("el-form-item"),k=e.resolveComponent("el-date-picker"),Y=e.resolveComponent("el-input-number"),N=e.resolveComponent("el-radio"),B=e.resolveComponent("el-radio-group"),w=e.resolveComponent("el-option"),G=e.resolveComponent("el-select"),A=e.resolveComponent("el-checkbox"),S=e.resolveComponent("el-button"),L=e.resolveComponent("el-form"),be=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",ee,[te,e.createVNode(L,{ref_key:"ruleFormRef",ref:I,model:e.unref(o),rules:e.unref(q),"label-width":"auto","inline-message":!1,"label-position":"left"},{default:e.withCtx(()=>[e.createVNode(r,{label:"活动名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:e.unref(o).name,"onUpdate:modelValue":t[0]||(t[0]=l=>e.unref(o).name=l),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"15",placeholder:"活动名称不超过15个字",disabled:e.unref(c)},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(r,{label:"活动日期",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",oe,[e.createVNode(r,{prop:"startTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:e.unref(o).startTime,"onUpdate:modelValue":t[1]||(t[1]=l=>e.unref(o).startTime=l),type:"datetime",disabled:e.unref(c),placeholder:"请选择开始时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":f},null,8,["modelValue","disabled"]),ae]),_:1}),e.createVNode(r,{prop:"endTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:e.unref(o).endTime,"onUpdate:modelValue":t[2]||(t[2]=l=>e.unref(o).endTime=l),disabled:e.unref(c),type:"datetime",placeholder:"请选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":f},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(r,{label:"砍价到底的人数",prop:"bargainingPeople",required:""},{default:e.withCtx(()=>[e.createVNode(Y,{modelValue:e.unref(o).bargainingPeople,"onUpdate:modelValue":t[3]||(t[3]=l=>e.unref(o).bargainingPeople=l),style:{width:"135px"},disabled:e.unref(c),precision:0,controls:!1,min:2},null,8,["modelValue","disabled"]),le]),_:1}),e.createVNode(r,{label:"砍价有效期",prop:"bargainValidityPeriod",required:""},{default:e.withCtx(()=>[e.createVNode(j,{modelValue:e.unref(o).bargainValidityPeriod,"onUpdate:modelValue":t[4]||(t[4]=l=>e.unref(o).bargainValidityPeriod=l),controls:!1,precision:0,min:5,disabled:e.unref(c)},{append:e.withCtx(()=>[e.createTextVNode(" 分钟 ")]),_:1},8,["modelValue","disabled"]),ne]),_:1}),e.createVNode(r,{label:"是否自我砍价",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:e.unref(o).isSelfBargain,"onUpdate:modelValue":t[5]||(t[5]=l=>e.unref(o).isSelfBargain=l),class:"ml-4",disabled:e.unref(c)},{default:e.withCtx(()=>[e.createVNode(N,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("否")]),_:1}),e.createVNode(N,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("是")]),_:1})]),_:1},8,["modelValue","disabled"]),re]),_:1}),e.createVNode(r,{label:"砍价用户类型",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:e.unref(o).userType,"onUpdate:modelValue":t[6]||(t[6]=l=>e.unref(o).userType=l),class:"ml-4",disabled:e.unref(c)},{default:e.withCtx(()=>[e.createVNode(N,{label:"UNLIMITED"},{default:e.withCtx(()=>[e.createTextVNode("不限")]),_:1}),e.createVNode(N,{label:"NEW_USER"},{default:e.withCtx(()=>[e.createTextVNode("新用户")]),_:1})]),_:1},8,["modelValue","disabled"]),de]),_:1}),e.createVNode(r,{label:"活动预热"},{default:e.withCtx(()=>[ie,e.createVNode(G,{modelValue:e.unref(o).activityPreheat,"onUpdate:modelValue":t[7]||(t[7]=l=>e.unref(o).activityPreheat=l),style:{width:"135px",margin:"0 10px"},maxlength:"15",placeholder:"请选择",disabled:e.unref(c)},{default:e.withCtx(()=>[e.createVNode(w,{label:"请选择",value:0}),e.createVNode(w,{label:"1小时",value:1}),e.createVNode(w,{label:"2小时",value:2}),e.createVNode(w,{label:"3小时",value:3}),e.createVNode(w,{label:"4小时",value:4}),e.createVNode(w,{label:"5小时",value:5})]),_:1},8,["modelValue","disabled"]),se,ce]),_:1}),e.createVNode(r,{label:"使用优惠"},{default:e.withCtx(()=>[e.createElementVNode("div",me,[e.createVNode(A,{modelValue:e.unref(o).stackable.vip,"onUpdate:modelValue":t[8]||(t[8]=l=>e.unref(o).stackable.vip=l),label:"会员价",disabled:e.unref(c)},null,8,["modelValue","disabled"]),e.createVNode(A,{modelValue:e.unref(o).stackable.coupon,"onUpdate:modelValue":t[9]||(t[9]=l=>e.unref(o).stackable.coupon=l),label:"优惠券",disabled:e.unref(c)},null,8,["modelValue","disabled"]),e.createVNode(A,{modelValue:e.unref(o).stackable.full,"onUpdate:modelValue":t[10]||(t[10]=l=>e.unref(o).stackable.full=l),label:"满减",disabled:e.unref(c)},null,8,["modelValue","disabled"]),pe])]),_:1}),e.createVNode(r,{label:"单次砍价金额范围",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createElementVNode("div",fe,[e.createVNode(B,{modelValue:e.unref(o).helpCutAmount,"onUpdate:modelValue":t[11]||(t[11]=l=>e.unref(o).helpCutAmount=l),class:"ml-4",disabled:e.unref(c)},{default:e.withCtx(()=>[e.createVNode(N,{label:"RANDOM_BARGAIN"},{default:e.withCtx(()=>[e.createTextVNode("随机砍价")]),_:1}),e.createVNode(N,{label:"FIX_BARGAIN"},{default:e.withCtx(()=>[e.createTextVNode("固定砍价")]),_:1})]),_:1},8,["modelValue","disabled"])]),ue]),_:1}),e.createVNode(r,{label:"活动商品",required:""},{default:e.withCtx(()=>[e.createVNode(S,{type:"primary",round:"",plain:"",disabled:e.unref(c),onClick:u},{default:e.withCtx(()=>[e.createTextVNode(" 选择商品 ")]),_:1},8,["disabled"]),_e]),_:1})]),_:1},8,["model","rules"]),e.createVNode(v,{ref_key:"selectGoodsTableRef",ref:h,"product-list":M.value,"is-edit":e.unref(c),"flat-good-list":U.value},null,8,["product-list","is-edit","flat-good-list"]),e.createVNode(be,{justify:"center",style:{"margin-top":"60px"}},{default:e.withCtx(()=>[e.createVNode(S,{round:"",plain:"",onClick:t[12]||(t[12]=l=>e.unref(E).back())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.unref(c)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(S,{key:0,type:"primary",round:"",onClick:s},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1}))]),_:1})])}}}),xe="";return H(ge,[["__scopeId","data-v-52566479"]])});
