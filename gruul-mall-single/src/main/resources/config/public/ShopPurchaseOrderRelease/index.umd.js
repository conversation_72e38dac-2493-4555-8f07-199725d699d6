(function(e,F){typeof exports=="object"&&typeof module<"u"?module.exports=F(require("vue"),require("vue-router"),require("lodash"),require("@/composables/useConvert"),require("element-plus"),require("@element-plus/icons-vue"),require("@/components/q-upload/q-upload.vue"),require("@/store/modules/shopInfo"),require("@/apis/upload"),require("@/components/element-plus-table-drag/drag-table.vue"),require("vue-draggable-next"),require("@/apis/http"),require("@/components/q-edit/q-edit.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","lodash","@/composables/useConvert","element-plus","@element-plus/icons-vue","@/components/q-upload/q-upload.vue","@/store/modules/shopInfo","@/apis/upload","@/components/element-plus-table-drag/drag-table.vue","vue-draggable-next","@/apis/http","@/components/q-edit/q-edit.vue"],F):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopPurchaseOrderRelease=F(e.ShopPurchaseOrderReleaseContext.Vue,e.ShopPurchaseOrderReleaseContext.VueRouter,e.ShopPurchaseOrderReleaseContext.Lodash,e.ShopPurchaseOrderReleaseContext.UseConvert,e.ShopPurchaseOrderReleaseContext.ElementPlus,e.ShopPurchaseOrderReleaseContext.ElementPlusIconsVue,e.ShopPurchaseOrderReleaseContext.QUpload,e.ShopPurchaseOrderReleaseContext.ShopInfoStore,e.ShopPurchaseOrderReleaseContext.UploadAPI,e.ShopPurchaseOrderReleaseContext.dragTable,e.ShopPurchaseOrderReleaseContext.VueDraggableNext,e.ShopPurchaseOrderReleaseContext.Request,e.ShopPurchaseOrderReleaseContext.QEdit))})(this,function(e,F,ee,re,R,Z,Q,me,ye,ne,fe,z,Ce){"use strict";var _e=document.createElement("style");_e.textContent=`.navLine[data-v-5dda9453]{margin:25px 0;height:40px;line-height:40px;background-color:#f8f8f8;padding-left:15px;font-weight:700}.release[data-v-5dda9453]{box-sizing:border-box;padding-bottom:62px}.release__step[data-v-5dda9453]{width:400px;margin:0 auto}.release__tool[data-v-5dda9453]{width:1010px;align-items:center;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;margin-left:-14px;z-index:100}[data-v-5dda9453] .el-step__head.is-process{color:#fff}[data-v-5dda9453] .el-step__head.is-process .el-step__icon.is-text{background-color:#4b80ff;border-color:#4b80ff}[data-v-5dda9453] .el-step__title.is-process{font-weight:400}[data-v-5dda9453] .el-step__title.is-finish{color:var(--el-text-color-primary)}[data-v-5dda9453] .el-step__head.is-finish{color:#fff}[data-v-5dda9453] .el-step__head.is-finish .el-step__icon.is-text{background-color:#4b80ff;border-color:#4b80ff}[data-v-5dda9453] .el-step__head.is-wait{color:#fff}[data-v-5dda9453] .el-step__head.is-wait .el-step__icon.is-text{background-color:#d7dce4;border-color:#d7dce4}[data-v-5dda9453] .el-step.is-center .el-step__line{left:65%;right:-30%}.title[data-v-58dc1461]{display:flex;justify-content:flex-start;align-items:center}.title__release[data-v-58dc1461]{height:20px;color:#333;font-size:16px;line-height:20px;border-left:4px solid #4b80ff;padding-left:8px}.norm[data-v-2cdac33b]{font-size:14px;margin:20px 0;display:flex;justify-content:"flex-start";align-items:"flex-start"}.norm__title[data-v-2cdac33b]{margin:0 20px 0 30px}.norm__item[data-v-2cdac33b]{color:#606266}.norm__item__name[data-v-2cdac33b]{display:flex}.norm__item__name--del[data-v-2cdac33b]{margin-left:10px}.norm__item__value[data-v-2cdac33b]{display:flex;justify-content:"flex-start";align-items:center}.norm__item__value--mg34[data-v-2cdac33b]{margin-right:34px}.norm__item__value__content[data-v-2cdac33b]{display:flex;justify-content:center;align-items:center;margin:20px 0;cursor:move}.specDetail[data-v-2cdac33b]{font-size:14px;text-align:center;display:flex;justify-content:"flex-start";align-items:"flex-start"}.specDetail__table[data-v-2cdac33b]{width:800px;display:flex;flex-direction:column;overflow-x:auto;background:#fcfcfc}.specDetail__title[data-v-2cdac33b]{margin:0 30px}.specDetail__table__title[data-v-2cdac33b]{line-height:50px;display:flex;justify-content:"flex-start";align-items:"flex-start"}.specDetail__table__title--primary[data-v-2cdac33b]{flex-shrink:0;width:150px}.specDetail__table__title--custom[data-v-2cdac33b]{flex-shrink:0;width:100px}.specDetail__table__title--header[data-v-2cdac33b]{display:flex;justify-content:center;align-items:center;flex-shrink:0}.specDetail__table__item[data-v-2cdac33b]{display:flex;justify-content:"flex-start";align-items:"flex-start"}.specDetail__table__item--font[data-v-2cdac33b]{flex-shrink:0;width:100px;line-height:70px;color:#606266}.specDetail__table__item--input[data-v-2cdac33b]{flex-shrink:0;width:150px;display:flex;justify-content:center;align-items:center}.specDetail__table__item--input>.el-input[data-v-2cdac33b]{width:80%}.specDetail__table__item--input>.el-input__inner[data-v-2cdac33b]{width:80%}.specDetail__table__item--upload[data-v-2cdac33b]{width:150px;display:flex;justify-content:center;align-items:center}.specDetail__table__item .el-form-item--small.el-form-item[data-v-2cdac33b]{margin:0!important}.batch[data-v-2cdac33b]{margin:30px;display:flex;justify-content:flex-start;align-items:center}.batch__list[data-v-2cdac33b]{display:flex;justify-content:flex-start;align-items:center;cursor:pointer;color:#409eff;margin-left:20px}.batch__list-item[data-v-2cdac33b]{display:flex;justify-content:center;align-items:center;margin-right:10px}.com__input--width[data-v-2cdac33b]{width:500px}.serveMsg[data-v-2cdac33b]{width:400px;display:flex}.FreightTemplateChoose[data-v-2cdac33b]{position:absolute}.brand[data-v-2cdac33b]{width:calc(100% - 120px);border:1px solid #d5d5d5;border-radius:2px;font-size:12px;color:#2e99f3;height:30px;line-height:30px;padding:0 5px;display:flex;justify-content:space-between;align-items:center}.brandTable[data-v-2cdac33b]{border:1px solid #e6e6e6;margin-top:15px;max-height:450px;overflow:hidden;overflow-y:auto}.brandTable__head[data-v-2cdac33b]{height:30px;background:#f6f6f6;display:flex;font-size:14px;color:#6a6a6a;line-height:30px}.brandTable__content[data-v-2cdac33b]{display:flex;align-items:center;width:580px}.brandTable__content--item1[data-v-2cdac33b]{margin-left:24px;width:198px;color:#6a6a6a;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.brandTable__content--item2[data-v-2cdac33b]{background:#fef6f3;border-radius:4px;font-size:14px;width:76px;height:26px;line-height:26px;color:#f4a584;text-align:center}.brandTable__content--item3[data-v-2cdac33b]{margin-left:179px;width:50px;height:50px}.inputWidth[data-v-2cdac33b]{width:calc(100% - 120px)}.com__attr[data-v-2cdac33b]{margin-top:10px;width:620px;padding:20px;border:1px solid #d7d7d7}.com__attr-header[data-v-2cdac33b]{display:flex;justify-content:space-between;align-items:center}.com__attr-content[data-v-2cdac33b]{display:flex;justify-content:space-between;align-items:center;margin:10px 0}.com__attr-input[data-v-2cdac33b]{width:230px}.com__attr-del[data-v-2cdac33b]{color:red}.com__imgText[data-v-2cdac33b]{position:absolute;right:0;bottom:0;font-size:12px;text-align:center;width:100%;background-color:#0000004d;border-radius:0 0 6px 6px;color:#fff}.goodsType .title[data-v-2cdac33b]{font-weight:400;font-size:16px;margin-bottom:20px}.goodsType .is-active .title[data-v-2cdac33b]{font-weight:700}.el-link.is-disabled[data-v-2cdac33b]{color:#999}.mleft-form-item .el-form-item__content{padding-left:100px}.el-popper.is-effect-tooltip{background-color:#fff;color:#9d9d9d;box-shadow:0 0 10px #00000026}.el-popper.is-effect-tooltip .el-popper__arrow:before{background:#fff;right:0}.goodsType .el-radio-button__original-radio:checked+.el-radio-button__inner{color:#333;background-color:unset}.goodsType .el-radio-button__inner{padding:20px 40px}.avatar-uploader .el-upload{border:1px dashed var(--el-border-color);border-radius:6px;cursor:pointer;position:relative;overflow:hidden;transition:var(--el-transition-duration-fast)}.main-uploader .el-upload{border:1px dashed var(--el-border-color);border-radius:6px;cursor:pointer;position:relative;overflow:hidden;width:100px;height:100px;transition:var(--el-transition-duration-fast)}.avatar-uploader .el-upload:hover,.main-uploader .el-upload:hover{border-color:var(--el-color-primary)}.el-icon.avatar-uploader-icon{font-size:28px;color:#8c939d;width:250px;height:120px;text-align:center}.el-icon.main-uploader-icon{font-size:28px;color:#8c939d;width:100px;height:100px;text-align:center}.norm__list[data-v-aad43b37]{padding-bottom:30px}.el-row[data-v-aad43b37]{padding:15px;background-color:#f8f8f8}.norm__item__name--left[data-v-aad43b37]{display:flex}.norm__item__name--left>.el-form-item--small.el-form-item[data-v-aad43b37]{margin:0!important}.button-new-tag[data-v-aad43b37]{margin-left:10px!important;height:32px;line-height:30px;padding-top:0;padding-bottom:0;margin-top:0!important}.el-tag[data-v-aad43b37]{margin-bottom:10px}.el-tag+.el-tag[data-v-aad43b37]{margin-left:10px}.norm__item__value__content[data-v-aad43b37],.el-form-item[data-v-aad43b37]{margin:0 9px}.norm__item__value__content[data-v-aad43b37]{justify-content:start}.el-button[data-v-aad43b37]{margin:10px 9px}.input-new-tag[data-v-aad43b37]{width:90px;margin-left:10px;vertical-align:bottom}.avatar-uploader .avatar[data-v-6b165c52]{width:150px;height:50px;display:block}.avatar-uploader .el-upload[data-v-6b165c52]{border:1px dashed var(--el-border-color);border-radius:6px;cursor:pointer;position:relative;overflow:hidden;transition:var(--el-transition-duration-fast)}.avatar-uploader .el-upload[data-v-6b165c52]:hover{border-color:var(--el-color-primary)}.el-icon.avatar-uploader-icon[data-v-6b165c52]{font-size:28px;color:#8c939d;width:50px;height:50px;text-align:center;border:1px dashed #ccc;border-radius:10px}.input-group[data-v-6b165c52]{display:flex;align-items:center}.input-group__prefix[data-v-6b165c52]{background-color:#f7f7f7;height:32px;line-height:32px;padding:0 9px;border:1px solid #dcdfe6;border-radius:4px;display:inline-block}.table-container[data-v-6b165c52]{padding-bottom:30px}[data-v-6b165c52] .qupload{width:fit-content;margin:0 auto}.spac[data-v-5c3e58cd]{background-color:#f8f8f8;padding-top:1px}.spac .goodsAttribut[data-v-5c3e58cd]{background-color:#fff;padding:10px;border-radius:5px;margin-top:20px}.spac .goodsAttribut .title[data-v-5c3e58cd]{font-weight:700;background-color:#fff;padding:15px;margin:0 -10px}.spac .goodsAttribut .el-button[data-v-5c3e58cd]{margin:0 20px}.spac .goodsAttribut .line[data-v-5c3e58cd]{display:flex;align-items:center;margin:20px 0}.spac .goodsAttribut .line .item[data-v-5c3e58cd]{border:1px solid #bbbbbb;padding:5px;margin-right:7px;border-radius:3px}.spac .goodsAttribut .line .deleteItem[data-v-5c3e58cd]{cursor:pointer;font-weight:700}.spac .goodsAttribut .el-tag[data-v-5c3e58cd]{margin:5px}.norm{font-size:14px;margin:20px 0;display:flex;justify-content:"flex-start";align-items:"flex-start"}.norm__title{margin:0 20px 0 30px}.norm__item{color:#606266}.norm__item__name{display:flex}.norm__item__name--del{margin-left:10px}.norm__item__value{display:flex;justify-content:"flex-start";align-items:center}.norm__item__value--mg34{margin-right:34px}.norm__item__value__content{display:flex;justify-content:center;align-items:center;margin:20px 0;cursor:move}.specDetail{font-size:14px;text-align:center;display:flex;justify-content:"flex-start";align-items:"flex-start"}.specDetail__table{width:800px;display:flex;flex-direction:column;overflow-x:auto;background:#fcfcfc}.specDetail__title{margin:0 30px}.specDetail__table__title{line-height:50px;display:flex;justify-content:"flex-start";align-items:"flex-start"}.specDetail__table__title--primary{flex-shrink:0;width:150px}.specDetail__table__title--custom{flex-shrink:0;width:100px}.specDetail__table__title--header{display:flex;justify-content:center;align-items:center;flex-shrink:0}.specDetail__table__item{display:flex;justify-content:"flex-start";align-items:"flex-start"}.specDetail__table__item--font{flex-shrink:0;width:100px;line-height:70px;color:#606266}.specDetail__table__item--input{flex-shrink:0;width:150px;display:flex;justify-content:center;align-items:center}.specDetail__table__item--input>.el-input{width:80%}.specDetail__table__item--input>.el-input__inner{width:80%}.specDetail__table__item--upload{width:150px;display:flex;justify-content:center;align-items:center}.specDetail__table__item .el-form-item--small.el-form-item{margin:0!important}.batch{margin:30px;display:flex;justify-content:flex-start;align-items:center}.batch__list{display:flex;justify-content:flex-start;align-items:center;cursor:pointer;color:#409eff;margin-left:20px}.batch__list-item{display:flex;justify-content:center;align-items:center;margin-right:10px}.com__input--width{width:500px}.serveMsg{width:400px;display:flex}.inputnumber{position:relative}.inputnumber__icon{width:34px;height:30px;position:absolute;right:1px;top:1px;background:#e6e8eb;color:#909399;text-align:center}.input_number .el-input__inner{text-align:left}.com__input--width{width:100%}.info[data-v-668d63d0]{display:flex}.info__edit[data-v-668d63d0]{border:1px solid #ccc;margin-top:18px;z-index:99}.info__edit[data-v-668d63d0] ::-webkit-scrollbar{display:none}
`,document.head.appendChild(_e);const Ne=(d,g)=>z.get({url:`addon-supplier/supplier/order/publish/product/${d}`,params:{supplierId:g}}),ke=(d,g)=>z.get({url:`gruul-mall-storage/storage/shop/${d}/product/${g}`}),we=()=>z.get({url:"gruul-mall-addon-platform/platform/shop/signing/category/choosable/list"}),Ie=(d,g)=>z.get({url:"gruul-mall-freight/logistics/template/get/list/",params:{current:d,size:g}}),Te=d=>z.post({url:"gruul-mall-goods/manager/product/issue",data:d}),ue=d=>z.get({url:"gruul-mall-goods/manager/feature/list",params:d}),Ee=d=>z.get({url:"gruul-mall-goods/manager/supplier/list",params:d}),Be=d=>z.get({url:"gruul-mall-goods/goods/product/category",params:d}),Se=d=>z.get({url:"gruul-mall-search/search/brand/brandInfo",params:d}),De=()=>z.get({url:"addon-intra-city-distribution/intraCityDistribution/config/"}),Le={class:"release"},Ae={class:"release__step"},Pe={class:"release__tool"},Ue=e.defineComponent({__name:"ShopPurchaseOrderRelease",setup(d){const g=e.ref("NewBasicInfo"),x=F.useRoute(),T=F.useRouter(),{divTenThousand:P,mulTenThousand:I}=re(),A=e.ref({name:"",saleDescribe:"",platformCategoryId:"",categoryId:"",providerId:"",widePic:"",distributionMode:[],videoUrl:"",albumPics:"",productType:"REAL_PRODUCT",specGroups:[],platformCategory:{one:null,two:null,three:null},shopCategory:{one:null,two:null,three:null},skus:[{id:"",image:"",initSalesVolume:0,limitNum:0,limitType:"UNLIMITED",price:0,productId:"",initStock:0,salePrice:0,shopId:"",stockType:"UNLIMITED",weight:0,specs:[]}],productParameters:[],productAttributes:[],serviceIds:[],detail:"",freightTemplateId:"0",status:"SELL_ON",brandId:"",sellType:"PURCHASE"}),t=e.ref([]),O=e.ref(""),V=e.ref(""),C=e.ref("");e.provide("form",{submitForm:A,commodityImgList:t,copyGoodsJD:C,copyGoodsAL:V,copyGoodsTB:O});const D=e.ref(),h={NewBasicInfo:e.defineAsyncComponent(()=>Promise.resolve().then(()=>st)),NewSaleInfo:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Ft)),NewProductInfo:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Wt))},r={NewBasicInfo:{prev:"",next:"NewSaleInfo",stepIndex:0},NewSaleInfo:{prev:"NewBasicInfo",next:"NewProductInfo",stepIndex:1},NewProductInfo:{prev:"NewSaleInfo",next:"",stepIndex:2}},c=[];F.onBeforeRouteLeave((l,y,E)=>{["/goods/category","/goods/supplier","/goods/attribute","/freight/logistics","/goods/purchase"].includes(l.path)||(A.value={name:"",saleDescribe:"",platformCategoryId:"",categoryId:"",providerId:"",distributionMode:[],productType:"REAL_PRODUCT",widePic:"",videoUrl:"",albumPics:"",specGroups:[],platformCategory:{one:null,two:null,three:null},shopCategory:{one:null,two:null,three:null},skus:[{id:"",image:"",initSalesVolume:0,limitNum:0,limitType:"UNLIMITED",price:0,productId:"",initStock:0,salePrice:0,shopId:"",stockType:"UNLIMITED",weight:0,specs:[]}],productParameters:[],productAttributes:[],serviceIds:[],detail:"",freightTemplateId:"0",status:"SELL_ON",brandId:"",sellType:"PURCHASE"},t.value=[],C.value="",V.value="",O.value="",g.value="NewBasicInfo"),E()});const _=e.computed(()=>r[g.value]),m=e.computed(()=>_.value.stepIndex),N=e.computed(()=>_.value.prev),i=e.computed(()=>_.value.next),w=()=>{c[m.value]||c.push(D.value)},s=()=>c[m.value]?c[m.value]:D.value,o=()=>{w(),g.value=N.value},f=async()=>{var y,E,W;let l=!0;if(i.value==="NewProductInfo"){const q=(y=s())==null?void 0:y.GoodOnlyRef.handleSpacObj();if(q){const{productAttribute:G,productParameter:L}=q;A.value.productAttributes=G,A.value.productParameters=L}A.value.specGroups.length!==0&&(l=B())}l&&((E=s())!=null&&E.currentFormRef)&&((W=s())==null?void 0:W.currentFormRef).validate(G=>{G&&(w(),g.value=i.value)})};function B(){const l=A.value.skus;if(l.length>0){const y=l.map(E=>E.price<=0?"请修改指导价且大于0":Number(E.salePrice)<=0?"请修改实售价且大于0":Number(E.salePrice)>Number(E.price)?"商品实售价大于指导价":"").filter(Boolean);if(y.length>0)return R.ElMessage.error(y[0]),!1}else return R.ElMessage.error("请完善商品规格信息"),!1;return!0}const n=()=>{T.replace("/goods/purchase")},k=async()=>{const l=ee.cloneDeep(A.value);Array.isArray(l.categoryId)&&(l.categoryId=l.categoryId[2]),Array.isArray(l.platformCategoryId)&&(l.platformCategoryId=l.platformCategoryId[2]),l.skus=l.skus.map(E=>(E.price=I(E.price).toNumber(),E.salePrice=I(E.salePrice).toNumber(),E)),l.specGroups.length===0&&(l.skus[0].image=l.albumPics.split(",")[0]),l.status="SELL_ON",l.supplierId=x.query.supplierId,l.sellType="PURCHASE",l.id=x.query.id;const{code:y}=await Te(l);if(y!==200)return R.ElMessage.error("上传商品失败");R.ElMessage.success("上传成功"),c[0].resetServiceAssuranceList(),T.replace("/goods/purchase")};return(l,y)=>{var G,L;const E=e.resolveComponent("el-step"),W=e.resolveComponent("el-steps"),q=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",Le,[e.createElementVNode("div",Ae,[e.createVNode(W,{active:m.value,"align-center":""},{default:e.withCtx(()=>[e.createVNode(E,{title:"1.编辑基本信息"}),e.createVNode(E,{title:"2.编辑销售信息"}),e.createVNode(E,{title:"3.编辑商品信息"})]),_:1},8,["active"])]),(e.openBlock(),e.createBlock(e.KeepAlive,{exclude:["NewProductInfo"]},[(e.openBlock(),e.createBlock(e.resolveDynamicComponent(h[g.value]),{ref_key:"componentRef",ref:D},null,512))],1024)),e.createElementVNode("div",Pe,[N.value!==""?(e.openBlock(),e.createBlock(q,{key:0,type:"primary",round:"",onClick:o},{default:e.withCtx(()=>[e.createTextVNode("上一步")]),_:1})):e.createCommentVNode("",!0),i.value!==""?(e.openBlock(),e.createBlock(q,{key:1,type:"primary",round:"",onClick:f},{default:e.withCtx(()=>[e.createTextVNode("下一步")]),_:1})):e.createCommentVNode("",!0),i.value===""&&((G=A.value)==null?void 0:G.status)==="PLATFORM_SELL_OFF"?(e.openBlock(),e.createBlock(q,{key:2,type:"primary",round:"",onClickOnce:n},{default:e.withCtx(()=>[e.createTextVNode(" 返回 ")]),_:1})):e.createCommentVNode("",!0),i.value===""&&((L=A.value)==null?void 0:L.status)!=="PLATFORM_SELL_OFF"?(e.openBlock(),e.createBlock(q,{key:3,type:"primary",round:"",onClick:k},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(x).query.id?"更新":"上架"),1)]),_:1})):e.createCommentVNode("",!0)])])}}}),xo="",J=(d,g)=>{const x=d.__vccOpts||d;for(const[T,P]of g)x[T]=P;return x},Re=J(Ue,[["__scopeId","data-v-5dda9453"]]),Me={class:"title"},$e={class:"title__release"},Oe=e.defineComponent({__name:"ReleaseTitle",props:{title:{default:""}},setup(d){const g=d;return(x,T)=>(e.openBlock(),e.createElementBlock("div",Me,[e.createElementVNode("div",$e,e.toDisplayString(g.title),1),e.renderSlot(x.$slots,"default",{},void 0,!0)]))}}),bo="",Y=J(Oe,[["__scopeId","data-v-58dc1461"]]),je=[{name:"全场包邮",state:!1,text:"所有商品均无条件包邮",enum:"NO_FREIGHT"},{name:"7天退换",state:!1,text:"商家承诺7天无理由退换货",enum:"SEVEN_END_BACK"},{name:"48小时发货",state:!1,text:"商家承诺订单在48小时内发布",enum:"TWO_DAY_SEND"},{name:"假一赔十",state:!1,text:"若收到商品是假冒品牌，可获得十倍赔偿",enum:"FAKE_COMPENSATE"},{name:"正品保证",state:!1,text:"商家承诺商品正品质量",enum:"ALL_ENSURE"}],Fe=d=>{const g=e.ref(je);function x(){d.value.serviceIds=g.value.filter(I=>I.state).map(I=>I.enum)}function T(){d.value.serviceIds.length&&(g.value=g.value.map(I=>(d.value.serviceIds.includes(I.enum)&&(I.state=!0),I)))}return e.onActivated(()=>T()),{serviceAssuranceList:g,updateserviceIds:x,resetServiceAssuranceList:()=>{for(let I=0;I<g.value.length;I++)g.value[I].state=!1}}},K=d=>(e.pushScopeId("data-v-2cdac33b"),d=d(),e.popScopeId(),d),qe={class:"brand"},Ge={key:1},ze=K(()=>e.createElementVNode("div",{style:{color:"#9d9d9d","font-size":"14px"}},"尺寸建议750x350（长方形模式）像素以上，大小1M以下",-1)),He=["src"],We=K(()=>e.createElementVNode("div",{style:{color:"#9d9d9d","font-size":"14px"}},"大小为5M以下",-1)),Ye={key:1,class:"com__imgText"},Je=K(()=>e.createElementVNode("div",{style:{color:"rgba(69, 64, 60, 0.6)","font-size":"12px"}}," 尺寸建议750x750（正方形模式）像素以上，大小1M以下，最多6张 (可拖拽图片调整顺序 ) ",-1)),Ke={key:0},Xe=K(()=>e.createElementVNode("br",null,null,-1)),Ze=K(()=>e.createElementVNode("br",null,null,-1)),Qe={class:"serveMsg"},ve={style:{width:"120px"}},et={style:{color:"#c6c6c6"}},tt={style:{display:"flex","justify-content":"end"}},ot={class:"brandTable"},at=K(()=>e.createElementVNode("div",{class:"brandTable__head"},[e.createElementVNode("div",{style:{"margin-left":"44px"}},"品牌名称"),e.createElementVNode("div",{style:{"margin-left":"166px"}},"状态"),e.createElementVNode("div",{style:{"margin-left":"214px"}},"图片")],-1)),lt={class:"brandTable__content"},rt={class:"brandTable__content--item1"},nt=e.defineComponent({__name:"NewBasicInfo",setup(d,{expose:g}){const x=F.useRoute(),T=F.useRouter(),P=e.ref(),I={}.VITE_BASE_URL+"gruul-mall-carrier-pigeon/oss/upload",A=e.inject("form"),t=A.submitForm,O=e.ref([]),V=A.commodityImgList,C=e.ref([]),D=e.ref([]),h=e.ref([]),r=e.ref(!1),c=e.ref(""),_=e.ref([]),m=e.reactive({size:1e3,current:1,total:0}),N=e.ref(""),i=e.ref(""),w=e.ref(null),s=e.ref(null),o={expandTrigger:"hover",label:"name",value:"id"},f=e.reactive({name:[{required:!0,message:"请填写商品名称",trigger:"blur"}],categoryId:[{required:!0,message:"请选择店铺分类",trigger:"blur"}],albumPics:[{required:!0,message:"请添加商品主图",trigger:"blur"}],platformCategoryId:[{required:!0,message:"请选择平台分类",trigger:"change"}],distributionMode:[{required:!0,message:"请选择配送方式",trigger:"change",type:"array"}]}),B=me.useShopInfoStore(),n=e.computed(()=>B.shopInfo.mode!=="O2O");e.onActivated(async()=>{T.options.history.state.forward!=="/freight/logistics?from=releaseGoods"&&await Zt(),he(),Promise.all([Kt(),Xt()]).then(()=>{te("platformCategory").then(()=>{}),te("shopCategory").then(()=>{})}),Yt(),oo()}),e.onMounted(()=>{E()}),e.watch(t.value,(u,a)=>{Qt()},{immediate:!0});const{serviceAssuranceList:k,updateserviceIds:l,resetServiceAssuranceList:y}=Fe(t);async function E(){const{code:u,data:a}=await Ie(1,1e3);if(u!==200)return R.ElMessage.error("获取物流列表失败");O.value=a.records}const W=u=>{const a=["video/mp4"],U=u.size<5242880;return a.includes(u.type)?U?!0:(R.ElMessage.error("上传视频大小不超过5M!"),!1):(R.ElMessage.error("上传视频只能是 mp4 格式!"),!1)},q=(u,a)=>{t.value.videoUrl=u.data},G=(u,a)=>{V.value.push(u),t.value.albumPics=ie(V)},L=u=>{V.value.splice(u,1),t.value.albumPics=ie(V)};function ie(u){return u.value.join(",")}async function Yt(){const{code:u,data:a}=await Ee({current:1,size:1e3,status:"REVIEW"});if(u!==200){R.ElMessage.error("获取供应商失败");return}C.value=a.records}const Jt=()=>{t.value.brandId="",i.value="",N.value=""};async function Kt(){const{code:u,data:a,success:U}=await we();if(u!==200){R.ElMessage.error("获取平台分类失败");return}D.value=de(1,a)}async function Xt(){const{code:u,data:a}=await Be({current:1,size:500});if(u!==200){R.ElMessage.error("获取店铺分类失败");return}h.value=de(1,a.records)}async function Zt(){var u,a,U,M,S;if(x.query.id){const{code:j,data:b}=await Ne(x.query.id,x.query.supplierId);if(j!==200){R.ElMessage.error("获取商品信息失败");return}V.value=(u=b==null?void 0:b.product)==null?void 0:u.albumPics.split(",");const $=(b==null?void 0:b.product)||{};$.freightTemplateId="0",t.value=Object.assign(t.value,$),(U=(a=b==null?void 0:b.product)==null?void 0:a.distributionMode)!=null&&U.includes("VIRTUAL")&&(t.value.distributionMode=["VIRTUAL"]),t.value.distributionMode=[],oe.value=(S=(M=b==null?void 0:b.product)==null?void 0:M.distributionMode)==null?void 0:S.includes("VIRTUAL")}}function de(u,a){const U=u===3;for(let M=0;M<a.length;){const S=a[M];if(U){S.disabled=!1,M++;continue}const j=S.children||S.secondCategoryVos||S.categoryThirdlyVos;delete S.secondCategoryVos,delete S.categoryThirdlyVos;const b=!j||j.length===0;if(S.disabled=b,b){a.splice(M,1);continue}if(de(u+1,j),j.length===0){a.splice(M,1);continue}S.children=j,M++}return a}function Qt(){let u=[];t.value.albumPics&&(u=t.value.albumPics.split(",")),V.value=u}const te=async u=>{const a=u==="platformCategory",U=a?w.value:s.value;if(!U)return;const M=await U.getCheckedNodes();let S=t.value[u];if(!S){const $={one:null,two:null,three:null};t.value[u]=$,S=$}if(!M||M.length===0){S.one=null,S.two=null,S.three=null;return}const j=M[0].pathValues,b=!j||j.length===0;if(a){const $=M[0].pathNodes[0].data.categoryId;S.one=b?null:$}else S.one=b?null:j[0];S.two=b?null:j[1],S.three=b?null:j[2]};async function he(){const{code:u,data:a}=await Se({...m,brandName:c.value});if(u!==200){R.ElMessage.error("获取品牌列表失败");return}_.value=a.records,m.total=a.total,a.records.forEach(U=>{U.id===t.value.brandId&&(i.value=U.brandName,N.value=U.id)})}const vt=()=>{m.current=1,he()},eo=()=>{r.value=!1},to=()=>{t.value.brandId=N.value,_.value.forEach(u=>{u.id===N.value&&(i.value=u.brandName)}),r.value=!1},xe=e.ref(!1);async function oo(){const u=await De();xe.value=!!u.data}const oe=e.ref(!1),ao=u=>{u.includes("VIRTUAL")&&(t.value.distributionMode=["VIRTUAL"]),oe.value=u.includes("VIRTUAL")};e.ref(3),g({currentFormRef:P,resetServiceAssuranceList:y});const lo=()=>{t.value.albumPics=ie(V)};return(u,a)=>{const U=e.resolveComponent("el-option"),M=e.resolveComponent("el-select"),S=e.resolveComponent("el-icon"),j=e.resolveComponent("el-tooltip"),b=e.resolveComponent("el-form-item"),$=e.resolveComponent("el-col"),ce=e.resolveComponent("el-input"),be=e.resolveComponent("el-cascader"),ae=e.resolveComponent("el-link"),ro=e.resolveComponent("el-tag"),v=e.resolveComponent("el-row"),no=e.resolveComponent("i-ep-plus"),so=e.resolveComponent("el-upload"),io=e.resolveComponent("i-ep-circle-close"),co=e.resolveComponent("VueDraggableNext"),le=e.resolveComponent("el-checkbox"),po=e.resolveComponent("el-checkbox-group"),mo=e.resolveComponent("logistics-setting"),fo=e.resolveComponent("el-form"),pe=e.resolveComponent("el-button"),_o=e.resolveComponent("el-image"),uo=e.resolveComponent("el-radio"),go=e.resolveComponent("el-radio-group"),ho=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(Y,{title:"基本信息"}),e.createVNode(fo,{ref_key:"currentFormRef",ref:P,model:e.unref(t),rules:f},{default:e.withCtx(()=>[e.createVNode(v,{gutter:8},{default:e.withCtx(()=>[e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"商品类型","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(M,{modelValue:e.unref(t).productType,"onUpdate:modelValue":a[0]||(a[0]=p=>e.unref(t).productType=p),class:"inputWidth"},{default:e.withCtx(()=>[e.createVNode(U,{label:"实物商品（快递/同城/自提）",value:"REAL_PRODUCT"}),n.value?(e.openBlock(),e.createBlock(U,{key:0,label:"虚拟商品（无需物流）",value:"VIRTUAL_PRODUCT"})):e.createCommentVNode("",!0)]),_:1},8,["modelValue"]),e.createVNode(j,{content:"不同的商品类型可编辑的字段内容不同，商品类型一旦发布后将不可更改！",effect:"effect-tooltip",placement:"top"},{default:e.withCtx(()=>[e.createVNode(S,{color:"#999",size:"14px",style:{"margin-left":"24px"}},{default:e.withCtx(()=>[e.createVNode(e.unref(Z.QuestionFilled))]),_:1})]),_:1})]),_:1})]),_:1}),e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"商品名称","label-width":"100px",prop:"name"},{default:e.withCtx(()=>[e.createVNode(ce,{modelValue:e.unref(t).name,"onUpdate:modelValue":a[1]||(a[1]=p=>e.unref(t).name=p),class:"inputWidth",maxlength:"55",placeholder:"请填写商品名称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"平台类目","label-width":"100px",prop:"platformCategoryId"},{default:e.withCtx(()=>[e.createVNode(be,{ref_key:"platformCategoryRef",ref:w,modelValue:e.unref(t).platformCategoryId,"onUpdate:modelValue":a[2]||(a[2]=p=>e.unref(t).platformCategoryId=p),options:D.value,props:o,class:"inputWidth",clearable:"",placeholder:"请选择平台类目","show-all-levels":"",style:{width:"calc(100% - 120px)"},onChange:a[3]||(a[3]=()=>te("platformCategory"))},null,8,["modelValue","options"])]),_:1})]),_:1}),e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"卖点描述","label-width":"100px",prop:"saleDescribe"},{default:e.withCtx(()=>[e.createVNode(ce,{modelValue:e.unref(t).saleDescribe,"onUpdate:modelValue":a[4]||(a[4]=p=>e.unref(t).saleDescribe=p),class:"inputWidth",maxlength:"60",placeholder:"请填写卖点描述"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"店铺类目","label-width":"100px",prop:"categoryId"},{default:e.withCtx(()=>[e.createVNode(be,{ref_key:"shopCategoryRef",ref:s,modelValue:e.unref(t).categoryId,"onUpdate:modelValue":a[5]||(a[5]=p=>e.unref(t).categoryId=p),options:h.value,props:o,class:"inputWidth",clearable:"",placeholder:"请选择店铺类目","show-all-levels":"",style:{width:"calc(100% - 120px)"},onChange:a[6]||(a[6]=()=>te("shopCategory"))},null,8,["modelValue","options"]),e.createVNode(ae,{underline:!1,style:{"margin-left":"15px"},type:"primary",onClick:a[7]||(a[7]=p=>e.unref(T).push({name:"goodsCategory",query:{from:"releaseGoods"}}))},{default:e.withCtx(()=>[e.createTextVNode(" 前往设置 ")]),_:1})]),_:1})]),_:1}),e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"供应商","label-width":"100px",prop:"providerId"},{default:e.withCtx(()=>[e.createVNode(M,{modelValue:e.unref(t).providerId,"onUpdate:modelValue":a[8]||(a[8]=p=>e.unref(t).providerId=p),class:"inputWidth",placeholder:"请选择供应商",style:{}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(C.value,p=>(e.openBlock(),e.createBlock(U,{key:p.id,label:p.name,value:p.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e.createVNode(ae,{underline:!1,style:{"margin-left":"15px"},type:"primary",onClick:a[9]||(a[9]=p=>e.unref(T).push({name:"goodsSupplier",query:{from:"releaseGoods"}}))},{default:e.withCtx(()=>[e.createTextVNode(" 前往设置 ")]),_:1})]),_:1})]),_:1}),e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"商品品牌","label-width":"100px"},{default:e.withCtx(()=>[e.createElementVNode("div",qe,[e.unref(t).brandId?(e.openBlock(),e.createBlock(ro,{key:0,closable:"",onClose:Jt},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(i.value),1)]),_:1})):(e.openBlock(),e.createElementBlock("div",Ge))]),e.createVNode(ae,{underline:!1,style:{"margin-left":"15px"},type:"primary",onClick:a[10]||(a[10]=p=>r.value=!0)},{default:e.withCtx(()=>[e.createTextVNode("选择+")]),_:1})]),_:1})]),_:1})]),_:1}),e.createVNode(v,{gutter:8},{default:e.withCtx(()=>[e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"商品大图","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(v,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(Q,{src:e.unref(t).widePic,"onUpdate:src":a[11]||(a[11]=p=>e.unref(t).widePic=p),format:{size:1},height:120,width:250},null,8,["src"])]),_:1}),ze]),_:1})]),_:1}),e.createVNode($,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{label:"商品视频","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(v,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(so,{action:I,"before-upload":W,"http-request":e.unref(ye.elementUploadRequest),"on-success":q,"show-file-list":!1,class:"avatar-uploader"},{default:e.withCtx(()=>[e.unref(t).videoUrl!==""?(e.openBlock(),e.createElementBlock("video",{key:0,src:e.unref(t).videoUrl,controls:"",style:{width:"250px",height:"120px"}},null,8,He)):(e.openBlock(),e.createBlock(S,{key:1,class:"avatar-uploader-icon"},{default:e.withCtx(()=>[e.createVNode(no)]),_:1}))]),_:1},8,["http-request"])]),_:1}),We]),_:1})]),_:1})]),_:1}),e.createVNode(b,{label:"商品主图","label-width":"100px",prop:"albumPics"},{default:e.withCtx(()=>[e.createVNode(v,{style:{width:"100%"}},{default:e.withCtx(()=>[e.unref(V).length?(e.openBlock(),e.createBlock(co,{key:0,modelValue:e.unref(V),"onUpdate:modelValue":a[12]||(a[12]=p=>e.isRef(V)?V.value=p:null),style:{display:"flex"},onSort:lo},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(V),(p,X)=>(e.openBlock(),e.createElementBlock("div",{key:p,style:{position:"relative","margin-right":"20px"}},[e.createVNode(Q,{src:e.unref(V)[X],"onUpdate:src":Ve=>e.unref(V)[X]=Ve,format:{size:1},height:100,width:100},null,8,["src","onUpdate:src"]),p?(e.openBlock(),e.createBlock(S,{key:0,color:"#7f7f7f",size:"20px",style:{position:"absolute",right:"-5px",top:"-5px",background:"#fff","border-radius":"50%"},onClick:Ve=>L(X)},{default:e.withCtx(()=>[e.createVNode(io)]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),X===0?(e.openBlock(),e.createElementBlock("div",Ye,"封面图")):e.createCommentVNode("",!0)]))),128))]),_:1},8,["modelValue"])):e.createCommentVNode("",!0),e.withDirectives(e.createVNode(Q,{format:{size:1},height:100,width:100,onChange:G},null,512),[[e.vShow,e.unref(V).length<=5]])]),_:1}),Je]),_:1}),e.unref(t).productType==="REAL_PRODUCT"?(e.openBlock(),e.createElementBlock("div",Ke,[e.createVNode(Y,{title:"物流信息"}),e.createVNode(b,{label:"配送方式（至少选一种）","label-width":"200px",required:"",style:{margin:"0"}}),e.createVNode(b,{label:"","label-width":"100px",prop:"distributionMode"},{default:e.withCtx(()=>[e.createVNode(po,{modelValue:e.unref(t).distributionMode,"onUpdate:modelValue":a[16]||(a[16]=p=>e.unref(t).distributionMode=p),onChange:ao},{default:e.withCtx(()=>[n.value?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(le,{disabled:oe.value,label:"EXPRESS"},{default:e.withCtx(()=>[e.createTextVNode(" 快递配送 "),e.createElementVNode("span",{style:{margin:"0 10px"},onClick:a[13]||(a[13]=e.withModifiers(()=>{},["stop"]))},"运费模板选择"),e.createVNode(M,{modelValue:e.unref(t).freightTemplateId,"onUpdate:modelValue":a[14]||(a[14]=p=>e.unref(t).freightTemplateId=p),class:"inputWidth",placeholder:"请选择运费模板"},{default:e.withCtx(()=>[e.createVNode(U,{value:"0",label:"商家包邮"}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(O.value,p=>(e.openBlock(),e.createBlock(U,{key:p.id,label:p.templateName,value:p.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e.createVNode(ae,{underline:!1,style:{"margin-left":"15px"},type:"primary",onClick:a[15]||(a[15]=e.withModifiers(p=>e.unref(T).push({name:"freightLogisticsIndex",query:{from:"releaseGoods"}}),["stop","prevent"]))},{default:e.withCtx(()=>[e.createTextVNode(" 前往设置 ")]),_:1})]),_:1},8,["disabled"]),Xe],64)):e.createCommentVNode("",!0),e.createVNode(le,{disabled:!xe.value||oe.value,label:"INTRA_CITY_DISTRIBUTION"},{default:e.withCtx(()=>[e.createTextVNode("同城配送")]),_:1},8,["disabled"]),Ze,e.createVNode(le,{label:"SHOP_STORE"},{default:e.withCtx(()=>[e.createTextVNode("到店自提")]),_:1})]),_:1},8,["modelValue"]),e.unref(t).freightTemplateId!=="0"?(e.openBlock(),e.createBlock(mo,{key:0,id:e.unref(t).freightTemplateId,data:O.value},null,8,["id","data"])):e.createCommentVNode("",!0)]),_:1})])):e.createCommentVNode("",!0),e.createVNode(Y,{title:"服务保障"}),e.createVNode(b,{class:"mleft-form-item"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(k),p=>(e.openBlock(),e.createElementBlock("div",{key:p.name,style:{display:"flex","flex-direction":"column"}},[e.createVNode(le,{modelValue:p.state,"onUpdate:modelValue":X=>p.state=X,onChange:e.unref(l)},{default:e.withCtx(()=>[e.createElementVNode("div",Qe,[e.createElementVNode("span",ve,e.toDisplayString(p.name),1),e.createElementVNode("span",et,e.toDisplayString(p.text),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]))),128))]),_:1})]),_:1},8,["model","rules"]),e.createVNode(ho,{modelValue:r.value,"onUpdate:modelValue":a[19]||(a[19]=p=>r.value=p),title:"选择品牌",width:"644px"},{footer:e.withCtx(()=>[e.createVNode(pe,{onClick:eo},{default:e.withCtx(()=>[e.createTextVNode("取 消")]),_:1}),e.createVNode(pe,{type:"primary",onClick:to},{default:e.withCtx(()=>[e.createTextVNode("确 定")]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("div",tt,[e.createVNode(ce,{modelValue:c.value,"onUpdate:modelValue":a[17]||(a[17]=p=>c.value=p),maxlength:"6",placeholder:"请输入品牌名称",style:{width:"208px"}},{append:e.withCtx(()=>[e.createVNode(pe,{icon:e.unref(Z.Search),onClick:vt},null,8,["icon"])]),_:1},8,["modelValue"])]),e.createElementVNode("div",ot,[at,e.createVNode(go,{modelValue:N.value,"onUpdate:modelValue":a[18]||(a[18]=p=>N.value=p)},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(_.value,p=>(e.openBlock(),e.createBlock(uo,{key:p.id,label:p.id,style:{height:"70px","border-bottom":"1px solid #e6e6e6","margin-left":"15px"}},{default:e.withCtx(()=>[e.createElementVNode("div",lt,[e.createElementVNode("div",rt,e.toDisplayString(p.brandName),1),e.createElementVNode("div",{style:e.normalizeStyle(p.status==="SELL_ON"?"background: #f5faf3;color: #82c26b;":""),class:"brandTable__content--item2"},e.toDisplayString(p.status==="SELL_ON"?"已上架":"已下架"),5),e.createVNode(_o,{src:p.brandLogo,class:"brandTable__content--item3"},null,8,["src"])])]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"])],64)}}}),yo="",Co="",st=Object.freeze(Object.defineProperty({__proto__:null,default:J(nt,[["__scopeId","data-v-2cdac33b"]])},Symbol.toStringTag,{value:"Module"})),it={class:"norm__list"},dt={class:"spec-container"},ct=e.defineComponent({__name:"GoodNorms",props:{classArr:{type:Array,default(){return[]}}},emits:["changeClass"],setup(d,{emit:g}){const x=d;e.reactive({name:[{required:!0,message:"请输入规格名称",trigger:"blur"}]}),e.ref(x.classArr);const T=(r,c,_)=>{let m=x.classArr;m[c].children[_].name.indexOf(r)===0&&m[c].children.splice(_,1),g("changeClass",{type:"delSpecValue",list:e.toRaw(m)})},P=r=>{g("changeClass",{type:"sortSpecValue",list:r})},I=r=>{let c=x.classArr;c[r].inputVisble=!0},A=r=>{var c;return!!((c=r.draggedContext.element)!=null&&c.name)},t=r=>{let c=x.classArr;c[r].inputValue&&c[r].children.push({name:c[r].inputValue,children:[],inputValue:"",inputVisble:!1}),c[r].inputVisble=!1,c[r].inputValue="",h(c)?g("changeClass",{type:"addSpecValue",list:e.toRaw(c)}):(c[r].children.pop(),R.ElMessage.warning("规格名称重复"))},O=r=>{let c=x.classArr;h(c)||(R.ElMessage.warning("规格名称重复"),c[r].name="")},V=()=>{if(!x.classArr.every(c=>!!c.name.trim())){R.ElMessage.warning("请输入规格名称");return}x.classArr.push({inputValue:"",inputVisble:!1,children:[],name:"",version:""}),g("changeClass",{type:"addSpec",list:e.toRaw(x.classArr)})},C=r=>{R.ElMessageBox.confirm("确认删除该规格?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{let c=x.classArr;c.splice(r,1),g("changeClass",{type:"delSpec",list:e.toRaw(c)}),R.ElMessage.success("删除成功")})},D=()=>{g("changeClass",{type:"sortSpecValue",list:e.toRaw(x.classArr)})};function h(r){const c=r.map(m=>m.name),_=r.reduce((m,N)=>m.concat(N.children.map(i=>i.name)),[]);return c.concat(_).length===Array.from(new Set(c.concat(_))).length}return(r,c)=>{const _=e.resolveComponent("el-button"),m=e.resolveComponent("el-input"),N=e.resolveComponent("el-table-column"),i=e.resolveComponent("el-tag"),w=e.resolveComponent("VueDraggableNext"),s=e.resolveComponent("el-icon"),o=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",it,[e.createVNode(_,{type:"primary",onClick:V},{default:e.withCtx(()=>[e.createTextVNode("新增规格")]),_:1}),e.createVNode(ne,{"limit-pid":!1,onChangeData:P},{default:e.withCtx(()=>[e.createVNode(o,{data:d.classArr,border:""},{default:e.withCtx(()=>[e.createVNode(N,{label:"规格名称",prop:"name",width:"160",fixed:"left"},{default:e.withCtx(({row:f,$index:B})=>[e.createVNode(m,{modelValue:f.name,"onUpdate:modelValue":n=>f.name=n,placeholder:"规格名称",style:{width:"130px"},maxlength:"20",onBlur:n=>O(B)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:1}),e.createVNode(N,{label:"规格值(拖拽可调整顺序)",prop:"children","min-width":"720"},{default:e.withCtx(({row:f,$index:B})=>[e.createElementVNode("div",dt,[f.children?(e.openBlock(),e.createBlock(w,{key:0,modelValue:f.children,"onUpdate:modelValue":n=>f.children=n,move:A,group:"tag",class:"tag-group",onSort:D},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.children,(n,k)=>(e.openBlock(),e.createBlock(i,{key:k,closable:"",effect:"plain",size:"large",type:"info","disable-transitions":!1,onClose:l=>T(n.name,B,k)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n.name),1)]),_:2},1032,["onClose"]))),128)),f.inputVisble?(e.openBlock(),e.createBlock(m,{key:0,ref:"saveTagInput",modelValue:f.inputValue,"onUpdate:modelValue":n=>f.inputValue=n,class:"input-new-tag",maxlength:"20",style:{"vertical-align":"middle","margin-bottom":"10px"},onKeyup:e.withKeys(n=>t(B),["enter"]),onBlur:n=>t(B)},null,8,["modelValue","onUpdate:modelValue","onKeyup","onBlur"])):(e.openBlock(),e.createBlock(_,{key:1,class:"button-new-tag spec-container-action",round:"",onClick:n=>I(B)},{default:e.withCtx(()=>[e.createTextVNode("添加新规格")]),_:2},1032,["onClick"]))]),_:2},1032,["modelValue","onUpdate:modelValue"])):e.createCommentVNode("",!0)])]),_:1}),e.createVNode(N,{label:"操作",width:"100",fixed:"right"},{default:e.withCtx(({$index:f})=>[e.createVNode(s,{size:"22px",color:"#FF4D4D",onClick:B=>C(f)},{default:e.withCtx(()=>[e.createVNode(e.unref(Z.Delete))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}}),ko="",pt=J(ct,[["__scopeId","data-v-aad43b37"]]);var H=(d=>(d.指导价="price",d.实售价="salePrice",d.重量="weight",d.限购类型="limitType",d.限购数量="limitNum",d.库存类型="stockType",d.设置库存="initStock",d.sku销量="initSalesVolume",d))(H||{});const se=d=>(e.pushScopeId("data-v-6b165c52"),d=d(),e.popScopeId(),d),mt={class:"table-container"},ft={class:"input-group"},_t=se(()=>e.createElementVNode("div",{class:"input-group__prefix"},"￥",-1)),ut={class:"input-group"},gt=se(()=>e.createElementVNode("div",{class:"input-group__prefix"},"￥",-1)),ht=se(()=>e.createElementVNode("div",{class:"input-group__prefix"},"kg",-1)),xt={class:"table-container"},bt=e.defineComponent({__name:"GoodNormTable",props:{list:{type:Array,default(){return[]}},classArr:{type:Array,default(){return[]}},memoSpecList:{type:Array,default(){return[]}}},emits:["changeNormList"],setup(d,{expose:g,emit:x}){const T=d,P=F.useRoute(),I=["sku图","指导价","实售价","重量","限购类型","限购数量","库存类型","设置库存","sku销量"],A=e.computed(()=>P.name==="releaseCommodityEdit"?["sku图","指导价","实售价","重量","限购类型","限购数量","sku销量"]:["sku图","指导价","实售价","重量","限购类型","限购数量","库存类型","设置库存","sku销量"]);console.log("$Route.query",P.query),e.ref(0),e.ref(0),e.computed(()=>{const h=T.classArr.map(c=>c.name),r=ee.cloneDeep(I);return r.splice(1,0,...h),r}),e.ref(0),e.ref(0),e.ref(0),e.ref(0),e.ref(0);const t=e.computed(()=>{const D=T.memoSpecList;let h=T.list;return D.length>0&&(h=h.map((r,c)=>{const _=D.filter(m=>!!C(r.specs,m.specs));return _.length>0&&(r=_[0]),r})),h}),O=()=>t.value,V=(D,h)=>{x("changeNormList",t.value.map(r=>(r[h]=D,r)))};g({getNormList:O});function C(D,h){if(D.length!==h.length)return!1;for(let r=0;r<D.length;r++)if(D[r]!==h[r])return!1;return!0}return(D,h)=>{const r=e.resolveComponent("el-table-column"),c=e.resolveComponent("el-input-number"),_=e.resolveComponent("el-table"),m=e.resolveComponent("el-option"),N=e.resolveComponent("el-select");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(Y,{title:"批量设置"}),e.createElementVNode("div",mt,[e.createVNode(_,{data:[{}]},{default:e.withCtx(()=>[e.createVNode(r,{label:"sku图",align:"center"},{default:e.withCtx(()=>[e.createVNode(Q,{width:50,height:50,"append-to-body":"",format:{types:["image/png","image/jpg","image/gif","image/jpeg","image/avif","image/webp"]},"onUpdate:src":h[0]||(h[0]=i=>V(i,"image"))})]),_:1}),e.createVNode(r,{label:"划线价",align:"center"},{default:e.withCtx(()=>[e.createElementVNode("div",ft,[_t,e.createVNode(c,{max:999999,precision:2,controls:!1,style:{width:"70px"},onChange:h[1]||(h[1]=i=>V(i,"price"))})])]),_:1}),e.createVNode(r,{label:"实售价",align:"center"},{default:e.withCtx(()=>[e.createElementVNode("div",ut,[gt,e.createVNode(c,{max:999999,precision:2,controls:!1,style:{width:"70px"},onChange:h[2]||(h[2]=i=>V(i,"salePrice"))})])]),_:1}),e.createVNode(r,{label:"设置库存",align:"center"},{default:e.withCtx(()=>[e.createVNode(c,{precision:0,controls:!1,style:{width:"70px"},onChange:h[3]||(h[3]=i=>V(i,"initStock"))})]),_:1}),e.createVNode(r,{label:"sku销量",align:"center"},{default:e.withCtx(()=>[e.createVNode(c,{precision:0,controls:!1,style:{width:"70px"},onChange:h[4]||(h[4]=i=>V(i,"initSalesVolume"))})]),_:1}),e.createVNode(r,{label:"限购数量",align:"center"},{default:e.withCtx(()=>[e.createVNode(c,{precision:0,controls:!1,style:{width:"70px"},onChange:h[5]||(h[5]=i=>V(i,"limitNum"))})]),_:1}),e.createVNode(r,{label:"重量",align:"center"},{default:e.withCtx(()=>[e.createVNode(c,{precision:0,controls:!1,style:{width:"70px"},onChange:h[6]||(h[6]=i=>V(i,"weight"))}),ht]),_:1})]),_:1})]),e.createVNode(Y,{title:"规格明细"}),e.createElementVNode("div",xt,[e.createVNode(_,{data:t.value},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(A.value.slice(0,1),(i,w)=>(e.openBlock(),e.createBlock(r,{key:w,label:i,width:"150",fixed:"left"},{default:e.withCtx(({row:s})=>[e.createVNode(Q,{src:s.image,"onUpdate:src":o=>s.image=o,width:50,height:50,"append-to-body":"",format:{types:["image/png","image/jpg","image/gif","image/jpeg","image/avif","image/webp"]}},null,8,["src","onUpdate:src"])]),_:2},1032,["label"]))),128)),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(T.classArr,(i,w)=>(e.openBlock(),e.createBlock(r,{key:w,align:"center",width:"180",label:i.name},{default:e.withCtx(({row:s})=>{var o;return[e.createTextVNode(e.toDisplayString(Array.isArray(s.specs)?(o=s.specs)==null?void 0:o[w]:s.specs.name),1)]}),_:2},1032,["label"]))),128)),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(A.value.slice(1),(i,w)=>(e.openBlock(),e.createBlock(r,{key:w,align:"center",width:"180",label:i},{default:e.withCtx(({row:s})=>[e.unref(H)[i]==="limitType"?(e.openBlock(),e.createBlock(N,{key:0,modelValue:s.limitType,"onUpdate:modelValue":o=>s.limitType=o},{default:e.withCtx(()=>[e.createVNode(m,{label:"规格限购",value:"SKU_LIMITED"}),e.createVNode(m,{label:"不限购",value:"UNLIMITED"})]),_:2},1032,["modelValue","onUpdate:modelValue"])):e.unref(H)[i]==="stockType"?(e.openBlock(),e.createBlock(N,{key:1,modelValue:s.stockType,"onUpdate:modelValue":o=>s.stockType=o},{default:e.withCtx(()=>[e.createVNode(m,{label:"限制库存",value:"LIMITED"}),e.createVNode(m,{label:"不限制库存",value:"UNLIMITED"})]),_:2},1032,["modelValue","onUpdate:modelValue"])):e.unref(H)[i]==="limitNum"?(e.openBlock(),e.createBlock(c,{key:2,modelValue:s.limitNum,"onUpdate:modelValue":o=>s.limitNum=o,disabled:s.limitType==="UNLIMITED",precision:0,controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled"])):e.unref(H)[i]==="initStock"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:3},[s.stockType==="UNLIMITED"?(e.openBlock(),e.createBlock(c,{key:0,"model-value":0,disabled:"",precision:0,controls:!1})):(e.openBlock(),e.createBlock(c,{key:1,modelValue:s.initStock,"onUpdate:modelValue":o=>s.initStock=o,precision:0,controls:!1},null,8,["modelValue","onUpdate:modelValue"]))],64)):["price","salePrice"].includes(e.unref(H)[i])?(e.openBlock(),e.createBlock(c,{key:4,modelValue:s[e.unref(H)[i]],"onUpdate:modelValue":o=>s[e.unref(H)[i]]=o,controls:!1,max:999999,min:0,precision:2},null,8,["modelValue","onUpdate:modelValue"])):(e.openBlock(),e.createBlock(c,{key:5,modelValue:s[e.unref(H)[i]],"onUpdate:modelValue":o=>s[e.unref(H)[i]]=o,precision:0,controls:!1},null,8,["modelValue","onUpdate:modelValue"]))]),_:2},1032,["label"]))),128))]),_:1},8,["data"])])])}}}),wo="",Vt=J(bt,[["__scopeId","data-v-6b165c52"]]),ge=d=>(e.pushScopeId("data-v-5c3e58cd"),d=d(),e.popScopeId(),d),yt=ge(()=>e.createElementVNode("span",{style:{color:"rgba(108, 108, 108, 1)","font-size":"12px","font-weight":"400","margin-left":"50px"}}," 下单时选择诸如甜度、口味、加料等商品属性不同属性值价格不同，可提升商品客单价及用户体验，拖动调整顺序 ",-1)),Ct={style:{"text-align":"center","font-size":"12px"}},Nt=ge(()=>e.createElementVNode("span",{style:{color:"rgba(108, 108, 108, 1)","font-weight":"400","font-size":"12px","margin-left":"50px"}}," 可拖动调整顺序 ",-1)),kt=e.defineComponent({__name:"GoodOnly",setup(d,{expose:g}){const{divTenThousand:x}=re(),T=F.useRoute();e.onActivated(()=>{t(),D()}),e.onBeforeMount(()=>{t(),D()});const P=e.inject("form"),I=e.ref([]),A=e.ref([]),t=async()=>{const{data:_}=await ue({featuresType:"ATTRIBUTE",size:100}),{data:m}=await ue({featuresType:"ARGUMENTS",size:100});I.value=_.records,A.value=m.records},O=()=>({featuresType:"",id:"",shopId:"",featuresValue:{featureName:"",isRequired:"",isMultiSelect:"",featureValues:[]}}),V=(_,m,N)=>_===""?"":_?N:m,C=e.ref({productAttribute:[],productParameter:[]}),D=()=>{var _,m,N,i;T.query.id?(C.value.productAttribute=((m=(_=P.submitForm.value.extra)==null?void 0:_.productAttributes)==null?void 0:m.map(w=>({id:w.id,featuresValue:w})))||[],C.value.productParameter=((i=(N=P.submitForm.value.extra)==null?void 0:N.productParameters)==null?void 0:i.map(w=>({id:w.id,featuresValue:w})))||[]):C.value={productAttribute:[],productParameter:[]}},h=(_,m,N)=>{C.value[_][m].featuresValue.featureValues=C.value[_][m].featuresValue.featureValues.filter((i,w)=>w!==N)},r=(_,m)=>{C.value[_]=C.value[_].filter((N,i)=>i!==m)};return g({handleSpacObj:()=>({productAttribute:C.value.productAttribute.map(_=>({..._.featuresValue,id:_.id})),productParameter:C.value.productParameter.map(_=>({..._.featuresValue,id:_.id}))})}),(_,m)=>{const N=e.resolveComponent("el-button"),i=e.resolveComponent("el-option"),w=e.resolveComponent("el-select"),s=e.resolveComponent("el-table-column"),o=e.resolveComponent("el-tag"),f=e.resolveComponent("el-icon"),B=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(Y,{title:"商品属性"},{default:e.withCtx(()=>[yt]),_:1}),e.createVNode(N,{type:"primary",style:{margin:"15px 0"},onClick:m[0]||(m[0]=n=>C.value.productAttribute.push(O()))},{default:e.withCtx(()=>[e.createTextVNode("添加属性")]),_:1}),e.createVNode(ne,{"limit-pid":!1,onChangeData:m[1]||(m[1]=n=>C.value.productAttribute=n)},{default:e.withCtx(()=>[e.createVNode(B,{data:C.value.productAttribute,border:""},{default:e.withCtx(()=>[e.createVNode(s,{label:"属性名称",width:"130"},{default:e.withCtx(({row:n,$index:k})=>[e.createVNode(w,{"model-value":n,"value-key":"id",onChange:l=>C.value.productAttribute[k]=e.unref(ee.cloneDeep)(l)},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(I.value,l=>(e.openBlock(),e.createBlock(i,{key:l.id,disabled:C.value.productAttribute.some(y=>y.id===l.id),label:l.featuresValue.featureName,value:l},null,8,["disabled","label","value"]))),128))]),_:2},1032,["model-value","onChange"])]),_:1}),e.createVNode(s,{label:"属性类型",width:"100"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",Ct,e.toDisplayString(V(n.featuresValue.isRequired,"必选","选填"))+" ， "+e.toDisplayString(V(n.featuresValue.isMultiSelect,"多选"," 单选")),1)]),_:1}),e.createVNode(s,{label:"属性值"},{default:e.withCtx(({row:n,$index:k})=>[n.featuresValue.featureValues?(e.openBlock(),e.createBlock(e.unref(fe.VueDraggableNext),{key:0,modelValue:n.featuresValue.featureValues,"onUpdate:modelValue":l=>n.featuresValue.featureValues=l,group:`arrTag${k}`},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.featuresValue.featureValues,(l,y)=>(e.openBlock(),e.createBlock(o,{key:y,closable:"",effect:"plain",size:"large",type:"info","disable-transitions":!1,style:{"margin-right":"10px","margin-bottom":"10px"},onClose:E=>h("productAttribute",k,y)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(l.firstValue)+" + "+e.toDisplayString(e.unref(x)(l.secondValue))+"元 ",1)]),_:2},1032,["onClose"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","group"])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(s,{label:"操作",width:"100",fixed:"right"},{default:e.withCtx(({$index:n})=>[e.createVNode(f,{size:"22px",color:"#FF4D4D",onClick:k=>r("productAttribute",n)},{default:e.withCtx(()=>[e.createVNode(e.unref(Z.Delete))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e.createVNode(Y,{title:"商品参数",style:{"margin-top":"15px"}},{default:e.withCtx(()=>[Nt]),_:1}),e.createVNode(N,{type:"primary",style:{margin:"15px 0"},onClick:m[2]||(m[2]=n=>C.value.productParameter.push(O()))},{default:e.withCtx(()=>[e.createTextVNode("添加参数")]),_:1}),e.createVNode(ne,{"limit-pid":!1,onChangeData:m[3]||(m[3]=n=>C.value.productParameter=n)},{default:e.withCtx(()=>[e.createVNode(B,{data:C.value.productParameter,border:""},{default:e.withCtx(()=>[e.createVNode(s,{label:"参数名称",width:"130"},{default:e.withCtx(({row:n,$index:k})=>[e.createVNode(w,{"model-value":n,"value-key":"id",onChange:l=>C.value.productParameter[k]=e.unref(ee.cloneDeep)(l)},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(A.value,l=>(e.openBlock(),e.createBlock(i,{key:l.id,disabled:C.value.productParameter.some(y=>y.id===l.id),label:l.featuresValue.featureName,value:l},null,8,["disabled","label","value"]))),128))]),_:2},1032,["model-value","onChange"])]),_:1}),e.createVNode(s,{label:"参数值"},{default:e.withCtx(({row:n,$index:k})=>[n.featuresValue.featureValues?(e.openBlock(),e.createBlock(e.unref(fe.VueDraggableNext),{key:0,modelValue:n.featuresValue.featureValues,"onUpdate:modelValue":l=>n.featuresValue.featureValues=l,group:`goodArrTag${k}`},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.featuresValue.featureValues,(l,y)=>(e.openBlock(),e.createBlock(o,{key:y,closable:"",effect:"plain",size:"large",type:"info","disable-transitions":!1,onClose:E=>h("productParameter",k,y)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(l.firstValue),1)]),_:2},1032,["onClose"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","group"])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(s,{label:"操作",width:"100",fixed:"right"},{default:e.withCtx(({$index:n})=>[e.createVNode(f,{size:"22px",color:"#FF4D4D",onClick:k=>r("productParameter",n)},{default:e.withCtx(()=>[e.createVNode(e.unref(Z.Delete))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})],64)}}}),Io="",wt=J(kt,[["__scopeId","data-v-5c3e58cd"]]),It={key:0},Tt={key:1},Et={class:"inputnumber"},Bt=e.createElementVNode("div",{class:"inputnumber__icon"},"元",-1),St={class:"inputnumber"},Dt=e.createElementVNode("div",{class:"inputnumber__icon"},"元",-1),Lt={class:"inputnumber"},At=e.createElementVNode("div",{class:"inputnumber__icon"},"个",-1),Pt={class:"inputnumber"},Ut=e.createElementVNode("div",{class:"inputnumber__icon"},"kg",-1),Rt={class:"inputnumber"},Mt=e.createElementVNode("div",{class:"inputnumber__icon"},"个",-1),$t={class:"inputnumber"},Ot=e.createElementVNode("div",{class:"inputnumber__icon"},"个",-1),jt=e.defineComponent({__name:"NewSaleInfo",setup(d,{expose:g}){const x=F.useRoute(),{divTenThousand:T}=re(),P=e.ref(),I=e.ref(),t=e.inject("form").submitForm,O=e.ref([]),V=e.ref(),C=(s,o,f)=>{o<=0?f(new Error("输入数值请大于0")):Number(t.value.skus[0].price)<Number(o)?f(new Error("实售价大于指导价")):f()},D=(s,o,f)=>{o<=0?f(new Error("输入数值请大于0")):Number(t.value.skus[0].salePrice)>Number(o)?f(new Error("实售价大于指导价")):f()},h=e.reactive({"skus[0].price":[{required:!0,validator:D,trigger:"blur"}],"skus[0].salePrice":[{required:!0,validator:C,trigger:"blur"}]});e.onActivated(()=>{_()});const r=async s=>{const{type:o,list:f}=s;if(!f.length){t.value.skus=[{id:"",image:"",initSalesVolume:0,limitNum:0,initStock:0,limitType:"UNLIMITED",price:0,productId:"",salePrice:0,shopId:"",stockType:"UNLIMITED",weight:0}];return}t.value.specGroups=f,o!=="addSpec"&&(t.value.skus=m(f))},c=s=>{t.value.skus=s};async function _(){var s,o,f,B;if(x.query.id){const n=((s=t.value)==null?void 0:s.sellType)==="CONSIGNMENT"?(o=t.value)==null?void 0:o.supplierId:me.useShopInfoStore().shopInfo.id,{code:k,data:l}=await ke(n,x.query.id);if(k!==200){R.ElMessage.error("获取商品sku失败");return}l.skus.forEach(y=>{y.initStock=0,y.price=Number(T(y.price)),y.salePrice=Number(T(y.salePrice))}),t.value=Object.assign(t.value,l),w(((B=(f=t.value)==null?void 0:f.skus)==null?void 0:B.length)>1?"MUTI_SPEC":"SINGLE_SPEC")}}function m(s){let o=[];return o=s.map(f=>f.children),o.length<=1?o=o[0]:o=o.reduce((f,B)=>{let n=[];return f.forEach(k=>{B.forEach(l=>{k instanceof Array?n.push([...k,l]):n.push([k,l])})}),n}),o.map(f=>N(f))}function N(s){let o=[];Array.isArray(s)?o=s.map(B=>B.name):o.push(s.name);const f=t.value.skus.find(B=>B.specs.every(n=>o.includes(n)));return f&&(f.specs=o),f||{specs:o,stockType:"LIMITED",initSalesVolume:0,limitType:"UNLIMITED",limitNum:0,image:"",price:0,initStock:0,salePrice:0,weight:"0"}}g({currentFormRef:P,GoodOnlyRef:I});const i=e.ref("SINGLE_SPEC"),w=s=>{s==="SINGLE_SPEC"&&t.value.specGroups.length&&(t.value.skus=[{id:"",image:"",initSalesVolume:0,limitNum:0,initStock:0,limitType:"UNLIMITED",price:0,productId:"",salePrice:0,shopId:"",stockType:"UNLIMITED",weight:0}],t.value.specGroups=[]),i.value=s};return e.onMounted(()=>{var s,o;w(((o=(s=t.value)==null?void 0:s.skus)==null?void 0:o.length)>1?"MUTI_SPEC":"SINGLE_SPEC")}),(s,o)=>{const f=e.resolveComponent("el-radio"),B=e.resolveComponent("el-radio-group"),n=e.resolveComponent("el-form-item"),k=e.resolveComponent("el-input-number"),l=e.resolveComponent("el-col"),y=e.resolveComponent("el-option"),E=e.resolveComponent("el-select"),W=e.resolveComponent("el-row"),q=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(q,{ref_key:"currentFormRef",ref:P,model:e.unref(t),rules:h,"label-width":"100px"},{default:e.withCtx(()=>{var G;return[e.createVNode(Y,{title:"规格类型"}),e.createVNode(n,{label:"规格类型",style:{"margin-top":"10px"}},{default:e.withCtx(()=>[e.createVNode(B,{"model-value":i.value,size:"small",onChange:w},{default:e.withCtx(()=>[e.createVNode(f,{label:"SINGLE_SPEC"},{default:e.withCtx(()=>[e.createTextVNode("单规格")]),_:1}),e.createVNode(f,{label:"MUTI_SPEC"},{default:e.withCtx(()=>[e.createTextVNode("多规格")]),_:1})]),_:1},8,["model-value"])]),_:1}),i.value==="MUTI_SPEC"?(e.openBlock(),e.createElementBlock("div",It,[e.createVNode(pt,{"class-arr":e.unref(t).specGroups,onChangeClass:r},null,8,["class-arr"]),e.unref(t).specGroups&&e.unref(t).specGroups.length>0?(e.openBlock(),e.createBlock(Vt,{key:0,ref_key:"goodNormTable",ref:V,list:e.unref(t).skus,"class-arr":e.unref(t).specGroups,"memo-spec-list":O.value,onChangeNormList:c},null,8,["list","class-arr","memo-spec-list"])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),i.value==="SINGLE_SPEC"&&((G=e.unref(t).skus)!=null&&G.length)?(e.openBlock(),e.createElementBlock("div",Tt,[e.createVNode(W,{gutter:8},{default:e.withCtx(()=>[e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"指导价",prop:"skus[0].price"},{default:e.withCtx(()=>[e.createElementVNode("div",Et,[e.createVNode(k,{modelValue:e.unref(t).skus[0].price,"onUpdate:modelValue":o[0]||(o[0]=L=>e.unref(t).skus[0].price=L),controls:!1,max:999999,precision:2,class:"input_number com__input--width"},null,8,["modelValue"]),Bt])]),_:1})]),_:1}),e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"销售价",prop:"skus[0].salePrice"},{default:e.withCtx(()=>[e.createElementVNode("div",St,[e.createVNode(k,{modelValue:e.unref(t).skus[0].salePrice,"onUpdate:modelValue":o[1]||(o[1]=L=>e.unref(t).skus[0].salePrice=L),max:999999,precision:2,controls:!1,class:"input_number com__input--width"},null,8,["modelValue"]),Dt])]),_:1})]),_:1}),e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"初始销量"},{default:e.withCtx(()=>[e.createElementVNode("div",Lt,[e.createVNode(k,{modelValue:e.unref(t).skus[0].initSalesVolume,"onUpdate:modelValue":o[2]||(o[2]=L=>e.unref(t).skus[0].initSalesVolume=L),placeholder:"0",controls:!1,class:"input_number com__input--width"},null,8,["modelValue"]),At])]),_:1})]),_:1}),e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"重量"},{default:e.withCtx(()=>[e.createElementVNode("div",Pt,[e.createVNode(k,{modelValue:e.unref(t).skus[0].weight,"onUpdate:modelValue":o[3]||(o[3]=L=>e.unref(t).skus[0].weight=L),controls:!1,class:"input_number com__input--width"},{append:e.withCtx(()=>[e.createTextVNode(".com")]),_:1},8,["modelValue"]),Ut])]),_:1})]),_:1}),e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"限购类型"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:e.unref(t).skus[0].limitType,"onUpdate:modelValue":o[4]||(o[4]=L=>e.unref(t).skus[0].limitType=L)},{default:e.withCtx(()=>[e.createVNode(y,{label:"商品限购",value:"PRODUCT_LIMITED"}),e.unref(t).specGroups.length?(e.openBlock(),e.createBlock(y,{key:0,label:"规格限购",value:"SKU_LIMITED"})):e.createCommentVNode("",!0),e.createVNode(y,{label:"不限购",value:"UNLIMITED"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"库存类型"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:e.unref(t).skus[0].stockType,"onUpdate:modelValue":o[5]||(o[5]=L=>e.unref(t).skus[0].stockType=L)},{default:e.withCtx(()=>[e.createVNode(y,{label:"限制库存",value:"LIMITED"}),e.createVNode(y,{label:"不限制库存",value:"UNLIMITED"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"限购数量"},{default:e.withCtx(()=>[e.createElementVNode("div",Rt,[e.createVNode(k,{modelValue:e.unref(t).skus[0].limitNum,"onUpdate:modelValue":o[6]||(o[6]=L=>e.unref(t).skus[0].limitNum=L),disabled:e.unref(t).skus[0].limitType==="UNLIMITED",min:0,controls:!1,class:"input_number com__input--width"},null,8,["modelValue","disabled"]),Mt])]),_:1})]),_:1}),e.createVNode(l,{span:12},{default:e.withCtx(()=>[e.createVNode(n,{label:"设置库存",prop:"skus[0].initStock"},{default:e.withCtx(()=>[e.createElementVNode("div",$t,[e.createVNode(k,{modelValue:e.unref(t).skus[0].initStock,"onUpdate:modelValue":o[7]||(o[7]=L=>e.unref(t).skus[0].initStock=L),disabled:e.unref(t).skus[0].stockType==="UNLIMITED",max:999999,min:0,precision:0,controls:!1,class:"input_number com__input--width"},null,8,["modelValue","disabled"]),Ot])]),_:1})]),_:1})]),_:1})])):e.createCommentVNode("",!0),e.createVNode(wt,{ref_key:"GoodOnlyRef",ref:I},null,512)]}),_:1},8,["model","rules"])}}}),To="",Ft=Object.freeze(Object.defineProperty({__proto__:null,default:jt},Symbol.toStringTag,{value:"Module"})),qt={class:"info"},Gt=["src"],zt={class:"info__edit"},Ht=e.defineComponent({__name:"NewProductInfo",setup(d){const x=e.inject("form").submitForm;return(T,P)=>(e.openBlock(),e.createElementBlock("div",qt,[e.createElementVNode("img",{src:T.exampleImg,class:"info__img"},null,8,Gt),e.createElementVNode("div",zt,[e.createVNode(Ce,{content:e.unref(x).detail,"onUpdate:content":P[0]||(P[0]=I=>e.unref(x).detail=I),height:"711px"},null,8,["content"])])]))}}),Eo="",Wt=Object.freeze(Object.defineProperty({__proto__:null,default:J(Ht,[["__scopeId","data-v-668d63d0"]])},Symbol.toStringTag,{value:"Module"}));return Re});
