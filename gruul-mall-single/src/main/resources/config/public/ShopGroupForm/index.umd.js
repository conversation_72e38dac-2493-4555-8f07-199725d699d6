(function(e,U){typeof exports=="object"&&typeof module<"u"?module.exports=U(require("vue"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("element-plus"),require("@/composables/useConvert"),require("@/utils/date"),require("@/utils/http"),require("@/apis/good"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","element-plus","@/composables/useConvert","@/utils/date","@/utils/http","@/apis/good","vue-router"],U):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopGroupForm=U(e.ShopGroupFormContext.Vue,e.ShopGroupFormContext.QChooseGoodsPopup,e.ShopGroupFormContext.ElementPlus,e.ShopGroupFormContext.UseConvert,e.ShopGroupFormContext.DateUtil,e.ShopGroupFormContext.UtilsHttp,e.ShopGroupFormContext.GoodAPI,e.ShopGroupFormContext.VueRouter))})(this,function(e,U,b,j,z,R,K,H){"use strict";var L=document.createElement("style");L.textContent=`.com[data-v-1b86df1b]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-1b86df1b]{width:62px;height:62px}.com__name[data-v-1b86df1b]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.groupForm[data-v-c95518e9]{padding:0 30px 60px}.groupForm__title[data-v-c95518e9]{font-size:14px;color:#606266;font-weight:700;margin-bottom:30px}.groupForm__stairs[data-v-c95518e9]{margin-bottom:16px}.groupForm__stairs--title[data-v-c95518e9]{font-size:12px;color:#333;font-weight:700}.groupForm__stairs--input[data-v-c95518e9]{margin:0 7px}.groupForm__btn[data-v-c95518e9]{height:60px;display:flex;justify-content:center;align-items:center}.tips[data-v-c95518e9]{font-size:12px;color:#c4c4c4}
`,document.head.appendChild(L);const Q={class:"com"},W={class:"com__name"},X=e.defineComponent({__name:"select-good-table",props:{mode:{type:String,default:"COMMON"},users:{type:Array,default(){return[]}},productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}}},setup(V,{expose:t}){const d=V,{divTenThousand:h,mulTenThousand:T}=j(),x=e.ref([]);e.watch(()=>d.productList,s=>{const k=F(s);x.value=S(k)}),e.watch(()=>d.flatGoodList,s=>{x.value=S(s)});function O(s){return s.skuItem.stockType==="LIMITED"?Number(s.skuItem.skuStock):1/0}function F(s,k){if(!s.length)return[];const c=[];return s.forEach(n=>{n.skuIds.forEach((u,p)=>{c.push({productId:n.productId,productName:n.productName,productPic:n.pic,skuItem:{productId:n.productId,skuId:u,skuName:n.specs[p],skuPrice:n.salePrices[p],skuStock:n.stocks[p],stockType:n.stockTypes[p]},rowTag:0,stock:0,prices:[],isJoin:!0})})}),c}function S(s,k){let c=0,n=s.length;for(let u=0;u<n;u++){const p=s[u];u===0&&(p.rowTag=1,c=0),u!==0&&(p.productId===s[u-1].productId?(p.rowTag=0,s[c].rowTag=s[c].rowTag+1):(p.rowTag=1,c=u)),p.prices=p.prices.map(y=>+y)}return s}const $=({row:s,column:k,rowIndex:c,columnIndex:n})=>{if(n===0)return{rowspan:s.rowTag,colspan:s.rowTag?1:0}};function A(s){return s.stockType==="UNLIMITED"?"不限购":s.skuStock}function G(){const s=e.toRaw(x.value),k=[],c=new Map;if(s.length)for(let n=0;n<s.length;n++){if(!s[n].isJoin)continue;const u=s[n],p=u.productId,y={skuId:u.skuItem.skuId,stock:+u.stock,prices:u.prices.map(w=>T(w).toNumber())};if(!c.has(p))c.set(p,k.length),k.push({productId:p,skus:[y]});else{const w=c.get(p);k[w].skus.push(y)}}return k}function B(){let s=!0;const k=x.value;if(!k.length)b.ElMessage.warning("请选择商品"),s=!1;else for(let c=0;c<k.length;c++)if(k[c].isJoin){if(!k[c].stock){b.ElMessage.warning("商品库存必须大于零"),s=!1;break}if(k[c].prices.length!==d.users.length||k[c].prices.some(n=>n===null)){b.ElMessage.warning("拼团价格填写完整"),s=!1;break}}return s}return t({getProduct:G,validateProduct:B}),(s,k)=>{const c=e.resolveComponent("el-image"),n=e.resolveComponent("el-table-column"),u=e.resolveComponent("el-input-number"),p=e.resolveComponent("el-input"),y=e.resolveComponent("el-switch"),w=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(w,{data:x.value,"span-method":$},{default:e.withCtx(()=>[e.createVNode(n,{label:"商品信息",width:"215"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",Q,[e.createVNode(c,{class:"com__pic",src:r.productPic},null,8,["src"]),e.createElementVNode("div",W,e.toDisplayString(r.productName),1)])]),_:1}),e.createVNode(n,{label:"规格"},{default:e.withCtx(({row:r})=>[e.createTextVNode(e.toDisplayString(r.skuItem.skuName),1)]),_:1}),e.createVNode(n,{label:"库存"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[e.createVNode(u,{modelValue:r.stock,"onUpdate:modelValue":_=>r.stock=_,min:0,style:{width:"80px"},max:O(r),disabled:d.isEdit,precision:0,controls:!1},null,8,["modelValue","onUpdate:modelValue","max","disabled"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(A(r.skuItem)),1)]),_:1}),d.mode==="COMMON"?(e.openBlock(),e.createBlock(n,{key:0,label:"拼团价"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[d.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(h)(r.prices[0]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[0],"onUpdate:modelValue":_=>r.prices[0]=_,min:.01,style:{width:"80px"},disabled:d.isEdit,precision:2,max:e.unref(h)(r.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(h)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),d.mode==="STAIRS"&&d.users.length>0?(e.openBlock(),e.createBlock(n,{key:1,label:"第一阶段拼团"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[d.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(h)(r.prices[0]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[0],"onUpdate:modelValue":_=>r.prices[0]=_,min:.01,style:{width:"80px"},disabled:d.isEdit,precision:2,max:e.unref(h)(r.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(h)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),d.mode==="STAIRS"&&d.users.length>1?(e.openBlock(),e.createBlock(n,{key:2,label:"第二阶段拼图"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[d.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(h)(r.prices[1]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[1],"onUpdate:modelValue":_=>r.prices[1]=_,disabled:d.isEdit,min:.01,style:{width:"80px"},max:r.prices[0]-.01,precision:2,controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(h)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),d.mode==="STAIRS"&&d.users.length>2?(e.openBlock(),e.createBlock(n,{key:3,label:"第三阶段拼图"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",null,[d.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(h)(r.prices[2]).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:r.prices[2],"onUpdate:modelValue":_=>r.prices[2]=_,disabled:d.isEdit,min:.01,style:{width:"80px"},precision:2,max:r.prices[1]-.01,controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(h)(r.skuItem.skuPrice)),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(n,{label:"是否参与"},{default:e.withCtx(({row:r})=>[e.createVNode(y,{modelValue:r.isJoin,"onUpdate:modelValue":_=>r.isJoin=_,size:"large",disabled:d.isEdit},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),Ve="",J=(V,t)=>{const d=V.__vccOpts||V;for(const[h,T]of t)d[h]=T;return d},Z=J(X,[["__scopeId","data-v-1b86df1b"]]),v=V=>R.http.post({url:"addon-team/team/activity",data:V}),ee=V=>R.http.get({url:`addon-team/team/activity/${V}`}),P=V=>(e.pushScopeId("data-v-c95518e9"),V=V(),e.popScopeId(),V),te={class:"groupForm"},oe=P(()=>e.createElementVNode("div",{class:"groupForm__title"},"基本信息",-1)),le={key:1},re={class:"groupForm__stairs--title"},ae=P(()=>e.createElementVNode("span",{class:"tips",style:{"margin-left":"8px"}},"商品按下单减库存，请设置未付款订单自动取消时间及时释放库存，可输入3-360分钟",-1)),se=P(()=>e.createElementVNode("div",{class:"tips"}," 开启模拟成团后，拼团有效期内人数未满的团，系统将会以“虚拟用户”凑满人数，使该团拼团成功。你只需要对已付款参团的真实买家发货。建议合理开启，以提高成团率。 ",-1)),ne={key:1,class:"tips"},de=P(()=>e.createElementVNode("span",{class:"tips",style:{"margin-left":"8px"}},[e.createTextVNode(" 叠加优惠可能导致实付金额为 "),e.createElementVNode("i",{style:{color:"red","font-weight":"700","font-style":"unset"}},"0"),e.createTextVNode(" (实付金额= 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1)),ie={class:"groupForm__btn"},ce=e.defineComponent({__name:"ShopGroupForm",setup(V){const t=e.ref({name:"",startTime:"",endTime:"",effectTimeout:0,mode:"COMMON",users:[],payTimeout:0,simulate:!1,huddle:!0,preheat:!1,preheatHours:1,stackable:{vip:!1,coupon:!1,full:!1},products:[]}),d=H.useRoute(),h=H.useRouter(),T=!!d.query.id,x=new z,O=e.ref(),F=e.ref([]),S=e.ref(!1),$=e.ref([]),A=e.ref([]),G=e.ref(),B=e.reactive({maxPrice:"",minPrice:"",activity:{endTime:"",startTime:""},keyword:"",categoryFirstId:""}),s=e.reactive({name:[{required:!0,message:"请输入活动名称",trigger:"blur"},{min:1,max:10,message:"活动名称字数为1-10",trigger:"blur"}],startTime:[{required:!0,message:"请选择日期"},{trigger:"blur",validator:Y}],endTime:[{required:!0,message:"请选择日期"},{trigger:"blur",validator:Y}],effectTimeout:[{validator:pe,trigger:"blur"}],mode:[{required:!0,message:"请选择拼团模式",trigger:"blur"}],users:[{validator:_,trigger:"blur"}],payTimeout:[{validator:me,trigger:"blur"}],simulate:[{required:!0,message:"请选择是否模拟成团",trigger:"blur"}],huddle:[{required:!0,message:"请选择是否凑团",trigger:"blur"}],preheat:[{required:!0,message:"请选择是否预热",trigger:"blur"}]});fe();const k=()=>{t.value.users.length<3?t.value.users.push(0):b.ElMessage.warning("最多添加三个梯队")},c=()=>{t.value.users.pop()},n=a=>{$.value=a.tempGoods},u=()=>{var a;(a=O.value)==null||a.validate(async o=>{if(o&&G.value.validateProduct()){t.value.products=G.value.getProduct();const{code:i,data:m,msg:I}=await v(t.value);i===200?(b.ElMessage.success("创建成功"),y()):b.ElMessage.error(I||"创建失败")}})},p=a=>{a[0].getTime()>=a[1].getTime()&&b.ElMessage.warning("结束时间大于开始时间"),t.value.startTime=x.getYMDHMSs(a[0]),t.value.endTime=x.getYMDHMSs(a[1]),B.activity.startTime=x.getYMDHMSs(a[0]),B.activity.endTime=x.getYMDHMSs(a[1])},y=()=>{h.go(-1)},w=a=>{a==="COMMON"&&(t.value.users=[t.value.users[0]])},r=()=>{if(!t.value.endTime||!t.value.startTime){b.ElMessage.warning("请先选择时间段");return}S.value=!0};function _(a,o,i){t.value.mode==="COMMON"&&(t.value.users.length&&t.value.users[0]>=2?i():i(new Error("参团人数应大于等于两人"))),t.value.mode==="STAIRS"&&(t.value.users.length?ue(t.value.users)?i():i(new Error("阶梯团人数应为递增人数")):i(new Error("请至少添加一项阶梯团人数")))}function pe(a,o,i){o>=15?i():i(new Error("拼团有效时间必须大于等于15分钟"))}function me(a,o,i){o>=3&&o<=369?i():i(new Error("订单关闭时间3-360分钟"))}function ue(a){for(let o=1;o<a.length;o++)if(a[o]<=a[o-1])return!1;return!0}function Y(a,o,i){new Date(t.value.startTime).getTime()>=new Date(t.value.endTime).getTime()?i(new Error("开始时间应小于结束时间")):i()}async function fe(){const a=d.query.id;if(a){const{data:o}=await ee(a);t.value=o,t.value.payTimeout=+o.payTimeout,F.value=[o.startTime,o.endTime],ke(o)}}async function ke(a){const o=a.products.map(f=>f.productId),{code:i,data:m}=await K.doGetRetrieveProduct({productId:o});if(i!==200)return b.ElMessage.error("获取活动商品信息失败");const I=[];for(let f=0;f<a.products.length;f++){const C=m.list.findIndex(g=>g.productId===a.products[f].productId);for(let g=0;g<a.products[C].skus.length;g++){let N={productName:m.list[f].productName,productPic:m.list[f].pic,productId:m.list[f].productId,skuItem:{productId:m.list[f].productId,skuId:"",skuName:"",skuPrice:"",skuStock:"",stockType:"LIMITED"},rowTag:0,stock:0,prices:[],isJoin:!0};const D=a.products[C].skus[g].skuId,E=m.list[f].skuIds.findIndex(l=>l===D);N.skuItem.skuId=m.list[f].skuIds[E],N.skuItem.skuName=m.list[f].specs[E],N.skuItem.skuPrice=m.list[f].salePrices[E],N.skuItem.skuStock=m.list[f].stocks[E],N.skuItem.stockType=m.list[f].stockTypes[E],N.stock=a.products[C].skus[g].stock,N.prices=a.products[C].skus[g].prices,I.push(N)}}A.value=I}function he(a){const o=x.getYMD(new Date),i=x.getYMD(a);return o===i?!1:new Date().getTime()>a.getTime()}return(a,o)=>{const i=e.resolveComponent("el-input"),m=e.resolveComponent("el-form-item"),I=e.resolveComponent("el-date-picker"),f=e.resolveComponent("el-radio"),C=e.resolveComponent("el-radio-group"),g=e.resolveComponent("el-button"),N=e.resolveComponent("el-input-number"),D=e.resolveComponent("el-checkbox"),E=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",te,[oe,e.createVNode(E,{ref_key:"ruleFormRef",ref:O,model:t.value,"label-width":"110","label-position":"right",rules:s,disabled:T},{default:e.withCtx(()=>[e.createVNode(m,{label:"活动名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:t.value.name,"onUpdate:modelValue":o[0]||(o[0]=l=>t.value.name=l),placeholder:"限10字",style:{width:"550px"},maxlength:"10"},null,8,["modelValue"])]),_:1}),e.createVNode(m,{label:"活动时间",prop:"startTime"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(I,{modelValue:F.value,"onUpdate:modelValue":o[1]||(o[1]=l=>F.value=l),style:{width:"550px"},type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间","disabled-date":he,onChange:p},null,8,["modelValue"])])]),_:1}),e.createVNode(m,{label:"拼团有效时间",prop:"effectTimeout",required:""},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:t.value.effectTimeout,"onUpdate:modelValue":o[2]||(o[2]=l=>t.value.effectTimeout=l),formatter:l=>Number(`${l}`.replace(/[^\d]/g,"")),style:{width:"550px"}},{append:e.withCtx(()=>[e.createTextVNode(" 分钟 ")]),_:1},8,["modelValue","formatter"])]),_:1}),e.createVNode(m,{label:"拼团模式",prop:"mode"},{default:e.withCtx(()=>[e.createVNode(C,{modelValue:t.value.mode,"onUpdate:modelValue":o[3]||(o[3]=l=>t.value.mode=l),onChange:w},{default:e.withCtx(()=>[e.createVNode(f,{label:"COMMON"},{default:e.withCtx(()=>[e.createTextVNode("普通拼团")]),_:1}),e.createVNode(f,{label:"STAIRS"},{default:e.withCtx(()=>[e.createTextVNode("阶梯拼团")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(m,{label:"参团人数",prop:"users",required:""},{default:e.withCtx(()=>[t.value.mode==="COMMON"?(e.openBlock(),e.createBlock(i,{key:0,modelValue:t.value.users[0],"onUpdate:modelValue":o[4]||(o[4]=l=>t.value.users[0]=l),formatter:l=>Number(`${l}`.replace(/[^\d]/g,"")),parser:l=>`${Number(l)>=100?100:l}`,style:{width:"550px"},max:100},{append:e.withCtx(()=>[e.createTextVNode(" 人 ")]),_:1},8,["modelValue","formatter","parser"])):(e.openBlock(),e.createElementBlock("div",le,[e.createVNode(g,{link:"",type:"primary",onClick:k},{default:e.withCtx(()=>[e.createTextVNode("添加拼团阶梯")]),_:1}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value.users,(l,q)=>(e.openBlock(),e.createElementBlock("div",{key:q,class:"groupForm__stairs"},[e.createElementVNode("span",re,"第"+e.toDisplayString(q+1)+"阶段人数",1),e.createVNode(i,{modelValue:t.value.users[q],"onUpdate:modelValue":M=>t.value.users[q]=M,class:"groupForm__stairs--input",style:{width:"450px"},formatter:M=>Number(`${M}`.replace(/[^\d]/g,"")),parser:M=>`${Number(M)>=100?100:M}`,max:100},{append:e.withCtx(()=>[e.createTextVNode(" 人 ")]),_:2},1032,["modelValue","onUpdate:modelValue","formatter","parser"]),q>0?(e.openBlock(),e.createBlock(g,{key:0,type:"primary",link:"",onClick:c},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:1})):e.createCommentVNode("",!0)]))),128))]))]),_:1}),e.createVNode(m,{label:"订单关闭时间",prop:"payTimeout"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:t.value.payTimeout,"onUpdate:modelValue":o[5]||(o[5]=l=>t.value.payTimeout=l),controls:!1,max:360,min:3},null,8,["modelValue"]),ae]),_:1}),e.createVNode(m,{label:"模拟成团",prop:"simulate"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(C,{modelValue:t.value.simulate,"onUpdate:modelValue":o[6]||(o[6]=l=>t.value.simulate=l)},{default:e.withCtx(()=>[e.createVNode(f,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(f,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),se])]),_:1}),e.createVNode(m,{label:"活动预热",prop:"preheat"},{default:e.withCtx(()=>[e.createElementVNode("div",null,[e.createVNode(C,{modelValue:t.value.preheat,"onUpdate:modelValue":o[7]||(o[7]=l=>t.value.preheat=l)},{default:e.withCtx(()=>[e.createVNode(f,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(f,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),e.createElementVNode("div",null,[t.value.preheat?(e.openBlock(),e.createBlock(i,{key:0,modelValue:t.value.preheatHours,"onUpdate:modelValue":o[8]||(o[8]=l=>t.value.preheatHours=l),style:{width:"450px"},formatter:l=>Number(`${l}`.replace(/[^\d]/g,"")),parser:l=>`${Number(l)>=24?24:l}`,max:100},{append:e.withCtx(()=>[e.createTextVNode(" 小时 ")]),_:1},8,["modelValue","formatter","parser"])):(e.openBlock(),e.createElementBlock("div",ne,"开启后，商品详情展示未开始的拼团活动，但活动开始前用户无法拼团购买。"))])])]),_:1}),e.createVNode(m,{label:"叠加优惠"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:t.value.stackable.vip,"onUpdate:modelValue":o[9]||(o[9]=l=>t.value.stackable.vip=l),label:"会员价"},null,8,["modelValue"]),e.createVNode(D,{modelValue:t.value.stackable.coupon,"onUpdate:modelValue":o[10]||(o[10]=l=>t.value.stackable.coupon=l),label:"优惠券"},null,8,["modelValue"]),e.createVNode(D,{modelValue:t.value.stackable.full,"onUpdate:modelValue":o[11]||(o[11]=l=>t.value.stackable.full=l),label:"满减"},null,8,["modelValue"]),de]),_:1}),e.createVNode(m,{label:"适用商品"},{default:e.withCtx(()=>[e.createVNode(g,{type:"primary",link:"",onClick:r},{default:e.withCtx(()=>[e.createTextVNode("选择商品")]),_:1})]),_:1})]),_:1},8,["model","rules"]),e.createVNode(U,{modelValue:S.value,"onUpdate:modelValue":o[12]||(o[12]=l=>S.value=l),"search-param":B,"onUpdate:searchParam":o[13]||(o[13]=l=>B=l),onOnConfirm:n},null,8,["modelValue","search-param"]),e.createVNode(Z,{ref_key:"selectGoodsTableRef",ref:G,mode:t.value.mode,users:t.value.users,"product-list":$.value,"is-edit":T,"flat-good-list":A.value},null,8,["mode","users","product-list","flat-good-list"])]),e.createElementVNode("div",ie,[e.createVNode(g,{round:"",onClick:y},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(g,{type:"primary",round:"",disabled:T,onClick:u},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])])}}}),_e="";return J(ce,[["__scopeId","data-v-c95518e9"]])});
