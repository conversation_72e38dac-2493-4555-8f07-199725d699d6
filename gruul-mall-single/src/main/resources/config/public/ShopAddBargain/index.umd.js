(function(e,C){typeof exports=="object"&&typeof module<"u"?module.exports=C(require("vue"),require("vue-router"),require("@/components/q-input-number/q-input-number.vue"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("element-plus"),require("@/composables/useConvert"),require("@/utils/date"),require("@/store/modules/shopInfo"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/q-input-number/q-input-number.vue","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","element-plus","@/composables/useConvert","@/utils/date","@/store/modules/shopInfo","@/apis/http"],C):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopAddBargain=C(e.ShopAddBargainContext.Vue,e.ShopAddBargainContext.VueRouter,e.ShopAddBargainContext.QInputNumber,e.ShopAddBargainContext.QChooseGoodsPopup,e.ShopAddBargainContext.ElementPlus,e.ShopAddBargainContext.UseConvert,e.ShopAddBargainContext.DateUtil,e.ShopAddBargainContext.ShopInfoStore,e.ShopAddBargainContext.Request))})(this,function(e,C,j,K,b,Q,W,X,z){"use strict";var H=document.createElement("style");H.textContent=`@charset "UTF-8";.com[data-v-040adf84]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-040adf84]{width:62px;height:62px}.com__name[data-v-040adf84]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.add[data-v-d7920af4]{margin:-20px -15px;height:calc(100vh - 85px);overflow-y:auto;scrollbar-width:none;-ms-overflow-style:none}.add[data-v-d7920af4]::-webkit-scrollbar{display:none}.bargaining_amount[data-v-d7920af4]{position:relative;width:100%}.bargaining_amount__description[data-v-d7920af4]{position:absolute;top:-35px;right:0;width:480px}.title[data-v-d7920af4]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-d7920af4]{font-size:12px;margin-left:12px;color:#c4c4c4}.use_discount[data-v-d7920af4]{display:flex;width:100%}.discount_msg[data-v-d7920af4]{display:inline-block;width:400px;flex:1}.rules[data-v-d7920af4]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-d7920af4]{width:300px;display:flex}.text[data-v-d7920af4]{font-size:14px;color:#333}.goodsData[data-v-d7920af4]{border:1px solid #ccc}.goods-list[data-v-d7920af4]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-d7920af4]{display:flex}.goods-list__goods-list__info-name[data-v-d7920af4]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-d7920af4]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-d7920af4]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-d7920af4]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-d7920af4]{font-size:16px}.ruleform-date[data-v-d7920af4]{width:100%;display:flex;align-items:center}.flex[data-v-d7920af4]{margin-top:10px;height:50px}.flex-item[data-v-d7920af4]{width:40%}.coupon-rules[data-v-d7920af4]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-d7920af4]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild(H);const Z={class:"com"},v={class:"com__name"},ee=e.defineComponent({__name:"select-goods-table",props:{productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}}},setup(_,{expose:E}){const h=_,{divTenThousand:x,mulTenThousand:N}=Q(),T=e.ref([]);e.watch(()=>h.productList,a=>{const m=M(a);T.value=D(m)}),e.watch(()=>h.flatGoodList,a=>{T.value=D(a)});function I(a){return a.skuItem.stockType==="LIMITED"?Number(a.skuItem.skuStock):1/0}function M(a,m){if(!a.length)return[];const c=[];return a.forEach(d=>{d.skuIds.forEach((u,f)=>{c.push({productId:d.productId,productName:d.productName,productPic:d.pic,skuItem:{productId:d.productId,skuId:u,skuName:d.specs[f],skuPrice:d.salePrices[f],skuStock:d.stocks[f],stockType:d.stockTypes[f]},rowTag:0,stock:0,isJoin:!0,floorPrice:.01})})}),c}function D(a,m){let c=0,d=a.length;for(let u=0;u<d;u++){const f=a[u];u===0&&(f.rowTag=1,c=0),u!==0&&(f.productId===a[u-1].productId?(f.rowTag=0,a[c].rowTag=a[c].rowTag+1):(f.rowTag=1,c=u))}return a}const o=({row:a,column:m,rowIndex:c,columnIndex:d})=>{if(d===0)return{rowspan:a.rowTag,colspan:a.rowTag?1:0}};function p(a){return a.stockType==="UNLIMITED"?"不限购":a.skuStock}function G(){return e.toRaw(T.value).filter(m=>m.isJoin).map(m=>{const{productId:c,productPic:d,floorPrice:u,productName:f,stock:B,skuItem:{skuId:P,skuStock:i,skuPrice:V,skuName:Y,stockType:L}}=m;return{activityId:"",productId:c,productPic:d,floorPrice:N(u).toString(),productName:f,stock:B,skuId:P,skuStock:+i,skuPrice:V,skuName:Y,stockType:L}})}function S(){let a=!0;const m=T.value;if(!m.length)b.ElMessage.warning("请选择商品"),a=!1;else for(let c=0;c<m.length;c++)if(m[c].isJoin&&!m[c].stock){b.ElMessage.warning("商品库存必须大于零"),a=!1;break}return a}return E({getProduct:G,validateProduct:S}),(a,m)=>{const c=e.resolveComponent("el-image"),d=e.resolveComponent("el-table-column"),u=e.resolveComponent("el-input"),f=e.resolveComponent("el-input-number"),B=e.resolveComponent("el-switch"),P=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(P,{data:T.value,"span-method":o,"max-height":500},{default:e.withCtx(()=>[e.createVNode(d,{label:"商品信息",width:"215"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",Z,[e.createVNode(c,{class:"com__pic",src:i.productPic},null,8,["src"]),e.createElementVNode("div",v,e.toDisplayString(i.productName),1)])]),_:1}),e.createVNode(d,{label:"规格"},{default:e.withCtx(({row:i})=>[e.createTextVNode(e.toDisplayString(i.skuItem.skuName),1)]),_:1}),e.createVNode(d,{label:"砍价底价（元）"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",null,[h.isEdit?(e.openBlock(),e.createBlock(u,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(x)(i.floorPrice).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(f,{key:1,modelValue:i.floorPrice,"onUpdate:modelValue":V=>i.floorPrice=V,min:.01,style:{width:"80px"},disabled:h.isEdit,precision:2,max:e.unref(x)(i.skuItem.skuPrice).sub(.01).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"实售价"+e.toDisplayString(e.unref(x)(i.skuItem.skuPrice)),1)]),_:1}),e.createVNode(d,{label:"砍价库存"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",null,[e.createVNode(f,{"model-value":+i.stock,min:0,style:{width:"80px"},max:I(i),disabled:h.isEdit,precision:0,controls:!1,"onUpdate:modelValue":V=>i.stock=V},null,8,["model-value","max","disabled","onUpdate:modelValue"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(p(i.skuItem)),1)]),_:1}),e.createVNode(d,{label:"是否参与"},{default:e.withCtx(({row:i})=>[e.createVNode(B,{modelValue:i.isJoin,"onUpdate:modelValue":V=>i.isJoin=V,size:"large",disabled:h.isEdit},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),ye="",J=(_,E)=>{const h=_.__vccOpts||_;for(const[x,N]of E)h[x]=N;return h},te=J(ee,[["__scopeId","data-v-040adf84"]]),O="addon-bargain/bargain/",oe=_=>z.post({url:O,data:_}),ae=_=>z.get({url:O+`${_.shopId}/${_.activityId}`}),g=_=>(e.pushScopeId("data-v-d7920af4"),_=_(),e.popScopeId(),_),le={style:{padding:"40px"},class:"add"},ne=g(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),re={class:"ruleform-date"},de=g(()=>e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1)),ie=g(()=>e.createElementVNode("span",{class:"msg"},"砍到底价所需人数",-1)),se=g(()=>e.createElementVNode("span",{class:"msg"},"砍价有效期是指从用户发起砍价到砍价截止的时间",-1)),ce=g(()=>e.createElementVNode("span",{class:"msg"},"是否用户发起砍价的同时为自己砍1次价",-1)),pe=g(()=>e.createElementVNode("span",{class:"msg"},"新用户是指未在本店铺购买过商品的用户",-1)),me=g(()=>e.createElementVNode("span",null,"活动前",-1)),fe=g(()=>e.createElementVNode("span",null,"开启活动预热",-1)),ue=g(()=>e.createElementVNode("span",{class:"msg"},"预热阶段用户可看到砍价活动，但活动开始前无法发起砍价",-1)),_e={class:"use_discount"},ge=g(()=>e.createElementVNode("div",{class:"msg discount_msg"},[e.createTextVNode(" 叠加优惠可能导致实付金额为 "),e.createElementVNode("strong",{style:{color:"red"}},"0"),e.createTextVNode(" (实付金额 = 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1)),he={class:"bargaining_amount"},Ve=g(()=>e.createElementVNode("div",null,[e.createElementVNode("p",{class:"msg"},"a.固定砍价 =（原价 - 砍价底价）/砍价人数"),e.createElementVNode("p",{class:"msg"}," b.随机砍价：最低砍价金额 = 1 ，最高砍价金额 = (原价 - 砍价底价) * 100 / 砍价到底人数 * 2 单位：分，最后一人砍完剩余价格 ")],-1)),be=g(()=>e.createElementVNode("span",{class:"msg"},"是否参与：SKU的粒度设置商品是否参与活动，是(默认)则参与活动，反则否",-1)),xe=e.defineComponent({__name:"ShopAddBargain",setup(_){const E=C.useRouter(),h=C.useRoute(),x=new W,N=e.ref(),T=e.reactive({form:{shopId:"",shopName:X.useShopInfoStore().shopInfo.name,name:"",startTime:"",endTime:"",bargainingPeople:0,bargainValidityPeriod:5,isSelfBargain:!1,userType:"NEW_USER",activityPreheat:0,stackable:{coupon:!1,vip:!1,full:!1},status:"ILLEGAL_SELL_OFF",helpCutAmount:"FIX_BARGAIN",bargainProducts:[],productNum:0},isEditDisable:!1,fullReductionTime:[],rules:{name:[{required:!0,message:"请输入活动名称",trigger:"blur"}],product:[{required:!0,message:"请选择商品",trigger:["blur","change"]}],startTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]},{validator:V,trigger:["blur","change"]}],endTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]},{validator:Y,trigger:["blur","change"]}]},chooseGoodsPopup:!1}),I=e.reactive({maxPrice:"",minPrice:"",activity:{endTime:"",startTime:""},keyword:"",categoryFirstId:""}),M=e.ref([]),D=e.ref([]),{form:o,isEditDisable:p,rules:G,chooseGoodsPopup:S}=e.toRefs(T),a=e.ref(),m={79111:"砍价商品在相同时间段内已存在",79112:"砍价活动不存在",79113:"当前商品不是砍价商品"};c();async function c(s=h.query){if(s.shopId&&s.activityId){p.value=!0;const{code:t,data:l,msg:r}=await ae(s);if(t!==200)return b.ElMessage.error(r||"获取活动详情失败");o.value=l,o.value.bargainProducts=l.bargainActivityProducts,o.value.bargainValidityPeriod=+l.bargainActivityProducts,D.value=d(l.bargainActivityProducts)}}function d(s){return s.map(t=>{const{activityId:l,productId:r,skuPrice:k,productPic:F,productName:w,skuId:A,skuStock:y,stock:$,skuName:U,floorPrice:q}=t;return{floorPrice:q,isJoin:!0,productId:r,productName:w,productPic:F,stock:$,skuItem:{productId:r,skuId:A,skuName:U,skuPrice:k,skuStock:y}}})}const u=async()=>{if(!(!a.value||!await a.value.validate())&&N.value&&N.value.validateProduct()){o.value.bargainProducts=N.value.getProduct(),o.value.productNum=o.value.bargainProducts.length;const{code:t,data:l,msg:r}=await oe(o.value);if(t===200)return f();if([79111,79112,79113].includes(t))return b.ElMessage.error(m[t]||"添加活动失败");b.ElMessage.error(r||"添加活动失败")}};function f(){b.ElMessage.success("添加活动成功"),E.push({name:"bargainIndex"})}const B=s=>{M.value=s.tempGoods};function P(s){const t=x.getYMD(new Date),l=x.getYMD(s);return t===l?!1:new Date().getTime()>s.getTime()}const i=async()=>{if(a.value&&o.value.endTime&&o.value.startTime){let s=!0,t=!0;if(await a.value.validateField("startTime",l=>{s=l}),await a.value.validateField("endTime",l=>{t=l}),s&&t){const{startTime:l,endTime:r}=o.value;I.activity.startTime=`${l}`,I.activity.endTime=`${r}`,S.value=!0;return}return b.ElMessage.warning("活动时间选择有误")}return b.ElMessage.warning("请选择活动时间")};function V(s,t,l){t?t&&o.value.endTime?R(new Date(o.value.endTime).getTime(),l,"开始日期和结束日期最少间隔5分钟",new Date(t).getTime(),1e3*60*5):R(new Date(t).getTime(),l,"开始日期必须是一个将来的时间",new Date().getTime(),1e3):l(new Error("请选择活动开始日期"))}function Y(s,t,l){t?t&&o.value.startTime?R(new Date(t).getTime(),l,"开始日期和结束日期最少间隔5分钟",new Date(o.value.startTime).getTime(),1e3*60*5):R(new Date(t).getTime(),l,"结束日期最少大当前时间5分钟",new Date().getTime()+1e3,1e3*60*5):l(new Error("请选择活动结束日期"))}function L(s,t=new Date().getTime()){const l=s-t;return(r=1e3)=>l>=r}function R(s,t,l,r,k){L(s,r)(k)?t():t(new Error(l))}return(s,t)=>{const l=e.resolveComponent("el-input"),r=e.resolveComponent("el-form-item"),k=e.resolveComponent("el-date-picker"),F=e.resolveComponent("el-input-number"),w=e.resolveComponent("el-radio"),A=e.resolveComponent("el-radio-group"),y=e.resolveComponent("el-option"),$=e.resolveComponent("el-select"),U=e.resolveComponent("el-checkbox"),q=e.resolveComponent("el-button"),Ne=e.resolveComponent("el-form"),we=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",le,[ne,e.createVNode(Ne,{ref_key:"ruleFormRef",ref:a,model:e.unref(o),rules:e.unref(G),"label-width":"auto","inline-message":!1,"label-position":"left"},{default:e.withCtx(()=>[e.createVNode(r,{label:"活动名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(l,{modelValue:e.unref(o).name,"onUpdate:modelValue":t[0]||(t[0]=n=>e.unref(o).name=n),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"10",placeholder:"活动名称不超过10个字",disabled:e.unref(p)},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(r,{label:"活动日期",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",re,[e.createVNode(r,{prop:"startTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:e.unref(o).startTime,"onUpdate:modelValue":t[1]||(t[1]=n=>e.unref(o).startTime=n),type:"datetime",disabled:e.unref(p),placeholder:"请选择开始时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":P},null,8,["modelValue","disabled"]),de]),_:1}),e.createVNode(r,{prop:"endTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:e.unref(o).endTime,"onUpdate:modelValue":t[2]||(t[2]=n=>e.unref(o).endTime=n),disabled:e.unref(p),type:"datetime",placeholder:"请选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":P},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(r,{label:"砍价到底的人数",prop:"bargainingPeople",required:""},{default:e.withCtx(()=>[e.createVNode(F,{modelValue:e.unref(o).bargainingPeople,"onUpdate:modelValue":t[3]||(t[3]=n=>e.unref(o).bargainingPeople=n),style:{width:"135px"},disabled:e.unref(p),precision:0,controls:!1,min:2},null,8,["modelValue","disabled"]),ie]),_:1}),e.createVNode(r,{label:"砍价有效期",prop:"bargainValidityPeriod",required:""},{default:e.withCtx(()=>[e.createVNode(j,{modelValue:e.unref(o).bargainValidityPeriod,"onUpdate:modelValue":t[4]||(t[4]=n=>e.unref(o).bargainValidityPeriod=n),controls:!1,precision:0,min:5,disabled:e.unref(p)},{append:e.withCtx(()=>[e.createTextVNode(" 分钟 ")]),_:1},8,["modelValue","disabled"]),se]),_:1}),e.createVNode(r,{label:"是否自我砍价",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createVNode(A,{modelValue:e.unref(o).isSelfBargain,"onUpdate:modelValue":t[5]||(t[5]=n=>e.unref(o).isSelfBargain=n),class:"ml-4",disabled:e.unref(p)},{default:e.withCtx(()=>[e.createVNode(w,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("否")]),_:1}),e.createVNode(w,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("是")]),_:1})]),_:1},8,["modelValue","disabled"]),ce]),_:1}),e.createVNode(r,{label:"砍价用户类型",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createVNode(A,{modelValue:e.unref(o).userType,"onUpdate:modelValue":t[6]||(t[6]=n=>e.unref(o).userType=n),class:"ml-4",disabled:e.unref(p)},{default:e.withCtx(()=>[e.createVNode(w,{label:"UNLIMITED"},{default:e.withCtx(()=>[e.createTextVNode("不限")]),_:1}),e.createVNode(w,{label:"NEW_USER"},{default:e.withCtx(()=>[e.createTextVNode("新用户")]),_:1})]),_:1},8,["modelValue","disabled"]),pe]),_:1}),e.createVNode(r,{label:"活动预热"},{default:e.withCtx(()=>[me,e.createVNode($,{modelValue:e.unref(o).activityPreheat,"onUpdate:modelValue":t[7]||(t[7]=n=>e.unref(o).activityPreheat=n),style:{width:"135px",margin:"0 10px"},maxlength:"15",placeholder:"请选择",disabled:e.unref(p)},{default:e.withCtx(()=>[e.createVNode(y,{label:"请选择",value:0}),e.createVNode(y,{label:"1小时",value:1}),e.createVNode(y,{label:"2小时",value:2}),e.createVNode(y,{label:"3小时",value:3}),e.createVNode(y,{label:"4小时",value:4}),e.createVNode(y,{label:"5小时",value:5})]),_:1},8,["modelValue","disabled"]),fe,ue]),_:1}),e.createVNode(r,{label:"使用优惠"},{default:e.withCtx(()=>[e.createElementVNode("div",_e,[e.createVNode(U,{modelValue:e.unref(o).stackable.vip,"onUpdate:modelValue":t[8]||(t[8]=n=>e.unref(o).stackable.vip=n),label:"会员价",disabled:e.unref(p)},null,8,["modelValue","disabled"]),e.createVNode(U,{modelValue:e.unref(o).stackable.coupon,"onUpdate:modelValue":t[9]||(t[9]=n=>e.unref(o).stackable.coupon=n),label:"优惠券",disabled:e.unref(p)},null,8,["modelValue","disabled"]),e.createVNode(U,{modelValue:e.unref(o).stackable.full,"onUpdate:modelValue":t[10]||(t[10]=n=>e.unref(o).stackable.full=n),label:"满减",disabled:e.unref(p)},null,8,["modelValue","disabled"]),ge])]),_:1}),e.createVNode(r,{label:"单次砍价金额范围",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createElementVNode("div",he,[e.createVNode(A,{modelValue:e.unref(o).helpCutAmount,"onUpdate:modelValue":t[11]||(t[11]=n=>e.unref(o).helpCutAmount=n),class:"ml-4",disabled:e.unref(p)},{default:e.withCtx(()=>[e.createVNode(w,{label:"RANDOM_BARGAIN"},{default:e.withCtx(()=>[e.createTextVNode("随机砍价")]),_:1}),e.createVNode(w,{label:"FIX_BARGAIN"},{default:e.withCtx(()=>[e.createTextVNode("固定砍价")]),_:1})]),_:1},8,["modelValue","disabled"])]),Ve]),_:1}),e.createVNode(r,{label:"活动商品",required:""},{default:e.withCtx(()=>[e.createVNode(q,{type:"primary",round:"",plain:"",disabled:e.unref(p),onClick:i},{default:e.withCtx(()=>[e.createTextVNode(" 选择商品 ")]),_:1},8,["disabled"]),be]),_:1})]),_:1},8,["model","rules"]),e.createVNode(te,{ref_key:"selectGoodsTableRef",ref:N,"product-list":M.value,"is-edit":e.unref(p),"flat-good-list":D.value},null,8,["product-list","is-edit","flat-good-list"]),e.createVNode(we,{justify:"center",style:{"margin-top":"60px"}},{default:e.withCtx(()=>[e.createVNode(q,{round:"",plain:"",onClick:t[12]||(t[12]=n=>e.unref(E).back())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.unref(p)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(q,{key:0,type:"primary",round:"",onClick:u},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1}))]),_:1})]),e.createVNode(K,{modelValue:e.unref(S),"onUpdate:modelValue":t[13]||(t[13]=n=>e.isRef(S)?S.value=n:null),"search-param":I,"onUpdate:searchParam":t[14]||(t[14]=n=>I=n),onOnConfirm:B},null,8,["modelValue","search-param"])])}}}),Te="";return J(xe,[["__scopeId","data-v-d7920af4"]])});
