(function(e,f){typeof exports=="object"&&typeof module<"u"?module.exports=f(require("vue"),require("element-plus"),require("@/apis/http"),require("@/composables/useConvert"),require("lodash")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/apis/http","@/composables/useConvert","lodash"],f):(e=typeof globalThis<"u"?globalThis:e||self,e.PaidMember=f(e.PaidMemberContext.Vue,e.PaidMemberContext.ElementPlus,e.PaidMemberContext.Request,e.PaidMemberContext.UseConvert,e.PaidMemberContext.Lodash))})(this,function(e,f,w,A,z){"use strict";var G=document.createElement("style");G.textContent=`.title[data-v-415c131f]{font-size:14px;color:#323233;font-weight:700;margin-bottom:20px}.msg[data-v-415c131f]{font-size:12px;color:#c4c4c4}.nav-button[data-v-415c131f]{width:1010px;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin:0 auto 0 -55px}.level[data-v-3ad90267]{font-size:12px;color:#ce732f}.pricing[data-v-3ad90267]{width:147px;font-size:12px;color:#838383;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.interests[data-v-3ad90267]{width:140px;font-size:12px;color:#333;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(G);const F=()=>w.get({url:"addon-member/paid/member/list "}),q=()=>w.get({url:"gruul-mall-user/user/member/rights/usable"}),j=a=>w.post({url:"addon-member/paid/member/save",data:a}),Y=a=>w.post({url:"addon-member/paid/member/update",data:a}),W=a=>w.get({url:`addon-member/paid/member/info?id=${a}`}),K=a=>w.del({url:`addon-member/paid/member/${a}`}),Q=a=>(e.pushScopeId("data-v-415c131f"),a=a(),e.popScopeId(),a),X={style:{padding:"0 40px"}},Z=Q(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),v={style:{width:"450px"}},ee={style:{width:"100%"}},te=e.defineComponent({__name:"EditPayingMembers",props:{memberId:{type:String,default:""},memberLevel:{type:Number,default:void 0}},setup(a,{expose:M}){const u=a,{mulHundred:y,divHundred:c,divTenThousand:V,mulTenThousand:k}=A();e.ref(!1);const R=e.ref(),g=e.ref(new Map),L=e.ref([]),x=e.ref(0),C=e.ref(0),r=e.reactive({id:"",paidMemberName:"",paidRuleJson:[],relevancyRightsList:[]}),D={INTEGRAL_MULTIPLE:P,GOODS_DISCOUNT:se},p={INTEGRAL_MULTIPLE:"积分值为2-9.9倍保留一位小数",GOODS_DISCOUNT:"商品折扣值为0.1-9.9折保留一位小数"},b=[{name:"1月",label:"ONE_MONTH"},{name:"三个月",label:"THREE_MONTH"},{name:"12个月",label:"TWELVE_MONTH"},{name:"3年",label:"THREE_YEAR"},{name:"5年",label:"FIVE_YEAR"}],N=e.reactive({paidMemberName:[{required:!0,message:"请输入会员名称",trigger:"blur"}],paidRuleJson:[{validator:T,trigger:"blur"}]});function T(t,o,n){for(let i=0;i<o.length;i++)if(!o[i].effectiveDurationType)return n("请完善付费规则");n()}ce();const I=async()=>{if(!r.paidRuleJson.length)return f.ElMessage.error("请填写付费规则"),Promise.reject("请填写付费规则");const t=R.value;if(await s(t),m()){const o=z.cloneDeep(r);o.relevancyRightsList=Array.from(g.value.values()).map(d=>(d.rightsType==="GOODS_DISCOUNT"?d.extendValue=Number(y(x.value)):d.rightsType==="INTEGRAL_MULTIPLE"?d.extendValue=Number(y(C.value)):delete d.extendValue,d)),o.paidRuleJson=o.paidRuleJson.map(d=>(d.price=Number(k(d.price)),d));const{code:n,msg:i}=u.memberId?await Y(o):await j(o);return n===200?(f.ElMessage.success("保存成功"),Promise.resolve("保存成功")):(f.ElMessage.error(i||"保存失败"),Promise.reject(i||"保存失败"))}else return Promise.reject("校验失败")},S=()=>{r.paidRuleJson.push({price:0,effectiveDurationType:""})},B=t=>{r.paidRuleJson.splice(t,1)},O=t=>{g.value.has(t.memberRightsId)?g.value.delete(t.memberRightsId):g.value.set(t.memberRightsId,t)};function s(t){return t?new Promise((o,n)=>{t==null||t.validate((i,d)=>{i?o("success valid"):n(d)})}):Promise.reject(new Error("no form instance input"))}function m(){const t=[];g.value.forEach(n=>{D[n.rightsType]&&(console.log("tempArr",n.rightsType==="GOODS_DISCOUNT"?x.value:C.value),t.push({type:D[n.rightsType](n.rightsType==="GOODS_DISCOUNT"?x.value:C.value),tips:p[n.rightsType]||""}))});const o=t.filter(n=>!n.type);return o.length&&f.ElMessage.warning(o[0].tips),!o.length}function P(t){const o=Number(t);return o>=2&&o<=9.9&&H(String(t))}function H(t,o){const n=/^\d+(\.\d?)?$/;return t!==""?n.test(t):!1}function se(t){console.log(t);const o=Number(t);return o>=.1&&o<=9.9&&H(String(t))}async function ce(){const{code:t,data:o}=await q();if(t!==200)return f.ElMessage.error("获取会员权益失败");L.value=o,pe()}async function pe(){if(u.memberId){const{code:t,data:o,msg:n}=await W(String(u.memberId));if(t===200&&o){const{id:i,paidMemberName:d,paidRuleJson:U,relevancyRightsList:E}=o;r.id=i,r.paidMemberName=d,r.paidRuleJson=U.map(h=>(h.price=Number(V(h.price)),h)),r.relevancyRightsList=E,E.forEach(h=>{h.rightsType==="GOODS_DISCOUNT"?x.value=Number(c(h.extendValue)):h.rightsType==="INTEGRAL_MULTIPLE"&&(C.value=Number(c(h.extendValue))),g.value.set(h.memberRightsId,h)})}else f.ElMessage.error(n||"获取失败")}}return M({handleSubmit:I}),(t,o)=>{const n=e.resolveComponent("el-form-item"),i=e.resolveComponent("el-input"),d=e.resolveComponent("el-option"),U=e.resolveComponent("el-select"),E=e.resolveComponent("el-table-column"),h=e.resolveComponent("el-input-number"),$=e.resolveComponent("el-link"),me=e.resolveComponent("el-table"),_e=e.resolveComponent("el-checkbox"),fe=e.resolveComponent("el-row"),he=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",X,[Z,e.createVNode(he,{ref_key:"ruleFormRef",ref:R,model:r,rules:N,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(n,{label:"会员等级"},{default:e.withCtx(()=>[e.createTextVNode(" SVIP"+e.toDisplayString(u.memberLevel),1)]),_:1}),e.createVNode(n,{label:"等级名称",prop:"paidMemberName"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:r.paidMemberName,"onUpdate:modelValue":o[0]||(o[0]=l=>r.paidMemberName=l),modelModifiers:{trim:!0},maxlength:8,minlength:3,placeholder:"请输入等级名称",style:{width:"226px"}},null,8,["modelValue"])]),_:1}),e.createVNode(n,{label:"付费规则",prop:"paidRuleJson",required:""},{default:e.withCtx(()=>[e.withDirectives(e.createElementVNode("div",v,[e.createVNode(me,{data:r.paidRuleJson,height:r.paidRuleJson.length>4?"220px":t.undef,size:"small",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(E,{label:"有效期"},{default:e.withCtx(({row:l})=>[e.createVNode(U,{modelValue:l.effectiveDurationType,"onUpdate:modelValue":_=>l.effectiveDurationType=_,class:"m-2",placeholder:"请选择"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(b,_=>e.createVNode(d,{key:_.name,label:_.name,value:_.label},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(_.name),1)]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(E,{label:"价格"},{default:e.withCtx(({row:l})=>[e.createVNode(h,{modelValue:l.price,"onUpdate:modelValue":_=>l.price=_,min:.01,precision:2,"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(E,{label:"操作",width:"50px"},{default:e.withCtx(({$index:l})=>[e.createVNode($,{underline:!1,type:"primary",onClick:_=>B(l)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","height"])],512),[[e.vShow,r.paidRuleJson.length]]),e.createElementVNode("div",ee,[e.createVNode($,{underline:!1,type:"primary",onClick:S},{default:e.withCtx(()=>[e.createTextVNode("添加规则")]),_:1})])]),_:1}),e.createVNode(n,{label:"权益"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(L.value,l=>(e.openBlock(),e.createBlock(fe,{key:l.id,style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(n,{"label-width":"0",style:{margin:"10px 0"}},{default:e.withCtx(()=>[(e.openBlock(),e.createBlock(_e,{key:l.id+g.value.has(l.id),checked:g.value.has(l.id),label:{name:l.rightsName,memberRightsId:l.id,extendValue:0},onChange:_=>O({name:l.rightsName,rightsType:l.rightsType,memberRightsId:l.id,extendValue:0})},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(l.rightsName),1)]),_:2},1032,["checked","label","onChange"])),l.rightsType==="GOODS_DISCOUNT"?(e.openBlock(),e.createBlock(i,{key:0,modelValue:x.value,"onUpdate:modelValue":o[1]||(o[1]=_=>x.value=_),style:{width:"130px",margin:"0 20px"}},{append:e.withCtx(()=>[e.createTextVNode("折")]),_:1},8,["modelValue"])):l.rightsType==="INTEGRAL_MULTIPLE"?(e.openBlock(),e.createBlock(i,{key:1,modelValue:C.value,"onUpdate:modelValue":o[2]||(o[2]=_=>C.value=_),style:{width:"130px",margin:"0 20px"}},{append:e.withCtx(()=>[e.createTextVNode("倍")]),_:1},8,["modelValue"])):e.createCommentVNode("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["model","rules"])])}}}),be="",J=(a,M)=>{const u=a.__vccOpts||a;for(const[y,c]of M)u[y]=c;return u},oe=J(te,[["__scopeId","data-v-415c131f"]]),ne={class:"level"},le={class:"pricing"},re={key:0},ae={key:1},ie={key:2},de=e.defineComponent({__name:"PaidMember",setup(a){const{divTenThousand:M,divHundred:u}=A(),y=e.ref([]),c=e.reactive({id:"",currentMemberLevel:void 0}),V=e.ref(!1),k=e.ref(null);r();const R=async p=>{f.ElMessageBox.confirm("确定需要删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:b,msg:N}=await K(p);b===200&&(f.ElMessage.success("删除成功"),r())})},g=()=>{c.id="",c.currentMemberLevel=y.value.length+1,V.value=!0},L=(p,b)=>{c.id=p,c.currentMemberLevel=b,V.value=!0},x=()=>{V.value=!1,c.id="",c.currentMemberLevel=void 0},C=async()=>{var p;await((p=k==null?void 0:k.value)==null?void 0:p.handleSubmit()),x(),r()};async function r(){const{code:p,data:b,msg:N}=await F();p===200?y.value=b:f.ElMessage.error(N||"获取列表失败")}function D(p){return`${{ONE_MONTH:"1个月",THREE_MONTH:"3个月",TWELVE_MONTH:"12个月",THREE_YEAR:"三年",FIVE_YEAR:"五年"}[p.effectiveDurationType]}${M(p.price)}`}return(p,b)=>{const N=e.resolveComponent("el-button"),T=e.resolveComponent("el-table-column"),I=e.resolveComponent("el-link"),S=e.resolveComponent("el-table"),B=e.resolveComponent("el-dialog"),O=e.resolveComponent("el-tab-pane");return e.openBlock(),e.createBlock(O,{label:"付费会员",name:"PayingMember"},{default:e.withCtx(()=>[e.createVNode(N,{round:"",style:{"margin-bottom":"15px"},type:"primary",onClick:g},{default:e.withCtx(()=>[e.createTextVNode("添加等级")]),_:1}),e.createVNode(S,{ref:"multipleTableRef","cell-style":{fontSize:"12px",color:"#333333"},data:y.value,"header-cell-style":{background:"#f6f8fa",fontWeight:400,color:"#333333"},"header-row-style":{fontSize:"12px",color:"#909399"}},{default:e.withCtx(()=>[e.createVNode(T,{align:"center",label:"会员等级"},{default:e.withCtx(({$index:s})=>[e.createElementVNode("div",ne,"SVIP"+e.toDisplayString(s+1),1)]),_:1}),e.createVNode(T,{align:"center",label:"付费会员名称"},{default:e.withCtx(({row:s})=>[e.createElementVNode("span",null,e.toDisplayString(s.paidMemberName),1)]),_:1}),e.createVNode(T,{align:"center",label:"定价",width:"160px"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",le,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.paidRuleJson,m=>(e.openBlock(),e.createElementBlock("div",{key:m.id},e.toDisplayString(D(m))+"元",1))),128))])]),_:1}),e.createVNode(T,{align:"center",label:"会员权益",width:"150px"},{default:e.withCtx(({row:s})=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.relevancyRightsList,m=>e.withDirectives((e.openBlock(),e.createElementBlock("div",{key:m.id,class:"interests"},[m.rightsType==="GOODS_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",re,"商品折扣"+e.toDisplayString(e.unref(u)(m.extendValue))+"折",1)):m.rightsType==="INTEGRAL_MULTIPLE"?(e.openBlock(),e.createElementBlock("div",ae,"积分"+e.toDisplayString(e.unref(u)(m.extendValue))+"倍",1)):(e.openBlock(),e.createElementBlock("div",ie,e.toDisplayString(m.rightsName),1))])),[[e.vShow,s.relevancyRightsList.length]])),128))]),_:1}),e.createVNode(T,{align:"center",fixed:"right",label:"操作",width:"150"},{default:e.withCtx(({row:s,$index:m})=>[e.createVNode(I,{underline:!1,size:"small",style:{padding:"0 5px"},type:"primary",onClick:P=>L(s.id,m+1)},{default:e.withCtx(()=>[e.createTextVNode(" 编辑 ")]),_:2},1032,["onClick"]),y.value.length-1===m?(e.openBlock(),e.createBlock(I,{key:0,underline:!1,size:"small",style:{padding:"0 5px"},type:"primary",onClick:P=>R(s.id)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(B,{modelValue:V.value,"onUpdate:modelValue":b[0]||(b[0]=s=>V.value=s),title:c.id?"编辑会员":"添加会员","destroy-on-close":"",onClose:x},{footer:e.withCtx(()=>[e.createVNode(N,{onClick:x},{default:e.withCtx(()=>[e.createTextVNode("取 消")]),_:1}),e.createVNode(N,{type:"primary",onClick:C},{default:e.withCtx(()=>[e.createTextVNode("保 存")]),_:1})]),default:e.withCtx(()=>[e.createVNode(oe,{ref_key:"editMemberRef",ref:k,"member-id":c.id,"member-level":c.currentMemberLevel},null,8,["member-id","member-level"])]),_:1},8,["modelValue","title"])]),_:1})}}}),ue="";return J(de,[["__scopeId","data-v-3ad90267"]])});
