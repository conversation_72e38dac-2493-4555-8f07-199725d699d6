(function(e,l){typeof exports=="object"&&typeof module<"u"?module.exports=l(require("vue"),require("element-plus"),require("@/apis/http"),require("@/store/modules/shopInfo"),require("@/constant")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/apis/http","@/store/modules/shopInfo","@/constant"],l):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopInvoiceHeader=l(e.ShopInvoiceHeaderContext.Vue,e.ShopInvoiceHeaderContext.ElementPlus,e.ShopInvoiceHeaderContext.Request,e.ShopInvoiceHeaderContext.ShopInfoStore,e.ShopInvoiceHeaderContext.Constant))})(this,function(e,l,m,y,V){"use strict";var x=document.createElement("style");x.textContent=`.head[data-v-5583842b]{padding:0 30px 20px;display:flex;align-items:center;justify-content:space-between}.head__title[data-v-5583842b]{color:#bebebe;font-size:14px}.flex[data-v-5583842b]{display:flex}
`,document.head.appendChild(x);const b=()=>m.get({url:"addon-invoice/invoice/invoice-headers/pageInvoiceHeader",params:{size:500,ownerType:"SHOP"}}),I=n=>m.put({url:"addon-invoice/invoice/invoice-headers/default-invoice-header",data:n}),w=n=>m.post({url:"addon-invoice/invoice/invoice-headers/invoice-header",data:n}),E=n=>m.del({url:`addon-invoice/invoice/invoice-headers/${n}`}),k=n=>m.get({url:"addon-invoice/invoice/invoice-headers/",params:n}),N=n=>(e.pushScopeId("data-v-5583842b"),n=n(),e.popScopeId(),n),H={class:"head"},S=N(()=>e.createElementVNode("div",{class:"head__title"},"您向供应商申请开票时需要使用的发票信息",-1)),T={class:"flex"},v={key:0,style:{color:"rgba(24, 144, 255, 1)"}},B={class:"right_btn"},A=N(()=>e.createElementVNode("div",{style:{"text-align":"center"}},"邮箱地址用于接收电子发票",-1)),R={class:"dialog-footer"},P=e.defineComponent({__name:"ShopInvoiceHeader",setup(n){const _=y.useShopInfoStore(),u=e.ref([]),i=e.ref(!1),f=e.ref(),a=e.ref({id:"",ownerType:"SHOP",ownerId:_.getterShopInfo.id,invoiceHeaderType:"ENTERPRISE",header:"",taxIdentNo:"",openingBank:"",bankAccountNo:"",enterprisePhone:"",enterpriseAddress:"",email:""}),q=e.reactive({invoiceHeaderType:[{required:!0,message:"请选择抬头类型",trigger:"blur"}],header:[{required:!0,message:"请输入抬头",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{pattern:V.REGEX.EMAIL,message:"请输入正确的邮箱",trigger:"blur"}],taxIdentNo:[{required:!0,message:"请输入税号",trigger:"blur"}],enterprisePhone:[{pattern:V.REGEX.MOBILE,message:"请输入正确的手机号",trigger:"blur"}]});h();async function h(){const{code:d,data:t,msg:r}=await b();if(d!==200)return l.ElMessage.error(r||"获取发票抬头列表失败");u.value=t.records}async function D(d){const{code:t,data:r,msg:s}=await I({id:d,ownerType:"SHOP",ownerId:_.getterShopInfo.id});if(t!==200)return l.ElMessage.error(s||"设置默认抬头失败");h()}const M=()=>{a.value={...a.value,openingBank:"",bankAccountNo:"",enterprisePhone:"",enterpriseAddress:""}};async function O(){f.value.validate(async d=>{d&&U()})}async function U(){a.value.invoiceHeaderType==="PERSONAL"&&(a.value.taxIdentNo="");const{code:d,data:t,msg:r}=await w(a.value);if(d!==200)return l.ElMessage.error(r||"保存失败");l.ElMessage.success("保存成功"),h(),i.value=!1}const $=()=>{i.value=!1,a.value={...a.value,id:"",invoiceHeaderType:"ENTERPRISE",header:"",email:"",taxIdentNo:"",openingBank:"",bankAccountNo:"",enterprisePhone:"",enterpriseAddress:""},f.value.resetFields()},z=d=>{l.ElMessageBox.confirm("确定要删除该抬头?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:t,success:r}=await E(d);t===200&&r?(l.ElMessage.success("删除成功"),h()):l.ElMessage.error("删除失败")})},F=async d=>{const{code:t,msg:r,data:s}=await k({invoiceHeaderId:d,invoiceHeaderOwnerType:"SHOP"});if(t!==200)return l.ElMessage.error(r||"获取详情失败");a.value={...a.value,...s},i.value=!0};return(d,t)=>{const r=e.resolveComponent("el-button"),s=e.resolveComponent("el-table-column"),L=e.resolveComponent("el-table"),g=e.resolveComponent("el-radio"),G=e.resolveComponent("el-radio-group"),c=e.resolveComponent("el-form-item"),p=e.resolveComponent("el-input"),j=e.resolveComponent("el-form"),X=e.resolveComponent("el-dialog"),W=e.resolveComponent("el-tab-pane");return e.openBlock(),e.createBlock(W,{label:"发票抬头"},{default:e.withCtx(()=>[e.createElementVNode("div",H,[S,e.createVNode(r,{type:"primary",round:"",onClick:t[0]||(t[0]=o=>i.value=!0)},{default:e.withCtx(()=>[e.createTextVNode("新增")]),_:1})]),e.createVNode(L,{stripe:"",class:"freight-add_container__table",data:u.value,style:{width:"100%"},"cell-style":{height:"80px"},"header-cell-style":{fontSize:"12px",fontWeight:"bold",color:"#515151",background:"#f6f8fa"}},{default:e.withCtx(()=>[e.createVNode(s,{prop:"contactName",label:"抬头",width:"200"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",T,[o.isDefault?(e.openBlock(),e.createElementBlock("div",v,"默认")):e.createCommentVNode("",!0),e.createElementVNode("div",null,e.toDisplayString(o.header),1)])]),_:1}),e.createVNode(s,{prop:"taxIdentNo",label:"税号",width:"200"}),e.createVNode(s,{prop:"email",label:"邮箱地址",width:"200"}),e.createVNode(s,{prop:"invoiceHeaderType",label:"类型",width:"70"},{default:e.withCtx(({row:o})=>[e.createTextVNode(e.toDisplayString(o.invoiceHeaderType==="PERSONAL"?"个人":"企业"),1)]),_:1}),e.createVNode(s,{fixed:"right",label:"操作",align:"center",width:"250"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",B,[o.isDefault?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(r,{key:0,type:"primary",size:"small",link:"",onClick:C=>D(o.id)},{default:e.withCtx(()=>[e.createTextVNode("设为默认")]),_:2},1032,["onClick"])),e.createVNode(r,{type:"primary",size:"small",link:"",onClick:C=>F(o.id)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"]),e.createVNode(r,{type:"danger",size:"small",link:"",onClick:C=>z(o.id)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),e.createVNode(X,{modelValue:i.value,"onUpdate:modelValue":t[10]||(t[10]=o=>i.value=o),title:"发票抬头",width:"650px",center:"",onClose:$},{footer:e.withCtx(()=>[e.createElementVNode("span",R,[e.createVNode(r,{onClick:t[9]||(t[9]=o=>i.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(r,{type:"primary",onClick:O},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(j,{ref_key:"FormRef",ref:f,"label-position":"right","label-width":"90px",model:a.value,rules:q,style:{"max-width":"100%"}},{default:e.withCtx(()=>[e.createVNode(c,{label:"抬头类型",prop:"invoiceHeaderType"},{default:e.withCtx(()=>[e.createVNode(G,{modelValue:a.value.invoiceHeaderType,"onUpdate:modelValue":t[1]||(t[1]=o=>a.value.invoiceHeaderType=o),onChange:M},{default:e.withCtx(()=>[e.createVNode(g,{label:"ENTERPRISE"},{default:e.withCtx(()=>[e.createTextVNode("企业")]),_:1}),e.createVNode(g,{label:"PERSONAL"},{default:e.withCtx(()=>[e.createTextVNode("个人 或 事业单位")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(c,{label:"发票抬头",prop:"header",required:!0},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.value.header,"onUpdate:modelValue":t[2]||(t[2]=o=>a.value.header=o)},null,8,["modelValue"])]),_:1}),e.createVNode(c,{label:"邮箱地址",prop:"email",required:!0},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.value.email,"onUpdate:modelValue":t[3]||(t[3]=o=>a.value.email=o)},null,8,["modelValue"])]),_:1}),a.value.invoiceHeaderType==="ENTERPRISE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(c,{label:"税号",prop:"taxIdentNo",required:!0},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.value.taxIdentNo,"onUpdate:modelValue":t[4]||(t[4]=o=>a.value.taxIdentNo=o)},null,8,["modelValue"])]),_:1}),e.createVNode(c,{label:"开户行",prop:"openingBank"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.value.openingBank,"onUpdate:modelValue":t[5]||(t[5]=o=>a.value.openingBank=o)},null,8,["modelValue"])]),_:1}),e.createVNode(c,{label:"银行账号",prop:"bankAccountNo"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.value.bankAccountNo,"onUpdate:modelValue":t[6]||(t[6]=o=>a.value.bankAccountNo=o)},null,8,["modelValue"])]),_:1}),e.createVNode(c,{label:"企业电话",prop:"enterprisePhone"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.value.enterprisePhone,"onUpdate:modelValue":t[7]||(t[7]=o=>a.value.enterprisePhone=o),maxlength:"11",onkeyup:"value=value.replace(/[^\\d]/g,'')"},null,8,["modelValue"])]),_:1}),e.createVNode(c,{label:"企业地址",prop:"enterpriseAddress"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.value.enterpriseAddress,"onUpdate:modelValue":t[8]||(t[8]=o=>a.value.enterpriseAddress=o)},null,8,["modelValue"])]),_:1})],64)):e.createCommentVNode("",!0)]),_:1},8,["model","rules"]),A]),_:1},8,["modelValue"])]),_:1})}}}),J="";return((n,_)=>{const u=n.__vccOpts||n;for(const[i,f]of _)u[i]=f;return u})(P,[["__scopeId","data-v-5583842b"]])});
