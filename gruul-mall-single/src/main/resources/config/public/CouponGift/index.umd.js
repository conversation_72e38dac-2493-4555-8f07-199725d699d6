(function(e,C){typeof exports=="object"&&typeof module<"u"?module.exports=C(require("vue"),require("@vueuse/core"),require("@/utils/date"),require("@/composables/useConvert"),require("element-plus"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@/utils/date","@/composables/useConvert","element-plus","@/apis/http"],C):(e=typeof globalThis<"u"?globalThis:e||self,e.CouponGift=C(e.CouponGiftContext.Vue,e.CouponGiftContext.VueUse,e.CouponGiftContext.DateUtil,e.CouponGiftContext.UseConvert,e.CouponGiftContext.ElementPlus,e.CouponGiftContext.Request))})(this,function(e,<PERSON>,M,k,E,A){"use strict";var I=document.createElement("style");I.textContent=`@charset "UTF-8";.title[data-v-96351694]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-96351694]{font-size:12px;margin-left:12px;color:#c4c4c4}.rules[data-v-96351694]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-96351694]{width:300px;display:flex}.text[data-v-96351694]{font-size:14px;color:#333}.goodsData[data-v-96351694]{border:1px solid #ccc}.goods-list[data-v-96351694]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-96351694]{display:flex}.goods-list__goods-list__info-name[data-v-96351694]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-96351694]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-96351694]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-96351694]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-96351694]{font-size:16px}.ruleform-date[data-v-96351694]{width:100%;display:flex;align-items:center}.flex[data-v-96351694]{margin-top:10px;height:50px}.flex-item[data-v-96351694]{width:40%}.coupon-rules[data-v-96351694]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-96351694]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild(I);const{divTenThousand:se,mulTenThousand:Y}=k(),P={PRICE_DISCOUNT:"无门槛折扣券",PRICE_REDUCE:"无门槛现金券",REQUIRED_PRICE_DISCOUNT:"满折券",REQUIRED_PRICE_REDUCE:"满减券"};var R=(r=>(r.PERIOD="PERIOD",r.IMMEDIATELY="IMMEDIATELY",r))(R||{});function q(r){return r?Y(r):""}const B=e.defineComponent({__name:"select-couppon-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(r,{emit:l}){const d=r,u=C.useVModel(d,"modelValue",l);return(t,s)=>{const p=e.resolveComponent("el-option"),m=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(m,{modelValue:e.unref(u),"onUpdate:modelValue":s[0]||(s[0]=g=>e.isRef(u)?u.value=g:null),placeholder:d.placeholder,style:{width:"150px"},onChange:s[1]||(s[1]=g=>l("change",g))},{default:e.withCtx(()=>[e.renderSlot(t.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Object.values(d.list).length?d.list:e.unref(P),(g,y)=>(e.openBlock(),e.createBlock(p,{key:y,label:g,value:y},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),S=r=>A.post({url:"addon-coupon/coupon/gifts",data:r}),O=(r,l,d)=>{if(r==="PRICE_REDUCE"){G(r,l);return}if(r==="PRICE_DISCOUNT"){U(r,l);return}if(r==="REQUIRED_PRICE_REDUCE"){U(r,l),F(r,l,d);return}if(r==="REQUIRED_PRICE_DISCOUNT"){const u=[{required:!0,message:"请输入金额",trigger:"blur"},{validator:(s,p,m)=>{Number(p)<=0?m(new Error("金额必须大于0")):m()},trigger:"blur"}],t=[{required:!0,message:"请输入优惠券规则",trigger:"blur"},{validator:(s,p,m)=>{Number(p)<=0&&Number(p)<100?m(new Error("0.1~99之间")):m()},trigger:"blur"}];l.requiredAmount=u,l.discount=t}},G=(r,l)=>{l.discount.length&&(l.discount[0].required=!1),l.requiredAmount.length&&(l.discount[0].required=!1);const d=[{required:!0,message:"请输入优惠券规则",trigger:"blur"},{validator:(u,t,s)=>{Number(t)<=0?s(new Error("金额必须大于0")):s()},trigger:"blur"}];l.amount=d},U=(r,l)=>{l.requiredAmount.length&&(l.discount[0].required=!1),l.amount.length&&(l.amount[0].required=!1);const d=[{required:!0,message:"请输入优惠券规则",trigger:"blur"},{validator:(u,t,s)=>{Number(t)<=0&&Number(t)<100?s(new Error("0.1~99之间")):s()},trigger:"blur"}];l.discount=d},F=(r,l,d)=>{l.discount.length&&(l.discount[0].required=!1);const u=[{required:!0,message:"请输入金额",trigger:"blur"},{validator:(s,p,m)=>{Number(p)<=0?m(new Error("金额必须大于0")):m()},trigger:"blur"}],t=[...u,{validator:(s,p,m)=>{Number(d.requiredAmount)-Number(p)<=0?m(new Error(`满减金额必须小于${Number(d.requiredAmount)}`)):m()},trigger:"blur"}];l.amount=t,l.requiredAmount=u},f=r=>(e.pushScopeId("data-v-96351694"),r=r(),e.popScopeId(),r),Q={style:{padding:"0 40px"}},z=f(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),j=f(()=>e.createElementVNode("span",{class:"msg"},"优惠券名称不超过5个字",-1)),L={key:0,class:"ruleform-date"},$=f(()=>e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1)),H={class:"period-validity text"},J={class:"coupon-rules"},K=f(()=>e.createElementVNode("span",{style:{"margin-left":"10px"}},"折，无门槛使用",-1)),W=f(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"元，无门槛使用",-1)),X={key:2,class:"flex",style:{width:"100%"}},Z=f(()=>e.createElementVNode("span",null,"满",-1)),v=f(()=>e.createElementVNode("span",null,"元,打",-1)),ee=f(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"折",-1)),te={key:3,class:"flex"},oe=f(()=>e.createElementVNode("span",null,"满",-1)),le=f(()=>e.createElementVNode("span",null,"元,减",-1)),re=f(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"元",-1)),ne=e.defineComponent({__name:"CouponGift",props:{properties:{type:Object,required:!0}},setup(r){const l=r,d=new M,u=e.ref(),t=e.ref({name:"",days:1,endDate:"",type:"PRICE_DISCOUNT",requiredAmount:1,discount:1,amount:1,effectType:R.PERIOD,startDate:"",num:1,limit:1,productType:l.properties.productType}),s={name:[{required:!0,message:"请输入优惠券名称",trigger:"blur"}],effectType:[{required:!0,message:"请选择有效时间",trigger:["blur","change"]}],type:[{required:!0,message:"请选择优惠券类型",trigger:["blur","change"]}],discount:[{required:!0,message:"请输入优惠券规则",trigger:"blur"}],num:[{required:!0,message:"请输入每人发送数量",trigger:"blur"}],limit:[{required:!0,message:"请输入每人发送数量",trigger:"blur"}],requiredAmount:[{required:!0,message:"请输入优惠券规则",trigger:"blur"}],amount:[{required:!0,message:"请输入优惠券规则",trigger:"blur"}],days:[{required:!0,message:"请输入优惠券有效期",trigger:"blur"},{validator:(n,o,c)=>{Number(o)===0?c("最少1天"):c()},trigger:"blur"}],startDate:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]}],endDate:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]}]},p=async()=>{var n,o;if(u.value)try{if(!await u.value.validate())return;g();const i=y();m(i);const{code:x}=await S({coupon:i,userIds:l.properties.userIds});if(x!==200){E.ElMessage.error("保存失败");return}E.ElMessage.success("保存成功"),l.properties.toBack&&l.properties.toBack()}catch(c){if((n=c.requiredAmount)!=null&&n.length){E.ElMessage.info(c.requiredAmount[0].message);return}if((o=c.amount)!=null&&o.length){E.ElMessage.info(c.amount[0].message);return}}};function m(n){n.effectType==="PERIOD"&&(n.days=null),n.effectType==="IMMEDIATELY"&&(n.startDate="",n.endDate=""),n.type==="PRICE_REDUCE"&&(n.requiredAmount=null,n.discount=null),n.type==="PRICE_DISCOUNT"&&(n.amount=null,n.requiredAmount=null),n.type==="REQUIRED_PRICE_REDUCE"&&(n.discount=null),n.type==="REQUIRED_PRICE_DISCOUNT"&&(n.amount=null)}const g=()=>{if(t.value.effectType==="PERIOD"){t.value.days=1;return}t.value.startDate="",t.value.endDate=""};function y(){const{name:n,days:o,endDate:c,productType:i,type:x,requiredAmount:b,discount:V,amount:D,effectType:_,startDate:h,num:N,limit:T,productIds:w}=t.value;return{name:n,days:o,endDate:c,productType:i,type:x,requiredAmount:q(b).toString(),discount:V,amount:q(D).toString(),effectType:_,startDate:h,num:N,limit:N,productIds:w}}const ae=()=>{O(t.value.type,s,t.value),t.value.amount=1,t.value.requiredAmount=1,t.value.discount=1},de=n=>!!t.value.endDate?t.value.endDate<d.getYMDs(n)||d.getYMDs(n)<d.getYMDs(new Date):d.getYMDs(n)<d.getYMDs(new Date),ie=n=>!!t.value.startDate?t.value.startDate>d.getYMDs(n)||d.getYMDs(n)<d.getYMDs(new Date):!1;return(n,o)=>{const c=e.resolveComponent("el-input"),i=e.resolveComponent("el-form-item"),x=e.resolveComponent("el-radio"),b=e.resolveComponent("el-radio-group"),V=e.resolveComponent("el-row"),D=e.resolveComponent("el-date-picker"),_=e.resolveComponent("el-input-number"),h=e.resolveComponent("el-table-column"),N=e.resolveComponent("el-table"),T=e.resolveComponent("el-form"),w=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",Q,[z,e.createVNode(T,{ref_key:"ruleFormRef",ref:u,"inline-message":!1,model:t.value,rules:s,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(i,{label:"优惠券名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(c,{modelValue:t.value.name,"onUpdate:modelValue":o[0]||(o[0]=a=>t.value.name=a),modelModifiers:{trim:!0},maxlength:"5",placeholder:"请输入优惠券名称",style:{width:"551px"}},null,8,["modelValue"]),j]),_:1}),e.createVNode(i,{label:"有效时间",prop:"effectType"},{default:e.withCtx(()=>[e.createVNode(V,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(b,{modelValue:t.value.effectType,"onUpdate:modelValue":o[1]||(o[1]=a=>t.value.effectType=a),class:"ml-4"},{default:e.withCtx(()=>[e.createVNode(x,{label:"PERIOD"},{default:e.withCtx(()=>[e.createTextVNode("固定时间")]),_:1}),e.createVNode(x,{label:"IMMEDIATELY"},{default:e.withCtx(()=>[e.createTextVNode("领券立即生效")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(V,{style:{"margin-top":"20px"}},{default:e.withCtx(()=>[t.value.effectType==="PERIOD"?(e.openBlock(),e.createElementBlock("div",L,[e.createVNode(i,{"inline-message":!1,prop:"startDate"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:t.value.startDate,"onUpdate:modelValue":o[2]||(o[2]=a=>t.value.startDate=a),"disabled-date":de,format:"YYYY/MM/DD",placeholder:"请选择开始时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"]),$]),_:1}),e.createVNode(i,{"inline-message":!1,prop:"endDate"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:t.value.endDate,"onUpdate:modelValue":o[3]||(o[3]=a=>t.value.endDate=a),"disabled-date":ie,format:"YYYY/MM/DD",placeholder:"请选择结束时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})])):(e.openBlock(),e.createBlock(i,{key:1,prop:"days"},{default:e.withCtx(()=>[e.createElementVNode("div",H,[e.createTextVNode(" 领券当日起 "),e.createVNode(_,{modelValue:t.value.days,"onUpdate:modelValue":o[4]||(o[4]=a=>t.value.days=a),modelModifiers:{number:!0},controls:!1,max:99999,min:t.value.days?1:0,style:{width:"20%",margin:"0 5px"}},null,8,["modelValue","min"]),e.createTextVNode(" 天内可用 ")])]),_:1}))]),_:1})]),_:1}),e.createVNode(i,{label:"活动规则"},{default:e.withCtx(()=>[e.createVNode(N,{"cell-style":{height:"60px"},data:[{}],"header-cell-style":{textAlign:"center",fontSize:"14px",color:"#606266"},border:"",style:{width:"90%"}},{default:e.withCtx(()=>[e.createVNode(h,{label:"选择优惠券类型",width:"170"},{default:e.withCtx(()=>[e.createVNode(i,{prop:"type"},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:t.value.type,"onUpdate:modelValue":o[5]||(o[5]=a=>t.value.type=a),placeholder:"全部类型",onChange:ae},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(h,{label:"优惠券规则",width:"280"},{default:e.withCtx(()=>[e.createElementVNode("div",J,[t.value.type==="PRICE_DISCOUNT"?(e.openBlock(),e.createBlock(i,{key:0,prop:"discount"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:t.value.discount,"onUpdate:modelValue":o[6]||(o[6]=a=>t.value.discount=a),modelModifiers:{number:!0},controls:!1,max:9.9,min:.1,precision:1,style:{width:"40%"}},null,8,["modelValue"]),K]),_:1})):e.createCommentVNode("",!0),t.value.type==="PRICE_REDUCE"?(e.openBlock(),e.createBlock(i,{key:1,prop:"amount"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:t.value.amount,"onUpdate:modelValue":o[7]||(o[7]=a=>t.value.amount=a),modelModifiers:{number:!0},controls:!1,max:999999,min:0,style:{width:"40%"}},null,8,["modelValue"]),W]),_:1})):e.createCommentVNode("",!0),t.value.type==="REQUIRED_PRICE_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",X,[e.createVNode(i,{"label-width":"0%",prop:"requiredAmount"},{default:e.withCtx(()=>[Z,e.createVNode(_,{modelValue:t.value.requiredAmount,"onUpdate:modelValue":o[8]||(o[8]=a=>t.value.requiredAmount=a),modelModifiers:{number:!0},controls:!1,max:99999,min:0,style:{width:"60%",margin:"0 5px"}},null,8,["modelValue"]),v]),_:1}),e.createVNode(i,{"label-width":"0%",prop:"discount"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:t.value.discount,"onUpdate:modelValue":o[9]||(o[9]=a=>t.value.discount=a),modelModifiers:{number:!0},controls:!1,max:9.9,min:.1,precision:1,style:{width:"60%","margin-left":"5px"}},null,8,["modelValue"]),ee]),_:1})])):e.createCommentVNode("",!0),t.value.type==="REQUIRED_PRICE_REDUCE"?(e.openBlock(),e.createElementBlock("div",te,[e.createVNode(i,{"label-width":"0%",prop:"requiredAmount"},{default:e.withCtx(()=>[oe,e.createVNode(_,{modelValue:t.value.requiredAmount,"onUpdate:modelValue":o[10]||(o[10]=a=>t.value.requiredAmount=a),modelModifiers:{number:!0},controls:!1,max:999999,min:0,style:{width:"80%","margin-left":"5px"}},null,8,["modelValue"])]),_:1}),e.createVNode(i,{"label-width":"0%",prop:"amount"},{default:e.withCtx(()=>[le,e.createVNode(_,{modelValue:t.value.amount,"onUpdate:modelValue":o[11]||(o[11]=a=>t.value.amount=a),modelModifiers:{number:!0},controls:!1,max:999999,min:0,style:{width:"60%","margin-left":"5px"}},null,8,["modelValue"]),re]),_:1})])):e.createCommentVNode("",!0)])]),_:1}),e.createVNode(h,{label:"每人发送（张）"},{default:e.withCtx(()=>[e.createVNode(i,{prop:"num"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:t.value.num,"onUpdate:modelValue":o[12]||(o[12]=a=>t.value.num=a),modelModifiers:{number:!0},controls:!1,max:99999,min:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),e.createVNode(V,{class:"nav-button",justify:"center"},{default:e.withCtx(()=>[e.createVNode(w,{plain:"",round:"",onClick:o[13]||(o[13]=a=>l.properties.toBack&&l.properties.toBack())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.createVNode(w,{round:"",type:"primary",onClick:p},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1})]),_:1})])}}}),ue="";return((r,l)=>{const d=r.__vccOpts||r;for(const[u,t]of l)d[u]=t;return d})(ne,[["__scopeId","data-v-96351694"]])});
