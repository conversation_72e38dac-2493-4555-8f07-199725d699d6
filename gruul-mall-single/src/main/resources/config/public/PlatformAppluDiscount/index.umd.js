(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("@vueuse/core"),require("@element-plus/icons-vue"),require("vue-router"),require("@/components/pageManage/PageManage.vue"),require("@/apis/http"),require("@/composables/useConvert"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@element-plus/icons-vue","vue-router","@/components/pageManage/PageManage.vue","@/apis/http","@/composables/useConvert","element-plus"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAppluDiscount=V(e.PlatformAppluDiscountContext.Vue,e.PlatformAppluDiscountContext.VueUse,e.PlatformAppluDiscountContext.ElementPlusIconsVue,e.PlatformAppluDiscountContext.VueRouter,e.PlatformAppluDiscountContext.PageManage,e.PlatformAppluDiscountContext.Request,e.PlatformAppluDiscountContext.UseConvert,e.PlatformAppluDiscountContext.ElementPlus))})(this,function(e,V,L,D,B,N,I,f){"use strict";var E=document.createElement("style");E.textContent=`.name[data-v-357aee4b]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
`,document.head.appendChild(E);const T=e.defineComponent({__name:"select-type-p",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},options:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(n,{emit:d}){const a=n,c=V.useVModel(a,"modelValue",d);return(m,p)=>{const o=e.resolveComponent("el-option"),i=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(i,{modelValue:e.unref(c),"onUpdate:modelValue":p[0]||(p[0]=s=>e.isRef(c)?c.value=s:null),placeholder:a.placeholder,style:{width:"150px"},onChange:p[1]||(p[1]=s=>d("change",s))},{default:e.withCtx(()=>[e.renderSlot(m.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.options,(s,_)=>(e.openBlock(),e.createBlock(o,{key:_,label:s,value:_},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),A=e.defineComponent({__name:"head-search-p",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0}},emits:["update:modelValue","del","search"],setup(n,{emit:d}){const a=n,c=V.useVModel(a,"modelValue",d),m={NOT_STARTED:"未开始",PROCESSING:"进行中",OVER:"已结束",ILLEGAL_SELL_OFF:"违规下架"};return(p,o)=>{const i=e.resolveComponent("el-button"),s=e.resolveComponent("el-space"),_=e.resolveComponent("el-col"),g=e.resolveComponent("el-option"),x=e.resolveComponent("el-input"),w=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(w,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px"}},{default:e.withCtx(()=>[e.createVNode(_,{span:14},{default:e.withCtx(()=>[e.createVNode(s,null,{default:e.withCtx(()=>[e.createVNode(i,{round:"",disabled:p.$props.batchDisabled,onClick:o[0]||(o[0]=h=>d("del"))},{default:e.withCtx(()=>[e.createTextVNode("批量删除")]),_:1},8,["disabled"])]),_:1})]),_:1}),e.createVNode(_,{span:9},{default:e.withCtx(()=>[e.createVNode(s,null,{default:e.withCtx(()=>[e.createVNode(T,{modelValue:e.unref(c).fullReductionStatus,"onUpdate:modelValue":o[1]||(o[1]=h=>e.unref(c).fullReductionStatus=h),options:m,onChange:o[2]||(o[2]=h=>d("search"))},{default:e.withCtx(()=>[e.createVNode(g,{label:"全部",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(x,{modelValue:e.unref(c).keyword,"onUpdate:modelValue":o[4]||(o[4]=h=>e.unref(c).keyword=h),placeholder:"输入关键词",style:{width:"55%"}},{append:e.withCtx(()=>[e.createVNode(i,{icon:e.unref(L.Search),onClick:o[3]||(o[3]=h=>d("search"))},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),O=n=>N.get({url:"addon-full-reduction/fullReduction/",params:n}),S=n=>N.del({url:"addon-full-reduction/fullReduction/batch",data:n}),M=n=>N.put({url:"addon-full-reduction/fullReduction/sellOf",data:n}),k=n=>(e.pushScopeId("data-v-357aee4b"),n=n(),e.popScopeId(),n),F={class:"name"},$={class:"name"},q=k(()=>e.createElementVNode("br",null,null,-1)),z=k(()=>e.createElementVNode("div",{style:{bottom:"20px",background:"#fff",width:"980px",height:"70px"}},null,-1)),P={style:{position:"fixed",bottom:"20px",background:"#fff",width:"980px",height:"70px","z-index":"1000"}},G=e.defineComponent({__name:"discountlist",props:{search:{type:Object,default:()=>({})}},setup(n,{expose:d}){const a=n,c=D.useRouter(),m={NOT_STARTED:"未开始",PROCESSING:"进行中",OVER:"已结束",ILLEGAL_SELL_OFF:"违规下架"},{divTenThousand:p}=I(),o=e.ref([]),i=e.ref(),s=e.reactive({size:10,current:1}),_=e.ref(0),g=e.ref([]);async function x(){const r={...s,...a.search},{code:u,data:l}=await O(r);if(u!==200)return f.ElMessage.error("获取满减列表失败");o.value=l.records,s.current=l.current,s.size=l.size,_.value=l.total}const w=async r=>{if(!await f.ElMessageBox.confirm("确定下架该满减活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:l,data:y}=await M([{shopId:r.shopId,fullReductionId:r.id}]);if(l!==200){f.ElMessage.error("下架失败");return}f.ElMessage.success("下架成功");const C=o.value.find(b=>b.id===r.id);C&&(C.fullReductionStatus="ILLEGAL_SELL_OFF")},h=async r=>{if(!await f.ElMessageBox.confirm("确定删除该满减活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:l,data:y}=await S([{shopId:r.shopId,fullReductionId:r.id}]);if(l!==200){f.ElMessage.error("删除失败");return}f.ElMessage.success("删除成功"),o.value=o.value.filter(C=>C.id!==r.id)},j=r=>{c.push({name:"applyDiscountBaseinfo",query:{id:r.id,shopId:r.shopId}})},H=async r=>{const{code:u,data:l}=await S(r);if(u!==200){f.ElMessage.error("删除失败");return}f.ElMessage.success("删除成功"),x()};return e.onBeforeMount(()=>{x()}),d({chooseList:g,handleDelBatch:H,initDiscountActiveList:x}),(r,u)=>{const l=e.resolveComponent("el-table-column"),y=e.resolveComponent("el-link"),C=e.resolveComponent("el-row"),b=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(b,{ref_key:"multipleTableRef",ref:i,data:o.value,stripe:"","header-row-style":{fontSize:"12px",color:"#000"},"header-cell-style":{background:"#f6f8fa"},height:"calc(100vh - 250px)","cell-style":{fontSize:"12px",color:"#333333"},onSelectionChange:u[0]||(u[0]=t=>g.value=t)},{default:e.withCtx(()=>[e.createVNode(l,{type:"selection",width:"55"}),e.createVNode(l,{label:"店铺名称",width:"140"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",F,e.toDisplayString(t.shopName),1)]),_:1}),e.createVNode(l,{label:"活动名称",width:"140",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",$,e.toDisplayString(t.fullReductionName),1)]),_:1}),e.createVNode(l,{label:"活动时间",align:"center",width:"280"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.fullReductionStartTime),1),q,e.createElementVNode("span",null,e.toDisplayString(t.fullReductionEndTime),1)]),_:1}),e.createVNode(l,{prop:"date",label:"参加人数",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.peopleNum),1)]),_:1}),e.createVNode(l,{prop:"date",label:"应收金额",align:"center"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.amountReceivable&&e.unref(p)(t.amountReceivable)),1)]),_:1}),e.createVNode(l,{prop:"address",label:"状态"},{default:e.withCtx(({row:{fullReductionStatus:t}})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:t==="ILLEGAL_SELL_OFF"?"red":""})},e.toDisplayString(m[t]),5)]),_:1}),e.createVNode(l,{prop:"address",label:"操作",fixed:"right",width:"150",align:"center"},{default:e.withCtx(({row:t})=>[e.createVNode(C,{justify:"end",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(y,{style:{padding:"0 5px"},underline:!1,type:"primary",size:"small",onClick:R=>j(t)},{default:e.withCtx(()=>[e.createTextVNode("查看")]),_:2},1032,["onClick"]),["ILLEGAL_SELL_OFF","OVER"].includes(t.fullReductionStatus)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(y,{key:0,style:{padding:"0 5px"},underline:!1,type:"primary",size:"small",onClick:R=>w(t)},{default:e.withCtx(()=>[e.createTextVNode(" 下架 ")]),_:2},1032,["onClick"])),e.createVNode(y,{style:{padding:"0 5px"},underline:!1,type:"primary",size:"small",onClick:R=>h(t)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),z,e.createElementVNode("div",P,[e.createVNode(B,{modelValue:s,"onUpdate:modelValue":u[1]||(u[1]=t=>s=t),"load-init":!0,total:_.value,onReload:x},null,8,["modelValue","total"])])])}}}),J="",U=((n,d)=>{const a=n.__vccOpts||n;for(const[c,m]of d)a[c]=m;return a})(G,[["__scopeId","data-v-357aee4b"]]);return e.defineComponent({__name:"PlatformAppluDiscount",setup(n){const d=e.reactive({fullReductionStatus:"",keyword:""}),a=e.ref(),c=e.computed(()=>a.value?!a.value.chooseList.length:!0),m=()=>{a.value.initDiscountActiveList()},p=()=>{const o=a.value.chooseList.map(i=>({fullReductionId:i.id,shopId:i.shopId}));a.value.handleDelBatch(o)};return(o,i)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode(A,{modelValue:d,"onUpdate:modelValue":i[0]||(i[0]=s=>d=s),"batch-disabled":c.value,onSearch:m,onDel:p},null,8,["modelValue","batch-disabled"]),e.createVNode(U,{ref_key:"listRef",ref:a,search:d},null,8,["search"])]))}})});
