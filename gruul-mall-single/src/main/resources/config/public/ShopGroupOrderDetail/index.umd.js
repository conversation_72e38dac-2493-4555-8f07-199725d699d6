(function(e,c){typeof exports=="object"&&typeof module<"u"?module.exports=c(require("vue"),require("@/utils/http"),require("@/components/PageManage.vue"),require("element-plus"),require("@/composables/useConvert"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@/utils/http","@/components/PageManage.vue","element-plus","@/composables/useConvert","vue-router"],c):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopGroupOrderDetail=c(e.ShopGroupOrderDetailContext.Vue,e.ShopGroupOrderDetailContext.UtilsHttp,e.ShopGroupOrderDetailContext.PageManageTwo,e.ShopGroupOrderDetailContext.ElementPlus,e.ShopGroupOrderDetailContext.UseConvert,e.ShopGroupOrderDetailContext.VueRouter))})(this,function(e,c,z,f,I,T){"use strict";var N=document.createElement("style");N.textContent=`.groupDetail__info[data-v-369cc5bd]{border:1px solid #d5d5d5;font-size:14px;color:#333;padding-top:25px}.groupDetail__info-row[data-v-369cc5bd]{display:flex;justify-content:flex-start;align-items:center;margin-bottom:30px}.groupDetail__info-row--label[data-v-369cc5bd]{width:104px;text-align:right}.groupDetail__info-row--right[data-v-369cc5bd]{display:flex;justify-content:center;align-items:center;margin-left:11px}.mg[data-v-369cc5bd]{margin-left:10px}.ellipsis[data-v-369cc5bd]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
`,document.head.appendChild(N);const q=o=>c.http.get({url:`addon-team/team/activity/order/summary?teamNo=${o}`}),j=(o,i)=>c.http.get({url:"addon-team/team/activity/order/users",params:{teamNo:o,size:i.size,current:i.current}}),n=o=>(e.pushScopeId("data-v-369cc5bd"),o=o(),e.popScopeId(),o),k={class:"groupDetail"},M={class:"groupDetail__info"},U={class:"groupDetail__info-row"},H=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"开团人:",-1)),P={class:"groupDetail__info-row--right"},A={class:"mg"},B={class:"groupDetail__info-row"},L=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"活动名称:",-1)),R={class:"groupDetail__info-row--right"},$={class:"groupDetail__info-row"},F=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"开团时间:",-1)),J={class:"groupDetail__info-row--right"},K={class:"groupDetail__info-row"},Q=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"参团人数:",-1)),W={class:"groupDetail__info-row--right"},X={class:"groupDetail__info-row"},Y=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"拼团商品:",-1)),Z={class:"groupDetail__info-row--right"},v={class:"mg"},ee={class:"groupDetail__info-row"},te=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"购买件数:",-1)),oe={class:"groupDetail__info-row--right"},re={class:"groupDetail__info-row"},ae=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"拼团状态:",-1)),ne={class:"groupDetail__info-row--right"},ie={class:"ellipsis"},le=e.defineComponent({__name:"ShopGroupOrderDetail",setup(o){const{divTenThousand:i}=I(),_=T.useRoute(),t=e.ref(),m=e.ref([]),l=e.reactive({current:1,size:10,total:0});ce(),u();const se=r=>({SUCCESS:"拼团成功",FAIL:"拼团失败",ING:"拼团中"})[r];async function ce(){const r=_.query.teamNo,{code:h,data:s}=await q(r);if(h!==200)return f.ElMessage.error("获取拼团信息失败");t.value=s}async function u(){const r=_.query.teamNo,{total:h,...s}=l,{code:g,data:p}=await j(r,s);if(g!==200)return f.ElMessage.error("获取拼团用户信息失败");m.value=p.records,l.total=p.total}const de=r=>{l.size=r,u()},_e=r=>{l.current=r,u()};return(r,h)=>{var D,w,V,x,y,C,E,S,b,O,G;const s=e.resolveComponent("el-image"),g=e.resolveComponent("el-col"),p=e.resolveComponent("el-row"),d=e.resolveComponent("el-table-column"),pe=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",k,[e.createElementVNode("div",M,[e.createElementVNode("div",U,[H,e.createElementVNode("div",P,[e.createVNode(s,{src:(D=t.value)==null?void 0:D.commanderAvatar,style:{width:"36px",height:"36px"}},null,8,["src"]),e.createElementVNode("span",A,e.toDisplayString((w=t.value)==null?void 0:w.commanderNickname),1)])]),e.createElementVNode("div",B,[L,e.createElementVNode("div",R,e.toDisplayString((V=t.value)==null?void 0:V.name),1)]),e.createElementVNode("div",$,[F,e.createElementVNode("div",J,e.toDisplayString((x=t.value)==null?void 0:x.openTime),1)]),e.createElementVNode("div",K,[Q,e.createElementVNode("div",W,e.toDisplayString((y=t.value)==null?void 0:y.currentNum)+"/"+e.toDisplayString((C=t.value)==null?void 0:C.totalNum),1)]),e.createElementVNode("div",X,[Y,e.createElementVNode("div",Z,[e.createVNode(s,{src:(E=t.value)==null?void 0:E.productImage,style:{width:"36px",height:"36px"}},null,8,["src"]),e.createElementVNode("span",v,e.toDisplayString((S=t.value)==null?void 0:S.productName),1)])]),e.createElementVNode("div",ee,[te,e.createElementVNode("div",oe,e.toDisplayString((b=t.value)==null?void 0:b.buyNum),1)]),e.createElementVNode("div",re,[ae,e.createElementVNode("div",ne,e.toDisplayString(((O=t.value)==null?void 0:O.status)&&se((G=t.value)==null?void 0:G.status)),1)])]),e.createVNode(pe,{data:m.value,width:"100%"},{default:e.withCtx(()=>[e.createVNode(d,{label:"拼团人信息",width:"200"},{default:e.withCtx(({row:a})=>[e.createVNode(p,{justify:"center",align:"middle"},{default:e.withCtx(()=>[e.createVNode(g,{span:6},{default:e.withCtx(()=>[e.createVNode(s,{src:a.avatar,style:{width:"36px",height:"36px"}},null,8,["src"])]),_:2},1024),e.createVNode(g,{span:18},{default:e.withCtx(()=>[e.createElementVNode("div",ie,e.toDisplayString(a.nickname),1)]),_:2},1024)]),_:2},1024)]),_:1}),e.createVNode(d,{label:"拼团价",align:"center"},{default:e.withCtx(({row:a})=>[e.createTextVNode(e.toDisplayString(e.unref(i)(a.price)),1)]),_:1}),e.createVNode(d,{label:"实付金额",align:"center"},{default:e.withCtx(({row:a})=>[e.createTextVNode(e.toDisplayString(e.unref(i)(a.amount)),1)]),_:1}),e.createVNode(d,{label:"团员身份",align:"center"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,e.toDisplayString(a.commander?"团长":"团员"),1)]),_:1}),e.createVNode(d,{label:"创建时间",prop:"createTime",width:"180",align:"center"}),e.createVNode(d,{label:"订单号",align:"center",width:"220",fixed:"right"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,e.toDisplayString(a.orderNo),1)]),_:1})]),_:1},8,["data"]),e.createVNode(p,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(z,{"page-size":l.size,"page-num":l.current,total:l.total,onHandleSizeChange:de,onHandleCurrentChange:_e},null,8,["page-size","page-num","total"])]),_:1})])}}}),me="";return((o,i)=>{const _=o.__vccOpts||o;for(const[t,m]of i)_[t]=m;return _})(le,[["__scopeId","data-v-369cc5bd"]])});
