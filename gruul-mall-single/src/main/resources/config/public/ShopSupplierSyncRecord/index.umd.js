(function(e,s){typeof exports=="object"&&typeof module<"u"?module.exports=s(require("vue"),require("@/apis/http"),require("@/components/pageManage/PageManage.vue"),require("element-plus"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@/apis/http","@/components/pageManage/PageManage.vue","element-plus","vue-router"],s):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopSupplierSyncRecord=s(e.ShopSupplierSyncRecordContext.Vue,e.ShopSupplierSyncRecordContext.Request,e.ShopSupplierSyncRecordContext.PageManage,e.ShopSupplierSyncRecordContext.ElementPlus,e.ShopSupplierSyncRecordContext.VueRouter))})(this,function(e,s,E,_,R){"use strict";var V=document.createElement("style");V.textContent=`.name[data-v-873b29c1]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.pagination[data-v-873b29c1]{display:flex;justify-content:flex-end;padding:15px 0}.json-display[data-v-873b29c1]{padding:15px;border-radius:4px;background:#f8f8f8;max-height:600px;overflow:auto}
`,document.head.appendChild(V);const b=r=>s.get({url:"addon-supplier/mp/supplierNotifyRecord/page",params:r}),T=(r,p)=>s.get({url:`addon-supplier/mp/supplierNotifyRecord/record/${r}/${p}`}),B=r=>s.post({url:"addon-supplier/mp/supplierNotifyRecord/record/export",data:r}),D={style:{background:"#f9f9f9","margin-bottom":"20px"}},z={class:"id"},A={class:"name"},I={class:"name"},U={class:"name"},q={key:0,class:"name"},L={key:1,class:"name"},M={class:"pagination"},O={class:"json-display"},H={class:"dialog-footer"},P=e.defineComponent({__name:"ShopSupplierSyncRecord",props:{properties:{type:Object,required:!0}},setup(r,{expose:p}){const f=e.ref(),y=e.ref([{name:"全部状态",key:""},{name:"未处理",key:"UNTREATED"},{name:"处理成功",key:"SUCCESS"},{name:"处理失败",key:"FAILED"}]),u=e.ref([{name:"全部状态",key:""},{name:"同步商品库存",key:"STOCK_BATCH_SAVE"},{name:"同步商品",key:"PRODUCT_BATCH_SAVE"},{name:"订单通知",key:"SUPPLIER_ORDER_NOTIFY"},{name:"问诊记录",key:"diagnose"},{name:"激活记录",key:"activate"}]),F=n=>{var o;return(o=y.value.find(l=>l.key===n))==null?void 0:o.name},j=n=>{var o;return(o=u.value.find(l=>l.key===n))==null?void 0:o.name},d=e.reactive({notifyType:"PRODUCT_BATCH_SAVE",syncStatus:""}),a=e.reactive({size:10,current:1,total:0}),$=n=>{a.size=n,i()},G=n=>{a.current=n,i()},K=()=>{a.current=1,i()},g=e.ref([]);async function i(){const n={supplierId:S.value,current:a.current,size:a.size},{code:o,data:l}=await b({...n,...d});if(o!==200)return _.ElMessage.error("获取列表失败");g.value=l.records,a.current=l.current,a.size=l.size,a.total=l.total}const k=e.ref(),C=e.ref(!1),Y=async n=>{const{code:o,data:l}=await T(S.value,n.id);if(o!==200)return _.ElMessage.error("获取内容失败");k.value=l==null?void 0:l.notify,C.value=!0},N=()=>{C.value=!1},J=e.ref([]),Q=async n=>{if(!n.id)return;const o={supplierId:S.value,exportIds:[n.id]},{code:l,msg:m}=await B(o);l===200?_.ElMessage.success({message:m||"导出成功"}):_.ElMessage.error({message:m||"导出失败"})},S=e.ref(),W=R.useRoute();return e.watch(()=>W,async n=>{n.query.id&&(S.value=n.query.id,await i())},{immediate:!0}),p({initCouponList:i}),(n,o)=>{const l=e.resolveComponent("el-option"),m=e.resolveComponent("el-select"),w=e.resolveComponent("el-form-item"),x=e.resolveComponent("el-col"),h=e.resolveComponent("el-button"),X=e.resolveComponent("el-row"),Z=e.resolveComponent("el-form"),c=e.resolveComponent("el-table-column"),v=e.resolveComponent("el-table"),ee=e.resolveComponent("el-card"),te=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",D,[e.createVNode(Z,{model:d},{default:e.withCtx(()=>[e.createVNode(X,{gutter:20},{default:e.withCtx(()=>[e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(w,{label:"类型"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:d.notifyType,"onUpdate:modelValue":o[0]||(o[0]=t=>d.notifyType=t),placeholder:"请选择"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(u.value,t=>(e.openBlock(),e.createBlock(l,{key:t.key,label:t.name,value:t.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(w,{label:"状态"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:d.syncStatus,"onUpdate:modelValue":o[1]||(o[1]=t=>d.syncStatus=t),placeholder:"请选择"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value,t=>(e.openBlock(),e.createBlock(l,{key:t.key,label:t.name,value:t.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(h,{round:"",type:"primary",onClick:K},{default:e.withCtx(()=>o[5]||(o[5]=[e.createTextVNode("搜索")])),_:1,__:[5]})]),_:1})]),_:1})]),_:1},8,["model"])]),e.createElementVNode("div",null,[e.createVNode(v,{ref_key:"multipleTableRef",ref:f,data:g.value,"cell-style":{fontSize:"12px",color:"#333333"},"header-cell-style":{background:"#f6f8fa"},"header-row-style":{fontSize:"12px",color:"#909399"},stripe:"",onSelectionChange:o[2]||(o[2]=t=>J.value=t)},{default:e.withCtx(()=>[e.createVNode(c,{type:"selection",width:"55"}),e.createVNode(c,{label:"ID"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",z,e.toDisplayString(t.id),1)]),_:1}),e.createVNode(c,{label:"供应商名称"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",A,e.toDisplayString(t.supplierName),1)]),_:1}),e.createVNode(c,{label:"类型"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",I,e.toDisplayString(j(t.notifyType)),1)]),_:1}),e.createVNode(c,{label:"推送时间"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",U,e.toDisplayString(t.notifyTime),1)]),_:1}),e.createVNode(c,{label:"状态"},{default:e.withCtx(({row:t})=>[t.syncStatus?(e.openBlock(),e.createElementBlock("div",q,e.toDisplayString(F(t.syncStatus)),1)):(e.openBlock(),e.createElementBlock("div",L,"无"))]),_:1}),e.createVNode(c,{fixed:"right",label:"操作",prop:"address",width:"120"},{default:e.withCtx(({row:t})=>[e.createVNode(h,{link:"",size:"small",type:"primary",onClick:oe=>Y(t)},{default:e.withCtx(()=>o[6]||(o[6]=[e.createTextVNode("查看 ")])),_:2,__:[6]},1032,["onClick"]),t.notifyType==="PRODUCT_BATCH_SAVE"?(e.openBlock(),e.createBlock(h,{key:0,link:"",size:"small",type:"primary",onClick:oe=>Q(t)},{default:e.withCtx(()=>o[7]||(o[7]=[e.createTextVNode("导出 ")])),_:2,__:[7]},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createElementVNode("div",M,[e.createVNode(E,{modelValue:a,"onUpdate:modelValue":o[3]||(o[3]=t=>a=t),"load-init":!0,"page-size":a.size,total:a.total,onReload:i,onHandleSizeChange:$,onHandleCurrentChange:G},null,8,["modelValue","page-size","total"])]),e.createVNode(te,{modelValue:C.value,"onUpdate:modelValue":o[4]||(o[4]=t=>C.value=t),"before-close":N,width:"800px"},{footer:e.withCtx(()=>[e.createElementVNode("span",H,[e.createVNode(h,{onClick:N},{default:e.withCtx(()=>o[8]||(o[8]=[e.createTextVNode("取消")])),_:1,__:[8]})])]),default:e.withCtx(()=>[e.createVNode(ee,{shadow:"hover"},{default:e.withCtx(()=>[e.createElementVNode("pre",O,e.toDisplayString(k.value),1)]),_:1})]),_:1},8,["modelValue"])])],64)}}}),ne="";return((r,p)=>{const f=r.__vccOpts||r;for(const[y,u]of p)f[y]=u;return f})(P,[["__scopeId","data-v-873b29c1"]])});
