(function(e,u){typeof exports=="object"&&typeof module<"u"?module.exports=u(require("vue"),require("lodash"),require("@/components/MCard.vue"),require("@/components/pageManage/PageManage.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("element-plus"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","lodash","@/components/MCard.vue","@/components/pageManage/PageManage.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","element-plus","@/apis/http"],u):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopAppLiveList=u(e.ShopAppLiveListContext.Vue,e.ShopAppLiveListContext.Lodash,e.ShopAppLiveListContext.MCard,e.ShopAppLiveListContext.PageManage,e.ShopAppLiveListContext.QTable,e.ShopAppLiveListContext.QTableColumn,e.ShopAppLiveListContext.ElementPlus,e.ShopAppLiveListContext.Request))})(this,function(e,u,A,D,O,S,L,E){"use strict";var R=document.createElement("style");R.textContent=`.el-row[data-v-13f1e533]{margin-bottom:8px}.el-row img+img[data-v-13f1e533]{margin-left:10px;width:50px;height:50px;vertical-align:top;object-fit:cover}.table[data-v-66507b2f]{overflow:scroll}.table-info[data-v-66507b2f]{display:flex;overflow:hidden}.table-info img[data-v-66507b2f]{width:50px;height:50px;border-radius:10px;flex-shrink:0}.table-info__wrapper[data-v-66507b2f]{margin-left:15px;overflow:hidden;flex:1}.table-info__wrapper--name .label[data-v-66507b2f]{display:inline-block;line-height:22px;padding:0 3px;border-radius:4px;color:#fff;margin-right:5px}.table-info__wrapper--name .label.in-live[data-v-66507b2f]{background-color:#085fdb}.table-info__wrapper--name .label.foretell[data-v-66507b2f]{background-color:#81b337}.table-info__wrapper--name .label.finish[data-v-66507b2f]{background-color:#000}.table-info__wrapper--name .label.violation[data-v-66507b2f]{background-color:#fd0505}.table-anchor[data-v-66507b2f]{text-align:center;line-height:22px}.table-performance[data-v-66507b2f]{width:100%;line-height:22px}.ellipsis[data-v-66507b2f]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.el-link+.el-link[data-v-66507b2f]{margin-left:5px}.table[data-v-ff47de91]{overflow-y:scroll}.table-info[data-v-ff47de91]{display:flex;width:300px;overflow:hidden}.table-info img[data-v-ff47de91]{width:50px;height:50px;border-radius:10px;flex-shrink:0}.table-info__wrapper[data-v-ff47de91]{margin-left:15px;overflow:hidden;flex:1}.table-info__wrapper--name span+span[data-v-ff47de91]{margin-left:5px}.table-performance[data-v-ff47de91]{width:100%;line-height:22px}.ellipsis[data-v-ff47de91]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
`,document.head.appendChild(R);const M=e.defineComponent({__name:"ShopAppLiveList",setup(_){const a=e.ref("room"),r={room:e.defineAsyncComponent(()=>Promise.resolve().then(()=>de)),anchor:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ue))};return(o,c)=>{const n=e.resolveComponent("el-tab-pane"),t=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(t,{modelValue:a.value,"onUpdate:modelValue":c[0]||(c[0]=i=>a.value=i),class:"demo-tabs"},{default:e.withCtx(()=>[e.createVNode(n,{label:"直播间",name:"room"}),e.createVNode(n,{label:"主播管理",name:"anchor"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(r[a.value])))])}}}),q={style:{background:"#f9f9f9"}},U=e.defineComponent({__name:"room-search",emits:["changeShow","onSearchParams"],setup(_,{emit:a}){const r=e.ref(!1),o=e.reactive({liveTitle:"",anchorNickname:"",createTime:"",liveId:"",phone:""});e.watch(()=>r.value,n=>{a("changeShow",n)});const c=()=>{const n=u.cloneDeep(o);a("onSearchParams",n)};return(n,t)=>{const i=e.resolveComponent("el-input"),d=e.resolveComponent("el-form-item"),p=e.resolveComponent("el-col"),N=e.resolveComponent("el-date-picker"),m=e.resolveComponent("el-row"),h=e.resolveComponent("el-button"),f=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",q,[e.createVNode(A,{modelValue:r.value,"onUpdate:modelValue":t[5]||(t[5]=C=>r.value=C)},{default:e.withCtx(()=>[e.createVNode(f,{ref:"form",model:o,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(m,null,{default:e.withCtx(()=>[e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"直播主题"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.liveTitle,"onUpdate:modelValue":t[0]||(t[0]=C=>o.liveTitle=C),placeholder:"请输入直播主题"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"主播昵称"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.anchorNickname,"onUpdate:modelValue":t[1]||(t[1]=C=>o.anchorNickname=C),placeholder:"请输入主播昵称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"直播时间"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:o.createTime,"onUpdate:modelValue":t[2]||(t[2]=C=>o.createTime=C),"end-placeholder":"结束日期","range-separator":"-","start-placeholder":"开始日期",style:{width:"224px"},type:"daterange","unlink-panels":"","value-format":"YYYY-MM-DD",onChange:c},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"直播ID"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.liveId,"onUpdate:modelValue":t[3]||(t[3]=C=>o.liveId=C),placeholder:"请输入直播ID"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"手机号"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.phone,"onUpdate:modelValue":t[4]||(t[4]=C=>o.phone=C),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(d,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(h,{class:"from_btn",round:"",type:"primary",onClick:c},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),P=_=>E.get({url:"addon-live/manager/live/list",params:_}),F=(_,a)=>E.get({url:`addon-live/manager/ban/reason/${_}/${a}`}),G=_=>E.get({url:"addon-live/manager/anchor/list",params:_}),j=(_,a)=>E.put({url:`addon-live/manager/update/anchor/${_}/${a}`}),H=_=>E.get({url:"gruul-mall-uaa/uaa/shop/admin/anchor/list",params:_}),Y=_=>E.post({url:"addon-live/manager/add/anchor",data:_}),W={class:"reason"},J=["src"],K=e.defineComponent({__name:"violation-reason",props:{id:{type:String,default:""},type:{type:String,default:""}},setup(_){const a=_,r={YELLOW_INVOLVEMENT:"涉黄",DRUG_RELATED:"涉毒",SENSITIVE_TOPIC:"敏感话题",OTHER:"其它"},o=e.ref({qualityInspector:"",sourceId:"",shopId:"",type:"",categoryTypes:"",reason:"",relevantEvidence:"",createTime:""});e.watch(a,n=>{c()}),c();async function c(){const{code:n,data:t}=await F(a.id,a.type);if(n!==200)return L.ElMessage.error("获取直播间列表失败");t.relevantEvidence=t.relevantEvidence.split(","),t.categoryTypes=t.categoryTypes.map(i=>r[i]).join(","),o.value=t}return(n,t)=>{const i=e.resolveComponent("el-col"),d=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",W,[e.createVNode(d,{gutter:8},{default:e.withCtx(()=>[e.createVNode(i,{span:12},{default:e.withCtx(()=>[e.createTextVNode("检查员："+e.toDisplayString(o.value.qualityInspector),1)]),_:1}),e.createVNode(i,{span:12},{default:e.withCtx(()=>[e.createTextVNode("检查时间："+e.toDisplayString(o.value.createTime),1)]),_:1})]),_:1}),e.createVNode(d,{gutter:8},{default:e.withCtx(()=>[e.createVNode(i,{span:24},{default:e.withCtx(()=>[e.createTextVNode("类型："+e.toDisplayString(o.value.categoryTypes),1)]),_:1})]),_:1}),e.createVNode(d,{gutter:8},{default:e.withCtx(()=>[e.createVNode(i,{span:24},{default:e.withCtx(()=>[e.createTextVNode("原因："+e.toDisplayString(o.value.reason),1)]),_:1})]),_:1}),e.createVNode(d,{gutter:8},{default:e.withCtx(()=>[e.createTextVNode(" 相关证据： "),e.createVNode(i,{span:24},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value.relevantEvidence,p=>(e.openBlock(),e.createElementBlock("img",{key:p,src:p,style:{width:"60px",height:"60px"}},null,8,J))),128))]),_:1})]),_:1})])}}}),Te="",B=(_,a)=>{const r=_.__vccOpts||_;for(const[o,c]of a)r[o]=c;return r},$=B(K,[["__scopeId","data-v-13f1e533"]]),X={class:"table-info"},Z=["src"],Q={class:"table-info__wrapper"},v={class:"table-info__wrapper--name"},ee={key:0,class:"label in-live"},te={key:1,class:"label foretell"},oe={key:2,class:"label finish"},ae={key:3,class:"label violation"},ne={class:"table-info__wrapper--description"},le={class:"table-info__wrapper--description ellipsis"},re={class:"table-anchor"},ce={class:"table-anchor__info"},se={class:"table-anchor__info"},ie=e.defineComponent({__name:"room-list",setup(_){const a=e.ref([]),r=e.ref("calc(100vh - 320px)"),o=e.ref(""),c=e.ref({liveTitle:"",anchorNickname:"",createTime:"",liveId:"",phone:"",beginTime:"",endTime:""}),n=e.reactive({size:10,current:1,total:0}),t=e.ref(!1),i=[{name:"",label:"全部"},{name:"PROCESSING",label:"直播中"},{name:"NOT_STARTED",label:"预告"},{name:"OVER",label:"已结束"},{name:"ILLEGAL_SELL_OFF",label:"违规下播"}],d=e.ref({id:""});p();async function p(){Array.isArray(c.value.createTime)&&(c.value.beginTime=c.value.createTime[0],c.value.endTime=c.value.createTime[1],c.value.createTime="");const b={...n,...c.value,status:o.value},{code:w,data:x}=await P(b);if(w!==200)return L.ElMessage.error("获取直播间列表失败");a.value=x.records,n.current=x.current,n.size=x.size,n.total=x.total}const N=b=>{n.size=b,p()},m=b=>{n.current=b,p()},h=b=>{c.value=b,n.current=1,p()},f=()=>{n.current=1,p()},C=b=>{r.value=b?"calc(100vh - 480px)":"calc(100vh - 320px)"},y=b=>{t.value=!0,d.value.id=b};return(b,w)=>{const x=e.resolveComponent("el-tab-pane"),I=e.resolveComponent("el-tabs"),V=e.resolveComponent("el-link"),g=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(U,{onOnSearchParams:h,onChangeShow:C}),e.createVNode(I,{modelValue:o.value,"onUpdate:modelValue":w[0]||(w[0]=l=>o.value=l),onTabChange:f},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(i,l=>e.createVNode(x,{key:l.name,label:l.label,name:l.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),e.createVNode(e.unref(O),{data:a.value,style:e.normalizeStyle({height:r.value}),class:"table"},{default:e.withCtx(()=>[e.createVNode(S,{align:"center",label:"直播信息"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",X,[e.createElementVNode("img",{src:l.pic},null,8,Z),e.createElementVNode("div",Q,[e.createElementVNode("div",v,[l.status==="PROCESSING"?(e.openBlock(),e.createElementBlock("span",ee,"直播中")):l.status==="NOT_STARTED"?(e.openBlock(),e.createElementBlock("span",te,"预告")):l.status==="OVER"?(e.openBlock(),e.createElementBlock("span",oe,"已结束")):l.status==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createElementBlock("span",ae,"违规下播")):e.createCommentVNode("",!0),e.createElementVNode("span",null,e.toDisplayString(l.liveTitle),1)]),e.createElementVNode("div",ne,"直播ID："+e.toDisplayString(l.id),1),e.createElementVNode("div",le,e.toDisplayString(l.liveSynopsis),1)])])]),_:1}),e.createVNode(S,{align:"center",label:"主播昵称"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",re,[e.createElementVNode("div",ce,e.toDisplayString(l.anchor.anchorNickname),1),e.createElementVNode("div",se,e.toDisplayString(l.anchor.phone),1)])]),_:1}),e.createVNode(S,{label:"操作",prop:"action"},{default:e.withCtx(({row:l})=>[l.status==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createBlock(V,{key:0,type:"primary",onClick:T=>y(l.id)},{default:e.withCtx(()=>[e.createTextVNode("违禁原因 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data","style"]),e.createVNode(D,{"page-num":n.current,"page-size":n.size,total:n.total,onHandleSizeChange:N,onHandleCurrentChange:m},null,8,["page-num","page-size","total"]),e.createVNode(g,{modelValue:t.value,"onUpdate:modelValue":w[1]||(w[1]=l=>t.value=l),title:"违禁原因",width:"550px"},{default:e.withCtx(()=>[e.createVNode($,{id:d.value.id,type:"LIVE"},null,8,["id"])]),_:1},8,["modelValue"])],64)}}}),Be="",de=Object.freeze(Object.defineProperty({__proto__:null,default:B(ie,[["__scopeId","data-v-66507b2f"]])},Symbol.toStringTag,{value:"Module"})),pe={style:{background:"#f9f9f9"}},me=e.defineComponent({__name:"anchor-search",emits:["changeShow","onSearchParams"],setup(_,{emit:a}){const r=e.ref(!1),o=e.reactive({anchorNickname:"",id:"",phone:""});e.watch(()=>r.value,n=>{a("changeShow",n)});const c=()=>{const n=u.cloneDeep(o);a("onSearchParams",n)};return(n,t)=>{const i=e.resolveComponent("el-input"),d=e.resolveComponent("el-form-item"),p=e.resolveComponent("el-col"),N=e.resolveComponent("el-row"),m=e.resolveComponent("el-button"),h=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",pe,[e.createVNode(A,{modelValue:r.value,"onUpdate:modelValue":t[3]||(t[3]=f=>r.value=f)},{default:e.withCtx(()=>[e.createVNode(h,{ref:"form",model:o,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(N,null,{default:e.withCtx(()=>[e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"主播昵称"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.anchorNickname,"onUpdate:modelValue":t[0]||(t[0]=f=>o.anchorNickname=f),placeholder:"请输入主播昵称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"手机号"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.phone,"onUpdate:modelValue":t[1]||(t[1]=f=>o.phone=f),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(p,{span:8},{default:e.withCtx(()=>[e.createVNode(d,{label:"主播ID"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.id,"onUpdate:modelValue":t[2]||(t[2]=f=>o.id=f),placeholder:"请输入主播ID"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(d,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(m,{class:"from_btn",round:"",type:"primary",onClick:c},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),_e=e.defineComponent({__name:"add-anchor",setup(_,{expose:a}){const r=e.reactive({userId:"",anchorSynopsis:""}),o=e.ref([{avatar:"",gender:"",mobile:"",nickname:"",userId:""}]),c={userId:[{required:!0,message:"请选择手机号",trigger:"change"}]},n=e.ref(null),t=e.ref("");async function i(m){const{code:h,data:f}=await H({keywords:m});if(h!==200)return L.ElMessage.error("获取主播电话失败");o.value=f.records}const d=m=>{m?i(m):o.value=[]},p=()=>new Promise((m,h)=>{n.value?n.value.validate(f=>{f?m(r):h("valid error")}):h("form instance not found")}),N=m=>{const h=o.value.filter(f=>f.userId===m);t.value=h[0].nickname};return a({getAnchorModel:p}),(m,h)=>{const f=e.resolveComponent("el-option"),C=e.resolveComponent("el-select"),y=e.resolveComponent("el-form-item"),b=e.resolveComponent("el-input"),w=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(w,{ref_key:"formRef",ref:n,model:r,rules:c},{default:e.withCtx(()=>[e.createVNode(y,{label:"手机号",prop:"phone"},{default:e.withCtx(()=>[e.createVNode(C,{modelValue:r.userId,"onUpdate:modelValue":h[0]||(h[0]=x=>r.userId=x),"remote-method":d,filterable:"",placeholder:"请选择手机号",remote:"",onChange:N},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,x=>(e.openBlock(),e.createBlock(f,{key:x.userId,label:x.mobile,value:x.userId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(y,{label:"昵称"},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(t.value),1)]),_:1}),e.createVNode(y,{label:"主播简介",prop:"introduce"},{default:e.withCtx(()=>[e.createVNode(b,{modelValue:r.anchorSynopsis,"onUpdate:modelValue":h[1]||(h[1]=x=>r.anchorSynopsis=x),placeholder:"请输入主播简介",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])}}}),fe={class:"table-info"},he=["src"],Ve={class:"table-info__wrapper"},ge={class:"table-info__wrapper--name"},Ce={class:"table-info__wrapper--description"},be={class:"table-info__wrapper--description ellipsis"},xe={key:0},Ne={key:1},we={key:2},ye={class:"dialog-footer"},ke=e.defineComponent({__name:"anchor-list",setup(_){const a=e.reactive({size:10,current:1,total:0}),r=e.ref(null),o=e.ref("calc(100vh - 380px)"),c=e.ref(""),n=[{name:"",label:"全部"},{name:"NORMAL",label:"启用"},{name:"FORBIDDEN",label:"禁用"},{name:"VIOLATION",label:"违规禁播"}],t=e.ref(!1),i=e.ref([]),d=e.ref(!1),p=e.ref({id:""}),N=e.ref({id:"",anchorNickname:"",phone:""});m();async function m(){const V={...a,...N.value,status:c.value},{code:g,data:l}=await G(V);if(g!==200)return L.ElMessage.error("获取直播间列表失败");i.value=l.records,a.current=l.current,a.size=l.size,a.total=l.total}const h=V=>{a.size=V,m()},f=V=>{a.current=V,m()},C=V=>{N.value=V,a.current=1,m()},y=()=>{a.current=1,m()},b=async(V,g)=>{const l=g!=="NORMAL",{code:T,data:k}=await j(V,l);if(T!==200)return L.ElMessage.error(l?"启用主播失败":"禁用主播失败");m()},w=V=>{d.value=!0,p.value.id=V},x=async()=>{var k;const V=await((k=r.value)==null?void 0:k.getAnchorModel());t.value=!0;const{code:g,data:l,msg:T}=await Y(V);g===200&&(m(),t.value=!1)},I=V=>{o.value=V?"calc(100vh - 480px)":"calc(100vh - 380px)"};return(V,g)=>{const l=e.resolveComponent("el-tab-pane"),T=e.resolveComponent("el-tabs"),k=e.resolveComponent("el-button"),Se=e.resolveComponent("el-switch"),Ee=e.resolveComponent("el-link"),z=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(me,{onOnSearchParams:C,onChangeShow:I}),e.createVNode(T,{modelValue:c.value,"onUpdate:modelValue":g[0]||(g[0]=s=>c.value=s),onTabChange:y},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(n,s=>e.createVNode(l,{key:s.name,label:s.label,name:s.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),e.createVNode(k,{style:{"margin-bottom":"8px"},type:"primary",onClick:g[1]||(g[1]=s=>t.value=!0)},{default:e.withCtx(()=>[e.createTextVNode("新增主播")]),_:1}),e.createVNode(e.unref(O),{data:i.value,style:e.normalizeStyle({height:o.value}),class:"table"},{default:e.withCtx(()=>[e.createVNode(S,{label:"主播信息",width:"300"},{default:e.withCtx(({row:s})=>[e.createElementVNode("div",fe,[e.createElementVNode("img",{src:s.anchorIcon},null,8,he),e.createElementVNode("div",Ve,[e.createElementVNode("div",ge,[e.createElementVNode("span",null,e.toDisplayString(s.anchorNickname),1),e.createElementVNode("span",null,e.toDisplayString(s.phone),1)]),e.createElementVNode("div",Ce,"主播ID："+e.toDisplayString(s.id),1),e.createElementVNode("div",be,e.toDisplayString(s.anchorSynopsis),1)])])]),_:1}),e.createVNode(S,{align:"center",label:"状态"},{default:e.withCtx(({row:s})=>[s.status==="NORMAL"?(e.openBlock(),e.createElementBlock("span",xe,"启用")):s.status==="FORBIDDEN"?(e.openBlock(),e.createElementBlock("span",Ne,"禁用")):s.status==="VIOLATION"?(e.openBlock(),e.createElementBlock("span",we,"违规禁播")):e.createCommentVNode("",!0)]),_:1}),e.createVNode(S,{label:"操作",prop:"action",width:"150"},{default:e.withCtx(({row:s})=>[s.status!=="VIOLATION"?(e.openBlock(),e.createBlock(Se,{key:0,"model-value":s.status==="NORMAL",onChange:Le=>b(s.id,s.status)},null,8,["model-value","onChange"])):(e.openBlock(),e.createBlock(Ee,{key:1,type:"primary",onClick:Le=>w(s.id)},{default:e.withCtx(()=>[e.createTextVNode("禁播原因")]),_:2},1032,["onClick"]))]),_:1})]),_:1},8,["data","style"]),e.createVNode(D,{"page-num":a.current,"page-size":a.size,total:a.total,onHandleSizeChange:h,onHandleCurrentChange:f},null,8,["page-num","page-size","total"]),e.createVNode(z,{modelValue:d.value,"onUpdate:modelValue":g[2]||(g[2]=s=>d.value=s),title:"违禁原因",width:"550px"},{default:e.withCtx(()=>[e.createVNode($,{id:p.value.id,type:"ANCHOR"},null,8,["id"])]),_:1},8,["modelValue"]),e.createVNode(z,{modelValue:t.value,"onUpdate:modelValue":g[4]||(g[4]=s=>t.value=s),"close-on-click-modal":!1,"destroy-on-close":"",title:"新增主播",width:"550px"},{footer:e.withCtx(()=>[e.createElementVNode("span",ye,[e.createVNode(k,{onClick:g[3]||(g[3]=s=>t.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(k,{type:"primary",onClick:x},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(_e,{ref_key:"addAnchorRef",ref:r},null,512)]),_:1},8,["modelValue"])],64)}}}),Ae="",ue=Object.freeze(Object.defineProperty({__proto__:null,default:B(ke,[["__scopeId","data-v-ff47de91"]])},Symbol.toStringTag,{value:"Module"}));return M});
