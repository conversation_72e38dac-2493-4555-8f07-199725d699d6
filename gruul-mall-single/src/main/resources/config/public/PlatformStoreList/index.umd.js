(function(e,_){typeof exports=="object"&&typeof module<"u"?module.exports=_(require("vue"),require("vue-router"),require("element-plus"),require("@/apis/http"),require("@/components/MCard.vue"),require("@/components/pageManage/PageManage.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","element-plus","@/apis/http","@/components/MCard.vue","@/components/pageManage/PageManage.vue"],_):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformStoreList=_(e.PlatformStoreListContext.Vue,e.PlatformStoreListContext.VueRouter,e.PlatformStoreListContext.ElementPlus,e.PlatformStoreListContext.Request,e.PlatformStoreListContext.MCard,e.PlatformStoreListContext.PageManage))})(this,function(e,_,c,C,g,y){"use strict";var N=document.createElement("style");N.textContent=`.button[data-v-9da13593]{margin:15px 0 20px}.ellipsis[data-v-9da13593]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;white-space:normal}
`,document.head.appendChild(N);const S=l=>C.get({url:"addon-shop-store/store/list",params:l}),b=(l,i)=>C.del({url:`addon-shop-store/store/del/${l}/${i}`}),w=(l,i)=>C.put({url:`addon-shop-store/store/update/${l}`,data:i}),E={style:{background:"#f9f9f9","margin-bottom":"20px"}},k={class:"ellipsis"},L={class:"ellipsis"},M={class:"ellipsis"},D=["onClick"],P=["onClick"],O=["onClick"],R={style:{position:"fixed",bottom:"10px",background:"#fff",width:"980px",height:"70px","z-index":"1000"}},B=e.defineComponent({__name:"PlatformStoreList",setup(l){const i=_.useRouter(),f=e.ref(!1),h=e.ref([]),a=e.reactive({shopName:"",storeName:"",status:""}),s=e.reactive({size:20,current:1,total:0});p();async function p(){const{code:n,data:o}=await S({...s,...a});n===200&&o?(h.value=o.records,s.total=o.total):c.ElMessage.error("获取列表失败")}const F=(n,o,d)=>{i.push({path:"/store/AddStore",query:{shopId:n,id:o,lookType:d}})},I=(n,o)=>{c.ElMessageBox.confirm("确定删除该门店吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:d,msg:r}=await b(n,o);d===200?(c.ElMessage.success("删除成功"),p()):c.ElMessage.error(r||"删除失败")})},T=async(n,o)=>{const{code:d,msg:r}=await w("PLATFORM_FORBIDDEN",[{shopId:n,ids:[o]}]);d===200?(c.ElMessage.success("修改状态成功"),p()):c.ElMessage.error(r||"修改状态失败")},q=n=>{s.size=n,p()},z=n=>{s.current=n,p()},A=()=>{s.current=1,p()};return(n,o)=>{const d=e.resolveComponent("el-input"),r=e.resolveComponent("el-form-item"),x=e.resolveComponent("el-col"),u=e.resolveComponent("el-option"),$=e.resolveComponent("el-select"),H=e.resolveComponent("el-row"),U=e.resolveComponent("el-button"),G=e.resolveComponent("el-form"),m=e.resolveComponent("el-table-column"),j=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",E,[e.createVNode(g,{modelValue:f.value,"onUpdate:modelValue":o[3]||(o[3]=t=>f.value=t)},{default:e.withCtx(()=>[e.createVNode(G,{model:a},{default:e.withCtx(()=>[e.createVNode(H,{gutter:20},{default:e.withCtx(()=>[e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(r,{label:"店铺名称"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:a.shopName,"onUpdate:modelValue":o[0]||(o[0]=t=>a.shopName=t),maxlength:"20",placeholder:"请填写店铺名称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(r,{label:"门店名称"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:a.storeName,"onUpdate:modelValue":o[1]||(o[1]=t=>a.storeName=t),maxlength:"20",placeholder:"请填写门店名称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(r,{label:"状态"},{default:e.withCtx(()=>[e.createVNode($,{modelValue:a.status,"onUpdate:modelValue":o[2]||(o[2]=t=>a.status=t),placeholder:"请选择"},{default:e.withCtx(()=>[e.createVNode(u,{label:"全部",value:" "}),e.createVNode(u,{label:"正常",value:"NORMAL"}),e.createVNode(u,{label:"店铺禁用",value:"SHOP_FORBIDDEN"}),e.createVNode(u,{label:"平台禁用",value:"PLATFORM_FORBIDDEN"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(U,{round:"",type:"primary",onClick:A},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),e.createVNode(j,{data:h.value,"header-cell-style":{"background-color":"#F6F8FA","font-weight":"bold",color:"#515151"},"empty-text":"暂无数据~",style:{width:"100%","margin-bottom":"70px"}},{default:e.withCtx(()=>[e.createVNode(m,{label:"店铺名称"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",k,e.toDisplayString(t.shopName),1)]),_:1}),e.createVNode(m,{label:"门店名称"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",L,e.toDisplayString(t.storeName),1)]),_:1}),e.createVNode(m,{label:"负责人手机号"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",null,e.toDisplayString(t.functionaryPhone),1)]),_:1}),e.createVNode(m,{label:"状态"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",null,e.toDisplayString(t.status==="NORMAL"?"正常":t.status==="SHOP_FORBIDDEN"?"店铺禁用":"平台禁用"),1)]),_:1}),e.createVNode(m,{label:"地址",width:"270px"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",M,e.toDisplayString(t.detailedAddress),1)]),_:1}),e.createVNode(m,{align:"center",label:"操作"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",{style:{color:"#2e99f3",cursor:"pointer"},onClick:V=>F(t.shopId,t.id,"OnlyLook")},"查看",8,D),t.status!=="PLATFORM_FORBIDDEN"?(e.openBlock(),e.createElementBlock("span",{key:0,style:{color:"#2e99f3",cursor:"pointer","margin-left":"10px"},onClick:V=>T(t.shopId,t.id)},"下架",8,P)):e.createCommentVNode("",!0),e.createElementVNode("span",{style:{color:"#2e99f3",cursor:"pointer","margin-left":"10px"},onClick:V=>I(t.shopId,t.id)},"删除",8,O)]),_:1})]),_:1},8,["data"]),e.createElementVNode("div",R,[e.createVNode(y,{"page-num":s.current,"page-size":s.size,total:s.total,onHandleSizeChange:q,onHandleCurrentChange:z},null,8,["page-num","page-size","total"])])])}}}),J="";return((l,i)=>{const f=l.__vccOpts||l;for(const[h,a]of i)f[h]=a;return f})(B,[["__scopeId","data-v-9da13593"]])});
