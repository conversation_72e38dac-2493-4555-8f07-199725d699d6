(function(e,a){typeof exports=="object"&&typeof module<"u"?module.exports=a(require("vue"),require("@/views/message/components/aside/Index.vue"),require("@/views/message/components/main/Index.vue"),require("@/views/message/components/types"),require("@/composables/stomp/StompHandler"),require("@/composables/stomp/typs"),require("@/apis/http"),require("@/store/modules/shopInfo")):typeof define=="function"&&define.amd?define(["vue","@/views/message/components/aside/Index.vue","@/views/message/components/main/Index.vue","@/views/message/components/types","@/composables/stomp/StompHandler","@/composables/stomp/typs","@/apis/http","@/store/modules/shopInfo"],a):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopSupplierMessage=a(e.ShopSupplierMessageContext.Vue,e.ShopSupplierMessageContext.MessageAside,e.ShopSupplierMessageContext.MessageMain,e.ShopSupplierMessageContext.MessageTypes,e.ShopSupplierMessageContext.StompHandler,e.ShopSupplierMessageContext.StompTypes,e.ShopSupplierMessageContext.Request,e.ShopSupplierMessageContext.ShopInfoStore))})(this,function(e,a,M,u,I,x,d,m){"use strict";var g=document.createElement("style");g.textContent=`.customer-service.el-container[data-v-635e916d]{border:1px solid var(--el-border-color);border-radius:12px;width:100%;min-height:700px;overflow:hidden}.el-aside[data-v-635e916d]{border-top-left-radius:8px;border-bottom-left-radius:8px;border-right:1px solid var(--el-border-color);min-height:600px;background-color:#fff}.el-main[data-v-635e916d]{width:600px;padding:0}
`,document.head.appendChild(g);const w=(s,o,t)=>d.get({url:"/gruul-mall-carrier-pigeon/pigeon/group-chat-room-messages/chat-rooms",params:{...o,keywords:s,senderType:t},showLoading:!1}),f=(s,o)=>d.get({url:"/gruul-mall-carrier-pigeon/pigeon/group-chat-room-messages/chat-room",params:{...s,...o}}),v=s=>d.post({url:"/gruul-mall-carrier-pigeon/pigeon/group-chat-room-messages/message",data:s,showLoading:!1}),l=e.ref(m.useShopInfoStore().getterShopInfo);m.useShopInfoStore().$subscribe((s,o)=>{const t=o.shopInfo;l.value=t,!(!t||!t.id||!t.token)&&(n.value=null,r.initLoad())});const r=e.reactive(new u.IPage(s=>w(S.value,s,4),30)),n=e.ref(null),S=e.ref(""),_=e.ref(!1),C=s=>{n.value=s,nextTick(()=>{s.chatWithShopInfo.shopId&&i.value.initLoad()})},y=s=>{S.value=s,r.initLoad()},b=s=>{_.value=s},i=e.ref(new u.IPage(s=>{if(!n.value)return Promise.reject();n.value.lastMessage&&(n.value.lastMessage.show=!1);const{chatWithShopInfo:o}=n.value;return f({senderShopId:o.shopId},s)},20)),L=s=>{if(!n.value)return;n.value.lastMessage&&(n.value.lastMessage.show=!1);const{chatWithShopInfo:o}=n.value;v({receiverShopId:o.shopId,messageType:s.messageType,content:s.message})},P=()=>{r.initLoad()},q=async()=>{await r.initLoad().then(s=>{i.value=new u.IPage(o=>{if(!n.value)return Promise.reject();n.value.lastMessage&&(n.value.lastMessage.show=!1);const{chatWithShopInfo:t}=n.value;return f({senderShopId:t.shopId},o)},20)}),I.stompHookMount(x.Channel.SUPPLIER_SHOP,{success:P,fail:()=>{},subscribe:s=>{T(s)}})},T=async s=>{var t;await r.initLoad();const o=s.sender.senderShopInfo.shopId;if(o===((t=n.value)==null?void 0:t.chatWithShopInfo.shopId)||o===l.value.id){const{sender:p,receiver:c,messageType:h,message:N}=s,V={sender:p,receiver:c,messageType:h,message:N,read:!1,handled:!1,show:!0,sendTime:String(+new Date)};i.value.concatData(V)}},k=()=>{i.value.loadMore()},H=e.defineComponent({__name:"ShopSupplierMessage",setup(s){return e.onMounted(q),(o,t)=>{const p=e.resolveComponent("el-aside"),c=e.resolveComponent("el-main"),h=e.resolveComponent("el-container");return e.openBlock(),e.createBlock(h,{class:"customer-service"},{default:e.withCtx(()=>[e.createVNode(p,{width:"200px"},{default:e.withCtx(()=>[e.createVNode(a,{"message-users":e.unref(r).records,onChange:e.unref(C),onKeywordChange:e.unref(y),onSearchFocus:e.unref(b)},null,8,["message-users","onChange","onKeywordChange","onSearchFocus"])]),_:1}),e.createVNode(c,null,{default:e.withCtx(()=>[e.createVNode(M,{messages:e.unref(i).records,"search-focus":e.unref(_),"shop-info":e.unref(l),user:e.unref(n),onMessageSubmit:e.unref(L),onLoadMore:e.unref(k)},null,8,["messages","search-focus","shop-info","user","onMessageSubmit","onLoadMore"])]),_:1})]),_:1})}}}),W="";return((s,o)=>{const t=s.__vccOpts||s;for(const[p,c]of o)t[p]=c;return t})(H,[["__scopeId","data-v-635e916d"]])});
