(function(e,U){typeof exports=="object"&&typeof module<"u"?module.exports=U(require("vue"),require("@vueuse/core"),require("@/components/q-area-choose/area-choose.vue"),require("element-plus"),require("@/utils/uuid"),require("vue-router"),require("@/components/pageManage/PageManage.vue"),require("@/components/PageManage.vue"),require("element-china-area-data"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@/components/q-area-choose/area-choose.vue","element-plus","@/utils/uuid","vue-router","@/components/pageManage/PageManage.vue","@/components/PageManage.vue","element-china-area-data","@/apis/http"],U):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopFreight=U(e.ShopFreightContext.Vue,e.ShopFreightContext.VueUse,e.ShopFreightContext.QAreaChoose,e.ShopFreightContext.ElementPlus,e.ShopFreightContext.UtilsUuid,e.ShopFreightContext.VueRouter,e.ShopFreightContext.PageManage,e.ShopFreightContext.PageManageTwo,e.ShopFreightContext.ElementChinaAreaData,e.ShopFreightContext.Request))})(this,function(e,U,K,h,F,q,J,L,X,$){"use strict";var j=document.createElement("style");j.textContent=`.number-input[data-v-ac297236]{width:80px}.dialogTab_container__input[data-v-ac297236]{text-align:center}.dialogTab_container__right_btn[data-v-ac297236]{display:flex;justify-content:space-evenly}.number-input[data-v-911b566b]{width:80px}.dialogTab_container__input[data-v-911b566b]{text-align:center}.dialogTab_container__right_btn[data-v-911b566b]{display:flex;justify-content:space-evenly}.templateNames[data-v-d3a228eb]{color:#ccc}.templateName[data-v-21093d04]{display:inline-block;margin-left:100px;color:#ccc}.Template_container__btn[data-v-1aff1d8d]{width:81px;height:36px;font-size:12px;margin-bottom:10px}.Template_container__tab[data-v-1aff1d8d]{border:1px solid #f2f2f2;border-bottom:0;margin-bottom:20px}.Template_container__head[data-v-1aff1d8d]{height:40px;display:flex;padding:0 10px;justify-content:space-between;align-items:center;background:#f6f8fa}.Template_container__head--left[data-v-1aff1d8d]{font-size:12px;font-weight:700;color:#515151}.Template_container__head--right_btn[data-v-1aff1d8d]{display:flex;justify-content:space-evenly;margin-right:15px}.Template_container__head--right_btn .el-button[data-v-1aff1d8d]:nth-child(1){margin-right:15px}.btn[data-v-e4d196dc]{font-size:12px;margin-bottom:10px}.template-table[data-v-e4d196dc]{height:calc(100vh - 300px);overflow:auto}.freight-add_container__btn[data-v-41195ec7]{width:100px;height:36px;font-size:12px;margin-bottom:10px}.freight-add_container__table_border[data-v-41195ec7]{border-left:1px solid #f2f2f2;border-right:1px solid #f2f2f2}.freight-add_container__table--loctionbtn[data-v-41195ec7]{height:60px;display:flex;justify-content:space-between;align-items:center;flex-direction:column}.right_btn[data-v-41195ec7]{display:flex;justify-content:center}.freight-add_container__btn[data-v-940ba9fd]{width:81px;height:36px;font-size:12px;margin-bottom:10px}.freight-add_container__table_border[data-v-940ba9fd]{border-left:1px solid #f2f2f2;border-right:1px solid #f2f2f2}.freight-add_container__table--loctionbtn[data-v-940ba9fd]{height:60px;display:flex;justify-content:space-between;align-items:center;flex-direction:column}.right_btn[data-v-940ba9fd]{display:flex;justify-content:space-around}.distributionServe_container__btn[data-v-f889e750],.distributionServe_container__btn[data-v-35a06e50]{width:81px;height:36px;font-size:12px;margin-bottom:10px}.line[data-v-9d14fba0]{height:38px;background:#f6f8fa;color:#515151;font-size:13px;margin:10px 0;display:flex;justify-content:flex-start;align-items:center}.line__column[data-v-9d14fba0]{width:4px;height:16px;margin:0 20px}
`,document.head.appendChild(j);const Z=e.defineComponent({__name:"ShopFreight",setup(r){const l={freightTemplate:e.defineAsyncComponent(()=>Promise.resolve().then(()=>We)),freeShippingCondition:e.defineAsyncComponent(()=>Promise.resolve().then(()=>at)),freightAdd:e.defineAsyncComponent(()=>Promise.resolve().then(()=>mt)),freightServe:e.defineAsyncComponent(()=>Promise.resolve().then(()=>kt)),freightPrint:e.defineAsyncComponent(()=>Promise.resolve().then(()=>zt)),freightSet:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Ft))},o=e.ref("freightTemplate");return(d,a)=>{const i=e.resolveComponent("el-tab-pane"),u=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(u,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=b=>o.value=b)},{default:e.withCtx(()=>[e.createVNode(i,{label:"运费模板",name:"freightTemplate"}),e.createVNode(i,{label:"包邮条件",name:"freeShippingCondition"}),e.createVNode(i,{label:"地址管理",name:"freightAdd"}),e.createVNode(i,{label:"物流服务",name:"freightServe"}),e.createVNode(i,{label:"物流设置",name:"freightSet"}),e.createVNode(i,{label:"打印设置",name:"freightPrint"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(l[o.value])))])}}}),v=["onClick"],ee={class:"dialogTab_container__right_btn"},te=e.defineComponent({__name:"un-free-form",props:{tableData:{type:Array,default:[{id:F(10),firstAmount:0,firstQuantity:"0",logisticsId:0,regionJson:[],secondAmount:0,secondQuantity:"0",valuationModel:"PKGS"}]},isPKGS:{type:Boolean,required:!0}},emits:["update:tableData"],setup(r,{emit:l}){const o=r,d=l,a=U.useVModel(o,"tableData",d),i=e.ref(!1),u=e.ref("0"),b=k=>{i.value=!0,u.value=k},g=e.computed(()=>{var k;return((k=a.value.find(y=>y.id===u.value))==null?void 0:k.regionJson)||[]}),T=e.computed(()=>a.value.map(y=>y.regionJson).flat(1)),N=k=>{a.value.find(y=>y.id===u.value).regionJson=k},S=k=>(console.log("da获取选中类名ta",k),k.map(y=>y.lowerCode.length===y.length?y.upperName:`${y.upperName}(${y.lowerCode.length}/${y.length})`).join(",")),D=(k,y)=>{if(k===-1){const c={id:F(10),firstAmount:0,firstQuantity:"0",logisticsId:0,regionJson:[],secondAmount:0,secondQuantity:"0",valuationModel:"PKGS"};a.value.push(c)}else{if(k===0)return h.ElMessage.error("至少保留一个配送区域");a.value=a.value.filter(c=>c.id!==y.id)}};return(k,y)=>{const c=e.resolveComponent("el-button"),s=e.resolveComponent("el-table-column"),C=e.resolveComponent("el-input-number"),_=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(_,{"cell-style":{padding:"15px 0"},data:e.unref(a),"header-cell-style":{fontSize:"14px",color:"#333"}},{default:e.withCtx(()=>[e.createVNode(s,{prop:"regionJson",label:"选择区域"},{default:e.withCtx(({row:t})=>[t.regionJson.length?(e.openBlock(),e.createElementBlock("div",{key:0,onClick:n=>b(t.id)},e.toDisplayString(S(t.regionJson)),9,v)):e.createCommentVNode("",!0),e.createVNode(c,{type:"primary",link:"",onClick:n=>b(t.id)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(t.regionJson.length?"编辑":"添加区域"),1)]),_:2},1032,["onClick"])]),_:1}),e.createVNode(s,{prop:"firstQuantity",label:o.isPKGS?"首件数（件）":"首重（kg）"},{default:e.withCtx(({row:t})=>[e.createVNode(C,{modelValue:t.firstQuantity,"onUpdate:modelValue":n=>t.firstQuantity=n,class:"number-input",controls:!1,precision:o.isPKGS?0:1,step:.1,min:0,"input-style":{textAlign:"center"}},null,8,["modelValue","onUpdate:modelValue","precision"])]),_:1},8,["label"]),e.createVNode(s,{prop:"firstAmount",label:"首费（元）"},{default:e.withCtx(({row:t})=>[e.createVNode(C,{modelValue:t.firstAmount,"onUpdate:modelValue":n=>t.firstAmount=n,class:"number-input",controls:!1,precision:2,step:.1,min:0,"input-style":{textAlign:"center"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(s,{prop:"secondQuantity",label:o.isPKGS?"续件数（件）":"续重（kg）"},{default:e.withCtx(({row:t})=>[e.createVNode(C,{modelValue:t.secondQuantity,"onUpdate:modelValue":n=>t.secondQuantity=n,class:"number-input",controls:!1,precision:o.isPKGS?0:1,step:.1,min:0,"input-style":{textAlign:"center"}},null,8,["modelValue","onUpdate:modelValue","precision"])]),_:1},8,["label"]),e.createVNode(s,{prop:"secondAmount",label:"续费（元）"},{default:e.withCtx(({row:t})=>[e.createVNode(C,{modelValue:t.secondAmount,"onUpdate:modelValue":n=>t.secondAmount=n,class:"number-input",controls:!1,precision:2,step:.1,min:0,"input-style":{textAlign:"center"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(s,{prop:"name",label:"操作","min-width":"50%"},{default:e.withCtx(({row:t,$index:n})=>[e.createElementVNode("div",ee,[e.unref(a).length-1===n?(e.openBlock(),e.createBlock(c,{key:0,type:"primary",link:"",onClick:E=>D(-1,t)},{default:e.withCtx(()=>[e.createTextVNode("添加")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.createVNode(c,{type:"danger",disabled:e.unref(a).length===1,link:"",onClick:E=>D(e.unref(a).length-1,t)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data"]),e.createVNode(K,{show:i.value,"onUpdate:show":y[0]||(y[0]=t=>i.value=t),"current-choose":g.value,"all-choose-arr":T.value,onChange:N},null,8,["show","current-choose","all-choose-arr"])],64)}}}),Lt="",A=(r,l)=>{const o=r.__vccOpts||r;for(const[d,a]of l)o[d]=a;return o},oe=A(te,[["__scopeId","data-v-ac297236"]]),G=r=>(e.pushScopeId("data-v-911b566b"),r=r(),e.popScopeId(),r),ae=["onClick"],ne=G(()=>e.createElementVNode("span",null," 在  ",-1)),le=G(()=>e.createElementVNode("span",null," 满 ",-1)),re=G(()=>e.createElementVNode("span",null,"  元以上包邮 ",-1)),ie=G(()=>e.createElementVNode("span",null,"在",-1)),se=G(()=>e.createElementVNode("span",null,"满 ",-1)),de=G(()=>e.createElementVNode("span",null,"   元以上包邮",-1)),ce={class:"dialogTab_container__right_btn"},pe=e.defineComponent({__name:"free-form",props:{tableData:{type:Array,default:[{id:F(10),amountNum:0,logisticsId:0,pieceNum:0,postType:"PKGS",region:[],weight:0}]},isPKGS:{type:Boolean,required:!0},isEdit:{type:Boolean,required:!0}},emits:["update:tableData"],setup(r,{emit:l}){const o=r,d=l,a=U.useVModel(o,"tableData",d),i=e.ref(0),u=e.ref(!1),b=[{value:"PKGS",label:"件数"},{value:"MONEY",label:"金额"},{value:"PKGS_MONEY",label:"件数+金额"}],g=[{value:"WEIGHT",label:"重量"},{value:"MONEY",label:"金额"},{value:"WEIGHT_MONEY",label:"重量+金额"}];e.watch(()=>o.isPKGS,(_,t)=>{o.isEdit||(_!==t&&_?a.value.forEach(n=>{n.postType="PKGS"}):a.value.forEach(n=>{n.postType="WEIGHT"}))},{immediate:!0});const T=(_,t)=>{u.value=!0,i.value=t},N=_=>{u.value=!0,i.value=_},S=e.computed(()=>{const _=i.value;return a.value[_].region}),D=e.computed(()=>a.value.map(t=>t.region).flat(1)),k=_=>_.map(t=>t.lowerCode.length===t.length?t.upperName:`${t.upperName}(${t.lowerCode.length}/${t.length})`).join(","),y=_=>{a.value[i.value].region=_},c=(_,t)=>{if(_===-1){const n={id:F(10),amountNum:0,logisticsId:0,pieceNum:0,postType:o.isPKGS?"PKGS":"WEIGHT",region:new Map,weight:0};a.value.push(n)}else{if(_===0)return h.ElMessage.error("至少保留一个配送区域");a.value=a.value.filter(n=>n.id!==t.id)}};function s(_){return["PKGS_MONEY","WEIGHT_MONEY"].includes(_)}function C(_){return["PKGS","WEIGHT"].includes(_)}return(_,t)=>{const n=e.resolveComponent("el-button"),E=e.resolveComponent("el-table-column"),V=e.resolveComponent("el-option"),m=e.resolveComponent("el-select"),p=e.resolveComponent("el-col"),B=e.resolveComponent("el-input-number"),M=e.resolveComponent("el-row"),I=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(I,{"cell-style":{padding:"30px 0"},data:e.unref(a),"header-cell-style":{fontSize:"14px",color:"#333"}},{default:e.withCtx(()=>[e.createVNode(E,{label:"选择区域","min-width":"90%",prop:"name"},{default:e.withCtx(({row:w,$index:f})=>[w.region.length?(e.openBlock(),e.createElementBlock("div",{key:0,onClick:P=>N(f)},e.toDisplayString(k(w.region)),9,ae)):e.createCommentVNode("",!0),e.createVNode(n,{link:"",type:"primary",onClick:P=>T(w,f)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(w.region.length?"编辑":"添加区域"),1)]),_:2},1032,["onClick"])]),_:1}),e.createVNode(E,{label:o.isPKGS?"首件数（件）":"首重（kg）","min-width":"350%",prop:"name"},{default:e.withCtx(({row:w})=>[e.createVNode(M,{gutter:20,align:"middle"},{default:e.withCtx(()=>[e.createVNode(p,{span:6},{default:e.withCtx(()=>[o.isPKGS?(e.openBlock(),e.createBlock(m,{key:0,modelValue:w.postType,"onUpdate:modelValue":f=>w.postType=f,placeholder:"Select",size:"small"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(b,f=>e.createVNode(V,{key:f.value,label:f.label,value:f.value},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])):(e.openBlock(),e.createBlock(m,{key:1,modelValue:w.postType,"onUpdate:modelValue":f=>w.postType=f,placeholder:"Select",size:"small"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(g,f=>e.createVNode(V,{key:f.value,label:f.label,value:f.value},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"]))]),_:2},1024),e.createVNode(p,{span:18},{default:e.withCtx(()=>[C(w.postType)?(e.openBlock(),e.createBlock(M,{key:0,align:"middle"},{default:e.withCtx(()=>[e.createVNode(p,{span:1},{default:e.withCtx(()=>[ne]),_:1}),e.createVNode(p,{span:5},{default:e.withCtx(()=>[o.isPKGS?(e.openBlock(),e.createBlock(B,{key:0,modelValue:w.pieceNum,"onUpdate:modelValue":f=>w.pieceNum=f,controls:!1,"input-style":{textAlign:"center"},min:0,precision:0,step:.1,class:"number-input"},null,8,["modelValue","onUpdate:modelValue"])):(e.openBlock(),e.createBlock(B,{key:1,modelValue:w.weight,"onUpdate:modelValue":f=>w.weight=f,controls:!1,"input-style":{textAlign:"center"},min:0,precision:1,step:.1,class:"number-input"},null,8,["modelValue","onUpdate:modelValue"]))]),_:2},1024),e.createVNode(p,{span:6},{default:e.withCtx(()=>[e.createElementVNode("span",null,"   "+e.toDisplayString(o.isPKGS?"件":"kg")+"以上包邮 ",1)]),_:1})]),_:2},1024)):w.postType==="MONEY"?(e.openBlock(),e.createBlock(M,{key:1,align:"middle"},{default:e.withCtx(()=>[e.createVNode(p,{span:1},{default:e.withCtx(()=>[le]),_:1}),e.createVNode(p,{span:5},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:w.amountNum,"onUpdate:modelValue":f=>w.amountNum=f,controls:!1,"input-style":{textAlign:"center"},min:0,precision:2,step:.1,class:"number-input"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e.createVNode(p,{span:6},{default:e.withCtx(()=>[re]),_:1})]),_:2},1024)):e.createCommentVNode("",!0),s(w.postType)?(e.openBlock(),e.createBlock(M,{key:2,align:"middle"},{default:e.withCtx(()=>[e.createVNode(p,{span:1},{default:e.withCtx(()=>[ie]),_:1}),e.createVNode(p,{span:5},{default:e.withCtx(()=>[o.isPKGS?(e.openBlock(),e.createBlock(B,{key:0,modelValue:w.pieceNum,"onUpdate:modelValue":f=>w.pieceNum=f,controls:!1,"input-style":{textAlign:"center"},min:0,precision:0,step:.1,class:"number-input"},null,8,["modelValue","onUpdate:modelValue"])):(e.openBlock(),e.createBlock(B,{key:1,modelValue:w.weight,"onUpdate:modelValue":f=>w.weight=f,controls:!1,"input-style":{textAlign:"center"},min:0,precision:1,step:.1,class:"number-input"},null,8,["modelValue","onUpdate:modelValue"]))]),_:2},1024),e.createVNode(p,{span:4},{default:e.withCtx(()=>[e.createElementVNode("span",null,"  "+e.toDisplayString(o.isPKGS?"件":"kg")+"以上,",1)]),_:1}),e.createVNode(p,{span:1},{default:e.withCtx(()=>[se]),_:1}),e.createVNode(p,{span:5},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:w.amountNum,"onUpdate:modelValue":f=>w.amountNum=f,controls:!1,"input-style":{textAlign:"center"},min:0,precision:2,step:.1,class:"number-input"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e.createVNode(p,{span:6},{default:e.withCtx(()=>[de]),_:1})]),_:2},1024)):e.createCommentVNode("",!0)]),_:2},1024)]),_:2},1024)]),_:1},8,["label"]),e.createVNode(E,{label:"操作","min-width":"50%",prop:"name"},{default:e.withCtx(({row:w,$index:f})=>[e.createElementVNode("div",ce,[e.unref(a).length-1===f?(e.openBlock(),e.createBlock(n,{key:0,link:"",type:"primary",onClick:P=>c(-1,w)},{default:e.withCtx(()=>[e.createTextVNode(" 添加 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.createVNode(n,{disabled:e.unref(a).length===1,link:"",type:"danger",onClick:P=>c(e.unref(a).length-1,w)},{default:e.withCtx(()=>[e.createTextVNode("删除 ")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data"]),e.createVNode(K,{show:u.value,"onUpdate:show":t[0]||(t[0]=w=>u.value=w),"all-choose-arr":D.value,"current-choose":S.value,onChange:y},null,8,["show","all-choose-arr","current-choose"])],64)}}}),Ot="",me=A(pe,[["__scopeId","data-v-911b566b"]]),ge=(r=>(e.pushScopeId("data-v-d3a228eb"),r=r(),e.popScopeId(),r))(()=>e.createElementVNode("span",{class:"templateNames"},"模板名称最多25个字",-1)),fe=e.defineComponent({__name:"freight-template-form",props:{isPKGS:{type:Boolean,required:!0},isEdit:{type:Boolean,required:!0}},setup(r){const l=r,o=e.ref(),d=e.inject("parentFreightTemplateForm"),a=e.reactive({templateName:[{required:!0,message:"请输入模板名称",trigger:"blur"},{min:2,max:25,message:"请输入2~25个字",trigger:"blur"}],valuationModel:[{required:!0,message:"请选择计费方式",trigger:"change"}]});return(i,u)=>{const b=e.resolveComponent("el-input"),g=e.resolveComponent("el-col"),T=e.resolveComponent("el-row"),N=e.resolveComponent("el-form-item"),S=e.resolveComponent("el-radio"),D=e.resolveComponent("el-radio-group"),k=e.resolveComponent("el-checkbox"),y=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(y,{ref_key:"fromRef",ref:o,model:e.unref(d),rules:a},{default:e.withCtx(()=>[e.createVNode(N,{label:"模板名称","label-width":"80px",prop:"templateName"},{default:e.withCtx(()=>[e.createVNode(T,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(g,{span:12},{default:e.withCtx(()=>[e.createVNode(b,{modelValue:e.unref(d).templateName,"onUpdate:modelValue":u[0]||(u[0]=c=>e.unref(d).templateName=c),maxlength:"25",placeholder:"请输入模板名称",style:{width:"80%"}},null,8,["modelValue"])]),_:1}),e.createVNode(g,{span:6},{default:e.withCtx(()=>[ge]),_:1})]),_:1})]),_:1}),e.createVNode(N,{label:"计费方式","label-width":"80px",prop:"valuationModel"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:e.unref(d).valuationModel,"onUpdate:modelValue":u[1]||(u[1]=c=>e.unref(d).valuationModel=c)},{default:e.withCtx(()=>[e.createVNode(S,{label:"PKGS",size:"large"},{default:e.withCtx(()=>[e.createTextVNode("按件数")]),_:1}),e.createVNode(S,{label:"WEIGHT",size:"large"},{default:e.withCtx(()=>[e.createTextVNode("按重量")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(N,null,{default:e.withCtx(()=>[e.createVNode(oe,{"table-data":e.unref(d).logisticsBaseModelDTO,"onUpdate:tableData":u[2]||(u[2]=c=>e.unref(d).logisticsBaseModelDTO=c),"is-p-k-g-s":l.isPKGS},null,8,["table-data","is-p-k-g-s"])]),_:1}),e.createVNode(N,{"label-width":"10px"},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:e.unref(d).choiceConditionPostage,"onUpdate:modelValue":u[3]||(u[3]=c=>e.unref(d).choiceConditionPostage=c),"false-label":0,"true-label":1,label:"指定包邮条件"},null,8,["modelValue"])]),_:1}),e.unref(d).choiceConditionPostage?(e.openBlock(),e.createBlock(N,{key:0},{default:e.withCtx(()=>[e.createVNode(me,{"table-data":e.unref(d).logisticsIncludePostageDTO,"onUpdate:tableData":u[4]||(u[4]=c=>e.unref(d).logisticsIncludePostageDTO=c),"is-edit":l.isEdit,"is-p-k-g-s":l.isPKGS},null,8,["table-data","is-edit","is-p-k-g-s"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model","rules"])}}}),qt="",_e=A(fe,[["__scopeId","data-v-d3a228eb"]]),ue=r=>$.get({url:"gruul-mall-shop/shop/logistics/address/list",params:r}),he=r=>$.del({url:`gruul-mall-shop/shop/logistics/address/del/${r}`}),H=r=>$.post({url:"gruul-mall-shop/shop/logistics/address/set",data:r}),Ce=()=>$.get({url:"gruul-mall-freight/fright/list",params:{current:1,size:1e3}}),Ve=()=>$.get({url:"gruul-mall-shop/shop/logistics/address/list",params:{current:1,size:1e3}}),ye=r=>$.post({url:"gruul-mall-freight/logistics/express/save",data:r}),be=(r,l)=>$.get({url:"gruul-mall-freight/logistics/express/page",params:{current:r,size:l}}),Ne=r=>$.post({url:"gruul-mall-freight/logistics/express/update",data:r}),xe=r=>$.del({url:`gruul-mall-freight/logistics/express/del/${r}`}),R=(r,l)=>$.put({url:`gruul-mall-shop/shop/logistics/address/set/def/address/${r}/${l}`}),ke=(r,l)=>$.get({url:"gruul-mall-freight/logistics/template/get/list/",params:{current:r,size:l}}),we=r=>$.post({url:"gruul-mall-freight/logistics/template/save/info",data:r}),Se=r=>$.del({url:`gruul-mall-freight/logistics/template/delete/info/${r}`}),Ee=r=>$.get({url:"gruul-mall-freight/logistics/template/get/info/",params:{id:r}}),Te=r=>$.post({url:"gruul-mall-freight/logistics/template/update/info",data:r}),Q=(r,l,o,d)=>$.post({url:"gruul-mall-freight/logistics/settings/edit",data:{customer:r,id:d,key:l,secret:o}}),Be=()=>$.get({url:"gruul-mall-freight/logistics/settings/get"}),Me=(r,l)=>$.post({url:"gruul-mall-freight/logistics/print/save",data:{deviceNo:r,printName:l}}),$e=(r,l,o)=>$.post({url:"gruul-mall-freight/logistics/print/update",data:{deviceNo:r,printName:l,id:o}}),Y=r=>$.get({url:"gruul-mall-freight/logistics/print/list",params:r}),De=r=>$.del({url:`gruul-mall-freight/logistics/print/del/${r}`}),ze=r=>$.get({url:"addon-freight/logistics/conditions/page",params:r}),Ie=r=>$.del({url:`addon-freight/logistics/conditions/delete/${r}`}),Pe=r=>$.post({url:"addon-freight/logistics/conditions/add",data:r}),W=r=>$.put({url:"addon-freight/logistics/conditions/update",data:r}),Ae={class:"my-header"},Fe=["id"],Ue={class:"dialog-footer"},Ge=e.defineComponent({__name:"freight-template-dialog",props:{isShow:{type:Boolean,default(){return!1}},id:{type:String,default:""}},emits:["update:isShow","close"],setup(r,{emit:l}){e.inject("parentLogisticsTemplateList");const o=r,d=l,a=U.useVModel(o,"isShow",d),i=e.ref({isEdit:!1,choiceConditionPostage:0,logisticsBaseModelDTO:[{id:F(10),firstAmount:0,firstQuantity:"0",logisticsId:0,regionJson:[],secondAmount:0,secondQuantity:"0",valuationModel:"PKGS"}],logisticsIncludePostageDTO:[{id:F(10),amountNum:0,logisticsId:0,pieceNum:0,postType:"PKGS",region:[],weight:0}],templateName:"",valuationModel:"PKGS"}),u=e.computed(()=>i.value.valuationModel==="PKGS");e.provide("parentFreightTemplateForm",i),b(),e.watch(()=>i.value.choiceConditionPostage,c=>{c&&!i.value.logisticsIncludePostageDTO[0]&&i.value.logisticsIncludePostageDTO.push({id:F(10),amountNum:0,logisticsId:0,pieceNum:0,postType:"PKGS",region:[],weight:0})},{immediate:!0,deep:!0});async function b(){if(o.id){const{code:c,data:s}=await Ee(o.id);if(c!==200)return h.ElMessage.error("获取运费模板失败");const{logisticsBaseModelVos:C,logisticsIncludePostageVos:_,templateName:t,valuationModel:n,id:E,choiceConditionPostage:V}=s;i.value.isEdit=!0,i.value.logisticsBaseModelDTO=C,i.value.logisticsIncludePostageDTO=_,i.value.templateName=t,i.value.valuationModel=n,i.value.choiceConditionPostage=V,i.value.id=E}}const g=()=>{h.ElMessageBox.confirm("关闭后内容将不保留?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{y(),a.value=!1}).catch(()=>{})},T=async()=>{if(!a.value)return;if(S()){if(D(),i.value.isEdit){N();return}const{code:s,data:C}=await we(i.value);if(s!==200)return h.ElMessage.error("添加运费模板失败");k("添加运费模板成功")}};async function N(){const{choiceConditionPostage:c}=i.value;!c&&(i.value.logisticsIncludePostageDTO=void 0),i.value.isEdit=void 0;const{code:s,data:C}=await Te(i.value);if(s!==200)return h.ElMessage.error("编辑失败");k("编辑运费模板成功")}function S(){const{templateName:c,logisticsBaseModelDTO:s,choiceConditionPostage:C,logisticsIncludePostageDTO:_,valuationModel:t}=i.value;if(!c.trim())return h.ElMessage.error("请输入运费模板名称"),!1;if(!s.every(m=>m.regionJson.length))return h.ElMessage.error("请选择地区"),!1;if(C&&!_.every(m=>m.region.length))return h.ElMessage.error("请选择包邮地区"),!1;if(s.every(m=>Number(m.firstQuantity)<=0||Number(m.secondQuantity)<=0))return t==="PKGS"?h.ElMessage.error("件数最少1"):h.ElMessage.error("重量最少0.1"),!1;if(s.every(m=>Number(m.firstAmount)<=0||Number(m.secondAmount)<=0))return h.ElMessage.error("首费或续费必须大于0"),!1;let V="";if(_&&C)for(let m=0;m<_.length;m++){const p=_[m];switch(p.postType){case"PKGS":V=Number(p.pieceNum)<=0?"包邮件数必须大于0":"",console.log(Number(p.pieceNum)<=0,V,p.pieceNum,p);break;case"PKGS_MONEY":{if(V=Number(p.pieceNum)<=0?"包邮件数必须大于0":"",V)break;V=Number(p.amountNum)<=0?"包邮金额必须大于0":""}break;case"WEIGHT":V=Number(p.weight)<=0?"包邮重量必须大于0,保留一位小数":"";break;case"WEIGHT_MONEY":{if(V=Number(p.weight)<=0?"包邮重量必须大于0,保留一位小数":"",V)break;V=Number(p.amountNum)<=0?"包邮金额必须大于0":""}break;case"MONEY":V=Number(p.amountNum)<=0?"包邮金额必须大于0":"";break}if(V)break}return V?(h.ElMessage.error(V),!1):!0}function D(){const{logisticsBaseModelDTO:c,logisticsIncludePostageDTO:s,choiceConditionPostage:C}=i.value;if(i.value.logisticsBaseModelDTO=c.map(_=>(_.id=void 0,_)),C){i.value.logisticsIncludePostageDTO=s.map(_=>(_.id=void 0,_));return}i.value.logisticsIncludePostageDTO=void 0}const k=c=>{h.ElMessage.success(`${c}`),y(),d("close"),a.value=!1},y=()=>{i.value={choiceConditionPostage:0,logisticsBaseModelDTO:[{id:F(10),firstAmount:0,firstQuantity:"0",logisticsId:0,regionJson:[],secondAmount:0,secondQuantity:"0",valuationModel:"PKGS"}],logisticsIncludePostageDTO:[{id:F(10),amountNum:0,logisticsId:0,pieceNum:0,postType:"PKGS",region:[],weight:0}],templateName:"",valuationModel:"PKGS"}};return(c,s)=>{const C=e.resolveComponent("el-button"),_=e.resolveComponent("el-dialog");return e.openBlock(),e.createBlock(_,{modelValue:e.unref(a),"onUpdate:modelValue":s[0]||(s[0]=t=>e.isRef(a)?a.value=t:null),"before-close":g,class:"dialog",width:"50%"},{header:e.withCtx(({titleId:t,titleClass:n})=>[e.createElementVNode("div",Ae,[e.createElementVNode("h4",{id:t,class:e.normalizeClass(n)},e.toDisplayString(`${o.id?"编辑":"添加"}`)+"运费模板",11,Fe)])]),footer:e.withCtx(()=>[e.createElementVNode("span",Ue,[e.createVNode(C,{onClick:g},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(C,{type:"primary",onClick:T},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(_e,{"is-edit":!!o.id,"is-p-k-g-s":u.value},null,8,["is-edit","is-p-k-g-s"])]),_:1},8,["modelValue"])}}}),jt="",Le=A(Ge,[["__scopeId","data-v-21093d04"]]),z=e.computed(()=>r=>{var l,o;return console.log((l=window==null?void 0:window.permissionList)==null?void 0:l.includes(r),"是否包含"),(o=window==null?void 0:window.permissionList)==null?void 0:o.includes(r)}),Oe={class:"Template_container__head"},Ke={class:"Template_container__head--left"},qe={class:"Template_container__head--right"},je={class:"Template_container__head--right_btn"},He=e.defineComponent({__name:"freight-template-table",props:{tableData:{type:Array,required:!0}},emits:["handleEditTemplate","handleDelTemplate"],setup(r,{emit:l}){const o=r,d=l,a=e.computed(()=>i=>{if(i&&i.length)return i.map(u=>u.lowerCode.length===u.length?u.upperName:`${u.upperName}(${u.lowerCode.length}/${u.length})`).join(",")});return(i,u)=>{const b=e.resolveComponent("el-button"),g=e.resolveComponent("el-table-column"),T=e.resolveComponent("el-table");return e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.tableData,N=>(e.openBlock(),e.createElementBlock("div",{key:N.id,class:"Template_container__tab"},[e.createElementVNode("div",Oe,[e.createElementVNode("div",Ke,e.toDisplayString(N.templateName),1),e.createElementVNode("div",qe,[e.createElementVNode("div",je,[e.unref(z)("freight:template:edit")?(e.openBlock(),e.createBlock(b,{key:0,link:"",size:"small",type:"primary",onClick:S=>d("handleEditTemplate",N.id)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.unref(z)("freight:template:delete")?(e.openBlock(),e.createBlock(b,{key:1,link:"",size:"small",type:"primary",onClick:S=>d("handleDelTemplate",N.id)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])])]),e.createVNode(T,{"cell-style":{fontSize:"14px",color:"#333",height:"80px"},data:N.logisticsBaseModelVos,"header-cell-style":{fontSize:"14px",color:"#515151",height:"80px",fontWeight:"normal"}},{default:e.withCtx(()=>[e.createVNode(g,{label:"可配送区域","min-width":"300%",prop:"da"},{default:e.withCtx(({row:S})=>[e.createElementVNode("div",null,e.toDisplayString(a.value(S.regionJson)),1)]),_:1}),e.createVNode(g,{label:N.valuationModel==="PKGS"?"首件数（件）":"首重量(kg)","min-width":"80%",prop:"date"},{default:e.withCtx(({row:S})=>[e.createElementVNode("div",null,e.toDisplayString(S.firstQuantity),1)]),_:2},1032,["label"]),e.createVNode(g,{label:"首费（元）","min-width":"70%",prop:"name"},{default:e.withCtx(({row:S})=>[e.createElementVNode("div",null,e.toDisplayString(S.firstAmount),1)]),_:1}),e.createVNode(g,{label:N.valuationModel==="PKGS"?"续件数（件）":"续重量(kg)","min-width":"70%",prop:"address"},{default:e.withCtx(({row:S})=>[e.createElementVNode("div",null,e.toDisplayString(S.secondQuantity),1)]),_:2},1032,["label"]),e.createVNode(g,{label:"续费（元）","min-width":"70%",prop:"addresss"},{default:e.withCtx(({row:S})=>[e.createElementVNode("div",null,e.toDisplayString(S.secondAmount),1)]),_:1})]),_:2},1032,["data"])]))),128)}}}),Ht="",Re=A(He,[["__scopeId","data-v-1aff1d8d"]]),Qe={class:"template-table"},Ye=e.defineComponent({__name:"freight-template",setup(r){const l=e.ref(!1),o=e.ref([]),d=e.reactive({size:20,current:1}),a=q.useRoute().query,i=q.useRouter(),u=e.ref(0),b=e.ref("");g();async function g(){const{code:y,data:c}=await ke(d.current,d.size);if(y!==200)return h.ElMessage.error("获取物流列表失败");o.value=c.records,u.value=c.total}const T=y=>{d.size=y,g()},N=y=>{d.current=y,g()},S=()=>{b.value="",l.value=!0},D=y=>{b.value=y,l.value=!0},k=y=>{h.ElMessageBox.confirm("确定要删除此模板吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:c,data:s,msg:C}=await Se(y);if(c!==200)return h.ElMessage.error(C||"删除失败");h.ElMessage.success("删除成功"),g()}).catch(()=>{})};return e.provide("parentLogisticsTemplateList",g),(y,c)=>{const s=e.resolveComponent("el-button"),C=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(C,{class:"btn"},{default:e.withCtx(()=>[e.unref(a).from?(e.openBlock(),e.createBlock(s,{key:0,round:"",onClick:c[0]||(c[0]=_=>e.unref(i).back())},{default:e.withCtx(()=>[e.createTextVNode("返回发布商品")]),_:1})):e.createCommentVNode("",!0),e.unref(z)("freight:template:add")?(e.openBlock(),e.createBlock(s,{key:1,round:"",type:"primary",onClick:S},{default:e.withCtx(()=>[e.createTextVNode("新增运费模板")]),_:1})):e.createCommentVNode("",!0)]),_:1}),e.createElementVNode("div",Qe,[e.createVNode(Re,{"table-data":o.value,onHandleEditTemplate:D,onHandleDelTemplate:k},null,8,["table-data"])]),e.createVNode(J,{modelValue:d,"onUpdate:modelValue":c[1]||(c[1]=_=>d=_),total:u.value,onReload:g,onHandleSizeChange:T,onHandleCurrentChange:N},null,8,["modelValue","total"]),l.value?(e.openBlock(),e.createBlock(Le,{key:0,id:b.value,"is-show":l.value,"onUpdate:isShow":c[2]||(c[2]=_=>l.value=_),onClose:g},null,8,["id","is-show"])):e.createCommentVNode("",!0)],64)}}}),Rt="",We=Object.freeze(Object.defineProperty({__proto__:null,default:A(Ye,[["__scopeId","data-v-e4d196dc"]])},Symbol.toStringTag,{value:"Module"})),Je={class:"freight-add_container"},Xe={class:"freight-add_container__table_border",style:{width:"100%"}},Ze={class:"right_btn"},ve={class:"my-header"},et=["id"],tt={class:"dialog-footer"},ot=e.defineComponent({__name:"free-shipping-condition",setup(r){let l=e.ref({current:1,records:[],size:10,total:0});const o=e.ref(!1);let d=e.ref({amount:0,status:0});const a=e.ref(""),i=e.ref(),u=e.reactive({amount:[{required:!0,message:"请输入联系人",trigger:"blur"}]});b();async function b(){const{current:s,size:C,total:_}=l.value,{data:t,code:n}=await ze({current:s,size:C,total:_});n===200?(t.records.forEach(E=>{E.amount=E.amount/1e4}),l.value=t):h.ElMessage({message:"请刷新重试...",type:"warning"})}const g=s=>{l.value.size=s,b()},T=s=>{l.value.current=s,b()},N=s=>{a.value=s.id;for(const C in d.value)d.value[C]=s[C];o.value=!0},S=s=>{h.ElMessageBox.confirm("确定删除此项?删除后不会保留已删除地址！",{title:"提示",confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",callback:async C=>{C!=="cancel"&&(await Ie(s),await b())}})},D=async()=>{await i.value.validate(),await k()},k=async()=>{if(a.value){const{code:s}=await W({...d.value,id:a.value,amount:d.value.amount*1e4});s===200&&(a.value=null,h.ElMessage.success("更新成功"),await b(),o.value=!1)}else{const{code:s}=await Pe({...d.value,amount:d.value.amount*1e4});s===200&&(h.ElMessage.success("增加成功"),await b(),o.value=!1)}},y=()=>{o.value=!1,a.value="",i.value.resetFields(),d.value={amount:0,status:0}},c=async s=>{console.log(s,"row");const{code:C}=await W({...s,amount:s.amount*1e4});C===200&&(a.value=null,h.ElMessage.success("更新成功"),await b())};return(s,C)=>{const _=e.resolveComponent("el-button"),t=e.resolveComponent("el-table-column"),n=e.resolveComponent("el-switch"),E=e.resolveComponent("el-table"),V=e.resolveComponent("el-input-number"),m=e.resolveComponent("el-form-item"),p=e.resolveComponent("el-form"),B=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",Je,[e.unref(z)("freight:condition:add")?(e.openBlock(),e.createBlock(_,{key:0,class:"freight-add_container__btn",round:"",type:"primary",onClick:C[0]||(C[0]=M=>o.value=!0)},{default:e.withCtx(()=>[e.createTextVNode("添加包邮条件 ")]),_:1})):e.createCommentVNode("",!0),e.createElementVNode("div",Xe,[e.createVNode(E,{"cell-style":{height:"80px"},data:e.unref(l).records,"header-cell-style":{fontSize:"12px",fontWeight:"bold",color:"#515151",background:"#f6f8fa"},class:"freight-add_container__table",stripe:"",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(t,{label:"包邮金额",prop:"amount"}),e.createVNode(t,{label:"启用状态",prop:"status"},{default:e.withCtx(M=>[e.createVNode(n,{modelValue:M.row.status,"onUpdate:modelValue":I=>M.row.status=I,disabled:!e.unref(z)("freight:condition:status:change"),onChange:I=>c(M.row)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),_:1}),e.createVNode(t,{label:"创建时间",prop:"createTime"}),e.createVNode(t,{label:"更新时间",prop:"updateTime"}),e.createVNode(t,{align:"center",fixed:"right",label:"操作","min-width":"150"},{default:e.withCtx(({row:M})=>[e.createElementVNode("div",Ze,[e.unref(z)("freight:condition:edit")?(e.openBlock(),e.createBlock(_,{key:0,style:{"margin-right":"20px"},link:"",size:"small",type:"primary",onClick:I=>N(M)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.unref(z)("freight:condition:delete")?(e.openBlock(),e.createBlock(_,{key:1,link:"",size:"small",type:"danger",onClick:I=>S(M.id)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["data"])]),e.createVNode(L,{"page-num":e.unref(l).current,"page-size":e.unref(l).size,total:e.unref(l).total,onHandleSizeChange:g,onHandleCurrentChange:T},null,8,["page-num","page-size","total"]),e.createVNode(B,{modelValue:o.value,"onUpdate:modelValue":C[3]||(C[3]=M=>o.value=M),width:"650px",onClose:y},{header:e.withCtx(({titleId:M,titleClass:I})=>[e.createElementVNode("div",ve,[e.createElementVNode("h4",{id:M,class:e.normalizeClass(I)},e.toDisplayString(a.value?"编辑包邮条件":"添加包邮条件"),11,et)])]),footer:e.withCtx(()=>[e.createElementVNode("span",tt,[e.createVNode(_,{onClick:C[2]||(C[2]=M=>o.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(_,{type:"primary",onClick:D},{default:e.withCtx(()=>[e.createTextVNode("提交")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(p,{ref_key:"FormRef",ref:i,model:e.unref(d),rules:u,"label-position":"right","label-width":"90px",style:{"max-width":"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{label:"包邮金额",prop:"amount"},{default:e.withCtx(()=>[e.createVNode(V,{modelValue:e.unref(d).amount,"onUpdate:modelValue":C[1]||(C[1]=M=>e.unref(d).amount=M),style:{"min-width":"200px"},min:1,placeholder:"请输入包邮金额"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Yt="",at=Object.freeze(Object.defineProperty({__proto__:null,default:A(ot,[["__scopeId","data-v-41195ec7"]])},Symbol.toStringTag,{value:"Module"})),nt={class:"freight-add_container"},lt={class:"freight-add_container__table_border",style:{width:"100%"}},rt={class:"freight-add_container__table--loctionbtn"},it={class:"right_btn"},st={class:"my-header"},dt=["id"],ct={class:"dialog-footer"},pt=e.defineComponent({__name:"freight-add",setup(r){let l=e.ref({current:1,records:[],size:10,total:0});const o=e.ref(!1),d=X.regionData;let a=e.ref({address:"",contactName:"",contactPhone:"",zipCode:"",Provinces:[]});const i=e.ref(""),u=e.ref(),b=e.reactive({contactName:[{required:!0,message:"请输入联系人",trigger:"blur"},{max:10,message:"最多输入10个字符",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3|5|6|7|8|9]\d{9}$/,message:"请输入正确的号码格式",trigger:"blur"}],Provinces:[{type:"array",required:!0,message:"请选择类别",trigger:"change"}],address:[{required:!0,message:"请填写详细地址",trigger:"blur"}],zipCode:[{required:!0,message:"请输入邮政编号",trigger:"blur"},{min:6,max:6,message:"请输入6位邮政编号",trigger:"blur"}]});g();async function g(){const{current:t,size:n,total:E}=l.value,{data:V,code:m}=await ue({current:t,size:n,total:E});m===200?l.value=V:h.ElMessage({message:"请刷新重试...",type:"warning"})}const T=t=>{l.value.size=t,g()},N=t=>{l.value.current=t,g()},S=t=>{i.value=t.id;for(const n in a.value)a.value[n]=t[n];a.value.Provinces=[t.provinceCode,t.cityCode,t.regionCode],o.value=!0},D=t=>{h.ElMessageBox.confirm("确定删除此项?删除后不会保留已删除地址！",{title:"提示",confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",callback:async n=>{n!=="cancel"&&(await s(t),await g())}})},k=async t=>{const{code:n}=await R(t,"DEF_SEND");n===200?(h.ElMessage.success("设置成功"),await g()):h.ElMessage.error({message:"设置失败"})},y=async t=>{const{code:n}=await R(t,"DEF_RECEIVE");n===200?(await g(),h.ElMessage.success("设置成功")):h.ElMessage.error({message:"设置失败"})},c=async()=>{await u.value.validate(),await C()},s=async t=>{const{code:n,msg:E}=await he(t);n===200?h.ElMessage({type:"success",message:"删除成功"}):h.ElMessage.error(E||"删除失败")},C=async()=>{const t={...a.value};if(t.provinceCode=a.value.Provinces[0],t.cityCode=a.value.Provinces[1],t.regionCode=a.value.Provinces[2],Reflect.deleteProperty(t,"Provinces"),i.value){const{code:n}=await H({...t,id:i.value});n===200&&(h.ElMessage.success("更新成功"),await g(),o.value=!1)}else{const{code:n}=await H({...t});n===200&&(h.ElMessage.success("增加成功"),await g(),o.value=!1)}},_=()=>{o.value=!1,i.value="",u.value.resetFields(),a.value={Provinces:[],address:"",contactName:"",contactPhone:"",zipCode:""}};return(t,n)=>{const E=e.resolveComponent("el-button"),V=e.resolveComponent("el-table-column"),m=e.resolveComponent("el-table"),p=e.resolveComponent("el-input"),B=e.resolveComponent("el-form-item"),M=e.resolveComponent("el-cascader"),I=e.resolveComponent("el-form"),w=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",nt,[e.unref(z)("freight:address:add")?(e.openBlock(),e.createBlock(E,{key:0,class:"freight-add_container__btn",round:"",type:"primary",onClick:n[0]||(n[0]=f=>o.value=!0)},{default:e.withCtx(()=>[e.createTextVNode("添加地址 ")]),_:1})):e.createCommentVNode("",!0),e.createElementVNode("div",lt,[e.createVNode(m,{"cell-style":{height:"80px"},data:e.unref(l).records,"header-cell-style":{fontSize:"12px",fontWeight:"bold",color:"#515151",background:"#f6f8fa"},class:"freight-add_container__table",stripe:"",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(V,{label:"联系人",prop:"contactName"}),e.createVNode(V,{label:"地址",prop:"address"}),e.createVNode(V,{label:"邮政编码",prop:"zipCode"}),e.createVNode(V,{label:"联系电话",prop:"contactPhone"}),e.createVNode(V,{align:"center",label:"默认地址",prop:"address"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",rt,[f.defSend==="YES"?(e.openBlock(),e.createBlock(E,{key:0,type:f.defSend==="YES"?"primary":"",size:"small",style:{cursor:"default !important"}},{default:e.withCtx(()=>[e.createTextVNode(" 发货地址 ")]),_:2},1032,["type"])):e.createCommentVNode("",!0),f.defReceive==="YES"?(e.openBlock(),e.createBlock(E,{key:1,type:f.defReceive==="YES"?"primary":"",size:"small",style:{margin:"0",cursor:"default !important"}},{default:e.withCtx(()=>[e.createTextVNode("收货地址 ")]),_:2},1032,["type"])):e.createCommentVNode("",!0)])]),_:1}),e.createVNode(V,{align:"center",fixed:"right",label:"操作","min-width":"150"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",it,[f.defSend!=="YES"&&e.unref(z)("freight:address:default:shipping")?(e.openBlock(),e.createBlock(E,{key:0,link:"",size:"small",type:"primary",onClick:P=>k(f.id)},{default:e.withCtx(()=>[e.createTextVNode("默认发货地址 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),f.defReceive!=="YES"&&e.unref(z)("freight:address:default:receipt")?(e.openBlock(),e.createBlock(E,{key:1,link:"",size:"small",type:"primary",onClick:P=>y(f.id)},{default:e.withCtx(()=>[e.createTextVNode("默认收货地址 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.unref(z)("freight:address:edit")?(e.openBlock(),e.createBlock(E,{key:2,link:"",size:"small",type:"primary",onClick:P=>S(f)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.unref(z)("freight:address:delete")?(e.openBlock(),e.createBlock(E,{key:3,link:"",size:"small",type:"danger",onClick:P=>D(f.id)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["data"])]),e.createVNode(L,{"page-num":e.unref(l).current,"page-size":e.unref(l).size,total:e.unref(l).total,onHandleSizeChange:T,onHandleCurrentChange:N},null,8,["page-num","page-size","total"]),e.createVNode(w,{modelValue:o.value,"onUpdate:modelValue":n[7]||(n[7]=f=>o.value=f),width:"650px",onClose:_},{header:e.withCtx(({titleId:f,titleClass:P})=>[e.createElementVNode("div",st,[e.createElementVNode("h4",{id:f,class:e.normalizeClass(P)},e.toDisplayString(i.value?"编辑地址":"添加地址"),11,dt)])]),footer:e.withCtx(()=>[e.createElementVNode("span",ct,[e.createVNode(E,{onClick:n[6]||(n[6]=f=>o.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(E,{type:"primary",onClick:c},{default:e.withCtx(()=>[e.createTextVNode("提交")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(I,{ref_key:"FormRef",ref:u,model:e.unref(a),rules:b,"label-position":"right","label-width":"90px",style:{"max-width":"100%"}},{default:e.withCtx(()=>[e.createVNode(B,{label:"联系人",prop:"contactName"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(a).contactName,"onUpdate:modelValue":n[1]||(n[1]=f=>e.unref(a).contactName=f),maxlength:"10",placeholder:"请输入联系人"},null,8,["modelValue"])]),_:1}),e.createVNode(B,{label:"联系电话",prop:"contactPhone"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(a).contactPhone,"onUpdate:modelValue":n[2]||(n[2]=f=>e.unref(a).contactPhone=f),maxlength:"11",onkeyup:"value=value.replace(/[^\\d]/g,'')",placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),e.createVNode(B,{label:"邮政编号",prop:"zipCode"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(a).zipCode,"onUpdate:modelValue":n[3]||(n[3]=f=>e.unref(a).zipCode=f),maxlength:"6",onkeyup:"value=value.replace(/[^\\d]/g,'')",placeholder:"请输入邮政编号"},null,8,["modelValue"])]),_:1}),e.createVNode(B,{label:"地区选择",prop:"Provinces"},{default:e.withCtx(()=>[e.createVNode(M,{modelValue:e.unref(a).Provinces,"onUpdate:modelValue":n[4]||(n[4]=f=>e.unref(a).Provinces=f),options:e.unref(d),style:{width:"100%"},filterable:"",placeholder:"请选择省/市/区"},null,8,["modelValue","options"])]),_:1}),e.createVNode(B,{label:"详细地址",prop:"address"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(a).address,"onUpdate:modelValue":n[5]||(n[5]=f=>e.unref(a).address=f),rows:4,maxlength:"50",placeholder:"",resize:"none",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Jt="",mt=Object.freeze(Object.defineProperty({__proto__:null,default:A(pt,[["__scopeId","data-v-940ba9fd"]])},Symbol.toStringTag,{value:"Module"})),gt={class:"distributionServe_container"},ft={style:{"margin-left":"10px"}},_t={style:{"margin-left":"10px"}},ut={style:{"margin-left":"10px"}},ht={style:{"margin-left":"10px"}},Ct={style:{"margin-left":"10px"}},Vt={class:"right_btn"},yt={class:"my-header"},bt=["id"],Nt={class:"dialog-footer"},xt=e.defineComponent({__name:"freight-serve",setup(r){const l=e.ref(!1),o=e.reactive({freightId:"",addressId:"",customerCode:"",customerPassword:"",networkName:"",networkCode:"",logisticsPrintId:""}),d=e.reactive({pageSize:20,pageNum:1,total:0}),a=e.ref(),i=e.reactive({freightId:[{required:!0,message:"请选择快递公司",trigger:"change"}],logisticsPrintId:[{required:!0,message:"请选择打印机",trigger:"change"}],customerCode:[{required:!0,message:"未填客户号",trigger:"blur"}],customerPassword:[{required:!0,message:"请输入密码",trigger:"blur"}],networkName:[{required:!0,message:"请输入网点名称",trigger:"blur"}]}),u=e.ref([]),b=e.ref([]),g=e.ref([]),T=e.ref(""),N=e.reactive({pageSize:20,pageNum:1,total:0}),S=e.ref([]);t(),_(),n(),E();const D=V=>{d.pageSize=V,n()},k=V=>{d.pageNum=V,n()},y=V=>{T.value=V.id;for(const m in o)V[m]&&(o[m]=V[m]);l.value=!0},c=async V=>{try{await h.ElMessageBox.confirm("确定删除此项?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const{code:m}=await xe(V.id);if(m!==200)return h.ElMessage.error("删除失败");n(),h.ElMessage.success("删除成功")}catch{}},s=async()=>{if(T.value){const p={...o,id:T.value},{code:B,msg:M}=await Ne(p);if(B!==200)return h.ElMessage.error(M||"更新失败");h.ElMessage.success("更新成功"),l.value=!1;return}await a.value.validate();const{code:V,msg:m}=await ye(o);if(V!==200)return h.ElMessage.error(m||"添加失败");h.ElMessage.success("添加成功"),l.value=!1},C=()=>{T.value="",a.value.resetFields();for(const V in o)o[V]="";n()};async function _(){const{code:V,data:m,success:p}=await Ve();if(V!==200&&p)return h.ElMessage.error("获取收货地址失败");b.value=m.records}async function t(){const{code:V,data:m,success:p}=await Ce();V===200&&p?u.value=m.records:h.ElMessage.error("获取物流公司失败")}async function n(){const{code:V,data:m,success:p}=await be(d.pageNum,d.pageSize);if(V===200&&p){g.value=m.records,d.pageSize=m.size,d.pageNum=m.current,d.total=m.total;return}h.ElMessage.error("获取物流服务列表失败")}async function E(){const{code:V,data:m}=await Y({size:N.pageSize,current:N.pageNum});V!==200&&h.ElMessage.error("打印机列表获取失败");const{records:p,current:B,size:M,total:I,searchCount:w}=m;S.value=p,N.pageNum=B,N.pageSize=M,N.total=I}return(V,m)=>{const p=e.resolveComponent("el-button"),B=e.resolveComponent("el-table-column"),M=e.resolveComponent("el-table"),I=e.resolveComponent("el-option"),w=e.resolveComponent("el-select"),f=e.resolveComponent("el-form-item"),P=e.resolveComponent("el-input"),Ut=e.resolveComponent("el-form"),Gt=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",gt,[e.unref(z)("freight:server:add")?(e.openBlock(),e.createBlock(p,{key:0,class:"distributionServe_container__btn",round:"",type:"primary",onClick:m[0]||(m[0]=x=>l.value=!0)},{default:e.withCtx(()=>[e.createTextVNode("添加服务 ")]),_:1})):e.createCommentVNode("",!0),e.createVNode(M,{data:g.value,"header-cell-style":{fontSize:"12px",fontWeight:"bold",color:"#515151",background:"#f6f8fa"},"empty-text":"暂无服务",stripe:"",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(B,{label:"快递公司",width:"180"},{default:e.withCtx(({row:x})=>[e.createElementVNode("span",ft,e.toDisplayString(x.logisticsCompanyName),1)]),_:1}),e.createVNode(B,{label:"网点名称",width:"180"},{default:e.withCtx(({row:x})=>[e.createElementVNode("span",_t,e.toDisplayString(x.networkName),1)]),_:1}),e.createVNode(B,{label:"网点编码"},{default:e.withCtx(({row:x})=>[e.createElementVNode("span",ut,e.toDisplayString(x.networkCode),1)]),_:1}),e.createVNode(B,{label:"客户号"},{default:e.withCtx(({row:x})=>[e.createElementVNode("span",ht,e.toDisplayString(x.customerCode),1)]),_:1}),e.createVNode(B,{label:"打印机名称"},{default:e.withCtx(({row:x})=>[e.createElementVNode("span",Ct,e.toDisplayString(x.printName),1)]),_:1}),e.createVNode(B,{fixed:"right",label:"操作",width:"120"},{default:e.withCtx(({row:x})=>[e.createElementVNode("div",Vt,[e.unref(z)("freight:server:edit")?(e.openBlock(),e.createBlock(p,{key:0,link:"",type:"primary",onClick:O=>y(x)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.unref(z)("freight:server:delete")?(e.openBlock(),e.createBlock(p,{key:1,link:"",type:"primary",onClick:O=>c(x)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["data"]),e.createVNode(L,{"page-num":d.pageNum,"page-size":d.pageSize,total:d.total,onHandleSizeChange:D,onHandleCurrentChange:k},null,8,["page-num","page-size","total"]),e.createVNode(Gt,{modelValue:l.value,"onUpdate:modelValue":m[8]||(m[8]=x=>l.value=x),width:"50%",onClose:C},{header:e.withCtx(({titleId:x,titleClass:O})=>[e.createElementVNode("div",yt,[e.createElementVNode("h4",{id:x,class:e.normalizeClass(O)},e.toDisplayString(T.value?"编辑物流服务":"新增物流服务"),11,bt)])]),footer:e.withCtx(()=>[e.createElementVNode("span",Nt,[e.createVNode(p,{onClick:m[7]||(m[7]=x=>l.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(p,{type:"primary",onClick:s},{default:e.withCtx(()=>[e.createTextVNode("提交")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(Ut,{ref_key:"FormRef",ref:a,model:o,rules:i,"label-position":"right","label-width":"130px",style:{"max-width":"100%"}},{default:e.withCtx(()=>[e.createVNode(f,{label:"选择快递公司",prop:"freightId"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:o.freightId,"onUpdate:modelValue":m[1]||(m[1]=x=>o.freightId=x),placeholder:"请选择快递公司",style:{width:"100%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(u.value,x=>(e.openBlock(),e.createBlock(I,{key:x.id,label:x.logisticsCompanyName,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(f,{label:"选择打印机",prop:"logisticsPrintId"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:o.logisticsPrintId,"onUpdate:modelValue":m[2]||(m[2]=x=>o.logisticsPrintId=x),placeholder:"请选择打印机",style:{width:"100%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(S.value,x=>(e.openBlock(),e.createBlock(I,{key:x.id,label:x.printName,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(f,{label:"客户号",prop:"customerCode"},{default:e.withCtx(()=>[e.createVNode(P,{modelValue:o.customerCode,"onUpdate:modelValue":m[3]||(m[3]=x=>o.customerCode=x),modelModifiers:{trim:!0},maxlength:"20"},null,8,["modelValue"])]),_:1}),e.createVNode(f,{label:"密码",prop:"customerPassword"},{default:e.withCtx(()=>[e.createVNode(P,{modelValue:o.customerPassword,"onUpdate:modelValue":m[4]||(m[4]=x=>o.customerPassword=x),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1}),e.createVNode(f,{label:"网点名称",prop:"networkName"},{default:e.withCtx(()=>[e.createVNode(P,{modelValue:o.networkName,"onUpdate:modelValue":m[5]||(m[5]=x=>o.networkName=x),modelModifiers:{trim:!0},maxlength:"20"},null,8,["modelValue"])]),_:1}),e.createVNode(f,{label:"网点编号",prop:"networkCode"},{default:e.withCtx(()=>[e.createVNode(P,{modelValue:o.networkCode,"onUpdate:modelValue":m[6]||(m[6]=x=>o.networkCode=x),modelModifiers:{trim:!0},maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Zt="",kt=Object.freeze(Object.defineProperty({__proto__:null,default:A(xt,[["__scopeId","data-v-f889e750"]])},Symbol.toStringTag,{value:"Module"})),wt={class:"distributionServe_container"},St={style:{"margin-left":"10px"}},Et={style:{"margin-left":"10px"}},Tt={class:"right_btn"},Bt={class:"my-header"},Mt=["id"],$t={class:"dialog-footer"},Dt=e.defineComponent({__name:"freight-print",setup(r){const l=e.ref(!1),o=e.ref([]),d=e.ref(),a=e.ref(),i=e.reactive({size:20,current:1,total:0}),u=e.reactive({printName:"",printCode:""}),b=e.reactive({printCode:[{required:!0,message:"请输入AppID",trigger:"blur"}],printName:[{required:!0,message:"请输入打印机名称",trigger:"blur"}]});g();async function g(){const{code:c,data:s}=await Y(i);c!==200&&h.ElMessage.error("列表获取失败");const{records:C,current:_,size:t,total:n,searchCount:E}=s;o.value=C,i.current=_,i.size=t,i.total=n}const T=c=>{i.size=c,g()},N=c=>{i.current=c,g()},S=async c=>{await h.ElMessageBox.confirm("确定删除此项?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const{code:s}=await De(c.id);if(s!==200)return h.ElMessage.error("删除失败");h.ElMessage.success("删除成功"),g()},D=()=>{d.value="",u.printCode="",u.printName="",a.value.resetFields(),g()},k=async()=>{if(!d.value){await a.value.validate();const{printName:_,printCode:t}=u,{code:n}=await Me(t,_);if(n!==200)return h.ElMessage.error("新增失败");h.ElMessage.success("新增成功"),l.value=!1;return}const{printCode:c,printName:s}=u,{code:C}=await $e(c,s,d.value);if(C!==200)return h.ElMessage.error("编辑失败");h.ElMessage.success("编辑成功"),l.value=!1},y=async c=>{d.value=c.id,u.printCode=c.deviceNo,u.printName=c.printName,l.value=!0};return(c,s)=>{const C=e.resolveComponent("el-button"),_=e.resolveComponent("el-table-column"),t=e.resolveComponent("el-table"),n=e.resolveComponent("el-input"),E=e.resolveComponent("el-form-item"),V=e.resolveComponent("el-form"),m=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",wt,[e.unref(z)("freight:printer:add")?(e.openBlock(),e.createBlock(C,{key:0,class:"distributionServe_container__btn",round:"",type:"primary",onClick:s[0]||(s[0]=p=>l.value=!0)},{default:e.withCtx(()=>[e.createTextVNode("添加打印机 ")]),_:1})):e.createCommentVNode("",!0),e.createVNode(t,{data:o.value,"header-cell-style":{fontSize:"12px",fontWeight:"bold",color:"#515151",background:"#f6f8fa"},"empty-text":"暂无打印机",stripe:"",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(_,{label:"打印名称"},{default:e.withCtx(({row:p})=>[e.createElementVNode("span",St,e.toDisplayString(p.printName),1)]),_:1}),e.createVNode(_,{label:"打印机身号"},{default:e.withCtx(({row:p})=>[e.createElementVNode("span",Et,e.toDisplayString(p.deviceNo),1)]),_:1}),e.createVNode(_,{fixed:"right",label:"操作",width:"120"},{default:e.withCtx(({row:p})=>[e.createElementVNode("div",Tt,[e.unref(z)("freight:printer:edit")?(e.openBlock(),e.createBlock(C,{key:0,link:"",type:"primary",onClick:B=>y(p)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.unref(z)("freight:printer:delete")?(e.openBlock(),e.createBlock(C,{key:1,link:"",type:"primary",onClick:B=>S(p)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["data"]),e.createVNode(L,{"page-num":i.current,"page-size":i.size,total:i.total,onHandleSizeChange:T,onHandleCurrentChange:N},null,8,["page-num","page-size","total"]),e.createVNode(m,{modelValue:l.value,"onUpdate:modelValue":s[4]||(s[4]=p=>l.value=p),width:"50%",onClose:D},{header:e.withCtx(({titleId:p,titleClass:B})=>[e.createElementVNode("div",Bt,[e.createElementVNode("h4",{id:p,class:e.normalizeClass(B)},e.toDisplayString(d.value?"编辑打印机":"新增打印机"),11,Mt)])]),footer:e.withCtx(()=>[e.createElementVNode("span",$t,[e.createVNode(C,{onClick:s[3]||(s[3]=p=>l.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(C,{type:"primary",onClick:k},{default:e.withCtx(()=>[e.createTextVNode("提交")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(V,{ref_key:"FormRef",ref:a,model:u,rules:b,"label-position":"right","label-width":"130px",style:{"max-width":"100%"}},{default:e.withCtx(()=>[e.createVNode(E,{label:"打印机名称",prop:"printName"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:u.printName,"onUpdate:modelValue":s[1]||(s[1]=p=>u.printName=p),modelModifiers:{trim:!0},maxlength:"20"},null,8,["modelValue"])]),_:1}),e.createVNode(E,{label:"打印机身号",prop:"printCode"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:u.printCode,"onUpdate:modelValue":s[2]||(s[2]=p=>u.printCode=p),modelModifiers:{trim:!0},maxlength:"40"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),eo="",zt=Object.freeze(Object.defineProperty({__proto__:null,default:A(Dt,[["__scopeId","data-v-35a06e50"]])},Symbol.toStringTag,{value:"Module"})),It={class:"line"},Pt=e.defineComponent({__name:"Line",props:{color:{type:String,default:"#08CC00"},name:{type:String,default:"下单设置"}},setup(r){const l=r;return(o,d)=>(e.openBlock(),e.createElementBlock("div",It,[e.createElementVNode("div",{style:e.normalizeStyle({background:l.color}),class:"line__column"},null,4),e.createElementVNode("div",null,e.toDisplayString(l.name),1)]))}}),oo="",At=A(Pt,[["__scopeId","data-v-9d14fba0"]]),Ft=Object.freeze(Object.defineProperty({__proto__:null,default:e.defineComponent({__name:"freight-set",setup(r){const l=e.ref(),o=e.ref(),d=e.reactive({key:[{required:!0,message:"请输入快递100 key",trigger:"blur"}],customer:[{required:!0,message:"请输入客户号",trigger:"blur"}],secret:[{required:!0,message:"请输入快递100 secret",trigger:"blur"}]}),a=e.reactive({key:"",customer:"",secret:""});i();async function i(){const{code:b,data:g}=await Be();b===200&&g&&g.id&&(o.value=g.id,a.customer=g.customer,a.key=g.key,a.secret=g.secret)}const u=async()=>{try{await l.value.validate();const{key:b,customer:g,secret:T}=a,{code:N}=o.value?await Q(g,b,T,o.value):await Q(g,b,T);if(N!==200)return h.ElMessage.error(o.value?"快递设置更新失败":"快递设置新增失败");h.ElMessage.success(o.value?"快递设置更新成功":"快递设置新增成功")}catch(b){const g=[];for(const T in b)g.push(b[T][0].message);h.ElMessage.error({message:g.join(" "),onClose:()=>{l.value.resetFields()}})}};return(b,g)=>{const T=e.resolveComponent("el-input"),N=e.resolveComponent("el-form-item"),S=e.resolveComponent("el-form"),D=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(At,{color:"#08CC00",name:"快递100"}),e.createVNode(S,{ref_key:"printSetFormRef",ref:l,model:a,rules:d,"show-message":!1,"label-position":"left","label-width":"120px",style:{padding:"1% 0 3% 3%"}},{default:e.withCtx(()=>[e.createVNode(N,{label:"快递100 key","label-width":"120px",prop:"key"},{default:e.withCtx(()=>[e.createVNode(T,{modelValue:a.key,"onUpdate:modelValue":g[0]||(g[0]=k=>a.key=k),modelModifiers:{trim:!0},maxlength:"50",style:{width:"90%"}},null,8,["modelValue"])]),_:1}),e.createVNode(N,{label:"快递100 客户号","label-width":"120px",prop:"customer"},{default:e.withCtx(()=>[e.createVNode(T,{modelValue:a.customer,"onUpdate:modelValue":g[1]||(g[1]=k=>a.customer=k),modelModifiers:{trim:!0},maxlength:"50",style:{width:"90%"}},null,8,["modelValue"])]),_:1}),e.createVNode(N,{label:"快递100 secret","label-width":"120px",prop:"secret"},{default:e.withCtx(()=>[e.createVNode(T,{modelValue:a.secret,"onUpdate:modelValue":g[2]||(g[2]=k=>a.secret=k),modelModifiers:{trim:!0},maxlength:"50",style:{width:"90%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),e.unref(z)("freight:logistics:setting")?(e.openBlock(),e.createBlock(D,{key:0,style:{"margin-left":"4%"},type:"primary",onClick:u},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1})):e.createCommentVNode("",!0)],64)}}})},Symbol.toStringTag,{value:"Module"}));return Z});
