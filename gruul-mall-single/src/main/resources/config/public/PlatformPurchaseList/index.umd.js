(function(e,w){typeof exports=="object"&&typeof module<"u"?module.exports=w(require("vue"),require("vue-router"),require("@/components/MCard.vue"),require("lodash"),require("decimal.js"),require("element-plus"),require("vue-clipboard3"),require("element-china-area-data"),require("@/components/q-address"),require("@/apis/http"),require("@/utils/date"),require("@/composables/useConvert"),require("@element-plus/icons-vue"),require("@/components/pageManage/PageManage.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/MCard.vue","lodash","decimal.js","element-plus","vue-clipboard3","element-china-area-data","@/components/q-address","@/apis/http","@/utils/date","@/composables/useConvert","@element-plus/icons-vue","@/components/pageManage/PageManage.vue"],w):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformPurchaseList=w(e.PlatformPurchaseListContext.Vue,e.PlatformPurchaseListContext.VueRouter,e.PlatformPurchaseListContext.MCard,e.PlatformPurchaseListContext.Lodash,e.PlatformPurchaseListContext.Decimal,e.PlatformPurchaseListContext.ElementPlus,e.PlatformPurchaseListContext.VueClipboard3,e.PlatformPurchaseListContext.ElementChinaAreaData,e.PlatformPurchaseListContext.QAddressIndex,e.PlatformPurchaseListContext.Request,e.PlatformPurchaseListContext.DateUtil,e.PlatformPurchaseListContext.UseConvert,e.PlatformPurchaseListContext.ElementPlusIconsVue,e.PlatformPurchaseListContext.PageManage))})(this,function(e,w,K,Q,I,A,X,J,Z,v,ee,q,te,ae){"use strict";var H=document.createElement("style");H.textContent=`.shop__search-visible[data-v-0732f6b3]{padding:20px 20px 0}.m__table{width:100%;table-layout:fixed;border-collapse:separate;font-size:12px}.m__table .hide{display:none}.m__table--container{background:#eef1f6;padding:11px 8px}.m__table--container.single{background:none}.m__table--container.single .m__table--head th:first-child{border-radius:10px 0 0}.m__table--container.single .m__table--head th:last-child{border-radius:0 10px 0 0}.m__table--container.single .m__table--head:after{display:none}.m__table--shrink{flex:1;height:100%}.m__table--center{display:flex;align-items:center}.m__table .close{background:#f5f5f5!important}.m__table .hover--class:hover .body--header{border-color:#bebebe!important;position:relative}.m__table .hover--class:hover .body--content td,.m__table .hover--class:hover .body--content td:first-child{border-color:#bebebe!important}.m__table .hover--class:hover .body--content td:last-child{border-color:#bebebe!important}.m__table .ordinary--class:hover .body--header,.m__table .ordinary--class:hover .body--content td{border-color:#bcdfff}.m__table .ordinary--class:hover .body--content td:last-child{border-color:#bcdfff}.m__table .need--border .body--content td{border-right:1px solid #bcdfff}.m__table .need--border .body--content td:last-child{border-color:#bcdfff}.m__table--empty .empty__td{width:960px;height:80px;background-color:#fff;margin-left:-15px;font-size:14px;color:#b3b3b3}.m__table--head th{background:#fff;padding:12px 10px;text-align:center;color:#515a6e;border-top:1px solid #d8eaf9;border-bottom:1px solid #d8eaf9;vertical-align:middle;font-size:14px;font-weight:400;color:#586884}.m__table--head th:first-child{border-left:1px solid #d8eaf9}.m__table--head th:last-child{border-right:1px solid #d8eaf9}.m__table--head:after{content:"-";display:block;line-height:14px;color:transparent}.m__table--head.padding:after{content:"-";display:block;color:transparent}.m__table--body .body--header{display:flex;justify-content:flex-start;align-items:center;padding:0 10px;border:1px solid #d8eaf9;font-size:13px;border-radius:10px 10px 0 0;height:55px;vertical-align:middle;background:#fff}.m__table--body.default .body--content .m__table--item:first-child{border-radius:0}.m__table--body.default .body--content .m__table--item:last-child{border-radius:0}.m__table--body.default .m__table--item{border-top:1px solid #d8eaf9;vertical-align:middle}.m__table--body .body--content td{padding:8px 0;border-top:0px;border-bottom:1px solid #d8eaf9;border-right:0px;font-size:12px;color:#50596d;background:#fff;vertical-align:middle}.m__table--body .body--content td .item__content{display:flex;justify-content:center;align-items:center}.m__table--body .body--content td .selection__checkbox{display:inline-block;width:100%;height:100%}.m__table--body .body--content td .selection__checkbox.selection{display:flex;justify-content:flex-start;align-items:center}.m__table--body .body--content td:nth-child(1){padding-left:10px}.m__table--body .body--content td:first-child{border-left:1px solid #d8eaf9}.m__table--body .body--content td:last-child{border-right:1px solid #d8eaf9}.m__table--body .body--content.is--multiple td:first-child{border-right:1px solid #d8eaf9!important}.m__table--body:after{content:"-";display:block;line-height:14px;color:transparent;width:100%}.m__table .el-checkbox{margin-right:10px!important;float:left}.count-down[data-v-0823a7bd]{color:red}.order-table[data-v-7bf26ea6]{overflow-x:auto;height:calc(100vh - 520px);transition:height .5s;word-break:break-all}.order-table__commodity[data-v-7bf26ea6]{width:280px;display:flex;justify-content:space-between;align-items:center}.order-table__commodity--name[data-v-7bf26ea6]{flex:1;overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin:0 10px}.order-table__commodity--info[data-v-7bf26ea6]{flex-shrink:0;display:flex;flex-direction:column;align-items:flex-end}.order-table__supplier[data-v-7bf26ea6]{display:flex;justify-content:flex-start;align-items:center;flex-direction:column;line-height:1.5}.order-table__actions[data-v-7bf26ea6]{display:flex;flex-wrap:wrap}.order-table__actions .el-link+.el-link[data-v-7bf26ea6]{margin-left:8px}.order-table__header[data-v-7bf26ea6]{font-size:11px;display:flex;justify-content:space-around;align-items:center;width:100%}.proof-img[data-v-7bf26ea6]{width:350px;height:350px;object-fit:contain}.copy[data-v-7bf26ea6]{color:#1890ff;margin-left:8px;cursor:pointer}.text-red[data-v-7bf26ea6]{color:red}
`,document.head.appendChild(H);const oe={style:{background:"#f9f9f9"}},ne=e.defineComponent({__name:"search",emits:["searchParams","showChange"],setup(t,{emit:n}){w.useRoute();const a=e.reactive({no:"",name:"",status:"",shopType:"",extractionType:""}),d=e.ref(!1),r=()=>{n("searchParams",a)};return e.watch(()=>d.value,c=>{n("showChange",c)}),(c,l)=>{const p=e.resolveComponent("el-input"),m=e.resolveComponent("el-form-item"),i=e.resolveComponent("el-col"),h=e.resolveComponent("el-option"),N=e.resolveComponent("el-select"),s=e.resolveComponent("el-row"),f=e.resolveComponent("el-button"),g=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",oe,[e.createVNode(g,{model:a},{default:e.withCtx(()=>[e.createVNode(K,{modelValue:d.value,"onUpdate:modelValue":l[5]||(l[5]=u=>d.value=u)},{default:e.withCtx(()=>[e.createVNode(s,{gutter:20},{default:e.withCtx(()=>[e.createVNode(i,{span:8},{default:e.withCtx(()=>[e.createVNode(m,{label:"供应商ID"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.no,"onUpdate:modelValue":l[0]||(l[0]=u=>a.no=u),maxlength:"20",placeholder:"请填写店铺ID"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(i,{span:8},{default:e.withCtx(()=>[e.createVNode(m,{label:"供应商名称"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:a.name,"onUpdate:modelValue":l[1]||(l[1]=u=>a.name=u),maxlength:"20",placeholder:"请填写店铺名称"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(i,{span:8},{default:e.withCtx(()=>[e.createVNode(m,{label:"状态"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:a.status,"onUpdate:modelValue":l[2]||(l[2]=u=>a.status=u),placeholder:"请选择"},{default:e.withCtx(()=>[e.createVNode(h,{label:"全部",value:" "}),e.createVNode(h,{label:"正常",value:"NORMAL"}),e.createVNode(h,{label:"禁用",value:"FORBIDDEN"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(s,{gutter:20},{default:e.withCtx(()=>[e.createVNode(i,{span:8},{default:e.withCtx(()=>[e.createVNode(m,{label:"提佣类型"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:a.extractionType,"onUpdate:modelValue":l[3]||(l[3]=u=>a.extractionType=u),placeholder:"请选择"},{default:e.withCtx(()=>[e.createVNode(h,{label:"全部",value:""}),e.createVNode(h,{label:"类目提佣",value:"CATEGORY_EXTRACTION"}),e.createVNode(h,{label:"订单金额提佣",value:"ORDER_SALES_EXTRACTION"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(i,{span:8},{default:e.withCtx(()=>[e.createVNode(m,{label:"供应商类型"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:a.shopType,"onUpdate:modelValue":l[4]||(l[4]=u=>a.shopType=u),placeholder:"请选择"},{default:e.withCtx(()=>[e.createVNode(h,{label:"全部",value:""}),e.createVNode(h,{label:"自营",value:"SELF_OWNED"}),e.createVNode(h,{label:"优选",value:"PREFERRED"}),e.createVNode(h,{label:"普通",value:"ORDINARY"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(s,{gutter:20},{default:e.withCtx(()=>[e.createVNode(i,{span:3},{default:e.withCtx(()=>[e.createVNode(f,{r:"",round:"",type:"primary",onClick:r},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["model"])])}}}),lt="",M=(t,n)=>{const a=t.__vccOpts||t;for(const[d,r]of n)a[d]=r;return a},le=M(ne,[["__scopeId","data-v-0732f6b3"]]),re=()=>({data:{type:Array,default(){return[]}},selection:{type:Boolean,default:!1},custom:{type:Boolean,default:!1},needBorder:{type:Boolean,default:!1},columns:{type:Array,default(){return[]}},tableHeaderClass:{type:String,default:""},rowHeaderClass:{type:String,default:""},needHoverBorder:{type:Boolean,default:!1},multipleKey:{type:String,default:""}}),se=e.defineComponent({props:re(),emits:["check"],setup(t,{slots:n,emit:a}){const d=e.ref([]),r=e.computed({get:()=>!!(l.value.length&&l.value.every(s=>s.checked)),set:s=>{i(s),a("check",h())}}),c=e.computed(()=>{const s=l.value.filter(f=>f.checked).length;return s!==0&&s!==l.value.length}),l=e.computed(()=>{const s=d.value;return s?p(s):[]}),p=s=>s?(s.forEach(f=>{const g=f,u=g.orderItems;g.packageMap=u.reduce((y,T,_)=>{var V;const C=String(_);return y.has(C)?(V=y.get(C))==null||V.push(T):y.set(C,[T]),y},new Map)}),s):[];e.watch(t,s=>{d.value=s.data},{immediate:!0});const m=(s,f)=>{Object.assign(s.props?s.props:{},f)};function i(s){l.value=l.value.map(f=>(f.checked=s,f))}function h(){return l.value.filter(s=>s.checked)}const N=(s,f)=>{const g=[],u=s.packageMap;let y=!0;return u.forEach((T,_)=>{g.push(e.createVNode("tr",{class:"body--content"},[n.default&&n.default().map((b,C)=>{var k;m(b,{row:s,packageId:_,shopOrderItems:T});const V=(k=b.props)==null?void 0:k["is-mixed"];if(V&&y||!V)return e.createVNode("td",{class:["o_table--item",!b&&"hide"],style:!V&&y&&u.size>1?{borderRight:"1px solid #d8eaf9"}:{},rowspan:V?u.size:void 0},[e.createVNode("div",{class:["selection__checkbox",t.selection&&C===0&&"selection"]},[e.createVNode("div",{class:["o_table--shrink"]},[b])])])})])),y=!1}),g};return()=>e.createVNode("table",{class:["m__table"],cellpadding:"0",cellspacing:"0"},[e.createVNode("colgroup",null,[t.columns.map(s=>e.createVNode("col",{width:s.width},null))]),e.createVNode("thead",{class:["m__table--head",t.tableHeaderClass,n.header&&"padding"]},[e.createVNode("tr",{class:"m__tr"},[t.columns.map((s,f)=>e.createVNode("th",{style:s.customStyle,class:f===0&&t.selection&&["m__table--center"]},[f===0&&t.selection&&e.createVNode(e.resolveComponent("el-checkbox"),{indeterminate:c.value,modelValue:r.value,"onUpdate:modelValue":g=>r.value=g,onChange:i.bind(this,r.value)},null),e.createVNode("div",{class:["m__table--shrink"]},[s.label])]))])]),l.value.length?l.value.map((s,f)=>e.createVNode("tbody",{class:["m__table--body",t.custom?"custom":"default",t.needBorder&&"need--border",t.needHoverBorder&&s.close?"hover--class":"ordinary--class"]},[n.header&&e.createVNode("tr",null,[e.createVNode("td",{colspan:t.columns.length},[e.createVNode("div",{class:["body--header",t.rowHeaderClass,{close:s.close}]},[n.header({row:s,index:f})])])]),N(s)])):e.createVNode("tbody",{class:"m__table--empty"},[e.createVNode("tr",null,[e.createVNode("td",{class:"empty__td",colspan:t.columns.length,align:"center"},[e.createTextVNode("暂无数据~")])])])])}}),rt="",de=()=>({data:{type:Array,default(){return[]}},selection:{type:Boolean,default:!1},custom:{type:Boolean,default:!1},needBorder:{type:Boolean,default:!1},columns:{type:Array,default(){return[]}},tableHeaderClass:{type:String,default:""},rowHeaderClass:{type:String,default:""},needHoverBorder:{type:Boolean,default:!1},multipleKey:{type:String,default:""}}),ce=e.defineComponent({props:de(),emits:["update:checkedItem"],setup(t,{slots:n,emit:a}){const d=e.ref(),r=e.ref(),c=()=>t.columns.length>0&&t.columns||n.default&&n.default().map(m=>{const i=m.props;return{label:i==null?void 0:i.label,width:i==null?void 0:i.width,prop:i==null?void 0:i.prop,customStyle:i==null?void 0:i.customStyle}}),l=m=>{a("update:checkedItem",m)},p=()=>{const m={default:()=>n.default&&n.default()};if(!n.default&&!t.columns.length&&!n.custom)throw new Error("请传入MTableColumn");return n.header&&(m.header=i=>n.header&&n.header(i)),m};return e.watch(t,m=>{r.value=Q.cloneDeep(m.data)},{immediate:!0}),()=>e.createVNode("div",{class:["m__table--container"]},[e.createVNode(se,{onCheck:l,columns:c(),data:r.value,custom:t.custom,selection:t.selection,tableHeaderClass:t.tableHeaderClass,rowHeaderClass:t.rowHeaderClass,needHoverBorder:t.needHoverBorder,multipleKey:t.multipleKey,needBorder:t.needBorder,ref:d},p())])}}),L=e.defineComponent({__name:"split-table-column",props:{prop:{type:String,default:""},align:{type:String,default:"center"},row:{type:Object,default(){return{}}},packageId:{type:String,default(){}},shopOrderItems:{type:Object,default(){return[]}}},setup(t){const n=t,a=d=>{switch(d){case"left":return"flex-start";case"right":return"flex-end";default:return"center"}};return(d,r)=>(e.openBlock(),e.createElementBlock("div",{class:"item__content",style:e.normalizeStyle({justifyContent:a(n.align)})},[e.renderSlot(d.$slots,"default",{row:n.row,shopOrderItems:n.shopOrderItems})],4))}}),ie=(t={})=>v.get({url:"addon-supplier/supplier/order",params:t}),me=["近一个月订单","近三个月订单","全部订单"],{divTenThousand:O}=q(),{toClipboard:$}=X(),F=new ee,z={UNPAID:"待支付",PAYMENT_AUDIT:"待审核",WAITING_FOR_DELIVER:"待发货",WAITING_FOR_PUTIN:"待收货",FINISHED:"待入库",CLOSED:"已关闭"},pe=(t,n)=>{t?n.value="calc(100vh - 450px)":n.value="calc(100vh - 350px)"},_e={待支付:1,待审核:2,待发货:2,部分发货:2,待入库:3,已完成:3,已关闭:1},fe={OFFLINE:"线下支付",BALANCE:"余额支付"},he=(t,n)=>{const a=e.ref(),d=e.ref(null);a.value=t;const r=e.reactive({statusText:"",activeStep:0}),c=r.statusText=j(t);r.activeStep=_e[c];const l=h=>{$(h).then(()=>{A.ElMessage.success("复制成功")}).catch(()=>A.ElMessage.error("复制失败"))},p=()=>{var N,s,f,g,u,y,T,_,b,C,V,k,R;const h=`
            收货人姓名：${(f=(s=(N=a==null?void 0:a.value)==null?void 0:N.extra)==null?void 0:s.receiver)==null?void 0:f.name}

            联系人电话：${(x=(u=(g=a==null?void 0:a.value)==null?void 0:g.extra)==null?void 0:u.receiver)==null?void 0:x.mobile}

            收货地址：${Z.AddressFn(J.regionData,((_=(V=a==null?void 0:a.value)==null?void 0:V.extra)==null?void 0:_.receiver.areaCode)||[])}${(T=(N=(b=a==null?void 0:a.value)==null?void 0:b.extra)==null?void 0:N.receiver)==null?void 0:T.address}

            采购备注：${(R=(k=a==null?void 0:a.value)==null?void 0:k.extra)==null?void 0:R.remark}
        `;$(h).then(()=>A.ElMessage.success("复制成功")).catch(()=>A.ElMessage.error("复制失败"))},m=e.computed(()=>h=>U(h)),i=e.computed(()=>h=>ue(h));return{orderDetails:a,stepInfo:r,payTypeMap:fe,divTenThousand:O,computedCalculateFreight:m,computedCalculateCommodityPrice:i,payOrderRef:d,copyOrderNo:l,handleCopyReceiver:p}},U=(t=[])=>t.reduce((n,a)=>n.plus(new I(O(a.freightPrice))),new I(0)),ue=(t=[])=>t.reduce((n,a)=>n.plus(new I(O(a.salePrice).mul(new I(a.num)))),new I(0)),be=()=>{const t={OFFLINE:"线下支付",BALANCE:"余额支付"},n=w.useRouter(),a=e.ref(!1),d=e.ref(),r=e.reactive({status:"",startTime:"",endTime:"",purchaser:"",no:""}),c=e.reactive({page:{current:1,size:10},total:0}),l=e.ref(" "),p=e.ref("全部订单"),m=e.ref([]),i=_=>{r.status=_,f()},h=e.computed(()=>_=>ye()),N=e.computed(()=>_=>j(_)),s=e.computed(()=>_=>U(_)),f=()=>{let _=[],b=0;ie({...c.page,...r,needSupplier:!0}).then(C=>{var V,k;_=(V=C.data)==null?void 0:V.records,b=((k=C.data)==null?void 0:k.total)||0}).catch(C=>{console.log("err",C)}).finally(()=>{m.value=_,c.total=Number(b)})},g=_=>{if(l.value=" ",p.value=_,p.value==="近一个月订单"){const b=F.getLastMonth(new Date);y(b)}else if(p.value==="近三个月订单"){const b=F.getLastThreeMonth(new Date);y(b)}else r.startTime="",r.endTime="",f()},u=_=>{r.startTime=_.startTime,r.endTime=_.endTime,r.purchaser=_.purchaser,r.no=_.no,c.page.current=1,f()},y=_=>{const b=F.getYMDs(new Date);r.startTime=_,r.endTime=b,f()};return{handleTabChange:i,pagination:c,initOrderList:f,orderDataList:m,handleQuickSearchCommand:g,handleSearch:u,quickSearchTabName:p,quickSearchTabNames:me,activeTabName:l,getMainOrderStatusText:N,computedBtnList:h,handleDispatchEvent:(_,b)=>{switch(_){case"details":n.push({path:"/order/purchase/details",query:{orderNo:b.no}});break}},computedCalculateFreight:s,showPayDialog:a,currentRow:d,payTypeMap:t}},j=t=>{if(!t)return"";if(t.status==="UNPAID")return"待支付";if(t.status==="PAYMENT_AUDIT")return"待审核";const n=["WAITING_FOR_DELIVER","WAITING_FOR_RECEIVE","COMPLETED"],a={WAITING_FOR_DELIVER:"待发货",WAITING_FOR_RECEIVE:"待入库",COMPLETED:"已完成"};if(t.status==="PAID"){let d="COMPLETED";for(const c of t.orderItems){const l=n.findIndex(p=>p===d);if(c.packageStatus==="WAITING_FOR_DELIVER"&&l>0){d="WAITING_FOR_DELIVER";continue}c.packageStatus==="WAITING_FOR_RECEIVE"&&l>1&&(d="WAITING_FOR_RECEIVE")}let r=a[d];return r==="待发货"&&t.orderItems.find(l=>l.packageStatus!=="WAITING_FOR_DELIVER")&&(r="部分发货"),r}return"已关闭"},ye=t=>{const n=[];return n.push({action:"details",text:"查看详情",type:"primary"}),n},xe=()=>{const t=e.ref(!1),n=e.ref("");return{showProof:t,goToShowProof:d=>{var r,c;n.value=((c=(r=d==null?void 0:d.extra)==null?void 0:r.pay)==null?void 0:c.proof)||"",t.value=!0},currentProof:n}};function ge(t,n=1e3,a={}){const{immediate:d=!0,immediateCallback:r=!1}=a;let c=null;const l=e.ref(!1);function p(){c&&(clearInterval(c),c=null)}function m(){l.value=!1,p()}function i(){n<=0||(l.value=!0,r&&t(),p(),c=setInterval(t,n))}return d&&i(),{isActive:l,pause:m,resume:i,clean:p}}const Ce=(t,n)=>{const a=e.reactive({day:"00",hours:"00",minutes:"00",seconds:"00"});if(!n)return{...e.toRefs(a)};const c=new Date(t.replaceAll("-","/")).getTime()+parseInt(n)*1e3-new Date().getTime(),{day:l,hours:p,minutes:m,seconds:i}=Ve(c);return a.day=l,a.hours=p,a.minutes=m,a.seconds=i,{...e.toRefs(a)}},Ve=t=>{let r=0,c=0,l=0,p=0;for(;t>864e5;)t-=864e5,r++;for(;t>36e5;)t-=36e5,c++;for(;t>6e4;)t-=6e4,l++;for(;t>1e3;)t-=1e3,p++;return{day:String(r).padStart(2,"0"),hours:String(c).padStart(2,"0"),minutes:String(l).padStart(2,"0"),seconds:String(p).padStart(2,"0")}},Ne={class:"count-down"},Te={key:0},ke=e.defineComponent({__name:"index",props:{createTime:{default:""},payTimeout:{default:""}},setup(t){const n=t,a=e.reactive({day:"00",hours:"00",minutes:"00",seconds:"00"}),d=e.ref(!0),{clean:r}=ge(()=>{const{day:c,hours:l,minutes:p,seconds:m}=Ce(n.createTime,n.payTimeout);a.day=c.value,a.hours=l.value,a.minutes=p.value,a.seconds=m.value,c.value==="00"&&l.value==="00"&&p.value==="00"&&m.value==="00"?(d.value=!0,e.nextTick(()=>r())):d.value=!1},1e3,{immediate:!0,immediateCallback:!0});return e.onBeforeUnmount(()=>r()),(c,l)=>(e.openBlock(),e.createElementBlock("div",Ne,[a.day!=="00"?(e.openBlock(),e.createElementBlock("span",Te,e.toDisplayString(a.day)+"天",1)):e.createCommentVNode("",!0),e.createElementVNode("span",null,e.toDisplayString(a.hours)+":"+e.toDisplayString(a.minutes)+":"+e.toDisplayString(a.seconds),1)]))}}),st="",Ee=M(ke,[["__scopeId","data-v-0823a7bd"]]),Se=["onClick"],Pe={class:"order-table__header"},we={class:"order-table__no"},Ie=["onClick"],Le={class:"order-table__freight"},Be={class:"order-table__pay"},De={class:"order-table__mode"},Ae=["onClick"],Re={class:"order-table__pay-time"},Me={class:"order-table__order-time"},Oe={class:"order-table__commodity"},Fe={class:"order-table__commodity--name"},qe={class:"order-table__commodity--info"},He={class:"order-table__amount"},$e={class:"order-table__amount--price"},ze={class:"order-table__amount--num"},Ue={class:"order-table__supplier"},je={class:"order-table__actions"},Ge=["src"],We={class:"dialog-footer"},Ye=e.defineComponent({__name:"PlatformPurchaseList",setup(t){const{copyOrderNo:n}=he(),{handleTabChange:a,pagination:d,initOrderList:r,orderDataList:c,handleQuickSearchCommand:l,handleSearch:p,quickSearchTabName:m,quickSearchTabNames:i,activeTabName:h,getMainOrderStatusText:N,computedBtnList:s,handleDispatchEvent:f,computedCalculateFreight:g,payTypeMap:u}=be(),{divTenThousand:y}=q(),{showProof:T,goToShowProof:_,currentProof:b}=xe(),C=e.ref("calc(100vh - 275px)"),V=S=>pe(S,C),k=[],R=()=>{Object.keys(z).forEach(S=>{k.push([S,z[S]])})},Ke=S=>{d.page.current=1,d.page.size=S,r()},Qe=S=>{d.page.current=S,r()};return r(),R(),(S,P)=>{const Xe=e.resolveComponent("el-icon"),Je=e.resolveComponent("el-dropdown-item"),Ze=e.resolveComponent("el-dropdown-menu"),ve=e.resolveComponent("el-dropdown"),G=e.resolveComponent("el-tab-pane"),et=e.resolveComponent("el-tabs"),tt=e.resolveComponent("el-image"),at=e.resolveComponent("el-link"),W=e.resolveComponent("el-button"),ot=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(le,{onSearch:e.unref(p),onChangeShow:V},null,8,["onSearch"]),e.createVNode(et,{modelValue:e.unref(h),"onUpdate:modelValue":P[0]||(P[0]=o=>e.isRef(h)?h.value=o:null),onTabChange:e.unref(a)},{default:e.withCtx(()=>[e.createVNode(G,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(m)),1),e.createVNode(ve,{placement:"bottom-end",trigger:"click",onCommand:e.unref(l)},{dropdown:e.withCtx(()=>[e.createVNode(Ze,null,{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(i),o=>(e.openBlock(),e.createBlock(Je,{key:o,command:o},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(o),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(Xe,{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(e.unref(te.ArrowDown))]),_:1})],8,Se)]),_:1},8,["onCommand"])]),_:1}),(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(k,o=>e.createVNode(G,{key:o[0],label:o[1],name:o[0]},null,8,["label","name"])),64))]),_:1},8,["modelValue","onTabChange"]),e.createVNode(e.unref(ce),{data:e.unref(c),style:e.normalizeStyle({height:C.value}),class:"order-table"},{header:e.withCtx(({row:o})=>{var x,E,B,D,Y;return[e.createElementVNode("div",Pe,[e.createElementVNode("span",we,[e.createElementVNode("span",null,e.toDisplayString(o.no),1),e.createElementVNode("span",{class:"copy",onClick:nt=>e.unref(n)(o.no)},"复制",8,Ie)]),e.createElementVNode("span",Le,"运费(元)："+e.toDisplayString(e.unref(g)(o==null?void 0:o.orderItems))+"元",1),e.createElementVNode("span",Be,"应付款(元)："+e.toDisplayString(e.unref(y)(o.payAmount))+"元",1),e.createElementVNode("span",De,e.toDisplayString(e.unref(u)[(E=(x=o==null?void 0:o.extra)==null?void 0:x.pay)==null?void 0:E.payType]),1),(D=(B=o==null?void 0:o.extra)==null?void 0:B.pay)!=null&&D.proof?(e.openBlock(),e.createElementBlock("span",{key:0,class:"order-table__proof",onClick:nt=>e.unref(_)(o)},"付款凭证",8,Ae)):e.createCommentVNode("",!0),e.createElementVNode("span",Re,"支付："+e.toDisplayString((Y=o==null?void 0:o.timeNodes)==null?void 0:Y.payTime),1),e.createElementVNode("span",Me,"下单："+e.toDisplayString(o.createTime),1)])]}),default:e.withCtx(()=>[e.createVNode(L,{label:"商品",width:"280px"},{default:e.withCtx(({shopOrderItems:o})=>{var x,E,B,D;return[e.createElementVNode("div",Oe,[e.createVNode(tt,{src:(x=o==null?void 0:o[0])==null?void 0:x.image,fits:"cover",style:{width:"63px",height:"63px"}},null,8,["src"]),e.createElementVNode("span",Fe,e.toDisplayString((E=o==null?void 0:o[0])==null?void 0:E.productName),1),e.createElementVNode("div",qe,[e.createElementVNode("span",null,"￥"+e.toDisplayString(e.unref(y)((B=o==null?void 0:o[0])==null?void 0:B.salePrice)),1),e.createElementVNode("span",null,e.toDisplayString((D=o==null?void 0:o[0])==null?void 0:D.num)+"件",1)])])]}),_:1}),e.createVNode(L,{"is-mixed":!0,label:"实付金额",width:"150px"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",He,[e.createElementVNode("div",$e,e.toDisplayString(e.unref(y)(o.payAmount))+"元",1),e.createElementVNode("div",ze,"共"+e.toDisplayString(o.orderItems.reduce((x,E)=>x+E.num,0))+"件",1)])]),_:1}),e.createVNode(L,{"is-mixed":!0,label:"供应商",width:"150px"},{default:e.withCtx(({row:o})=>{var x,E;return[e.createElementVNode("div",Ue,[e.createElementVNode("span",null,e.toDisplayString((x=o==null?void 0:o.extraInfo)==null?void 0:x.supplierName),1),e.createElementVNode("span",null,e.toDisplayString((E=o==null?void 0:o.extraInfo)==null?void 0:E.supplierPhone),1)])]}),_:1}),e.createVNode(L,{"is-mixed":!0,label:"订单状态",width:"150px"},{default:e.withCtx(({row:o})=>{var x;return[e.createElementVNode("span",{class:e.normalizeClass({"text-red":e.unref(N)(o)==="待支付"})},[e.createTextVNode(e.toDisplayString(e.unref(N)(o))+" ",1),e.unref(N)(o)==="待支付"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createTextVNode(" ("),e.createVNode(Ee,{"create-time":o==null?void 0:o.createTime,"pay-timeout":(x=o==null?void 0:o.extra)==null?void 0:x.payTimeout},null,8,["create-time","pay-timeout"]),e.createTextVNode(") ")],64)):e.createCommentVNode("",!0)],2)]}),_:1}),e.createVNode(L,{"is-mixed":!0,label:"操作",width:"150px"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",je,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(s)(o),x=>(e.openBlock(),e.createBlock(at,{key:x.action,type:x.type,onClick:E=>e.unref(f)(x.action,o)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(x.text),1)]),_:2},1032,["type","onClick"]))),128))])]),_:1})]),_:1},8,["data","style"]),e.createVNode(ae,{"page-size":e.unref(d).page.size,"page-num":e.unref(d).page.current,total:e.unref(d).total,"load-init":"",onHandleSizeChange:Ke,onHandleCurrentChange:Qe},null,8,["page-size","page-num","total"]),e.createVNode(ot,{modelValue:e.unref(T),"onUpdate:modelValue":P[3]||(P[3]=o=>e.isRef(T)?T.value=o:null),title:"付款凭证",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",We,[e.createVNode(W,{onClick:P[1]||(P[1]=o=>T.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(W,{type:"primary",onClick:P[2]||(P[2]=o=>T.value=!1)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("img",{src:e.unref(b),class:"proof-img"},null,8,Ge)]),_:1},8,["modelValue"])])}}}),dt="";return M(Ye,[["__scopeId","data-v-7bf26ea6"]])});
