(function(e,m){typeof exports=="object"&&typeof module<"u"?module.exports=m(require("vue"),require("vue-router"),require("@/components/q-map/q-map.vue"),require("@/components/q-upload/q-upload.vue"),require("element-plus"),require("@/libs/validate"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/q-map/q-map.vue","@/components/q-upload/q-upload.vue","element-plus","@/libs/validate","@/apis/http"],m):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopStoreInfo=m(e.ShopStoreInfoContext.Vue,e.ShopStoreInfoContext.VueRouter,e.ShopStoreInfoContext.QMap,e.ShopStoreInfoContext.QUpload,e.ShopStoreInfoContext.ElementPlus,e.ShopStoreInfoContext.LibsValidate,e.ShopStoreInfoContext.Request))})(this,function(e,m,w,b,p,v,_){"use strict";var g=document.createElement("style");g.textContent=`.storeForm[data-v-58122942]{overflow:hidden}.storeForm__tool[data-v-58122942]{width:1010px;align-items:center;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;margin-left:-14px;z-index:999}.DeliveryDay[data-v-58122942]{width:108px;background:#d5d5d5;text-align:center;font-size:14px;color:#333}.salesclerkinfo[data-v-58122942]{margin-left:35px;font-size:14px;margin-bottom:60px}.salesclerkinfo__head[data-v-58122942]{display:flex;align-items:center;justify-content:space-between;margin-bottom:20px;width:800px}.salesclerkinfo__head--explain[data-v-58122942]{color:#999797}.salesclerkinfo__item[data-v-58122942]{margin-right:30px}
`,document.head.appendChild(g);const E=d=>_.post({url:"addon-shop-store/store/issue",data:d}),C=d=>_.post({url:"addon-shop-store/store/update",data:d}),D=(d,u)=>_.get({url:`addon-shop-store/store/info/${d}`,params:u}),h=d=>(e.pushScopeId("data-v-58122942"),d=d(),e.popScopeId(),d),q={style:{width:"150px"}},k=h(()=>e.createElementVNode("div",{style:{margin:"0 10px"}},"至",-1)),I={style:{width:"150px"}},T={style:{display:"flex"}},H=h(()=>e.createElementVNode("div",{class:"DeliveryDay"},"天后开始提货",-1)),M={style:{display:"flex","margin-left":"13px"}},U=h(()=>e.createElementVNode("div",{class:"DeliveryDay"},"天后结束提货",-1)),B={key:0,class:"salesclerkinfo"},L=h(()=>e.createElementVNode("div",{class:"salesclerkinfo__head"},[e.createElementVNode("div",null,"店员手机号"),e.createElementVNode("div",{class:"salesclerkinfo__head--explain"},"店员手机号用于门店自提订单（门店移动端）的核销")],-1)),A={key:1,class:"storeForm__tool"},F=e.defineComponent({__name:"ShopStoreInfo",setup(d){const u=m.useRoute(),c=m.useRouter(),y=e.ref(),i=e.ref([]),o=e.ref({id:"",storeName:"",storeLogo:"",storeImg:"",storePhone:"",functionaryName:"",functionaryPhone:"",businessStartTime:"00:00:00",businessEndTime:"23:59:59",detailedAddress:"",startDeliveryDay:0,endDeliveryDay:0,location:{type:"Point",coordinates:["121.583336","29.990282"]},shopAssistantList:[]}),P=e.reactive({storeName:[{required:!0,message:"请填写门店名称",trigger:"blur"}],storeLogo:[{required:!0,message:"请上传门店logo",trigger:"blur"}],storeImg:[{required:!0,message:"请上传门店图片",trigger:"blur"}],functionaryName:[{required:!0,message:"请填写负责人姓名",trigger:"blur"}],functionaryPhone:[{required:!0,validator:$,trigger:"blur"}],businessStartTime:[{required:!0,message:"请填写营业开始时间",trigger:"blur"}],businessEndTime:[{required:!0,message:"请填写营业结束时间",trigger:"blur"}],startDeliveryDay:[{required:!0,message:"请填写开始提货时间",trigger:"blur"}],endDeliveryDay:[{required:!0,message:"请填写结束提货时间",trigger:"blur"}],detailedAddress:[{required:!0,message:"请选择地址",trigger:"blur"},{min:2,max:200,message:"输入长度必须在2~200以内",trigger:"blur"}]}),x=e.ref(!0),a=e.ref(!1);R();async function R(){if(u.query.shopId&&u.query.id){u.query.lookType==="OnlyLook"&&(a.value=!0);const{code:s,data:t,msg:n}=await D(u.query.shopId,{id:u.query.id});s===200&&t?(i.value=t.storeImg.split(","),o.value=t):p.ElMessage.error(n||"获取失败")}}const j=s=>{if(!a.value){if(u.query.id&&x.value){x.value=!1;return}o.value.detailedAddress=s.address,o.value.location.coordinates=s.position}};function $(s,t,n){t===""?n(new Error("请填写手机号")):v.REGEX_MOBILE(t)?n():n(new Error("请填写正确的手机号"))}const z=async()=>{if(!(!y.value||(o.value.storeImg=i.value.filter(t=>t.trim()).join(","),!await y.value.validate()))){if(o.value.startDeliveryDay>o.value.endDeliveryDay)return p.ElMessage.error("结束提货时间应大于等于开始提货时间");if(!i.value.length){p.ElMessage.error("请上传门店图片");return}o.value.id?G():O()}},O=async()=>{const{code:s,msg:t}=await E(o.value);s===200?(p.ElMessage.success("新增门店成功"),c.push({path:"/shop/store"})):p.ElMessage.error(t||"新增失败")},G=async()=>{const{code:s,msg:t}=await C(o.value);s===200?(p.ElMessage.success("修改门店信息成功"),c.push({path:"/shop/store"})):p.ElMessage.error(t||"修改门店信息失败")},f=(s,t)=>{const n=[];for(let l=s;l<=t;l++)n.push(l);return n},X=()=>{if(o.value.businessEndTime){const s=Number(o.value.businessEndTime.split(":")[0]);return f(s+1,24)}return[]},J=()=>f(1,59),K=()=>f(1,59),W=()=>{if(o.value.businessStartTime){const s=Number(o.value.businessStartTime.split(":")[0]);return f(0,s-1).concat(s-1,24)}return[]},Y=()=>f(0,58),Z=()=>f(0,58);return(s,t)=>{const n=e.resolveComponent("el-input"),l=e.resolveComponent("el-form-item"),V=e.resolveComponent("el-time-picker"),Q=e.resolveComponent("el-row"),N=e.resolveComponent("el-input-number"),ee=e.resolveComponent("el-form"),S=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(ee,{ref_key:"StoreFormRef",ref:y,model:o.value,rules:P,"label-width":"100px",style:{"margin-bottom":"60px"}},{default:e.withCtx(()=>[e.createVNode(l,{label:"门店名称",prop:"storeName"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:o.value.storeName,"onUpdate:modelValue":t[0]||(t[0]=r=>o.value.storeName=r),disabled:a.value,maxlength:"25",placeholder:"请输入门店名称"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(l,{label:"负责人姓名",prop:"functionaryName"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:o.value.functionaryName,"onUpdate:modelValue":t[1]||(t[1]=r=>o.value.functionaryName=r),disabled:a.value,maxlength:"8",placeholder:"请输入负责人姓名"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(l,{label:"负责人电话",prop:"functionaryPhone"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:o.value.functionaryPhone,"onUpdate:modelValue":t[2]||(t[2]=r=>o.value.functionaryPhone=r),disabled:a.value,maxlength:"11",placeholder:"请输入负责人电话"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(l,{label:"营业时间"},{default:e.withCtx(()=>[e.createVNode(Q,{style:{display:"flex",width:"100%"}},{default:e.withCtx(()=>[e.createElementVNode("div",q,[e.createVNode(l,{"label-width":"0px",prop:"businessStartTime"},{default:e.withCtx(()=>[e.createVNode(V,{modelValue:o.value.businessStartTime,"onUpdate:modelValue":t[3]||(t[3]=r=>o.value.businessStartTime=r),disabled:a.value,"disabled-hours":X,"disabled-minutes":J,"disabled-seconds":K,format:"HH:mm:ss",placeholder:"开始时间","value-format":"HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})]),k,e.createElementVNode("div",I,[e.createVNode(l,{"label-width":"0px",prop:"businessEndTime"},{default:e.withCtx(()=>[e.createVNode(V,{modelValue:o.value.businessEndTime,"onUpdate:modelValue":t[4]||(t[4]=r=>o.value.businessEndTime=r),disabled:a.value,"disabled-hours":W,"disabled-minutes":Y,"disabled-seconds":Z,format:"HH:mm:ss",placeholder:"结束时间","value-format":"HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})])]),_:1})]),_:1}),e.createVNode(l,{label:"提货时间"},{default:e.withCtx(()=>[e.createVNode(l,{"label-width":"0px",prop:"startDeliveryDay"},{default:e.withCtx(()=>[e.createElementVNode("div",T,[e.createVNode(N,{modelValue:o.value.startDeliveryDay,"onUpdate:modelValue":t[5]||(t[5]=r=>o.value.startDeliveryDay=r),controls:!1,disabled:a.value,min:0,step:1,"step-strictly":"",style:{width:"110px"}},null,8,["modelValue","disabled"]),H])]),_:1}),e.createVNode(l,{"label-width":"0px",prop:"endDeliveryDay"},{default:e.withCtx(()=>[e.createElementVNode("div",M,[e.createVNode(N,{modelValue:o.value.endDeliveryDay,"onUpdate:modelValue":t[6]||(t[6]=r=>o.value.endDeliveryDay=r),controls:!1,disabled:a.value,min:o.value.startDeliveryDay,step:1,"step-strictly":"",style:{width:"110px"}},null,8,["modelValue","disabled","min"]),U])]),_:1})]),_:1}),e.createVNode(l,{label:"详细地址",prop:"detailedAddress"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:o.value.detailedAddress,"onUpdate:modelValue":t[7]||(t[7]=r=>o.value.detailedAddress=r),disabled:a.value,maxlength:"60",placeholder:"请选择经纬度"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(l,null,{default:e.withCtx(()=>[e.createVNode(w,{coordinates:o.value.location.coordinates,onChange:j},null,8,["coordinates"])]),_:1}),e.createVNode(l,{label:"门店logo",prop:"storeLogo"},{default:e.withCtx(()=>[e.createVNode(b,{src:o.value.storeLogo,"onUpdate:src":t[8]||(t[8]=r=>o.value.storeLogo=r),disabled:a.value},null,8,["src","disabled"]),e.createVNode(l,{label:"门店图片",prop:"storeImg"},{default:e.withCtx(()=>[e.createVNode(b,{src:i.value[0],"onUpdate:src":t[9]||(t[9]=r=>i.value[0]=r),disabled:a.value},null,8,["src","disabled"]),e.createVNode(b,{src:i.value[1],"onUpdate:src":t[10]||(t[10]=r=>i.value[1]=r),disabled:a.value,style:{margin:"0 20px"}},null,8,["src","disabled"]),e.createVNode(b,{src:i.value[2],"onUpdate:src":t[11]||(t[11]=r=>i.value[2]=r),disabled:a.value},null,8,["src","disabled"])]),_:1})]),_:1})]),_:1},8,["model","rules"]),a.value?(e.openBlock(),e.createElementBlock("div",B,[L,e.createElementVNode("div",null,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value.shopAssistantList,r=>(e.openBlock(),e.createElementBlock("span",{key:r.assistantPhone,class:"salesclerkinfo__item"},e.toDisplayString(r.assistantPhone),1))),128))])])):e.createCommentVNode("",!0),a.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("div",A,[e.createVNode(S,{round:"",onClick:t[12]||(t[12]=r=>e.unref(c).push("/shop/store"))},{default:e.withCtx(()=>[e.createTextVNode(" 取消")]),_:1}),e.createVNode(S,{round:"",type:"primary",onClick:z},{default:e.withCtx(()=>[e.createTextVNode(" 保存")]),_:1})]))])}}}),te="";return((d,u)=>{const c=d.__vccOpts||d;for(const[y,i]of u)c[y]=i;return c})(F,[["__scopeId","data-v-58122942"]])});
