(function(e,t){typeof exports=="object"&&typeof module<"u"?module.exports=t(require("vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/apis/http"],t):(e=typeof globalThis<"u"?globalThis:e||self,e.CityDistributionCheckBox=t(e.CityDistributionCheckBoxContext.Vue,e.CityDistributionCheckBoxContext.Request))})(this,function(e,t){"use strict";const r=()=>t.get({url:"addon-intra-city-distribution/intraCityDistribution/config/"});return e.defineComponent({__name:"CityDistributionCheckBox",props:{properties:{type:Object,required:!0}},setup(s){const n=e.ref(!1),d=s;return e.onMounted(()=>{r().then(i=>{n.value=i.code!==200||!i.data})}),(i,o)=>{const c=e.resolveComponent("el-checkbox");return e.openBlock(),e.createBlock(c,{disabled:n.value||d.properties.disable,label:"INTRA_CITY_DISTRIBUTION"},{default:e.withCtx(()=>o[0]||(o[0]=[e.createTextVNode("同城配送")])),_:1},8,["disabled"])}}})});
