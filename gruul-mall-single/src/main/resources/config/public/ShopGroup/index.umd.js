(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("@vueuse/core"),require("vue-router"),require("@/composables/useConvert"),require("@/components/PageManage.vue"),require("@/store/modules/shopInfo"),require("element-plus"),require("@element-plus/icons-vue"),require("@/utils/http")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","vue-router","@/composables/useConvert","@/components/PageManage.vue","@/store/modules/shopInfo","element-plus","@element-plus/icons-vue","@/utils/http"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopGroup=V(e.ShopGroupContext.Vue,e.ShopGroupContext.VueUse,e.ShopGroupContext.VueRouter,e.ShopGroupContext.UseConvert,e.ShopGroupContext.PageManageTwo,e.ShopGroupContext.ShopInfoStore,e.ShopGroupContext.ElementPlus,e.ShopGroupContext.ElementPlusIconsVue,e.ShopGroupContext.UtilsHttp))})(this,function(e,V,x,w,S,G,y,D,N){"use strict";var E=document.createElement("style");E.textContent=`.column[data-v-c60098d6]{width:966px;height:144px;background:#f9f9f9;margin-bottom:10px;padding:10px;display:flex;justify-content:center;align-items:center}.column__left[data-v-c60098d6]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-around;align-items:flex-start;width:50%;height:100%;font-size:12px;color:#333}.column__left--title[data-v-c60098d6]{font-size:14px}.column__left--statistical[data-v-c60098d6]{width:100%;color:#a9a9a9;display:flex;justify-content:center;align-items:center;padding-right:40px;justify-content:space-between}.column__center[data-v-c60098d6]{width:20%;height:100%}.column__center--title[data-v-c60098d6]{font-size:14px}.column__right[data-v-c60098d6]{width:30%;height:100%;display:flex;justify-content:center;align-items:center}.nots[data-v-c60098d6]{color:#2e99f3}.ongoing[data-v-c60098d6]{color:#f57373}.hasEnded[data-v-c60098d6],.off[data-v-c60098d6],.suspended[data-v-c60098d6]{color:#a9a9a9}.preheat[data-v-c60098d6]{color:"#29C90A"}.container[data-v-9cb872a8]{overflow-y:scroll}.group__head[data-v-776b1d0f]{display:flex;justify-content:flex-end;align-items:center}.group__head--center[data-v-776b1d0f]{margin:0 20px}.group__userInfo[data-v-776b1d0f]{display:flex;justify-content:space-between;align-items:center}.group__userInfo--text[data-v-776b1d0f]{width:90px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.group__goodInfo[data-v-776b1d0f]{display:flex;justify-content:space-between;align-items:center}.group__goodInfo--text[data-v-776b1d0f]{width:130px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.group__content[data-v-776b1d0f]{margin-top:10px}
`,document.head.appendChild(E);const T=e.defineComponent({__name:"ShopGroup",setup(t){const c={GroupActivity:e.defineAsyncComponent(()=>Promise.resolve().then(()=>J)),GroupOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>oe))},o=e.ref("GroupActivity");return(l,n)=>{const i=e.resolveComponent("el-tab-pane"),r=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(r,{modelValue:o.value,"onUpdate:modelValue":n[0]||(n[0]=m=>o.value=m)},{default:e.withCtx(()=>[e.createVNode(i,{label:"拼团活动",name:"GroupActivity"}),e.createVNode(i,{label:"拼团订单",name:"GroupOrder"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(c[o.value])))])}}}),O=e.defineComponent({__name:"select-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Array,default:()=>[{label:"1",value:"2"}]}},emits:["update:modelValue","change"],setup(t,{emit:c}){const o=t,l=V.useVModel(o,"modelValue",c);return(n,i)=>{const r=e.resolveComponent("el-option"),m=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(m,{modelValue:e.unref(l),"onUpdate:modelValue":i[0]||(i[0]=u=>e.isRef(l)?l.value=u:null),placeholder:o.placeholder,style:{width:"150px"},onChange:i[1]||(i[1]=u=>c("change"))},{default:e.withCtx(()=>[e.renderSlot(n.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.list,(u,s)=>(e.openBlock(),e.createBlock(r,{key:s,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),A=e.defineComponent({__name:"head-search",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},leftBtnText:{type:String,default:"leftBtnText"}},emits:["update:modelValue","leftBtnClick","search"],setup(t,{emit:c}){const o=t,l=[{value:"OPENING",label:"未开始"},{value:"OPEN",label:"进行中"},{value:"FINISHED",label:"已结束"},{value:"VIOLATION",label:"违规下架"}],n=V.useVModel(o,"modelValue",c);return(i,r)=>{const m=e.resolveComponent("el-button"),u=e.resolveComponent("el-option"),s=e.resolveComponent("el-col"),_=e.resolveComponent("el-date-picker"),h=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(h,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px",width:"100%"}},{default:e.withCtx(()=>[e.createVNode(s,{span:14},{default:e.withCtx(()=>[e.createVNode(m,{round:"",type:"primary",onClick:r[0]||(r[0]=a=>c("leftBtnClick"))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(o.leftBtnText),1)]),_:1}),e.createVNode(O,{modelValue:e.unref(n).status,"onUpdate:modelValue":r[1]||(r[1]=a=>e.unref(n).status=a),style:{"margin-left":"15px"},list:l,onChange:r[2]||(r[2]=a=>c("search"))},{default:e.withCtx(()=>[e.createVNode(u,{label:"全部状态",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(s,{span:10},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:e.unref(n).date,"onUpdate:modelValue":r[3]||(r[3]=a=>e.unref(n).date=a),style:{width:"400px"},format:"YYYY/MM/DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:r[4]||(r[4]=a=>c("search",a))},null,8,["modelValue"])]),_:1})]),_:1})}}}),$={class:"column"},j={class:"column__left"},z={class:"column__left--title"},M={class:"column__left--statistical"},L={class:"column__center"},U={class:"column__right"},F=e.defineComponent({__name:"column",props:{item:{type:Object,required:!0}},emits:["del"],setup(t,{emit:c}){const{divTenThousand:o}=w(),l=x.useRouter(),n={OPENING:{title:"未开始",class:"nots"},PREHEAT:{title:"预售中",class:"preheat"},OPEN:{title:"进行中",class:"ongoing"},FINISHED:{title:"已结束",class:"hasEnded"},VIOLATION:{title:"违规下架",class:"off"}},i=r=>{l.push({name:"marketingAppGroupForm",query:{id:r}})};return(r,m)=>{const u=e.resolveComponent("el-button"),s=e.resolveComponent("el-button-group");return e.openBlock(),e.createElementBlock("div",$,[e.createElementVNode("div",j,[e.createElementVNode("h1",z,e.toDisplayString(t.item.name),1),e.createElementVNode("time",null,"活动时间："+e.toDisplayString(t.item.startTime)+" 至 "+e.toDisplayString(t.item.endTime),1),e.createElementVNode("div",null,"活动商品："+e.toDisplayString(t.item.productNum||0)+"件",1),e.createElementVNode("div",M,[e.createElementVNode("span",null,"参加人数："+e.toDisplayString(t.item.users||0),1),e.createElementVNode("span",null,"支付单数："+e.toDisplayString(t.item.orders||0),1),e.createElementVNode("span",null,"应收金额："+e.toDisplayString(t.item.amount&&e.unref(o)(t.item.amount)||0),1)])]),e.createElementVNode("div",L,[e.createElementVNode("h1",{class:e.normalizeClass(["column__center--title",n[t.item.status].class])},e.toDisplayString(n[t.item.status].title),3)]),e.createElementVNode("div",U,[e.createVNode(s,null,{default:e.withCtx(()=>[e.createVNode(u,{round:"",onClick:m[0]||(m[0]=_=>i(t.item.id))},{default:e.withCtx(()=>[e.createTextVNode("查看活动 ")]),_:1}),e.createVNode(u,{round:"",disabled:t.item.status==="OPEN",onClick:m[1]||(m[1]=_=>c("del",{shopId:t.item.shopId,activityId:t.item.id}))},{default:e.withCtx(()=>[e.createTextVNode("删除活动")]),_:1},8,["disabled"])]),_:1})])])}}}),re="",b=(t,c)=>{const o=t.__vccOpts||t;for(const[l,n]of c)o[l]=n;return o},q=b(F,[["__scopeId","data-v-c60098d6"]]),H=t=>N.http.get({url:"addon-team/team/activity",params:t},{isMock:!1}),P=t=>N.http.get({url:"addon-team/team/activity/order",params:t},{isMock:!1}),Y=t=>N.http.post({url:"addon-team/team/activity/delete/batch",data:t}),R=e.defineComponent({__name:"group-activity",setup(t){const c=x.useRouter(),o=e.ref([]),l=e.reactive({keyword:"",date:"",status:""}),n=e.reactive({size:10,current:1,total:0});i();async function i(){const{date:s,status:_}=l,h={startTime:"",endTime:""};Array.isArray(s)&&s.length===2&&(h.startTime=s[0],h.endTime=s[1]);const a={...n,...h,shopId:G.useShopInfoStore().shopInfo.id,status:_},{code:p,data:f}=await H(a);if(p!==200)return y.ElMessage.error("获取活动列表失败");o.value=f.records,n.total=f.total}const r=async s=>{try{if(!await y.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:h,data:a}=await Y([s]);if(h!==200){y.ElMessage.error("删除失败");return}y.ElMessage.success("删除成功"),o.value=o.value.filter(p=>p.id!==s.activityId),n.total--}catch{}},m=s=>{n.current=s,i()},u=s=>{n.size=s,i()};return(s,_)=>{const h=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(A,{modelValue:l,"onUpdate:modelValue":_[0]||(_[0]=a=>l=a),"left-btn-text":"新增拼团活动",onSearch:i,onLeftBtnClick:_[1]||(_[1]=a=>e.unref(c).push({name:"marketingAppGroupNewForm"}))},null,8,["modelValue"]),e.createElementVNode("div",{style:e.normalizeStyle({height:"calc(100vh - 240px)"}),class:"container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,a=>(e.openBlock(),e.createBlock(q,{key:a.id,item:a,onDel:r},null,8,["item"]))),128))],4),e.createVNode(h,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(S,{"page-size":n.size,"page-num":n.current,total:n.total,onHandleCurrentChange:m,onHandleSizeChange:u},null,8,["page-size","page-num","total"])]),_:1})],64)}}}),se="",J=Object.freeze(Object.defineProperty({__proto__:null,default:b(R,[["__scopeId","data-v-9cb872a8"]])},Symbol.toStringTag,{value:"Module"})),K={class:"group__head"},Q={class:"group__head--center"},W={class:"group__content"},X={class:"group__userInfo"},Z={class:"group__userInfo--text"},v={class:"group__goodInfo"},ee={class:"group__goodInfo--text"},te=e.defineComponent({__name:"group-order",setup(t){const c=x.useRouter(),o=e.reactive({status:"SUCCESS",commander:"",productName:""}),l=e.reactive({current:1,size:10,total:0}),n=e.ref([]),{divTenThousand:i}=w();a();const r=p=>({SUCCESS:"拼团成功",FAIL:"拼团失败",ING:"拼团中"})[p],m=p=>{c.push({name:"marketingAppGroupOrderDetail",query:{teamNo:p}})},u=p=>{l.size=p,a()},s=p=>{l.current=p,a()},_=()=>{a()},h=p=>{o.status=p,a()};async function a(){const p={...l,...o},{data:f,code:C}=await P(p);if(C!==200)return y.ElMessage.error("获取拼团订单失败");n.value=f.records,l.total=f.total}return(p,f)=>{const C=e.resolveComponent("el-option"),ne=e.resolveComponent("el-select"),k=e.resolveComponent("el-input"),I=e.resolveComponent("el-button"),B=e.resolveComponent("el-image"),g=e.resolveComponent("el-table-column"),le=e.resolveComponent("el-table"),ae=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",K,[e.createElementVNode("div",null,[e.createVNode(ne,{modelValue:o.status,"onUpdate:modelValue":f[0]||(f[0]=d=>o.status=d),placeholder:"请选择活动状态",onChange:h},{default:e.withCtx(()=>[e.createVNode(C,{label:"拼团成功",value:"SUCCESS"}),e.createVNode(C,{label:"拼团失败",value:"FAIL"}),e.createVNode(C,{label:"拼团中",value:"ING"})]),_:1},8,["modelValue"])]),e.createElementVNode("div",Q,[e.createVNode(k,{modelValue:o.commander,"onUpdate:modelValue":f[1]||(f[1]=d=>o.commander=d),placeholder:"发起人"},null,8,["modelValue"])]),e.createElementVNode("div",null,[e.createVNode(k,{modelValue:o.productName,"onUpdate:modelValue":f[2]||(f[2]=d=>o.productName=d),placeholder:"商品名称"},{append:e.withCtx(()=>[e.createVNode(I,{icon:e.unref(D.Search),onClick:_},null,8,["icon"])]),_:1},8,["modelValue"])])]),e.createElementVNode("div",W,[e.createVNode(le,{data:n.value,"header-cell-style":{height:"46px",background:"#F6F8FA"}},{default:e.withCtx(()=>[e.createVNode(g,{label:"发起人",width:"160"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",X,[e.createVNode(B,{src:d.commanderAvatar,style:{width:"36px",height:"36px"}},null,8,["src"]),e.createElementVNode("div",Z,e.toDisplayString(d.commanderNickname),1)])]),_:1}),e.createVNode(g,{label:"商品信息",width:"200"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",v,[e.createVNode(B,{src:d.productImage,style:{width:"36px",height:"36px"}},null,8,["src"]),e.createElementVNode("div",ee,e.toDisplayString(d.productName),1)])]),_:1}),e.createVNode(g,{label:"开团时间",prop:"openTime",align:"center",width:"170"}),e.createVNode(g,{label:"参团人数",align:"center"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",null,e.toDisplayString(d.currentNum)+"/"+e.toDisplayString(d.totalNum),1)]),_:1}),e.createVNode(g,{label:"订单总金额",align:"center"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",null,e.toDisplayString(e.unref(i)(d.amount)),1)]),_:1}),e.createVNode(g,{label:"状态",align:"center"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",null,e.toDisplayString(r(d.status)),1)]),_:1}),e.createVNode(g,{label:"操作",align:"center",width:"100",fixed:"right"},{default:e.withCtx(({row:d})=>[e.createVNode(I,{link:"",type:"primary",onClick:pe=>m(d.teamNo)},{default:e.withCtx(()=>[e.createTextVNode("拼团详情")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),e.createVNode(ae,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(S,{"page-size":l.size,"page-num":l.current,total:l.total,onHandleSizeChange:u,onHandleCurrentChange:s},null,8,["page-size","page-num","total"])]),_:1})],64)}}}),ce="",oe=Object.freeze(Object.defineProperty({__proto__:null,default:b(te,[["__scopeId","data-v-776b1d0f"]])},Symbol.toStringTag,{value:"Module"}));return T});
