(function(e,c){typeof exports=="object"&&typeof module<"u"?module.exports=c(require("vue"),require("vue-router"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/apis/http"],c):(e=typeof globalThis<"u"?globalThis:e||self,e.<PERSON>eightCheckBox=c(e.FreightCheckBoxContext.Vue,e.FreightCheckBoxContext.VueRouter,e.FreightCheckBoxContext.Request))})(this,function(e,c,u){"use strict";const h=e.defineComponent({__name:"logistics-setting",props:{data:{type:Object,default:()=>null}},setup(n){const r=n,f=e.computed(()=>r.data&&r.data.logisticsBaseModelVos||[]),i=e.computed(()=>r.data&&r.data.valuationModel||"PKGS"),p=e.computed(()=>s=>{if(s&&s.length)return s.map(a=>a.lowerCode.length===a.length?a.upperName:`${a.upperName}(${a.lowerCode.length}/${a.length})`).join(",")});return(s,a)=>{const o=e.resolveComponent("el-table-column"),t=e.resolveComponent("el-table");return e.withDirectives((e.openBlock(),e.createBlock(t,{"cell-style":{fontSize:"14px",color:"#333",height:"80px"},data:f.value,"header-cell-style":{fontSize:"14px",color:"#515151",height:"80px",fontWeight:"normal"},border:"",style:{width:"80%","margin-top":"20px"}},{default:e.withCtx(()=>[e.createVNode(o,{label:"可配送区域"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,e.toDisplayString(p.value(l.regionJson)),1)]),_:1}),e.createVNode(o,{label:i.value==="PKGS"?"首件数（件）":"首重量(kg)","min-width":"80%"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,e.toDisplayString(l.firstQuantity)+e.toDisplayString(),1)]),_:1},8,["label"]),e.createVNode(o,{label:"首费（元）","min-width":"70%"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,e.toDisplayString(l.firstAmount),1)]),_:1}),e.createVNode(o,{label:i.value==="PKGS"?"续件数（件）":"续重量(kg)","min-width":"70%"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,e.toDisplayString(l.secondQuantity),1)]),_:1},8,["label"]),e.createVNode(o,{label:"续费（元）","min-width":"70%"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,e.toDisplayString(l.secondAmount),1)]),_:1})]),_:1},8,["data"])),[[e.vShow,n.data]])}}}),g=(n,r)=>u.get({url:"gruul-mall-freight/logistics/template/get/list/",params:{current:n,size:r}});return e.defineComponent({__name:"FreightCheckBox",props:{properties:{type:Object,required:!0}},setup(n){const r=n,f=c.useRouter(),i=e.ref(),p=e.ref(new Map);e.onMounted(()=>{s()}),e.watch(()=>r.properties.templateId,o=>{i.value=o,console.log("currentTemplateId.value",i.value)},{immediate:!0});async function s(){const{code:o,data:t}=await g(1,1e3);if(o!==200||!t)return;const l=t.records;!l||!l.length||l.forEach(m=>{p.value.set(m.id,m)})}const a=o=>{r.properties.templateChange(o)};return(o,t)=>{const l=e.resolveComponent("el-option"),m=e.resolveComponent("el-select"),x=e.resolveComponent("el-link"),C=e.resolveComponent("el-checkbox");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(C,{disabled:n.properties.disable,label:"EXPRESS"},{default:e.withCtx(()=>[t[4]||(t[4]=e.createTextVNode(" 快递配送 ")),e.createElementVNode("span",{style:{margin:"0 10px"},onClick:t[0]||(t[0]=e.withModifiers(()=>{},["stop"]))},"运费模板选择"),e.createVNode(m,{disabled:n.properties.disable,modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=d=>i.value=d),class:"inputWidth",placeholder:"请选择运费模板",onChange:a},{default:e.withCtx(()=>[e.createVNode(l,{value:"0",label:"商家包邮"}),e.createVNode(l,{value:"1",label:"供应商条件包邮"}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Array.from(p.value.values()),d=>(e.openBlock(),e.createBlock(l,{key:d.id,label:d.templateName,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"]),e.createVNode(x,{disabled:n.properties.disable,underline:!1,style:{"margin-left":"15px"},type:"primary",onClick:t[2]||(t[2]=e.withModifiers(d=>e.unref(f).push({name:"freightLogisticsIndex",query:{from:"releaseGoods"}}),["stop","prevent"]))},{default:e.withCtx(()=>t[3]||(t[3]=[e.createTextVNode(" 前往设置 ")])),_:1},8,["disabled"])]),_:1},8,["disabled"]),t[5]||(t[5]=e.createElementVNode("br",null,null,-1)),e.createVNode(h,{data:p.value.get(i.value)},null,8,["data"])],64)}}})});
