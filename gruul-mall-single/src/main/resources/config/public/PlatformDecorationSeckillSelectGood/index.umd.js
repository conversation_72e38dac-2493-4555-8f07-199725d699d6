(function(e,_){typeof exports=="object"&&typeof module<"u"?module.exports=_(require("vue"),require("@/components/PageManage.vue"),require("element-plus"),require("@/apis/http"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","@/components/PageManage.vue","element-plus","@/apis/http","@/composables/useConvert"],_):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformDecorationSeckillSelectGood=_(e.PlatformDecorationSeckillSelectGoodContext.Vue,e.PlatformDecorationSeckillSelectGoodContext.PageManageTwo,e.PlatformDecorationSeckillSelectGoodContext.ElementPlus,e.PlatformDecorationSeckillSelectGoodContext.Request,e.PlatformDecorationSeckillSelectGoodContext.UseConvert))})(this,function(e,_,g,S,N){"use strict";var b=document.createElement("style");b.textContent=`.title[data-v-9470572c]{font-size:15px;font-weight:700;display:flex;margin-bottom:20px;margin-top:-40px}.digGoods[data-v-9470572c]{border-top:1px solid #d7d7d7;padding-top:10px}.digGoods__box[data-v-9470572c]{background-color:#f2f2f2;padding:10px}.digGoods__box--top[data-v-9470572c]{display:flex;justify-content:space-between}.digGoods__box--content[data-v-9470572c]{margin-top:10px;background-color:#fff;border-radius:5px;display:flex;flex-wrap:wrap;padding:5px}.digGoods__box--content--good[data-v-9470572c]{width:33%;margin-left:2px;margin-bottom:4px;height:80px;border-radius:5px;padding:5px;display:flex}.digGoods__box--content--good--img[data-v-9470572c]{width:65px;height:65px;position:relative}.digGoods__box--content--good--imgShadow[data-v-9470572c]{width:65px;height:65px;position:absolute;background-color:#0009;display:flex;justify-content:center;align-items:center}.digGoods__box--content--good--shopName[data-v-9470572c]{margin-left:10px;font-size:12px;display:flex;flex-direction:column;justify-content:space-between}.digGoods__box--bottom[data-v-9470572c]{display:flex;justify-content:space-between;align-items:center}.serachBtn[data-v-9470572c]{width:32px;height:32px;display:flex;justify-content:center;align-items:center;border:1px solid #dcdfe6;background-color:#fff;cursor:pointer;border-left:none;border-radius:4px}
`,document.head.appendChild(b);const w=l=>S.get({url:`addon-seckill/seckillPromotion/notStartedAndProcessing?size=1000&shopId=${l}`}),I=l=>S.get({url:"addon-seckill/seckillPromotion/secKillProduct",params:l}),B=(l=>(e.pushScopeId("data-v-9470572c"),l=l(),e.popScopeId(),l))(()=>e.createElementVNode("div",{class:"title"},"选择商品",-1)),P={class:"digGoods"},D={class:"digGoods__box"},z={class:"digGoods__box--top"},T={key:0,class:"digGoods__box--content"},j=["onClick"],M=["src"],L={key:0,class:"digGoods__box--content--good--imgShadow"},q={class:"digGoods__box--content--good--shopName"},v={key:1,class:"digGoods__box--content",style:{display:"flex","justify-content":"center","align-items":"center",height:"250px"}},O={class:"digGoods__box--bottom"},A=e.defineComponent({__name:"PlatformDecorationSeckillSelectGood",props:{properties:{type:Object,default:{}}},setup(l,{expose:C}){const m=l,{divTenThousand:x}=N(),i=e.ref([]),h=e.ref([]),y=e.ref([]),p=e.ref(!1),s=e.shallowReactive({current:1,size:10,total:0,choosedSession:""});e.watch(m.properties,async o=>{o.goodsVisible&&(K(),h.value=JSON.parse(JSON.stringify(o.pointGoodsList)),R(),await Q(),u())},{immediate:!0}),C({tempGoods:h,search:s,goodsList:i,allChecked:p});const G={borderGet:"2px solid #2D8CF0",borderNoGet:"2px solid #f2f2f2"},$=o=>{s.choosedSession=o,u()},F=o=>{o.isCheck=!o.isCheck;const t=h.value;if(t.length>=16)return g.ElMessage.error("最多选择16个商品"),!1;if(o.isCheck)t.push(o);else{const c=t.findIndex(a=>a.productId===o.productId);c!==-1&&t.splice(c,1)}},U=()=>{let o=!1;const t=i.value,c=h.value,a=p.value;if(t.map(d=>{if(a){if(c.length>=16)return o=!0;c.find(r=>r.productId===d.productId)||c.push(d)}return d.isCheck=a}),o)return g.ElMessage.error("最多选择16个商品");a||t.forEach(d=>{const r=c.findIndex(f=>f.productId===d.productId);r!==-1&&c.splice(r,1)})},H=o=>{s.size=o,u()},J=o=>{s.current=o,p.value=!1,u()};async function K(){s.current=1,s.size=10,p.value=!1}async function R(){let o=!0;i.value.forEach(t=>{const c=V(t.productId);t.isCheck=c,o=o&&c}),p.value=o}async function Q(){const{code:o,data:t}=await w();o===200?t.records.length&&(y.value=t.records,s.choosedSession=t.records[0].startTime):g.ElMessage.error("获取场次失败")}async function u(){const{current:o,size:t,choosedSession:c}=s;if(!s.choosedSession)return g.ElMessage.warning("请先选中场次");const{code:a,data:d}=await I({current:o,size:t,startTime:c});if(a!==200)return g.ElMessage.error("获取商品失败");if(d.records.length){let r=!0;d.records.forEach(f=>{const k=V(f.productId);f.isCheck=k,r=r&&k}),p.value=r,i.value=d.records,s.total=d.total}}function V(o){return h.value.findIndex(t=>t.productId===o)!==-1}return(o,t)=>{const c=e.resolveComponent("el-option"),a=e.resolveComponent("el-option-group"),d=e.resolveComponent("el-select"),r=e.resolveComponent("i-ep-check"),f=e.resolveComponent("el-icon"),k=e.resolveComponent("el-checkbox");return e.openBlock(),e.createElementBlock("div",null,[B,e.createElementVNode("div",P,[e.createElementVNode("div",D,[e.createElementVNode("div",z,[e.createVNode(d,{modelValue:e.unref(s).choosedSession,"onUpdate:modelValue":t[0]||(t[0]=n=>e.unref(s).choosedSession=n),style:{width:"190px"},placeholder:"全部分类"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value,n=>(e.openBlock(),e.createBlock(a,{key:n.startTime},{default:e.withCtx(()=>[e.createVNode(c,{label:n.startTime,value:n.startTime,onClick:E=>$(n.startTime)},null,8,["label","value","onClick"])]),_:2},1024))),128))]),_:1},8,["modelValue"])]),i.value.length>0?(e.openBlock(),e.createElementBlock("div",T,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value,(n,E)=>(e.openBlock(),e.createElementBlock("div",{key:E,class:"digGoods__box--content--good",style:e.normalizeStyle({border:n.isCheck?G.borderGet:G.borderNoGet}),onClick:ee=>F(n)},[e.createElementVNode("img",{src:n.productPic,class:"digGoods__box--content--good--img"},null,8,M),n.isCheck?(e.openBlock(),e.createElementBlock("div",L,[e.createVNode(f,{color:"#fff",size:"40px"},{default:e.withCtx(()=>[e.createVNode(r)]),_:1})])):e.createCommentVNode("",!0),e.createElementVNode("div",q,[e.createElementVNode("div",null,e.toDisplayString(n.productName),1),e.createElementVNode("div",null," ￥"+e.toDisplayString(e.unref(x)(n.minPrice))+"-￥"+e.toDisplayString(e.unref(x)(n.maxPrice)),1)])],12,j))),128))])):e.createCommentVNode("",!0),i.value.length===0?(e.openBlock(),e.createElementBlock("div",v," 暂无相关商品信息，请选择其他时段 ")):e.createCommentVNode("",!0),e.createElementVNode("div",O,[e.createVNode(k,{modelValue:p.value,"onUpdate:modelValue":t[1]||(t[1]=n=>p.value=n),style:{"margin-top":"40px"},onChange:U},{default:e.withCtx(()=>[e.createTextVNode("全选")]),_:1},8,["modelValue"]),e.createVNode(_,{"page-size":e.unref(s).size,"page-num":e.unref(s).current,total:e.unref(s).total,onHandleSizeChange:H,onHandleCurrentChange:J},null,8,["page-size","page-num","total"])])])])])}}}),X="";return((l,C)=>{const m=l.__vccOpts||l;for(const[x,i]of C)m[x]=i;return m})(A,[["__scopeId","data-v-9470572c"]])});
