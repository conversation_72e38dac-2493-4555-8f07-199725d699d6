(function(e,T){typeof exports=="object"&&typeof module<"u"?module.exports=T(require("vue"),require("vue-router"),require("@vueuse/core"),require("@/utils/date"),require("@/composables/useConvert"),require("@/components/pageManage/PageManage.vue"),require("@/store/modules/shopInfo"),require("element-plus"),require("@/apis/good"),require("@/apis/decoration"),require("@element-plus/icons-vue"),require("@/apis/http"),require("lodash")):typeof define=="function"&&define.amd?define(["vue","vue-router","@vueuse/core","@/utils/date","@/composables/useConvert","@/components/pageManage/PageManage.vue","@/store/modules/shopInfo","element-plus","@/apis/good","@/apis/decoration","@element-plus/icons-vue","@/apis/http","lodash"],T):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopCouponInfo=T(e.ShopCouponInfoContext.Vue,e.ShopCouponInfoContext.VueRouter,e.ShopCouponInfoContext.VueUse,e.ShopCouponInfoContext.DateUtil,e.ShopCouponInfoContext.UseConvert,e.ShopCouponInfoContext.PageManage,e.ShopCouponInfoContext.ShopInfoStore,e.ShopCouponInfoContext.ElementPlus,e.ShopCouponInfoContext.GoodAPI,e.ShopCouponInfoContext.DecorationAPI,e.ShopCouponInfoContext.ElementPlusIconsVue,e.ShopCouponInfoContext.Request,e.ShopCouponInfoContext.Lodash))})(this,function(e,T,ne,W,B,le,de,w,ae,K,J,G,re){"use strict";var X=document.createElement("style");X.textContent=`@charset "UTF-8";.title[data-v-685ed522]{font-size:15px;font-weight:700;display:flex;margin-bottom:20px;margin-top:-40px}.digGoods[data-v-685ed522]{border-top:1px solid #d7d7d7;padding-top:10px}.digGoods__box[data-v-685ed522]{background-color:#f2f2f2;padding:10px}.digGoods__box--top[data-v-685ed522]{display:flex;justify-content:space-between}.digGoods__box--content[data-v-685ed522]{margin-top:10px;background-color:#fff;border-radius:5px;display:flex;flex-wrap:wrap;padding:5px}.digGoods__box--content--good[data-v-685ed522]{width:33%;margin-left:2px;margin-bottom:4px;height:80px;border-radius:5px;padding:5px;display:flex}.digGoods__box--content--good--img[data-v-685ed522]{width:65px;height:65px;position:relative}.digGoods__box--content--good--imgShadow[data-v-685ed522]{width:65px;height:65px;position:absolute;background-color:#0009;display:flex;justify-content:center;align-items:center}.digGoods__box--content--good--shopName[data-v-685ed522]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-between;align-items:flex-start;margin-left:10px;padding:5px;font-size:12px}.digGoods__box--content--good--shopName--name[data-v-685ed522]{width:150px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.bottom[data-v-685ed522]{display:flex;justify-content:space-between;align-items:flex-end}.bottom-container[data-v-685ed522]{display:flex;justify-content:center;align-items:center;flex-direction:column}.title[data-v-64abf646]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-64abf646]{font-size:12px;margin-left:12px;color:#c4c4c4}.rules[data-v-64abf646]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-64abf646]{width:300px;display:flex}.text[data-v-64abf646]{font-size:14px;color:#333}.goodsData[data-v-64abf646]{border:1px solid #ccc}.goods-list[data-v-64abf646]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-64abf646]{display:flex}.goods-list__goods-list__info-name[data-v-64abf646]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-64abf646]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-64abf646]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-64abf646]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-64abf646]{font-size:16px}.ruleform-date[data-v-64abf646]{width:100%;display:flex;align-items:center}.flex[data-v-64abf646]{margin-top:10px;height:50px}.flex-item[data-v-64abf646]{width:40%}.coupon-rules[data-v-64abf646]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-64abf646]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild(X);const{divTenThousand:ie,mulTenThousand:se}=B(),pe={PRICE_DISCOUNT:"无门槛折扣券",PRICE_REDUCE:"无门槛现金券",REQUIRED_PRICE_DISCOUNT:"满折券",REQUIRED_PRICE_REDUCE:"满减券"};var L=(l=>(l.PERIOD="PERIOD",l.IMMEDIATELY="IMMEDIATELY",l))(L||{});function Z(l){return l?se(l):""}function v(l){return l?ie(l).toString():""}function ue(l){const{name:a,days:V,type:m,endDate:x,productIds:C,productType:s,requiredAmount:c,discount:N,amount:h,effectType:b,startDate:o,num:S,limit:q,id:M,shopId:P}=l;return{shopId:P,id:M,name:a,days:V,endDate:x,productType:s,type:m,requiredAmount:v(c).toString(),discount:N,amount:v(h),effectType:b,startDate:o,num:S,limit:q,productIds:C}}const ce=l=>{const a=new W;return l.status!=="OK"?"违规下架":l.effectType==="PERIOD"&&l.endDate>=a.getYMDs()?l.stock==="0"?"已结束":l.startDate>a.getYMDs()?"未开始":"进行中":l.effectType==="PERIOD"&&l.endDate<a.getYMDs()?"已结束":l.effectType==="IMMEDIATELY"?l.stock!=="0"?"进行中":"已结束":""},me=e.defineComponent({__name:"select-couppon-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(l,{emit:a}){const V=l,m=a,x=ne.useVModel(V,"modelValue",m);return(C,s)=>{const c=e.resolveComponent("el-option"),N=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(N,{modelValue:e.unref(x),"onUpdate:modelValue":s[0]||(s[0]=h=>e.isRef(x)?x.value=h:null),placeholder:V.placeholder,style:{width:"150px"},onChange:s[1]||(s[1]=h=>m("change",h))},{default:e.withCtx(()=>[e.renderSlot(C.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Object.values(V.list).length?V.list:e.unref(pe),(h,b)=>(e.openBlock(),e.createBlock(c,{key:b,label:h,value:b},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),fe={class:"digGoods"},ge={class:"digGoods__box"},xe={class:"digGoods__box--top"},Ce={key:0,class:"digGoods__box--content"},Ve=["onClick"],_e=["src"],ye={key:0,class:"digGoods__box--content--good--imgShadow"},he={class:"digGoods__box--content--good--shopName"},be={class:"digGoods__box--content--good--shopName--name"},Ne={key:1,class:"digGoods__box--content",style:{display:"flex","justify-content":"center","align-items":"center",height:"250px"}},Ee={class:"bottom"},we=e.defineComponent({__name:"choose-goods-popup",props:{pointGoodsList:{type:Array,default(){return[]}},goodsVisible:{type:Boolean,default:!1}},setup(l,{expose:a}){const V=l,{divTenThousand:m}=B(),x=e.reactive({size:10,current:1,total:0}),C=e.reactive({size:10,current:1,total:0}),s=e.ref([]),c=e.ref([]),N=e.ref([]),h=e.ref(""),b=e.ref(!1),o=e.shallowReactive({maxPrice:"",minPrice:"",salePrice:"",keyword:"",categoryFirstId:""});e.watch(V,u=>{if(c.value.length!==u.pointGoodsList.length){const r=c.value.filter(i=>{const n=i.id;return u.pointGoodsList.findIndex(t=>t.id===n)===-1});c.value.forEach(i=>{r.forEach(n=>{n.id===i.id&&(i.isCheck=!1)})}),c.value=c.value.filter(i=>i.isCheck)}},{deep:!0}),a({tempGoods:c,search:o,goodsList:s});const S={borderGet:"2px solid #2D8CF0",borderNoGet:"2px solid #f2f2f2"};e.onMounted(()=>{$()});const q=u=>{o.categoryFirstId=u.id,k()},M=()=>{k()},P=u=>{u.isCheck=!u.isCheck;const r=c.value;if(u.isCheck)r.push(u);else{const i=r.findIndex(n=>n.id===u.id);i!==-1&&r.splice(i,1)}},U=()=>{const u=s.value,r=c.value,i=b.value;u.map(n=>(i&&(r.find(t=>t.id===n.id)||r.push(n)),n.isCheck=i)),i||u.forEach(n=>{const t=r.findIndex(g=>g.id===n.id);t!==-1&&r.splice(t,1)})},Y=u=>{x.size=u,k()},O=u=>{x.current=u,b.value=!1,k()};async function $(){const{code:u,data:r}=await ae.doGetHighestCategoryLevel({size:1e3});u===200?(N.value=r.records,x.total=r.total):w.ElMessage.error("获取分类列表失败")}async function k(){const u=de.useShopInfoStore().shopInfo.id;(o.minPrice||o.maxPrice)&&(o.salePrice=`${o.minPrice}_${o.maxPrice}`);const{code:r,data:i}=await K.doGetRetrieveCommodity({...o,...C,shopId:u});if(r===200){let n=!0;i.list.forEach(t=>{const g=z(t.id);t.isCheck=g,n=n&&g}),b.value=n,s.value=i.list,C.total=i.total}else w.ElMessage.error("获取商品失败")}function z(u){return c.value.findIndex(r=>r.id===u)!==-1}function F(u){const r=m(Math.max(...u)).toString(),i=m(Math.min(...u)).toString();return u.length>1?`${i}~${r}`:i}return(u,r)=>{const i=e.resolveComponent("el-option"),n=e.resolveComponent("el-option-group"),t=e.resolveComponent("el-select"),g=e.resolveComponent("el-input"),f=e.resolveComponent("el-button"),_=e.resolveComponent("el-icon"),y=e.resolveComponent("el-checkbox");return e.openBlock(),e.createElementBlock("div",null,[r[9]||(r[9]=e.createElementVNode("div",{class:"title"},"选择商品",-1)),e.createElementVNode("div",fe,[e.createElementVNode("div",ge,[e.createElementVNode("div",xe,[e.createVNode(t,{modelValue:h.value,"onUpdate:modelValue":r[0]||(r[0]=p=>h.value=p),placeholder:"全部分类",style:{width:"120px"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(N.value,p=>(e.openBlock(),e.createBlock(n,{key:p.id},{default:e.withCtx(()=>[e.createVNode(i,{label:p.name,value:p.name,onClick:I=>q(p)},null,8,["label","value","onClick"])]),_:2},1024))),128))]),_:1},8,["modelValue"]),e.createElementVNode("div",null,[r[6]||(r[6]=e.createElementVNode("span",{style:{margin:"0px 10px 0px 25px",color:"#a1a1a1",width:"60px","line-height":"32px"}},"价格",-1)),e.createVNode(g,{modelValue:e.unref(o).minPrice,"onUpdate:modelValue":r[1]||(r[1]=p=>e.unref(o).minPrice=p),maxlength:"20",style:{width:"60px"}},null,8,["modelValue"]),r[7]||(r[7]=e.createElementVNode("span",{style:{margin:"0px 5px","line-height":"32px"}},"-",-1)),e.createVNode(g,{modelValue:e.unref(o).maxPrice,"onUpdate:modelValue":r[2]||(r[2]=p=>e.unref(o).maxPrice=p),maxlength:"20",style:{width:"60px"}},null,8,["modelValue"])]),e.createVNode(g,{modelValue:e.unref(o).keyword,"onUpdate:modelValue":r[3]||(r[3]=p=>e.unref(o).keyword=p),class:"input-with-select",maxlength:"20",placeholder:"请输入关键词",style:{width:"200px","margin-left":"10px"}},{append:e.withCtx(()=>[e.createVNode(f,{icon:e.unref(J.Search),onClick:M},null,8,["icon"])]),_:1},8,["modelValue"])]),s.value.length>0?(e.openBlock(),e.createElementBlock("div",Ce,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value,(p,I)=>(e.openBlock(),e.createElementBlock("div",{key:I,style:e.normalizeStyle({border:p.isCheck?S.borderGet:S.borderNoGet}),class:"digGoods__box--content--good",onClick:E=>P(p)},[e.createElementVNode("img",{src:p.pic,class:"digGoods__box--content--good--img"},null,8,_e),p.isCheck?(e.openBlock(),e.createElementBlock("div",ye,[e.createVNode(_,{color:"#fff",size:"40px"},{default:e.withCtx(()=>[e.createVNode(e.unref(J.Check))]),_:1})])):e.createCommentVNode("",!0),e.createElementVNode("div",he,[e.createElementVNode("div",be,e.toDisplayString(p.productName),1),e.createElementVNode("div",null," ￥"+e.toDisplayString(p.salePrices&&F(p.salePrices)),1)])],12,Ve))),128))])):e.createCommentVNode("",!0),s.value.length===0?(e.openBlock(),e.createElementBlock("div",Ne," 暂无相关商品信息，请选择其他分类 ")):e.createCommentVNode("",!0),e.createElementVNode("div",Ee,[e.createVNode(y,{modelValue:b.value,"onUpdate:modelValue":r[4]||(r[4]=p=>b.value=p),style:{"margin-top":"40px"},onChange:U},{default:e.withCtx(()=>r[8]||(r[8]=[e.createTextVNode("全选")])),_:1},8,["modelValue"]),e.createVNode(le,{modelValue:C,"onUpdate:modelValue":r[5]||(r[5]=p=>C=p),"load-init":!0,total:C.total,background:"",onReload:k,onHandleSizeChange:Y,onHandleCurrentChange:O},null,8,["modelValue","total"])])])])])}}}),Qe="",ee=(l,a)=>{const V=l.__vccOpts||l;for(const[m,x]of a)V[m]=x;return V},Ie=ee(we,[["__scopeId","data-v-685ed522"]]),De=l=>G.post({url:"addon-coupon/coupon",data:l}),ke=(l,a)=>G.get({url:`addon-coupon/coupon/shop/${l}/${a}`}),Te=(l,a)=>G.put({url:`addon-coupon/coupon/${l}`,data:a}),Se=(l,a)=>G.put({url:`addon-coupon/coupon/${l}/working`,data:a}),qe=(l,a,V)=>{if(l==="PRICE_REDUCE"){Re(l,a);return}if(l==="PRICE_DISCOUNT"){te(l,a);return}if(l==="REQUIRED_PRICE_REDUCE"){te(l,a),Me(l,a,V);return}if(l==="REQUIRED_PRICE_DISCOUNT"){const m=[{required:!0,message:"请输入金额",trigger:"blur"},{validator:(C,s,c)=>{Number(s)<=0?c(new Error("金额必须大于0")):c()},trigger:"blur"}],x=[{required:!0,message:"请输入优惠券规则",trigger:"blur"},{validator:(C,s,c)=>{Number(s)<=0&&Number(s)<100?c(new Error("0.1~99之间")):c()},trigger:"blur"}];a.requiredAmount=m,a.discount=x}},Re=(l,a)=>{a.discount.length&&(a.discount[0].required=!1),a.requiredAmount.length&&(a.discount[0].required=!1);const V=[{required:!0,message:"请输入优惠券规则",trigger:"blur"},{validator:(m,x,C)=>{Number(x)<=0?C(new Error("金额必须大于0")):C()},trigger:"blur"}];a.amount=V},te=(l,a)=>{a.requiredAmount.length&&(a.discount[0].required=!1),a.amount.length&&(a.amount[0].required=!1);const V=[{required:!0,message:"请输入优惠券规则",trigger:"blur"},{validator:(m,x,C)=>{Number(x)<=0&&Number(x)<100?C(new Error("0.1~99之间")):C()},trigger:"blur"}];a.discount=V},Me=(l,a,V)=>{a.discount.length&&(a.discount[0].required=!1);const m=[{required:!0,message:"请输入金额",trigger:"blur"},{validator:(C,s,c)=>{Number(s)<=0?c(new Error("金额必须大于0")):c()},trigger:"blur"}],x=[...m,{validator:(C,s,c)=>{Number(V.requiredAmount)-Number(s)<=0?c(new Error(`满减金额必须小于${Number(V.requiredAmount)}`)):c()},trigger:"blur"}];a.amount=x,a.requiredAmount=m},Pe={style:{padding:"0 40px"}},Ge={key:0,class:"ruleform-date"},Ue={class:"period-validity text"},Ae={class:"coupon-rules"},Be={key:2,class:"flex",style:{width:"100%"}},Le={key:3,class:"flex"},Ye={class:"goods-list__info"},Oe={class:"goods-list__goods-list__info-name"},$e={class:"goods-list__goods-list__info-name--name"},ze={class:"goods-list__goods-list__info-name--price"},Fe={key:0,class:"text"},je={class:"dialog-footer"},He=e.defineComponent({__name:"ShopCouponInfo",setup(l){const a=T.useRouter(),V=T.useRoute(),m=new W,x=e.ref(!1),C=e.ref(""),s=e.ref([]),c=e.ref(""),N=e.ref(),h=e.ref(!1),b=e.ref(),o=e.ref({name:"",days:1,endDate:"",productType:"SHOP_ALL",type:"PRICE_DISCOUNT",requiredAmount:1,discount:1,amount:1,effectType:L.PERIOD,startDate:"",num:1,limit:1,shopId:"",productIds:[],createTime:"",usedCount:"",userGetType:"DEFAULT"}),S=B(),q={name:[{required:!0,message:"请输入优惠券名称",trigger:"blur"}],effectType:[{required:!0,message:"请选择有效时间",trigger:["blur","change"]}],userGetType:[{required:!0,message:"请选择领取方式",trigger:"blur"}],type:[{required:!0,message:"请选择优惠券类型",trigger:["blur","change"]}],discount:[{required:!0,message:"请输入优惠券规则",trigger:"blur"}],num:[{required:!0,message:"请输入发行量",trigger:"blur"}],limit:[{required:!0,message:"请输入每人发送数量",trigger:"blur"}],requiredAmount:[{required:!0,message:"请输入优惠券规则",trigger:"blur"}],amount:[{required:!0,message:"请输入优惠券规则",trigger:"blur"}],productType:[{required:!0,message:"请选择优惠券类型",trigger:["blur","change"]}],days:[{required:!0,message:"请输入优惠券有效期",trigger:"blur"},{validator:(n,t,g)=>{Number(t)===0?g("最少1天"):g()},trigger:"blur"}],startDate:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]}],endDate:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]}]};e.watch(()=>V,async n=>{var f;if(!n.query.id||!n.query.shopId)return;const{code:t,data:g}=await ke(n.query.shopId.toString(),n.query.id.toString());if(t!==200){w.ElMessage.error("获取优惠券信息失败");return}if(o.value=ue(g),C.value=ce(g),c.value=g.endDate,x.value=!0,(f=g.productIds)!=null&&f.length){const{code:_,data:y}=await K.doGetRetrieveCommodity({productId:g.productIds});if(_!==200){w.ElMessage.error("获取商品信息失败");return}s.value=y.list.map(p=>({...p,isCheck:!0}))}},{immediate:!0});const M=async()=>{var g,f;if(!N.value)return;const{num:n,limit:t}=o.value;if(!n||!t){w.ElMessage.error("发行量或每人发送数量只能是正整数和数字");return}try{if(!await N.value.validate())return;if(["ASSIGNED","ASSIGNED_NOT"].includes(o.value.productType)&&!s.value.length){s.value.length,w.ElMessage.error("请选择指定商品");return}U();const y=Y();o.value.productType==="SHOP_ALL"&&(y.productIds=null),P(y);const p=o.value.id,{code:I}=await(p?C.value==="进行中"?Se(p,{name:y.name,days:y.days,endDate:y.endDate,productType:y.productType,productIds:y.productIds}):Te(p,y):De(y));if(I!==200){w.ElMessage.error("保存失败");return}w.ElMessage.success("保存成功"),z(),a.push({name:"coupons"})}catch(_){if((g=_.requiredAmount)!=null&&g.length){w.ElMessage.info(_.requiredAmount[0].message);return}if((f=_.amount)!=null&&f.length){w.ElMessage.info(_.amount[0].message);return}}};function P(n){n.effectType==="PERIOD"&&(n.days=null),n.effectType==="IMMEDIATELY"&&(n.startDate="",n.endDate=""),n.type==="PRICE_REDUCE"&&(n.requiredAmount=null,n.discount=null),n.type==="PRICE_DISCOUNT"&&(n.amount=null,n.requiredAmount=null),n.type==="REQUIRED_PRICE_REDUCE"&&(n.discount=null),n.type==="REQUIRED_PRICE_DISCOUNT"&&(n.amount=null)}const U=()=>{if(o.value.effectType==="PERIOD"){o.value.days=1;return}o.value.startDate="",o.value.endDate=""};function Y(){const{name:n,days:t,endDate:g,productType:f,userGetType:_,type:y,requiredAmount:p,discount:I,amount:E,effectType:D,startDate:A,num:j,limit:H,productIds:R,shopId:Q}=o.value;return{name:n,days:t,endDate:g,productType:f,userGetType:_,type:y,requiredAmount:Z(p).toString(),discount:I,amount:Z(E).toString(),effectType:D,startDate:A,num:j,shopId:Q,limit:H,productIds:s.value.length?s.value.map(d=>d.productId):null}}const O=()=>{h.value=!1},$=()=>{s.value=re.cloneDeep(b.value.tempGoods),h.value=!1},k=async n=>{await w.ElMessageBox.confirm("确定移除该商品?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&(s.value=s.value.filter(g=>g.productId!==n))};function z(){N.value&&(N.value.resetFields(),o.value={name:"",days:1,endDate:"",productType:"SHOP_ALL",type:"PRICE_DISCOUNT",requiredAmount:1,discount:1,amount:1,effectType:L.PERIOD,startDate:"",num:1,shopId:"",limit:1,productIds:[],createTime:"",usedCount:"",userGetType:0},s.value=[])}const F=()=>{qe(o.value.type,q,o.value),o.value.amount=1,o.value.requiredAmount=1,o.value.discount=1},u=n=>x.value?c.value<m.getYMDs(n):!!o.value.endDate?o.value.endDate<m.getYMDs(n)||m.getYMDs(n)<m.getYMDs(new Date):m.getYMDs(n)<m.getYMDs(new Date),r=n=>x.value?c.value>m.getYMDs(n):!!o.value.startDate?o.value.startDate>m.getYMDs(n)||m.getYMDs(n)<m.getYMDs(new Date):!1,i=n=>x.value&&!n.includes(C.value);return(n,t)=>{const g=e.resolveComponent("el-input"),f=e.resolveComponent("el-form-item"),_=e.resolveComponent("el-radio"),y=e.resolveComponent("el-radio-group"),p=e.resolveComponent("el-row"),I=e.resolveComponent("el-date-picker"),E=e.resolveComponent("el-input-number"),D=e.resolveComponent("el-table-column"),A=e.resolveComponent("el-table"),j=e.resolveComponent("el-image"),H=e.resolveComponent("el-link"),R=e.resolveComponent("el-button"),Q=e.resolveComponent("el-form"),oe=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",Pe,[t[43]||(t[43]=e.createElementVNode("h1",{class:"title"},"基本信息",-1)),e.createVNode(Q,{ref_key:"ruleFormRef",ref:N,"inline-message":!1,model:o.value,rules:q,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(f,{label:"优惠券名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(g,{modelValue:o.value.name,"onUpdate:modelValue":t[0]||(t[0]=d=>o.value.name=d),modelModifiers:{trim:!0},disabled:i(["未开始","进行中"]),maxlength:"5",placeholder:"请输入优惠券名称",style:{width:"551px"}},null,8,["modelValue","disabled"]),t[20]||(t[20]=e.createElementVNode("span",{class:"msg"},"优惠券名称不超过5个字",-1))]),_:1}),e.createVNode(f,{label:"领取方式",prop:"userGetType"},{default:e.withCtx(()=>[e.createVNode(p,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:o.value.userGetType,"onUpdate:modelValue":t[1]||(t[1]=d=>o.value.userGetType=d),disabled:i(["未开始"]),class:"ml-4"},{default:e.withCtx(()=>[e.createVNode(_,{label:"DEFAULT"},{default:e.withCtx(()=>t[21]||(t[21]=[e.createTextVNode("用户主动领取")])),_:1}),e.createVNode(_,{label:"SYSTEM"},{default:e.withCtx(()=>t[22]||(t[22]=[e.createTextVNode("系统自动发放")])),_:1})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e.createVNode(f,{label:"有效时间",prop:"effectType"},{default:e.withCtx(()=>[e.createVNode(p,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:o.value.effectType,"onUpdate:modelValue":t[2]||(t[2]=d=>o.value.effectType=d),disabled:i(["未开始"]),class:"ml-4"},{default:e.withCtx(()=>[e.createVNode(_,{label:"PERIOD"},{default:e.withCtx(()=>t[23]||(t[23]=[e.createTextVNode("固定时间")])),_:1}),e.createVNode(_,{label:"IMMEDIATELY"},{default:e.withCtx(()=>t[24]||(t[24]=[e.createTextVNode("领券立即生效")])),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),e.createVNode(p,{style:{"margin-top":"20px"}},{default:e.withCtx(()=>[o.value.effectType==="PERIOD"?(e.openBlock(),e.createElementBlock("div",Ge,[e.createVNode(f,{"inline-message":!1,prop:"startDate"},{default:e.withCtx(()=>[e.createVNode(I,{modelValue:o.value.startDate,"onUpdate:modelValue":t[3]||(t[3]=d=>o.value.startDate=d),disabled:i(["未开始"]),"disabled-date":u,format:"YYYY/MM/DD",placeholder:"请选择开始时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","disabled"]),t[25]||(t[25]=e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1))]),_:1}),e.createVNode(f,{"inline-message":!1,prop:"endDate"},{default:e.withCtx(()=>[e.createVNode(I,{modelValue:o.value.endDate,"onUpdate:modelValue":t[4]||(t[4]=d=>o.value.endDate=d),disabled:i(["未开始","进行中"]),"disabled-date":r,format:"YYYY/MM/DD",placeholder:"请选择结束时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue","disabled"])]),_:1})])):(e.openBlock(),e.createBlock(f,{key:1,prop:"days"},{default:e.withCtx(()=>[e.createElementVNode("div",Ue,[t[26]||(t[26]=e.createTextVNode(" 领券当日起 ")),e.createVNode(E,{modelValue:o.value.days,"onUpdate:modelValue":t[5]||(t[5]=d=>o.value.days=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始","进行中"]),max:99999,min:o.value.days?1:0,style:{width:"20%",margin:"0 5px"}},null,8,["modelValue","disabled","min"]),t[27]||(t[27]=e.createTextVNode(" 天内可用 "))])]),_:1}))]),_:1})]),_:1}),e.createVNode(f,{label:"活动规则"},{default:e.withCtx(()=>[e.createVNode(A,{"cell-style":{height:"60px"},data:[{}],"header-cell-style":{textAlign:"center",fontSize:"14px",color:"#606266"},border:"",style:{width:"90%"}},{default:e.withCtx(()=>[e.createVNode(D,{label:"选择优惠券类型",width:"170"},{default:e.withCtx(()=>[e.createVNode(f,{prop:"type"},{default:e.withCtx(()=>[e.createVNode(me,{modelValue:o.value.type,"onUpdate:modelValue":t[6]||(t[6]=d=>o.value.type=d),disabled:i(["未开始"]),placeholder:"全部类型",onChange:F},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e.createVNode(D,{label:"优惠券规则",width:"280"},{default:e.withCtx(()=>[e.createElementVNode("div",Ae,[o.value.type==="PRICE_DISCOUNT"?(e.openBlock(),e.createBlock(f,{key:0,prop:"discount"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:o.value.discount,"onUpdate:modelValue":t[7]||(t[7]=d=>o.value.discount=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:9.9,min:.1,precision:1,style:{width:"40%"}},null,8,["modelValue","disabled"]),t[28]||(t[28]=e.createElementVNode("span",{style:{"margin-left":"10px"}},"折，无门槛使用",-1))]),_:1})):e.createCommentVNode("",!0),o.value.type==="PRICE_REDUCE"?(e.openBlock(),e.createBlock(f,{key:1,prop:"amount"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:o.value.amount,"onUpdate:modelValue":t[8]||(t[8]=d=>o.value.amount=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:999999,min:0,style:{width:"40%"}},null,8,["modelValue","disabled"]),t[29]||(t[29]=e.createElementVNode("span",{style:{"margin-left":"5px"}},"元，无门槛使用",-1))]),_:1})):e.createCommentVNode("",!0),o.value.type==="REQUIRED_PRICE_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",Be,[e.createVNode(f,{"label-width":"0%",prop:"requiredAmount"},{default:e.withCtx(()=>[t[30]||(t[30]=e.createElementVNode("span",null,"满",-1)),e.createVNode(E,{modelValue:o.value.requiredAmount,"onUpdate:modelValue":t[9]||(t[9]=d=>o.value.requiredAmount=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:99999,min:0,style:{width:"60%",margin:"0 5px"}},null,8,["modelValue","disabled"]),t[31]||(t[31]=e.createElementVNode("span",null,"元,打",-1))]),_:1}),e.createVNode(f,{"label-width":"0%",prop:"discount"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:o.value.discount,"onUpdate:modelValue":t[10]||(t[10]=d=>o.value.discount=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:9.9,min:.1,precision:1,style:{width:"60%","margin-left":"5px"}},null,8,["modelValue","disabled"]),t[32]||(t[32]=e.createElementVNode("span",{style:{"margin-left":"5px"}},"折",-1))]),_:1})])):e.createCommentVNode("",!0),o.value.type==="REQUIRED_PRICE_REDUCE"?(e.openBlock(),e.createElementBlock("div",Le,[e.createVNode(f,{"label-width":"0%",prop:"requiredAmount"},{default:e.withCtx(()=>[t[33]||(t[33]=e.createElementVNode("span",null,"满",-1)),e.createVNode(E,{modelValue:o.value.requiredAmount,"onUpdate:modelValue":t[11]||(t[11]=d=>o.value.requiredAmount=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:999999,min:0,style:{width:"80%","margin-left":"5px"}},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(f,{"label-width":"0%",prop:"amount"},{default:e.withCtx(()=>[t[34]||(t[34]=e.createElementVNode("span",null,"元,减",-1)),e.createVNode(E,{modelValue:o.value.amount,"onUpdate:modelValue":t[12]||(t[12]=d=>o.value.amount=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:999999,min:0,style:{width:"60%","margin-left":"5px"}},null,8,["modelValue","disabled"]),t[35]||(t[35]=e.createElementVNode("span",{style:{"margin-left":"5px"}},"元",-1))]),_:1})])):e.createCommentVNode("",!0)])]),_:1}),e.createVNode(D,{label:"发行量（张）"},{default:e.withCtx(()=>[e.createVNode(f,{prop:"num"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:o.value.num,"onUpdate:modelValue":t[13]||(t[13]=d=>o.value.num=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:99999,min:1},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e.createVNode(D,{label:"每人发送（张）"},{default:e.withCtx(()=>[e.createVNode(f,{prop:"limit"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:o.value.limit,"onUpdate:modelValue":t[14]||(t[14]=d=>o.value.limit=d),modelModifiers:{number:!0},controls:!1,disabled:i(["未开始"]),max:99999,min:1},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1}),e.createVNode(f,{label:"商品选择",prop:"productType"},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:o.value.productType,"onUpdate:modelValue":t[15]||(t[15]=d=>o.value.productType=d),disabled:i(["未开始","进行中"])},{default:e.withCtx(()=>[e.createVNode(_,{label:"SHOP_ALL"},{default:e.withCtx(()=>t[36]||(t[36]=[e.createTextVNode("全部商品参与")])),_:1}),e.createVNode(_,{label:"ASSIGNED"},{default:e.withCtx(()=>t[37]||(t[37]=[e.createTextVNode("指定商品参与")])),_:1}),e.createVNode(_,{label:"ASSIGNED_NOT"},{default:e.withCtx(()=>t[38]||(t[38]=[e.createTextVNode("指定商品不参与")])),_:1})]),_:1},8,["modelValue","disabled"]),o.value.productType!=="SHOP_ALL"&&s.value.length?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass([s.value.length&&"goodsData","goods-list"])},[e.createVNode(A,{data:s.value,"header-cell-style":{fontSize:"14px",color:"#606266",background:"#f2f2f2",height:"54px",fontWeight:"normal"},height:"260px",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(D,{label:"商品信息"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",Ye,[e.createVNode(j,{"preview-src-list":[d.pic],"preview-teleported":!0,src:d.pic,fit:"",style:{width:"60px",height:"60px"}},null,8,["preview-src-list","src"]),e.createElementVNode("div",Oe,[e.createElementVNode("div",$e,e.toDisplayString(d.productName),1),e.createElementVNode("div",ze,e.toDisplayString(e.unref(S).divTenThousand(d.salePrices[0])),1)])])]),_:1}),e.createVNode(D,{label:"操作",width:"80px"},{default:e.withCtx(({row:d})=>[e.createVNode(H,{disabled:i(["未开始"]),underline:!1,type:"primary",onClick:Je=>k(d.productId)},{default:e.withCtx(()=>t[39]||(t[39]=[e.createTextVNode("删除 ")])),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])],2)):e.createCommentVNode("",!0),o.value.productType!=="SHOP_ALL"?(e.openBlock(),e.createBlock(p,{key:1,justify:"space-between",style:{width:"90%","margin-top":"10px"}},{default:e.withCtx(()=>[e.createVNode(R,{disabled:i(["未开始","进行中"]),plain:"",round:"",type:"primary",onClick:t[16]||(t[16]=d=>h.value=!0)},{default:e.withCtx(()=>t[40]||(t[40]=[e.createTextVNode("选择商品 ")])),_:1},8,["disabled"]),o.value.productType!=="SHOP_ALL"&&s.value.length?(e.openBlock(),e.createElementBlock("span",Fe,"已选择"+e.toDisplayString(s.value.length)+"款商品",1)):e.createCommentVNode("",!0)]),_:1})):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["model"]),e.createVNode(p,{class:"nav-button",justify:"center"},{default:e.withCtx(()=>[e.createVNode(R,{plain:"",round:"",onClick:t[17]||(t[17]=d=>e.unref(a).back())},{default:e.withCtx(()=>t[41]||(t[41]=[e.createTextVNode("返回")])),_:1}),e.createVNode(R,{disabled:i(["","未开始","进行中"]),round:"",type:"primary",onClick:M},{default:e.withCtx(()=>t[42]||(t[42]=[e.createTextVNode("保存 ")])),_:1},8,["disabled"])]),_:1})]),e.createVNode(oe,{modelValue:h.value,"onUpdate:modelValue":t[19]||(t[19]=d=>h.value=d),"before-close":O,width:"800px"},{footer:e.withCtx(()=>[e.createElementVNode("span",je,[e.createVNode(R,{onClick:t[18]||(t[18]=d=>h.value=!1)},{default:e.withCtx(()=>t[44]||(t[44]=[e.createTextVNode("取消")])),_:1}),e.createVNode(R,{type:"primary",onClick:$},{default:e.withCtx(()=>t[45]||(t[45]=[e.createTextVNode(" 确认 ")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(Ie,{ref_key:"chooseGoodsPopupRef",ref:b,"point-goods-list":s.value},null,8,["point-goods-list"])]),_:1},8,["modelValue"])])}}}),We="";return ee(He,[["__scopeId","data-v-64abf646"]])});
