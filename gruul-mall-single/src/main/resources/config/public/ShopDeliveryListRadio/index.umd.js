(function(e,t){typeof exports=="object"&&typeof module<"u"?module.exports=t(require("vue")):typeof define=="function"&&define.amd?define(["vue"],t):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopDeliveryListRadio=t(e.ShopDeliveryListRadioContext.Vue))})(this,function(e){"use strict";const t=(n,i)=>{const o=n.__vccOpts||n;for(const[c,d]of i)o[c]=d;return o},r={};function s(n,i){const o=e.resolveComponent("el-radio");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(o,{label:"EXPRESS",size:"small"},{default:e.withCtx(()=>[e.createTextVNode("手动发货")]),_:1}),e.createVNode(o,{label:"PRINT_EXPRESS",size:"small"},{default:e.withCtx(()=>[e.createTextVNode("打印快递单并发货")]),_:1})],64)}return t(r,[["render",s]])});
