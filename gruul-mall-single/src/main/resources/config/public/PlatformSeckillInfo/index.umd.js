(function(e,x){typeof exports=="object"&&typeof module<"u"?module.exports=x(require("vue"),require("vue-router"),require("@/utils/date"),require("decimal.js"),require("element-plus"),require("@vueuse/core"),require("@/composables/useConvert"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/utils/date","decimal.js","element-plus","@vueuse/core","@/composables/useConvert","@/apis/http"],x):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformSeckillInfo=x(e.PlatformSeckillInfoContext.Vue,e.PlatformSeckillInfoContext.VueRouter,e.PlatformSeckillInfoContext.DateUtil,e.PlatformSeckillInfoContext.Decimal,e.PlatformSeckillInfoContext.ElementPlus,e.PlatformSeckillInfoContext.VueUse,e.PlatformSeckillInfoContext.UseConvert,e.PlatformSeckillInfoContext.Request))})(this,function(e,x,F,z,U,G,L,B){"use strict";var $=document.createElement("style");$.textContent=`@charset "UTF-8";.title[data-v-34b0c30d]{font-size:14px;color:#323233;font-weight:700;margin-bottom:20px}.msg[data-v-34b0c30d]{font-size:12px;color:#c4c4c4}.nav-button[data-v-34b0c30d]{width:1010px;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin:0 auto 0 -55px}.goods-list[data-v-34b0c30d]{width:100%;height:300px;overflow-x:scroll}.goods-list__info[data-v-34b0c30d]{width:100%;display:flex}.goods-list__goods-list__info-name[data-v-34b0c30d]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-34b0c30d]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-34b0c30d]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-34b0c30d]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.table-msg[data-v-34b0c30d]{font-size:12px;color:#838383}
`,document.head.appendChild($);const{divTenThousand:A,mulTenThousand:Ne}=L();function O(a,d){const i=[];return a.forEach(p=>{const{productId:m,productName:b,productPic:H}=p;p.secKillProductSkus.forEach(u=>{const{actualPaidPrice:C,seckillPrice:t,seckillStock:_,skuId:h,skuName:w,skuStock:S,stockType:M,limitType:q}=u,K={productId:m,productName:b,productPic:H,shopId:d,sku:{limitType:q,productId:m,stockType:M,seckillLimit:"",seckillPrice:A(t).toNumber(),seckillStock:Number(_),skuId:h,skuName:(w==null?void 0:w.split(" "))||[],skuPrice:C,actualPaidPrice:C,skuStock:S}};i.push(K)})}),i}let k=[],T=0;const W=a=>{k=[],T=0;for(let d=0;d<a.length;d++)d===0?(k.push(1),T=0):a[d].productId===a[d-1].productId?(k[T]+=1,k.push(0)):(k.push(1),T=d)},J=({row:a,column:d,rowIndex:i,columnIndex:p})=>{if(p===0){const m=k[i],b=m>0?1:0;return{rowspan:m,colspan:b}}},j="addon-seckill/seckillPromotion/",Q=(a,d)=>B.get({url:j+`${a}/${d}`}),X=(a,d,i)=>B.get({url:j+`secKillProduct/${a}/${d}`,params:i}),y=a=>(e.pushScopeId("data-v-34b0c30d"),a=a(),e.popScopeId(),a),Z={style:{padding:"0 40px"}},v=y(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),ee={style:{width:"240px"}},te={style:{width:"240px"}},oe=y(()=>e.createElementVNode("span",{class:"msg"},[e.createElementVNode("span",{style:{color:"#000","margin-right":"20px"}},"分钟后关闭订单"),e.createTextVNode(" 可输入3-360分钟")],-1)),le={class:"goods-list__info"},se={style:{width:"60px",height:"60px"}},ae={class:"goods-list__goods-list__info-name"},ne={class:"goods-list__goods-list__info-name--name"},de={class:"table-msg"},ie={class:"table-msg"},re={class:"table-msg"},ce=y(()=>e.createElementVNode("span",{class:"msg"},"仅勾选的抵扣方式，可在下单时间时使用",-1)),pe=y(()=>e.createElementVNode("span",{class:"msg"},"买家下单时仅可用与勾选的活动类型同时使用",-1)),me={class:"nav-button"},r=!0,ue=e.defineComponent({__name:"PlatformSeckillInfo",setup(a){const d=x.useRouter(),i=x.useRoute().query.secKillId,p=x.useRoute().query.shopId,{divTenThousand:m}=L();e.ref(!1);const b=new F().getYMDs(),H=e.ref(),u=e.ref([]),C=e.ref(!1),t=e.ref({id:"",date:"",seckillId:"",time:"",shopId:"",secKillName:"",seckillStatus:"NOT_STARTED",startTime:"",endTime:"",orderClosingTime:0,deductionType:!1,applyTypes:[],secKillProducts:[]}),_=e.reactive({current:1,pages:1,size:10}),h=e.ref();e.onMounted(()=>{M()});async function w(s=!1){s?s&&_.current<_.pages&&(_.current++,u.value=u.value.concat(await S())):(_.current=1,u.value=await S())}async function S(){const{code:s,data:l,msg:c}=await X(p,i,_);if(s!==200)return U.ElMessage.error("获取秒杀商品信息失败"),[];_.pages=l.pages;const n=l.records,N=O(n,t.value.shopId);return W(N),N}async function M(){if(i){const{code:s,data:l}=await Q(p,i);if(s!==200){U.ElMessage.error("获取秒杀信息失败");return}const{deductionType:c,endTime:n,id:N,orderClosingTime:I,secKillName:E,startTime:P,applyTypes:R,shopId:g,seckillStatus:Y}=l;t.value.deductionType=c,t.value.id=N,t.value.orderClosingTime=I,t.value.secKillName=E,t.value.date=n.split(" ")[0],t.value.startTime=P.split(" ")[1],t.value.endTime=n.split(" ")[1],t.value.applyTypes=R||[],t.value.shopId=g,t.value.seckillStatus=Y,w()}}G.useEventListener(h,"scroll",s=>{h.value&&i&&h.value.scrollTop+h.value.clientHeight+1>=h.value.scrollHeight&&w(!0)});const q=s=>{U.ElMessageBox.confirm("确定移除该商品?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",callback:l=>{l!=="cancel"&&(u.value=u.value.filter(c=>c.sku.skuId!==s))}})},K=s=>new Date(s).toLocaleDateString()===new Date().toLocaleDateString()?!1:new z(new Date(s).getTime()).lessThan(new z(new Date().getTime())),f=(s,l)=>{const c=[];for(let n=s;n<=l;n++)c.push(n);return c},fe=()=>{if(t.value.date&&t.value.date===b)return f(0,new Date().getHours()).concat(new Date().getHours(),24);if(t.value.endTime){const s=Number(t.value.endTime.split(":")[0]);return f(s+1,24)}return[]},_e=()=>f(1,59),he=()=>f(1,59),xe=()=>{if(t.value.date&&t.value.date===b){if(t.value.startTime){const s=Number(t.value.startTime.split(":")[0]);return f(0,s-1).concat(s-1,24)}return f(0,new Date().getHours()).concat(new Date().getHours(),24)}if(t.value.startTime){const s=Number(t.value.startTime.split(":")[0]);return f(0,s-1).concat(s-1,24)}return[]},ge=()=>f(0,58),Ve=()=>f(0,58);return(s,l)=>{const c=e.resolveComponent("el-input"),n=e.resolveComponent("el-form-item"),N=e.resolveComponent("el-date-picker"),I=e.resolveComponent("el-time-picker"),E=e.resolveComponent("el-row"),P=e.resolveComponent("el-link"),R=e.resolveComponent("el-image"),g=e.resolveComponent("el-table-column"),Y=e.resolveComponent("el-table"),D=e.resolveComponent("el-checkbox"),ke=e.resolveComponent("el-checkbox-group"),be=e.resolveComponent("el-form"),we=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",Z,[v,e.createVNode(be,{ref_key:"ruleFormRef",ref:H,model:t.value,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(n,{label:"活动名称",prop:"secKillName"},{default:e.withCtx(()=>[e.createVNode(c,{modelValue:t.value.secKillName,"onUpdate:modelValue":l[0]||(l[0]=o=>t.value.secKillName=o),modelModifiers:{trim:!0},disabled:r,maxlength:5,minlength:3,placeholder:"请输入活动名称",style:{width:"60%","margin-right":"15px"}},null,8,["modelValue"]),e.createElementVNode("span",{class:"msg"},"活动名称不超过5个字"+e.toDisplayString(r))]),_:1}),e.createVNode(n,{label:"活动时间",prop:"date",required:""},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:t.value.date,"onUpdate:modelValue":l[1]||(l[1]=o=>t.value.date=o),disabled:r,"disabled-date":K,format:"YYYY/MM/DD",placeholder:"请选择日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),e.createVNode(n,{label:"选择时段",required:""},{default:e.withCtx(()=>[e.createVNode(E,{style:{display:"flex",width:"100%"}},{default:e.withCtx(()=>[e.createElementVNode("div",ee,[e.createVNode(n,{"label-width":"0px",prop:"startTime"},{default:e.withCtx(()=>[e.createVNode(I,{modelValue:t.value.startTime,"onUpdate:modelValue":l[2]||(l[2]=o=>t.value.startTime=o),disabled:r,"disabled-hours":fe,"disabled-minutes":_e,"disabled-seconds":he,format:"HH:mm:ss",placeholder:"开始时间","value-format":"HH:mm:ss"},null,8,["modelValue"])]),_:1})]),e.createElementVNode("div",te,[e.createVNode(n,{"label-width":"0px",prop:"endTime"},{default:e.withCtx(()=>[e.createVNode(I,{modelValue:t.value.endTime,"onUpdate:modelValue":l[3]||(l[3]=o=>t.value.endTime=o),disabled:r,"disabled-hours":xe,"disabled-minutes":ge,"disabled-seconds":Ve,format:"HH:mm:ss",placeholder:"结束时间","value-format":"HH:mm:ss"},null,8,["modelValue"])]),_:1})])]),_:1})]),_:1}),e.createVNode(n,{label:"未支付订单",prop:"orderClosingTime"},{default:e.withCtx(()=>[e.createVNode(c,{modelValue:t.value.orderClosingTime,"onUpdate:modelValue":l[4]||(l[4]=o=>t.value.orderClosingTime=o),controls:!1,disabled:r,style:{width:"25%","margin-right":"15px"}},null,8,["modelValue"]),oe]),_:1}),e.createVNode(n,{label:"适用商品",required:""},{default:e.withCtx(()=>[e.createVNode(E,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(P,{disabled:r,underline:!1,type:"primary",onClick:l[5]||(l[5]=o=>C.value=!0)},{default:e.withCtx(()=>[e.createTextVNode(" 选择商品 ")]),_:1})]),_:1}),u.value.length?(e.openBlock(),e.createElementBlock("div",{key:0,ref_key:"goodsDataRef",ref:h,class:"goods-list"},[e.createVNode(Y,{"cell-style":{height:"80px"},data:u.value,"header-cell-style":{fontSize:"14px",color:"#606266",background:"#f2f2f2",height:"54px",fontWeight:"normal"},"span-method":e.unref(J),border:"",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(g,{label:"商品信息"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",le,[e.createElementVNode("div",se,[e.createVNode(R,{"preview-src-list":[o.productPic],"preview-teleported":!0,src:o.productPic,fit:"fill",style:{width:"60px",height:"60px"}},null,8,["preview-src-list","src"])]),e.createElementVNode("div",ae,[e.createElementVNode("div",ne,e.toDisplayString(o.productName),1)])])]),_:1}),e.createVNode(g,{align:"center",label:"规格",width:"100px"},{default:e.withCtx(({row:o})=>{var V;return[e.createElementVNode("div",de,e.toDisplayString(((V=o.sku.skuName)==null?void 0:V.join(" "))||"-"),1)]}),_:1}),e.createVNode(g,{align:"center",label:"秒杀价（元）",width:"120px"},{default:e.withCtx(({row:o})=>[e.createVNode(c,{modelValue:o.sku.seckillPrice,"onUpdate:modelValue":V=>o.sku.seckillPrice=V,controls:!1,disabled:r,style:{width:"80%"}},null,8,["modelValue","onUpdate:modelValue"]),e.createElementVNode("div",ie,"实售价：￥"+e.toDisplayString(e.unref(m)(o.sku.skuPrice)),1)]),_:1}),e.createVNode(g,{align:"center",label:"秒杀库存",width:"120px"},{default:e.withCtx(({row:o})=>[e.createVNode(c,{modelValue:o.sku.seckillStock,"onUpdate:modelValue":V=>o.sku.seckillStock=V,controls:!1,disabled:r,precision:0,style:{width:"80%"}},null,8,["modelValue","onUpdate:modelValue"]),e.createElementVNode("div",re,"库存："+e.toDisplayString(o.sku.limitType==="UNLIMITED"?"不限购":o.sku.skuStock),1)]),_:1}),e.createVNode(g,{label:"操作",width:"80px"},{default:e.withCtx(({row:o})=>[e.createVNode(P,{disabled:r,underline:!1,type:"primary",onClick:V=>q(o.sku.skuId)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","span-method"])],512)):e.createCommentVNode("",!0)]),_:1}),e.createVNode(n,{label:"可用抵扣"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:t.value.deductionType,"onUpdate:modelValue":l[6]||(l[6]=o=>t.value.deductionType=o),disabled:r},{default:e.withCtx(()=>[e.createTextVNode(" 积分 "),ce]),_:1},8,["modelValue"])]),_:1}),e.createVNode(n,{label:"使用优惠"},{default:e.withCtx(()=>[e.createVNode(ke,{modelValue:t.value.applyTypes,"onUpdate:modelValue":l[7]||(l[7]=o=>t.value.applyTypes=o),disabled:r},{default:e.withCtx(()=>[e.createVNode(D,{label:0},{default:e.withCtx(()=>[e.createTextVNode("会员价")]),_:1}),e.createVNode(D,{label:1},{default:e.withCtx(()=>[e.createTextVNode("优惠券")]),_:1}),e.createVNode(D,{label:2},{default:e.withCtx(()=>[e.createTextVNode(" 满减 "),pe]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),e.createElementVNode("div",me,[e.createVNode(we,{plain:"",round:"",onClick:l[8]||(l[8]=o=>e.unref(d).back())},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1})])])}}}),Te="";return((a,d)=>{const i=a.__vccOpts||a;for(const[p,m]of d)i[p]=m;return i})(ue,[["__scopeId","data-v-34b0c30d"]])});
