(function(e,F){typeof exports=="object"&&typeof module<"u"?module.exports=F(require("vue"),require("vue-router"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/PageManage.vue"),require("@/composables/usePlatformGoodStatus"),require("element-plus"),require("@/composables/useConvert"),require("@element-plus/icons-vue"),require("@vueuse/core"),require("element-china-area-data"),require("@/components/q-address"),require("@/assets/json/data.json"),require("@/components/q-address/q-address.vue"),require("@/utils/Storage"),require("lodash"),require("@/components/MCard.vue"),require("@/utils/date"),require("vue-clipboard3"),require("@/components/remark/remark-flag.vue"),require("@/components/remark/remark-popup.vue"),require("@/components/q-editor/editor.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/PageManage.vue","@/composables/usePlatformGoodStatus","element-plus","@/composables/useConvert","@element-plus/icons-vue","@vueuse/core","element-china-area-data","@/components/q-address","@/assets/json/data.json","@/components/q-address/q-address.vue","@/utils/Storage","lodash","@/components/MCard.vue","@/utils/date","vue-clipboard3","@/components/remark/remark-flag.vue","@/components/remark/remark-popup.vue","@/components/q-editor/editor.vue","@/apis/http"],F):(e=typeof globalThis<"u"?globalThis:e||self,e.Integral=F(e.IntegralContext.Vue,e.IntegralContext.VueRouter,e.IntegralContext.QTable,e.IntegralContext.QTableColumn,e.IntegralContext.PageManageTwo,e.IntegralContext.PlatformGoodStatus,e.IntegralContext.ElementPlus,e.IntegralContext.UseConvert,e.IntegralContext.ElementPlusIconsVue,e.IntegralContext.VueUse,e.IntegralContext.ElementChinaAreaData,e.IntegralContext.QAddressIndex,e.IntegralContext.handleGetCompanyName,e.IntegralContext.QAddress,e.IntegralContext.Storage,e.IntegralContext.Lodash,e.IntegralContext.MCard,e.IntegralContext.DateUtil,e.IntegralContext.VueClipboard3,e.IntegralContext.RemarkFlag,e.IntegralContext.RemarkPopup,e.IntegralContext.EditorTwo,e.IntegralContext.Request))})(this,function(e,F,ce,G,pe,Ne,_,de,me,_e,Ce,we,ke,fe,Ia,ue,be,Ee,Se,Ie,Te,ge,$){"use strict";var Ve=document.createElement("style");Ve.textContent=`.commodity[data-v-53afb80a]{display:flex;justify-content:center;align-items:center;justify-content:flex-start;align-items:flex-start}.commodity__right[data-v-53afb80a]{margin-left:10px;width:100px}.commodity__right--name[data-v-53afb80a]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.commodity__right--price[data-v-53afb80a]{width:150px;margin-top:30px;font-size:12px}.del[data-v-53afb80a]{cursor:pointer;margin:0 10px;color:red}.bj[data-v-53afb80a]{cursor:pointer;margin:0 10px;color:#409eff}.show[data-v-53afb80a]{display:none}.input-with-select[data-v-53afb80a]{width:350px;float:right;position:relative;z-index:9}[data-v-53afb80a] .body--header{width:14px;height:14px;float:left;border:0;transform:translate(5px,50px)}[data-v-53afb80a] .m__table--shrink{margin-left:50px}[data-v-53afb80a] .m__table--head.padding:after{content:""}[data-v-53afb80a] .m__table--body:after{content:""}.notShipment__info[data-v-b841043e]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-between;align-items:flex-start;color:#838383}.notShipment__show[data-v-b841043e]{font-size:14px;color:#333;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.el-table td[data-v-b841043e]{border-bottom:none}.tableStyle[data-v-b841043e]:before{width:0}.el-table[data-v-b841043e]{border-radius:9px;border:1px solid #d5d5d5}.el-table th.is-leaf[data-v-b841043e]{border-bottom:none}.footer[data-v-b841043e]{display:flex;justify-content:flex-end;align-items:center}.demo-tabs>.el-tabs__content[data-v-e6433084]{padding:32px;color:#6b778c;font-size:32px;font-weight:600}.goods-Infor[data-v-393fc12a]{display:flex;justify-content:center;align-items:center;align-items:flex-start}.goods-Infor__name[data-v-393fc12a]{height:68px;display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-around;align-items:flex-start}.goods-Infor__name--integral[data-v-393fc12a]{color:#ff7417}.base-vip-table[data-v-393fc12a]{overflow:auto}.base-vip-table-top[data-v-393fc12a]{display:flex;justify-content:center;align-items:center;justify-content:space-between;width:100%}.base-vip-table-top__left[data-v-393fc12a]{margin-left:25px}.base-vip-table-top--no[data-v-393fc12a]{margin-right:20px}.base-vip-table-top--copy[data-v-393fc12a]{margin-right:55px;color:#00a1ff;cursor:pointer}.operation[data-v-393fc12a]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-between}.ellipsis[data-v-393fc12a]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.fix[data-v-393fc12a]{width:100%;cursor:pointer;display:flex;justify-content:center;align-items:center;height:40px;background:#f9f9f9}.fix__arrow[data-v-393fc12a]{margin-left:4px}.fix__down[data-v-393fc12a]{transform:rotate(180deg)}.title[data-v-cc353116]{margin:15px 10px;display:flex;justify-content:center;align-items:center;justify-content:flex-start;font-size:14px;color:#2e99f3;font-weight:700}.title[data-v-cc353116]:before{content:"";display:inline-block;height:14px;width:2.5px;background:#2e99f3;margin-right:9px}.use_rules[data-v-cc353116]{font-size:14px;font-family:sans-serif,sans-serif-Normal;font-weight:400;text-align:LEFT;color:#333;line-height:30px}.use_rules__msg[data-v-cc353116]{font-size:14px;text-align:LEFT;color:#d3d3d3}.save[data-v-cc353116]{margin:30px 0;text-align:center}.get_rules[data-v-cc353116]{text-indent:1em;padding:10px 0}.p20[data-v-cc353116]{padding:0 0 0 20px}
`,document.head.appendChild(Ve);const Re=e.defineComponent({__name:"Integral",setup(i){const x=e.ref("goods"),u=F.useRoute(),l=[{label:"积分商品",name:"goods"},{label:"积分订单",name:"order"},{label:"积分规则",name:"rules"}],o={goods:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Ze)),order:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ea)),rules:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Ea))};return e.watchEffect(()=>{u.query.type&&u.query.type==="order"&&(x.value=u.query.type)}),(h,y)=>{const S=e.resolveComponent("el-tab-pane"),N=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(N,{modelValue:x.value,"onUpdate:modelValue":y[0]||(y[0]=T=>x.value=T),class:"demo-tabs"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(l,T=>e.createVNode(S,{key:T.label,label:T.label,name:T.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o[x.value])))])}}}),K="addon-integral/integral/",ie=K+"product/",ae=K+"order/",De=i=>$.get({url:ie+"list",params:i}),he=(i,x)=>$.put({url:ie+`updateStatus/${i}`,data:x}),Oe=i=>$.del({url:ie+`delete/${i}`}),Be=i=>$.get({url:ae+"list",params:i}),Ge=()=>$.get({url:K+"rules/info"}),$e=i=>$.post({url:K+"rules/save",data:i}),Le=i=>$.post({url:K+"rules/update",data:i}),Ue=i=>$.get({url:ae+`deliver/single/undeliver/${i}`}),Me=i=>$.put({url:ae+"deliver/",data:i}),ye=i=>$.put({url:ae+`deliver/complete/${i}/`}),xe=i=>(e.pushScopeId("data-v-53afb80a"),i=i(),e.popScopeId(),i),Ae={class:"commodity"},qe={style:{width:"68px",height:"68px"}},Pe={class:"commodity__right"},ze={class:"commodity__right--name"},Fe={class:"commodity__right--price"},je={style:{"font-size":"15px",color:"#ecb789"}},Ye=xe(()=>e.createElementVNode("span",{style:{color:"#ecb789"}}," 分 ",-1)),He=xe(()=>e.createElementVNode("span",{style:{color:"#f00"}}," ￥ ",-1)),Qe={style:{"font-size":"15px",color:"#f00"}},Xe={class:"shop-name"},Je=["onClick"],We=["onClick"],Ke=e.defineComponent({__name:"integral-goods",setup(i){const{divTenThousand:x}=de(),u=e.reactive({size:20,current:1,total:0}),l=e.ref({status:"",keywords:""});e.reactive([{label:"DELETE",name:"删除"}]);const o=F.useRouter(),h=e.ref([]),y=e.ref([]),S=e.ref("calc(100vh - 310px)");N();async function N(){const{status:a,keywords:d}=l.value,{code:f,data:b,msg:E}=await De({keyword:d,status:a,...u});if(f!==200){_.ElMessage.error(E||"获取积分商品列表失败");return}h.value=b.records,u.total=b.total}const T=async()=>{if(!y.value.length){_.ElMessage.error("请选择商品");return}try{if(!await _.ElMessageBox.confirm("确定进行删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const d=y.value.map(f=>f.id);await C(d)}catch{return}},C=async a=>{const{code:d,data:f}=await Oe(a);if(d!==200){_.ElMessage.error("删除失败");return}_.ElMessage.success("删除成功"),N()},L=async a=>{if(!y.value.length){_.ElMessage.error("请选择商品");return}try{if(!await _.ElMessageBox.confirm(`确定进行${a==="SELL_OFF"?"下":"上"}架?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const f=y.value.map(b=>b.id);await q(a,f)}catch{return!1}};async function q(a,d){const{code:f,data:b,msg:E}=await he(a,d);if(f!==200)return _.ElMessage.error(E||"操作失败"),!1;_.ElMessage.success("操作成功"),N()}const Y=()=>{o.push({name:"addIntegralMallGoods"})},X=a=>{o.push({name:"addIntegralMallGoods",query:{id:a.id}})},w=async(a,d)=>{switch(a){case"DELETE":try{if(!await _.ElMessageBox.confirm("确定进行删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;C([d.id])}catch(f){console.log(f)}break}},k=a=>{u.size=a,N()},r=a=>{u.current=a,N()},t=async a=>{const{code:d,data:f,msg:b}=await he(a.status==="SELL_OFF"?"SELL_ON":"SELL_OFF",[a.id]);if(d!==200)return _.ElMessage.error(b||"操作失败"),!1;N()},g=()=>{N()};return(a,d)=>{const f=e.resolveComponent("el-button"),b=e.resolveComponent("el-input"),E=e.resolveComponent("el-tab-pane"),U=e.resolveComponent("el-tabs"),R=e.resolveComponent("el-image"),P=e.resolveComponent("el-tag"),D=e.resolveComponent("el-switch");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(b,{modelValue:l.value.keywords,"onUpdate:modelValue":d[0]||(d[0]=V=>l.value.keywords=V),placeholder:"请输入关键词",class:"input-with-select"},{append:e.withCtx(()=>[e.createVNode(f,{icon:e.unref(me.Search),onClick:N},null,8,["icon"])]),_:1},8,["modelValue"]),e.createVNode(U,{modelValue:l.value.status,"onUpdate:modelValue":d[1]||(d[1]=V=>l.value.status=V),type:"card",class:"demo-tabs",onTabChange:g},{default:e.withCtx(()=>[e.createVNode(E,{label:"全部",name:""}),e.createVNode(E,{label:"已上架",name:"SELL_ON"}),e.createVNode(E,{label:"已下架",name:"SELL_OFF"})]),_:1},8,["modelValue"]),e.createVNode(f,{type:"primary",plain:"",onClick:Y},{default:e.withCtx(()=>[e.createTextVNode("新增积分商品")]),_:1}),e.createVNode(f,{type:"primary",plain:"",onClick:d[2]||(d[2]=V=>L("SELL_ON"))},{default:e.withCtx(()=>[e.createTextVNode("批量上架")]),_:1}),e.createVNode(f,{type:"primary",plain:"",onClick:d[3]||(d[3]=V=>L("SELL_OFF"))},{default:e.withCtx(()=>[e.createTextVNode("批量下架")]),_:1}),e.createVNode(f,{type:"danger",plain:"",onClick:T},{default:e.withCtx(()=>[e.createTextVNode("批量删除")]),_:1}),e.createVNode(e.unref(ce),{ref:"multipleTableRef","checked-item":y.value,"onUpdate:checkedItem":d[4]||(d[4]=V=>y.value=V),data:h.value,selection:!0,style:e.normalizeStyle([{"margin-top":"10px",overflow:"auto"},{height:S.value}])},{header:e.withCtx(()=>[]),default:e.withCtx(()=>[e.createVNode(G,{label:"商品",prop:"goodInfo",align:"left",width:"300"},{default:e.withCtx(({row:V})=>[e.createElementVNode("div",Ae,[e.createElementVNode("div",qe,[e.createVNode(R,{style:{width:"68px",height:"68px"},src:V.pic,fit:"fill"},null,8,["src"])]),e.createElementVNode("div",Pe,[e.createElementVNode("div",ze,e.toDisplayString(V.name),1),e.createElementVNode("div",Fe,[e.createElementVNode("span",je,e.toDisplayString(V.integralPrice),1),Ye,e.createTextVNode("+"),He,e.createElementVNode("span",Qe,e.toDisplayString(e.unref(x)(V.salePrice)),1)])])])]),_:1}),e.createVNode(G,{label:"库存",prop:"stock",align:"center"}),e.createVNode(G,{label:"状态",prop:"status",align:"center"},{default:e.withCtx(({row:V})=>[e.createVNode(P,{class:"ml-2",type:V.status==="SELL_ON"?"success":"danger"},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(Ne.usePlatformGoodStatus)(V.status)),1)]),_:2},1032,["type"])]),_:1}),e.createVNode(G,{label:"上下架"},{default:e.withCtx(({row:V})=>[e.createElementVNode("span",Xe,[e.createVNode(D,{"model-value":V.status==="SELL_ON",class:"ml-2",onChange:m=>t(V)},null,8,["model-value","onChange"])])]),_:1}),e.createVNode(G,{label:"操作",align:"center"},{default:e.withCtx(({row:V})=>[e.createElementVNode("span",{style:{cursor:"pointer",margin:"0 10px",color:"#409eff"},onClick:m=>X(V)},"编辑",8,Je),e.createElementVNode("span",{class:e.normalizeClass(V.status==="SELL_OFF"?" del":"show del"),onClick:m=>w("DELETE",V)},"删除",10,We)]),_:1})]),_:1},8,["checked-item","data","style"]),e.createVNode(pe,{"page-size":u.size,"page-num":u.current,total:u.total,onHandleSizeChange:k,onHandleCurrentChange:r},null,8,["page-size","page-num","total"])])}}}),Ta="",Z=(i,x)=>{const u=i.__vccOpts||i;for(const[l,o]of x)u[l]=o;return u},Ze=Object.freeze(Object.defineProperty({__proto__:null,default:Z(Ke,[["__scopeId","data-v-53afb80a"]])},Symbol.toStringTag,{value:"Module"})),Q=i=>(e.pushScopeId("data-v-b841043e"),i=i(),e.popScopeId(),i),ve={style:{display:"flex"}},et={class:"notShipment__info",style:{width:"200px","padding-left":"10px"}},tt={class:"notShipment__show"},at=Q(()=>e.createElementVNode("div",{class:"send"},"订单号",-1)),ot={class:"send"},lt=Q(()=>e.createElementVNode("div",{class:"send"},"创建时间",-1)),nt={class:"send"},rt=Q(()=>e.createElementVNode("div",{class:"send"},"收货地址",-1)),st={key:0},dt={key:1},it=Q(()=>e.createElementVNode("div",null,"发货方式",-1)),ct=Q(()=>e.createElementVNode("div",{class:"send"},"物流公司",-1)),pt=Q(()=>e.createElementVNode("div",null,"物流单号",-1)),mt=Q(()=>e.createElementVNode("div",null,"发货地址",-1)),_t={class:"footer"},ft=e.defineComponent({__name:"notShipment",props:{currentNo:{type:String,required:!0},isShow:{type:Boolean,required:!0}},emits:["update:isShow","upload-list"],setup(i,{emit:x}){const u=i,l=ke;de();const o=_e.useVModel(u,"isShow",x),h=e.ref({sellerRemark:"",buyerRemark:"",payTime:"",deliveryTime:"",accomplishTime:"",buyerId:"",buyerNickname:"",createTime:"",freightPrice:0,image:"",salePrice:"",integralOrderReceiver:{address:"",areaCode:[],createTime:"",id:"",mobile:"",name:"",orderNo:"",updateTime:""},no:"",num:1,price:"",productName:"",status:"PAID"}),y=e.ref(),S=e.ref(),N=e.reactive({address:[],company:[{required:!0,message:"请选择物流公司",trigger:"change"}],expressNo:[{required:!0,message:"请填写物流单号",trigger:"blur"},{min:8,max:20,message:"请填写正确的物流单号",trigger:"blur"}]}),T=[{key:"EXPRESS",value:"手动发货"},{key:"WITHOUT",value:"无需物流发货"}],C=e.reactive({deliverType:"EXPRESS",receiver:{name:"",mobile:"",address:""},expressCompany:"shunfeng",expressCode:"shunfeng",addressaddress:"",expressNo:""});L();async function L(){if(!u.currentNo){_.ElMessage.error("获取订单号失败"),o.value=!1;return}const{code:w,data:k,msg:r}=await Ue(u.currentNo);if(w!==200){_.ElMessage.error(r||"获取订单详情失败"),o.value=!1;return}h.value=k,y.value.toggleRowSelection(h.value,!0)}const q=async()=>{var w;try{if(await((w=S.value)==null?void 0:w.validate())){const r=Y(C.deliverType);console.log(r);const{code:t,msg:g,data:a}=await Me([r]);if(t!==200){_.ElMessage.error(g||"发货失败");return}_.ElMessage.success("发货成功"),o.value=!1,x("upload-list")}}catch{}},Y=w=>{const{deliverType:k,expressCode:r,expressNo:t}=C;return w==="EXPRESS"?{integralOrderNo:u.currentNo,integralOrderDeliverType:k,expressCompanyName:r,expressNo:t}:{integralOrderNo:u.currentNo,integralOrderDeliverType:k}},X=()=>{o.value=!1};return(w,k)=>{const r=e.resolveComponent("el-avatar"),t=e.resolveComponent("el-table-column"),g=e.resolveComponent("el-table"),a=e.resolveComponent("el-form-item"),d=e.resolveComponent("el-radio"),f=e.resolveComponent("el-radio-group"),b=e.resolveComponent("el-option"),E=e.resolveComponent("el-select"),U=e.resolveComponent("el-col"),R=e.resolveComponent("el-row"),P=e.resolveComponent("el-input"),D=e.resolveComponent("el-form"),V=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(D,{ref_key:"ruleFormRef",ref:S,model:C,class:"notShipment",rules:N},{default:e.withCtx(()=>[e.createVNode(g,{ref_key:"tableRef",ref:y,"empty-text":"暂无数据~",data:[h.value],"max-height":"250",style:{width:"100%","margin-bottom":"20px"},"header-row-style":()=>({fontSize:"14px",color:"#333333",fontWeight:"bold"})},{default:e.withCtx(()=>[e.createVNode(t,{label:"商品信息"},{default:e.withCtx(({row:m})=>[e.createElementVNode("div",ve,[e.createVNode(r,{style:{width:"68px",height:"68px","flex-shrink":"0"},shape:"square",size:"large",src:m.image},null,8,["src"]),e.createElementVNode("div",et,[e.createElementVNode("div",tt,e.toDisplayString(m.productName),1),e.createElementVNode("div",null,"积分："+e.toDisplayString(m.price),1)])])]),_:1}),e.createVNode(t,{label:"数量",width:"100"},{default:e.withCtx(({row:m})=>[e.createElementVNode("div",null,"*"+e.toDisplayString(m.num),1)]),_:1})]),_:1},8,["data"]),e.createVNode(a,{"label-width":"90px"},{label:e.withCtx(()=>[at]),default:e.withCtx(()=>[e.createElementVNode("div",ot,e.toDisplayString(u.currentNo),1)]),_:1}),e.createVNode(a,{"label-width":"90px"},{label:e.withCtx(()=>[lt]),default:e.withCtx(()=>[e.createElementVNode("div",nt,e.toDisplayString(h.value.createTime),1)]),_:1}),e.createVNode(a,{"label-width":"90px"},{label:e.withCtx(()=>[rt]),default:e.withCtx(()=>[h.value.integralOrderReceiver.address?(e.openBlock(),e.createElementBlock("div",st,[e.createVNode(fe,{address:h.value.integralOrderReceiver.areaCode},null,8,["address"]),e.createTextVNode(e.toDisplayString(h.value.integralOrderReceiver.address),1)])):(e.openBlock(),e.createElementBlock("div",dt,"暂无地址信息"))]),_:1}),e.createVNode(a,{"label-width":"90px"},{label:e.withCtx(()=>[it]),default:e.withCtx(()=>[e.createVNode(f,{modelValue:C.deliverType,"onUpdate:modelValue":k[0]||(k[0]=m=>C.deliverType=m),class:"ml-4",size:"large"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(T,m=>e.createVNode(d,{key:m.key,label:m.key,size:"large"},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(m.value),1)]),_:2},1032,["label"])),64))]),_:1},8,["modelValue"])]),_:1}),C.deliverType!=="WITHOUT"?(e.openBlock(),e.createBlock(a,{key:0,"label-width":"90px",prop:""},{label:e.withCtx(()=>[ct]),default:e.withCtx(()=>[e.createVNode(R,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(U,{span:20},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:C.expressCode,"onUpdate:modelValue":k[1]||(k[1]=m=>C.expressCode=m),class:"m-2",placeholder:"Select",style:{width:"100%",height:"30px"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(l),m=>(e.openBlock(),e.createBlock(b,{key:m.companyName,label:m.companyName,value:m.companyCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0),C.deliverType==="EXPRESS"?(e.openBlock(),e.createBlock(a,{key:1,"label-width":"90px",prop:"expressNo"},{label:e.withCtx(()=>[pt]),default:e.withCtx(()=>[e.createVNode(R,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(U,{span:20},{default:e.withCtx(()=>[e.createVNode(P,{modelValue:C.expressNo,"onUpdate:modelValue":k[2]||(k[2]=m=>C.expressNo=m),placeholder:"",style:{width:"100%",height:"30px"},maxlength:"40"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0),C.deliverType==="PRINT_EXPRESS"?(e.openBlock(),e.createBlock(a,{key:2,"label-width":"90px",prop:""},{label:e.withCtx(()=>[mt]),default:e.withCtx(()=>[e.createVNode(R,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(U,{span:20},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:C.addressaddress,"onUpdate:modelValue":k[3]||(k[3]=m=>C.addressaddress=m),placeholder:"选择发货地址",style:{width:"100%",height:"30px"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(w.deliveryAddressData,m=>(e.openBlock(),e.createBlock(b,{key:m.id,value:m.id,label:`${e.unref(we.AddressFn)(e.unref(Ce.regionData),[m.provinceCode,m.cityCode,m.regionCode])}${m.address}`},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model","rules"]),e.createElementVNode("footer",_t,[e.createVNode(V,{onClick:X},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createTextVNode(),e.createVNode(V,{type:"primary",onClick:q},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})])],64)}}}),Da="",ut=Z(ft,[["__scopeId","data-v-b841043e"]]),gt={class:"my-header"},Vt=["id"],ht=e.defineComponent({__name:"order-shipment",props:{isShow:{type:Boolean,default(){return!1}},currentNo:{type:String,default:""}},emits:["update:isShow","upload-list"],setup(i,{emit:x}){const u=i,l=_e.useVModel(u,"isShow",x),o=()=>{l.value=!1};return(h,y)=>{const S=e.resolveComponent("el-dialog");return e.openBlock(),e.createBlock(S,{modelValue:e.unref(l),"onUpdate:modelValue":y[2]||(y[2]=N=>e.isRef(l)?l.value=N:null),width:"35%","before-close":o,"destroy-on-close":""},{header:e.withCtx(({titleId:N,titleClass:T})=>[e.createElementVNode("div",gt,[e.createElementVNode("h4",{id:N,class:e.normalizeClass(T)},"商品发货",10,Vt)])]),default:e.withCtx(()=>[e.createVNode(ut,{"is-show":e.unref(l),"onUpdate:isShow":y[0]||(y[0]=N=>e.isRef(l)?l.value=N:null),"current-no":u.currentNo,onUploadList:y[1]||(y[1]=N=>x("upload-list"))},null,8,["is-show","current-no"])]),_:1},8,["modelValue"])}}}),Oa="",yt=Z(ht,[["__scopeId","data-v-e6433084"]]),xt={UNPAID:"未支付",PAID:"待发货",ON_DELIVERY:"已发货",ACCOMPLISH:"已完成",SYSTEM_CLOSED:"已关闭"},W=i=>(e.pushScopeId("data-v-393fc12a"),i=i(),e.popScopeId(),i),Nt=W(()=>e.createElementVNode("br",null,null,-1)),Ct={style:{float:"right"}},wt=["onClick"],kt={class:"base-vip-table-top"},bt={class:"base-vip-table-top__left"},Et={class:"base-vip-table-top--no"},St=["onClick"],It={class:"base-vip-table-top--time"},Tt={class:"goods-Infor"},Rt={class:"goods-Infor__name"},Dt={class:"ellipsis"},Ot={class:""},Bt={class:"goods-Infor__name--integral"},Gt={class:"commodity__right--price"},$t={style:{"font-size":"15px",color:"#ecb789"}},Lt=W(()=>e.createElementVNode("span",{style:{color:"#ecb789"}}," 分 ",-1)),Ut=W(()=>e.createElementVNode("span",{style:{color:"#f00"}}," ￥ ",-1)),Mt={style:{"font-size":"15px",color:"#f00"}},At={class:"avatar_text avatar_text__bottom money_text"},qt={style:{width:"200px",height:"16px"}},Pt={style:{display:"block","font-weight":"bold","margin-right":"30px",width:"160px",height:"16px",overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},zt=["onClick"],Ft={style:{padding:"10px 0","font-size":"14px"},class:"money_text"},jt={style:{display:"inline"}},Yt=W(()=>e.createElementVNode("br",null,null,-1)),Ht={class:"commodity__right--price",style:{"margin-top":"10px"}},Qt={style:{"font-size":"14px",color:"#ecb789"}},Xt=W(()=>e.createElementVNode("span",{style:{color:"#ecb789"}}," 分 ",-1)),Jt=W(()=>e.createElementVNode("span",{style:{color:"#f00"}}," ￥ ",-1)),Wt={style:{"font-size":"14px",color:"#f00"}},Kt={class:"money_text",style:{"margin-bottom":"5px"}},Zt={class:"operation"},vt=e.defineComponent({__name:"integral-order",emits:["search-data","changeShow"],setup(i,{emit:x}){const{toClipboard:u}=Se(),l=e.reactive({size:20,current:1,total:0}),o=e.ref({no:"",consignee:"",productName:"",date:"",status:""}),{divTenThousand:h}=de(),y=e.ref(!1),S=e.ref([]),N=e.ref("");e.reactive([{label:"SELL_OFF",name:"下架"},{label:"SELL_ON",name:"上架"},{label:"DELETE",name:"删除"}]);const T=F.useRouter(),C=e.ref([]),L=e.ref([]),q=e.ref(!1),Y=e.ref(""),X=e.ref("calc(100vh - 310px)");w();async function w(){var M,J;const p=ue.cloneDeep(o.value);Array.isArray(p.date)&&(p.startTime=(M=p.date)==null?void 0:M[0],p.endTime=(J=p.date)==null?void 0:J[1]),delete p.date;const{code:c,data:I,msg:O}=await Be({...l,...p});if(c!==200){_.ElMessage.error(O||"获取订单失败");return}C.value=I.records,l.total=I.total}const k=p=>{Y.value=p.no,q.value=!0},r=p=>{S.value=[p.no],p.sellerRemark&&(N.value=p.sellerRemark),y.value=!0},t=p=>{l.size=p,w()},g=p=>{l.current=p,w()},a=()=>{L.value=[],w()},d=p=>{T.push({name:"integralMallOrderDetail",query:{no:p}})},f=async p=>{try{await _.ElMessageBox.confirm("确认买家已经收到货?","提示",{confirmButtonText:"确定",cancelButtonText:"关闭",type:"warning"});const{code:c,msg:I,data:O}=await ye(p.no);if(c!==200){_.ElMessage.error(I||"确认收货失败");return}_.ElMessage.success("确认收货成功"),p.status="ACCOMPLISH"}catch{}},b=async p=>{try{await _.ElMessageBox.confirm("确认关闭订单?","提示",{confirmButtonText:"确定",cancelButtonText:"关闭",type:"warning"});const{code:c,msg:I,data:O}=await ye(p.no);if(c!==200){_.ElMessage.error(I||"关闭订单失败");return}_.ElMessage.success("关闭订单成功"),p.status="SYSTEM_CLOSED"}catch{}},E=e.ref(!1);e.watch(()=>E.value,p=>{x("changeShow",p)});const U=()=>{o.value.no="",o.value.consignee="",o.value.productName="",o.value.date=""},R=new Ee,P=e.ref(" "),D=e.ref("全部订单"),V=p=>{o.value.status=p,w()},m=p=>{P.value=" ",D.value=p,D.value==="近一个月订单"?(R.getLastMonth(new Date),v()):D.value==="近三个月订单"?(R.getLastThreeMonth(new Date),v()):(o.value.date="",w())},v=async p=>{var I,O;const c=ue.cloneDeep(o.value);if(Array.isArray(c.date)){c.startTime=(I=c.date)==null?void 0:I[0],c.endTime=(O=c.date)==null?void 0:O[1];const M=R.getYMDs(new Date);c.startTime="",c.endTime=M,delete c.date}w()};async function ee(p){try{await u(p),_.ElMessage.success("复制成功")}catch{_.ElMessage.error("复制失败")}}return(p,c)=>{const I=e.resolveComponent("el-input"),O=e.resolveComponent("el-date-picker"),M=e.resolveComponent("el-button"),J=e.resolveComponent("el-icon"),oe=e.resolveComponent("el-dropdown-item"),le=e.resolveComponent("el-dropdown-menu"),ne=e.resolveComponent("el-dropdown"),z=e.resolveComponent("el-tab-pane"),re=e.resolveComponent("el-tabs"),se=e.resolveComponent("el-image"),te=e.resolveComponent("el-row"),H=e.resolveComponent("el-link");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(be,{modelValue:E.value,"onUpdate:modelValue":c[4]||(c[4]=n=>E.value=n)},{default:e.withCtx(()=>[e.createElementVNode("view",null,[e.createTextVNode(" 订单号："),e.createVNode(I,{modelValue:o.value.no,"onUpdate:modelValue":c[0]||(c[0]=n=>o.value.no=n),placeholder:"请输入订单号",style:{width:"240px",margin:"0 20px 0 15px"}},null,8,["modelValue"]),e.createTextVNode(" 收货人姓名："),e.createVNode(I,{modelValue:o.value.consignee,"onUpdate:modelValue":c[1]||(c[1]=n=>o.value.consignee=n),placeholder:"请输入收货人姓名",style:{width:"240px","margin-right":"20px"}},null,8,["modelValue"]),Nt,e.createTextVNode(" 商品名称："),e.createVNode(I,{modelValue:o.value.productName,"onUpdate:modelValue":c[2]||(c[2]=n=>o.value.productName=n),placeholder:"请输入商品名称",style:{width:"240px",margin:"0px 20px 0 0"}},null,8,["modelValue"]),e.createTextVNode(" 下单时间："),e.createVNode(O,{modelValue:o.value.date,"onUpdate:modelValue":c[3]||(c[3]=n=>o.value.date=n),type:"daterange","value-format":"YYYY-MM-DD","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{margin:"10px 0 0 15px"}},null,8,["modelValue"]),e.createElementVNode("view",Ct,[e.createVNode(M,{type:"primary",plain:"",onClick:w},{default:e.withCtx(()=>[e.createTextVNode("查询")]),_:1}),e.createVNode(M,{type:"info",plain:"",onClick:U},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})])])]),_:1},8,["modelValue"]),e.createVNode(re,{modelValue:P.value,"onUpdate:modelValue":c[5]||(c[5]=n=>P.value=n),type:"card",class:"demo-tabs",onTabChange:V},{default:e.withCtx(()=>[e.createVNode(z,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(D.value),1),e.createVNode(ne,{placement:"bottom-end",trigger:"click",onCommand:m},{dropdown:e.withCtx(()=>[e.createVNode(le,null,{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(["近一个月订单","近三个月订单","全部订单"],n=>e.createVNode(oe,{key:n,command:n},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n),1)]),_:2},1032,["command"])),64))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(J,{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(e.unref(me.ArrowDown))]),_:1})],8,wt)]),_:1})]),_:1}),e.createVNode(z,{label:"待付款",name:"UNPAID"}),e.createVNode(z,{label:"待发货",name:"PAID"}),e.createVNode(z,{label:"待收货",name:"ON_DELIVERY"}),e.createVNode(z,{label:"已完成",name:"ACCOMPLISH"}),e.createVNode(z,{label:"已关闭",name:"SYSTEM_CLOSED"})]),_:1},8,["modelValue"]),e.createVNode(e.unref(ce),{"checked-item":L.value,"onUpdate:checkedItem":c[6]||(c[6]=n=>L.value=n),data:C.value,class:"base-vip-table",selection:!0,style:e.normalizeStyle({height:X.value})},{header:e.withCtx(({row:n})=>[e.createElementVNode("div",kt,[e.createElementVNode("div",bt,[e.createElementVNode("span",Et,"订单号 : "+e.toDisplayString(n.no),1),e.createElementVNode("span",{class:"base-vip-table-top--copy",onClick:A=>ee(n.no)},"复制",8,St),e.createElementVNode("span",It,"创建时间 : "+e.toDisplayString(n.createTime),1)]),e.createVNode(Ie,{content:n.sellerRemark,onSeeRemark:A=>r(n)},null,8,["content","onSeeRemark"])])]),default:e.withCtx(()=>[e.createVNode(G,{label:"商品",width:"226"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",Tt,[e.createVNode(se,{class:"customer-Infor__img",fit:"cover",style:{width:"68px",height:"68px","margin-right":"10px"},src:n.image},null,8,["src"]),e.createElementVNode("div",Rt,[e.createElementVNode("div",Dt,[e.createElementVNode("span",Ot,e.toDisplayString(n.productName),1)]),e.createElementVNode("div",Bt,[e.createElementVNode("div",Gt,[e.createElementVNode("span",$t,e.toDisplayString(n.price),1),Lt,e.createTextVNode("+"),Ut,e.createElementVNode("span",Mt,e.toDisplayString(e.unref(h)(n.salePrice)),1)])])])])]),_:1}),e.createVNode(G,{label:"客户"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",At,[e.createElementVNode("div",qt,[e.createElementVNode("span",Pt,e.toDisplayString(n.integralOrderReceiverVO.name),1),e.createElementVNode("span",{class:"base-vip-table-top--copy",style:{"margin-right":"0",float:"right",transform:"translateY(-16px)"},onClick:A=>ee(n.integralOrderReceiverVO.name)},"复制",8,zt)]),e.createElementVNode("div",Ft,e.toDisplayString(n.integralOrderReceiverVO.mobile),1),e.createElementVNode("div",null,[e.createVNode(fe,{address:n.integralOrderReceiverVO.areaCode},null,8,["address"]),e.createTextVNode(e.toDisplayString(n.integralOrderReceiverVO.address),1)])])]),_:1}),e.createVNode(G,{prop:"sex",label:"交易信息",class:"rate_size"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",jt,[e.createTextVNode(" 运费：￥"+e.toDisplayString(n.freightPrice&&e.unref(h)(n.freightPrice).toFixed(2))+" ",1),Yt,e.createElementVNode("div",Ht,[e.createTextVNode(" 积分（现金）： "),e.createElementVNode("span",Qt,e.toDisplayString(n.price),1),Xt,e.createTextVNode("+"),Jt,e.createElementVNode("span",Wt,e.toDisplayString(e.unref(h)(n.salePrice)),1)])])]),_:1}),e.createVNode(G,{label:"订单状态"},{default:e.withCtx(({row:n})=>[e.createVNode(te,{justify:"space-between",align:"middle"},{default:e.withCtx(()=>[e.createVNode(te,{justify:"space-between",align:"middle",style:{"flex-direction":"column"}},{default:e.withCtx(()=>[e.createElementVNode("div",Kt,e.toDisplayString(e.unref(xt)[n.status]),1)]),_:2},1024)]),_:2},1024)]),_:1}),e.createVNode(G,{prop:"sex",label:"操作",width:"130"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",Zt,[e.createVNode(H,{underline:!1,type:"primary",onClick:A=>d(n.no)},{default:e.withCtx(()=>[e.createTextVNode("查看详情")]),_:2},1032,["onClick"]),["ON_DELIVERY"].includes(n.status)?(e.openBlock(),e.createBlock(H,{key:0,underline:!1,type:"danger",style:{"margin-top":"10px"},onClick:A=>f(n)},{default:e.withCtx(()=>[e.createTextVNode(" 确认收货 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),["UNPAID"].includes(n.status)?(e.openBlock(),e.createBlock(H,{key:1,underline:!1,type:"danger",style:{"margin-top":"10px"},onClick:A=>b(n)},{default:e.withCtx(()=>[e.createTextVNode(" 关闭订单 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),["PAID"].includes(n.status)?(e.openBlock(),e.createBlock(H,{key:2,underline:!1,type:"danger",style:{"margin-top":"10px"},onClick:A=>k(n)},{default:e.withCtx(()=>[e.createTextVNode(" 发货 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.createVNode(H,{underline:!1,type:"primary",style:{"margin-top":"10px"},onClick:A=>r(n)},{default:e.withCtx(()=>[e.createTextVNode(" 备注 ")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["checked-item","data","style"]),e.createVNode(pe,{"page-size":l.size,"page-num":l.current,total:l.total,onHandleSizeChange:t,onHandleCurrentChange:g},null,8,["page-size","page-num","total"]),e.createVNode(Te,{isShow:y.value,"onUpdate:isShow":c[7]||(c[7]=n=>y.value=n),ids:S.value,"onUpdate:ids":c[8]||(c[8]=n=>S.value=n),remark:N.value,"onUpdate:remark":c[9]||(c[9]=n=>N.value=n),"remark-type":"INTEGRAL",onSuccess:a},null,8,["isShow","ids","remark"]),e.createVNode(yt,{isShow:q.value,"onUpdate:isShow":c[10]||(c[10]=n=>q.value=n),"current-no":Y.value,onUploadList:w},null,8,["isShow","current-no"])],64)}}}),Ba="",ea=Object.freeze(Object.defineProperty({__proto__:null,default:Z(vt,[["__scopeId","data-v-393fc12a"]])},Symbol.toStringTag,{value:"Module"})),j=i=>(e.pushScopeId("data-v-cc353116"),i=i(),e.popScopeId(),i),ta=j(()=>e.createElementVNode("div",{class:"title"},"积分使用规则",-1)),aa={class:"use_rules p20"},oa={key:0,style:{border:"1px solid #ccc"}},la={key:1},na=j(()=>e.createElementVNode("div",{class:"title"},"积分有效期",-1)),ra={class:"use_rules p20"},sa={key:1},da=j(()=>e.createElementVNode("div",{class:"title"},"积分值规则",-1)),ia={class:"use_rules p20"},ca={key:0,style:{border:"1px solid #ccc"}},pa={key:1},ma=j(()=>e.createElementVNode("div",{class:"title"},"积分获取规则",-1)),_a={class:"use_rules p20"},fa=j(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（限制用户首次分享才可获得积分）",-1)),ua={class:"get_rules"},ga=j(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（每日首次登录获得积分，7天中有2个节点可以额外赠送积分，第8天00:00数据清空）",-1)),Va={class:"get_rules"},ha=j(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（每日签到获得积分，7天中有2个节点可以额外赠送积分，第8天00:00数据清空）",-1)),ya={class:"get_rules"},xa=j(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（成功交易笔数及实付金额只统计订单状态为【已完成】的数值，且将【已结算订单、积分订单、储值充值】剔除）",-1)),Na={class:"save"},Ca={key:0},wa={key:0},ka={key:1},ba=e.defineComponent({__name:"IntegralRules",setup(i){F.useRouter();const x=e.ref(!1),u=e.ref(!1),l=e.ref({indate:0,useRule:"<div>（1）积分使用过程中不找零、不兑现、不开发票，不可转移至其他账户。</div><div>（2）使用积分进行兑换，兑换申请一经提交, 一律不能退货</div><div>（3）如因积分商品缺货等原因导致的退货退款，积分会原路返还</div><div>（4）兑换礼品涉及运费和配送费由用户自行承担。</div><div>（5）启山智软保留最终解释权。</div>",ruleInfo:"<div>月度滚动过期制。 每个自然月的第1天00：00分自动清零 已满一年的积分。 举例：2022年8月1日开始清除2023年7月31日</div>",integralGainRule:[{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"SHARE",open:!1},{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"LOGIN",open:!1},{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"SING_IN",open:!1},{rulesParameter:{consumeJson:[{consumeGrowthValueType:"",isSelected:!1,orderQuantityAndAmount:"",presentedGrowthValue:""}]},gainRuleType:"CONSUME",open:!1}]}),o=e.ref({LOGIN:[{key:0,value:0},{key:0,value:0}],SING_IN:[{key:0,value:0},{key:0,value:0}],CONSUME:[{key:0,value:0},{key:0,value:0}]});S();const h=e.ref(),y=e.ref("");async function S(){const{code:r,data:t,msg:g}=await Ge();if(r!==200){_.ElMessage.error(g||"积分规则获取失败");return}if(t&&t.useRule){L(t),l.value=t,h.value=t.integralGainRule[3].rulesParameter.consumeJson,t.integralGainRule[3].rulesParameter.consumeJson[0].isSelected===!0?y.value="ORDER_QUANTITY":y.value="ORDER_AMOUNT";return}u.value=!0}const N=(r=!1)=>{x.value=!x.value,r&&S()},T=async()=>{if(!Y())return;const r=q();if(r==="COMPLETE"){C();const{code:t,msg:g}=await(u.value?$e(l.value):Le(l.value));if(t!==200){_.ElMessage.error(g||`${u.value?"保存":"修改"}积分规则失败`);return}_.ElMessage.success(`${u.value?"保存":"修改"}积分规则成功`),S(),w()}else _.ElMessage.info(`连续${r==="LOGIN"?"登录":"签到"}天数输入重复，请修改`)};function C(){l.value.integralGainRule.forEach(r=>{let t={};const g=o.value[r.gainRuleType];if(g){for(let a=0;a<g.length;a++){const d=g[a].key,f=g[a].value;t[d]=f}r.rulesParameter.extendValue=t}})}function L(r){r.integralGainRule.forEach(t=>{let g=[],a=o.value[t.gainRuleType];if(console.log("integralArray",a),a){for(const d in t.rulesParameter.extendValue)console.log("key",d),g.push({key:d,value:t.rulesParameter.extendValue[d]});o.value[t.gainRuleType]=g}})}function q(){const r=new Map;if(o.value.LOGIN.filter(a=>!r.has(a.key)&&r.set(a.key,a.value)).length!==o.value.LOGIN.length)return"LOGIN";r.clear();const g=o.value.SING_IN.filter(a=>!r.has(a.key)&&r.set(a.key,a.value)).length;return g!==o.value.SING_IN.length?(console.log("singInLen",g),"SING_IN"):"COMPLETE"}function Y(){const{indate:r,useRule:t,ruleInfo:g,integralGainRule:a}=l.value;if(!r)return _.ElMessage.info("请输入积分有效期"),!1;if(!t.trim().length||t==="<p><br></p>")return _.ElMessage.info("请输入积分规则"),!1;if(!g.trim().length||g==="<p><br></p>")return _.ElMessage.info("请输入积分值信息"),!1;if(a.every(b=>!!b.rulesParameter.basicsValue))return _.ElMessage.info("请输入首次赠送积分值信息"),!1;const f=X();return console.log(f),f?(_.ElMessage.info(f),!1):!0}function X(){let r="";const t=o.value.LOGIN,g=o.value.SING_IN,a=o.value.SING_IN;if(t.forEach(d=>{if(!d.key)return r="请输入连续登录天数",r;if(!d.value)return r="请输入连续登录赠送积分值",r}),r||(g.forEach(d=>{if(!d.key)return r="请输入连续签到天数",r;if(!d.value)return r="请输入连续登签到赠送积分值",r}),r)||(a.forEach(d=>{if(!d.key)return r;if(!d.value)return r="请输入赠送的积分值",r}),r))return r}function w(){x.value=!1,u.value=!1}const k=r=>{console.log(r),r==="ORDER_QUANTITY"&&(h.value[0].isSelected=!0,h.value[1].isSelected=!1),r==="ORDER_AMOUNT"&&(h.value[1].isSelected=!0,h.value[0].isSelected=!1)};return(r,t)=>{var V,m,v,ee,p,c,I,O,M,J,oe,le,ne,z,re,se,te,H,n,A;const g=e.resolveComponent("el-button"),a=e.resolveComponent("el-input-number"),d=e.resolveComponent("el-checkbox"),f=e.resolveComponent("el-col"),b=e.resolveComponent("el-input"),E=e.resolveComponent("el-radio"),U=e.resolveComponent("el-form-item"),R=e.resolveComponent("el-row"),P=e.resolveComponent("el-radio-group"),D=e.resolveDirective("dompurify-html");return e.openBlock(),e.createElementBlock("div",null,[e.withDirectives(e.createVNode(g,{round:"",type:"primary",onClick:t[0]||(t[0]=s=>N(!1))},{default:e.withCtx(()=>[e.createTextVNode("编辑积分规则")]),_:1},512),[[e.vShow,!x.value]]),ta,e.createElementVNode("div",aa,[x.value?(e.openBlock(),e.createElementBlock("div",oa,[e.createVNode(ge,{modelValue:l.value.useRule,"onUpdate:modelValue":t[1]||(t[1]=s=>l.value.useRule=s),height:200},null,8,["modelValue"])])):e.withDirectives((e.openBlock(),e.createElementBlock("div",la,null,512)),[[D,l.value.useRule]])]),na,e.createElementVNode("div",ra,[e.createElementVNode("div",null,[e.createTextVNode(" 积分有效期为 "),x.value?(e.openBlock(),e.createBlock(a,{key:0,modelValue:l.value.indate,"onUpdate:modelValue":t[2]||(t[2]=s=>l.value.indate=s),controls:!1,min:1,max:12,style:{width:"50px"}},null,8,["modelValue"])):(e.openBlock(),e.createElementBlock("span",sa,e.toDisplayString(l.value.indate),1)),e.createTextVNode(" 个月 ")])]),da,e.createElementVNode("div",ia,[x.value?(e.openBlock(),e.createElementBlock("div",ca,[e.createVNode(ge,{modelValue:l.value.ruleInfo,"onUpdate:modelValue":t[3]||(t[3]=s=>l.value.ruleInfo=s)},null,8,["modelValue"])])):e.withDirectives((e.openBlock(),e.createElementBlock("div",pa,null,512)),[[D,l.value.ruleInfo]])]),ma,e.createElementVNode("div",_a,[x.value?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(d,{modelValue:l.value.integralGainRule[0].open,"onUpdate:modelValue":t[4]||(t[4]=s=>l.value.integralGainRule[0].open=s)},{default:e.withCtx(()=>[e.createTextVNode("分享")]),_:1},8,["modelValue"]),fa,e.createElementVNode("div",ua,[e.createTextVNode(" 每日首次分享获得 "),e.createVNode(a,{modelValue:l.value.integralGainRule[0].rulesParameter.basicsValue,"onUpdate:modelValue":t[5]||(t[5]=s=>l.value.integralGainRule[0].rulesParameter.basicsValue=s),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分 ")]),e.createVNode(d,{modelValue:l.value.integralGainRule[1].open,"onUpdate:modelValue":t[6]||(t[6]=s=>l.value.integralGainRule[1].open=s)},{default:e.withCtx(()=>[e.createTextVNode("登录")]),_:1},8,["modelValue"]),ga,e.createElementVNode("div",Va,[e.createTextVNode(" 每日首次登录获得 "),e.createVNode(a,{modelValue:l.value.integralGainRule[1].rulesParameter.basicsValue,"onUpdate:modelValue":t[7]||(t[7]=s=>l.value.integralGainRule[1].rulesParameter.basicsValue=s),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分, 第 "),e.createVNode(a,{modelValue:o.value.LOGIN[0].key,"onUpdate:modelValue":t[8]||(t[8]=s=>o.value.LOGIN[0].key=s),controls:!1,min:1,max:6,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 天连续登录将额外获赠 "),e.createVNode(a,{modelValue:o.value.LOGIN[0].value,"onUpdate:modelValue":t[9]||(t[9]=s=>o.value.LOGIN[0].value=s),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分； 第 "),e.createVNode(a,{modelValue:o.value.LOGIN[1].key,"onUpdate:modelValue":t[10]||(t[10]=s=>o.value.LOGIN[1].key=s),controls:!1,min:1,max:7,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 天连续登录将额外获得 "),e.createVNode(a,{modelValue:o.value.LOGIN[1].value,"onUpdate:modelValue":t[11]||(t[11]=s=>o.value.LOGIN[1].value=s),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分。 ")]),e.createVNode(d,{modelValue:l.value.integralGainRule[2].open,"onUpdate:modelValue":t[12]||(t[12]=s=>l.value.integralGainRule[2].open=s),style:{"margin-right":"30px"}},{default:e.withCtx(()=>[e.createTextVNode("签到")]),_:1},8,["modelValue"]),ha,e.createElementVNode("div",ya,[e.createTextVNode(" 每日首次签到获得 "),e.createVNode(a,{modelValue:l.value.integralGainRule[2].rulesParameter.basicsValue,"onUpdate:modelValue":t[13]||(t[13]=s=>l.value.integralGainRule[2].rulesParameter.basicsValue=s),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分, 第 "),e.createVNode(a,{modelValue:o.value.SING_IN[0].key,"onUpdate:modelValue":t[14]||(t[14]=s=>o.value.SING_IN[0].key=s),max:6,controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 天连续签到将额外获赠 "),e.createVNode(a,{modelValue:o.value.SING_IN[0].value,"onUpdate:modelValue":t[15]||(t[15]=s=>o.value.SING_IN[0].value=s),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode("点积分； 第 "),e.createVNode(a,{modelValue:o.value.SING_IN[1].key,"onUpdate:modelValue":t[16]||(t[16]=s=>o.value.SING_IN[1].key=s),controls:!1,min:1,style:{width:"100px"},max:7},null,8,["modelValue"]),e.createTextVNode(" 天连续签到将额外获得 "),e.createVNode(a,{modelValue:o.value.SING_IN[1].value,"onUpdate:modelValue":t[17]||(t[17]=s=>o.value.SING_IN[1].value=s),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分。 ")]),e.createVNode(d,{modelValue:l.value.integralGainRule[3].open,"onUpdate:modelValue":t[18]||(t[18]=s=>l.value.integralGainRule[3].open=s),style:{"margin-right":"30px"}},{default:e.withCtx(()=>[e.createTextVNode("消费获得")]),_:1},8,["modelValue"]),xa,e.createVNode(P,{modelValue:y.value,"onUpdate:modelValue":t[19]||(t[19]=s=>y.value=s),class:"ml-4",style:{"margin-top":"5px"},onChange:k},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(h.value,(s,Sa)=>(e.openBlock(),e.createElementBlock(e.Fragment,{key:Sa},[s.consumeGrowthValueType==="ORDER_QUANTITY"?(e.openBlock(),e.createBlock(R,{key:0},{default:e.withCtx(()=>[e.createVNode(f,{span:1}),e.createVNode(f,{span:22},{default:e.withCtx(()=>[e.createVNode(U,null,{default:e.withCtx(()=>[e.createVNode(E,{modelValue:s.isSelected,"onUpdate:modelValue":B=>s.isSelected=B,label:"ORDER_QUANTITY"},{default:e.withCtx(()=>[e.createTextVNode(" 每成功交易(已完成) "),e.createVNode(b,{modelValue:s.orderQuantityAndAmount,"onUpdate:modelValue":B=>s.orderQuantityAndAmount=B,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 笔订单，奖励 "),e.createVNode(b,{modelValue:s.presentedGrowthValue,"onUpdate:modelValue":B=>s.presentedGrowthValue=B,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 点积分 ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)):s.consumeGrowthValueType==="ORDER_AMOUNT"?(e.openBlock(),e.createBlock(R,{key:1},{default:e.withCtx(()=>[e.createVNode(f,{span:1}),e.createVNode(f,{span:22},{default:e.withCtx(()=>[e.createVNode(U,null,{default:e.withCtx(()=>[e.createVNode(E,{modelValue:s.isSelected,"onUpdate:modelValue":B=>s.isSelected=B,label:"ORDER_AMOUNT"},{default:e.withCtx(()=>[e.createTextVNode(" 实付金额(不含运费)，每满 "),e.createVNode(b,{modelValue:s.orderQuantityAndAmount,"onUpdate:modelValue":B=>s.orderQuantityAndAmount=B,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 元，奖励 "),e.createVNode(b,{modelValue:s.presentedGrowthValue,"onUpdate:modelValue":B=>s.presentedGrowthValue=B,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 点积分 ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)):e.createCommentVNode("",!0)],64))),128))]),_:1},8,["modelValue"]),e.createElementVNode("div",Na,[e.createVNode(g,{round:"",onClick:t[20]||(t[20]=s=>N(!0))},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.createVNode(g,{round:"",type:"primary",onClick:T},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1})])],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[(V=l.value.integralGainRule)!=null&&V[0].open?(e.openBlock(),e.createElementBlock("div",Ca," 每日首次分享获得 "+e.toDisplayString(l.value.integralGainRule[0].rulesParameter.basicsValue)+" 点积分 ",1)):e.createCommentVNode("",!0),e.withDirectives(e.createElementVNode("div",null," 每日首次登录获得 "+e.toDisplayString((m=l.value.integralGainRule)==null?void 0:m[1].rulesParameter.basicsValue)+" 点积分, 第 "+e.toDisplayString((v=o.value.LOGIN)==null?void 0:v[0].key)+" 天连续登录将额外获赠 "+e.toDisplayString((ee=o.value.LOGIN)==null?void 0:ee[0].value)+" 点积分； 第 "+e.toDisplayString((p=o.value.LOGIN)==null?void 0:p[1].key)+" 天连续登录将额外获得 "+e.toDisplayString((c=o.value.LOGIN)==null?void 0:c[1].value)+" 点积分。 ",513),[[e.vShow,(I=l.value.integralGainRule)==null?void 0:I[1].open]]),e.withDirectives(e.createElementVNode("div",null," 每日首次签到获得 "+e.toDisplayString((O=l.value.integralGainRule)==null?void 0:O[2].rulesParameter.basicsValue)+" 点积分, 第 "+e.toDisplayString((M=o.value.SING_IN)==null?void 0:M[0].key)+" 天连续签到将额外获赠 "+e.toDisplayString((J=o.value.SING_IN)==null?void 0:J[0].value)+" 点积分； 第 "+e.toDisplayString((oe=o.value.SING_IN)==null?void 0:oe[1].key)+" 天连续签到将额外获得 "+e.toDisplayString((le=o.value.SING_IN)==null?void 0:le[1].value)+" 点积分。 ",513),[[e.vShow,(ne=l.value.integralGainRule)==null?void 0:ne[2].open]]),e.withDirectives(e.createElementVNode("div",null,[(z=h.value)!=null&&z[0].isSelected?(e.openBlock(),e.createElementBlock("p",wa," 每成功交易（已完成），"+e.toDisplayString((re=h.value)==null?void 0:re[0].orderQuantityAndAmount)+" 笔订单，奖励 "+e.toDisplayString((se=h.value)==null?void 0:se[0].presentedGrowthValue)+" 点积分 ",1)):(te=h.value)!=null&&te[1].isSelected?(e.openBlock(),e.createElementBlock("p",ka," 实付金额（不含运费），每满 "+e.toDisplayString((H=h.value)==null?void 0:H[1].orderQuantityAndAmount)+" 元，奖励 "+e.toDisplayString((n=h.value)==null?void 0:n[1].presentedGrowthValue)+" 点积分 ",1)):e.createCommentVNode("",!0)],512),[[e.vShow,(A=l.value.integralGainRule)==null?void 0:A[3].open]])],64))])])}}}),$a="",Ea=Object.freeze(Object.defineProperty({__proto__:null,default:Z(ba,[["__scopeId","data-v-cc353116"]])},Symbol.toStringTag,{value:"Module"}));return Re});
