(function(e,S){typeof exports=="object"&&typeof module<"u"?module.exports=S(require("vue"),require("vue-router"),require("decimal.js"),require("@/components/q-address/q-address.vue"),require("element-plus"),require("vue-clipboard3"),require("element-china-area-data"),require("@/components/q-address"),require("@/apis/http"),require("@/utils/date"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","vue-router","decimal.js","@/components/q-address/q-address.vue","element-plus","vue-clipboard3","element-china-area-data","@/components/q-address","@/apis/http","@/utils/date","@/composables/useConvert"],S):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformPurchaseInfo=S(e.PlatformPurchaseInfoContext.Vue,e.PlatformPurchaseInfoContext.VueRouter,e.PlatformPurchaseInfoContext.Decimal,e.PlatformPurchaseInfoContext.QAddress,e.PlatformPurchaseInfoContext.ElementPlus,e.PlatformPurchaseInfoContext.VueClipboard3,e.PlatformPurchaseInfoContext.ElementChinaAreaData,e.PlatformPurchaseInfoContext.QAddressIndex,e.PlatformPurchaseInfoContext.Request,e.PlatformPurchaseInfoContext.DateUtil,e.PlatformPurchaseInfoContext.UseConvert))})(this,function(e,S,k,Ve,P,ue,Ee,Ie,Q,Ce,be){"use strict";var K=document.createElement("style");K.textContent=`.details__status[data-v-7632e011]{display:flex;align-items:center;border:1px solid #d5d5d5}.details__status--steps[data-v-7632e011]{padding:20px;margin:20px;width:700px;border-left:1px solid #d5d5d5}.details__status--title[data-v-7632e011]{font-size:28px;font-weight:700;color:#515151;width:223px;text-align:center}.details__userInfo[data-v-7632e011]{display:flex;margin-bottom:22px;padding:0 30px}.details__userInfo--left[data-v-7632e011]{flex:.5}.details__userInfo--left div[data-v-7632e011]:nth-of-type(n+2){margin-bottom:11px}.details__userInfo--right[data-v-7632e011]{margin-left:30px;flex:.5}.details__userInfo--right div[data-v-7632e011]:nth-of-type(n+2){margin-bottom:11px}.details__userInfo--title[data-v-7632e011]{font-size:14px;color:#333;font-weight:700;margin:17px 0}.details__table--main[data-v-7632e011]{height:300px}.details__table--main .commodity[data-v-7632e011]{display:flex;align-items:stretch}.details__table--main .commodity .commodity__info[data-v-7632e011]{margin-left:15px;flex:1;overflow:hidden;display:flex;flex-direction:column;justify-content:flex-start;align-items:flex-start;text-align:left}.details__table--main .commodity .commodity__info--title[data-v-7632e011]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;width:100%}.details__table--main .commodity .commodity__info--spec[data-v-7632e011]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;font-size:.8em;width:100%}.details__subtotal[data-v-7632e011]{display:flex;flex-direction:column;align-items:flex-end;width:100%;margin-top:30px;line-height:1.5}.details__subtotal--title[data-v-7632e011]{font-size:1.3em;font-weight:600}.details__subtotal .pay-price[data-v-7632e011]{font-size:1.2em}.details .text-red[data-v-7632e011]{color:red}.proof-img[data-v-7632e011]{width:350px;height:350px;object-fit:contain}.copy[data-v-7632e011]{margin-left:5px;color:#409eff;cursor:pointer}.logisticsInfo[data-v-8f575d92]{display:flex;justify-content:space-between}.logisticsInfo__left[data-v-8f575d92]{flex:3}.logisticsInfo__right[data-v-8f575d92]{flex:2}.logisticsInfo__timeline-con[data-v-8f575d92]{max-height:500px;overflow:auto}.logisticsInfo__title[data-v-8f575d92]{font-size:14px;color:#333;font-weight:700;margin:17px 0}.logisticsInfo__text[data-v-8f575d92]{margin-bottom:11px}.logisticsInfo__timeline[data-v-8f575d92]{margin-top:20px}.logisticsInfo__timeline li[data-v-8f575d92]:nth-child(1) .el-timeline-item__timestamp{color:#000}.logisticsInfo__timeline--status[data-v-8f575d92]{font-size:13px;font-weight:700;margin-right:10px;color:#838383}.logisticsInfo__timeline--time[data-v-8f575d92]{color:#838383}.logisticsInfo__divider[data-v-8f575d92]{height:4px;margin:0 -15px;background:#f2f2f2}
`,document.head.appendChild(K);const Te=o=>Q.get({url:`addon-supplier/supplier/order/delivery/${o}`}),Se=o=>Q.get({url:`addon-supplier/supplier/order/${o}`}),ke=(o,c)=>Q.get({url:"gruul-mall-freight/logistics/node",params:{companyCode:o,waybillNo:c}}),Pe=e.defineComponent({__name:"PlatformPurchaseInfo",setup(o){const c=e.ref("basic"),t={basic:e.defineAsyncComponent(()=>Promise.resolve().then(()=>mt)),delivery:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Pt))},a=e.ref(0),l=S.useRoute(),p=e.ref();async function m(){if(l.query.orderNo){const{code:i,data:s}=await Se(l.query.orderNo);p.value=s,a.value=Date.now()}}const g=e.computed(()=>{var i,s;return(s=(i=p==null?void 0:p.value)==null?void 0:i.orderItems)==null?void 0:s.filter(d=>d.packageStatus!=="WAITING_FOR_DELIVER").length});return m(),(i,s)=>{const d=e.resolveComponent("el-tab-pane"),h=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(h,{modelValue:c.value,"onUpdate:modelValue":s[0]||(s[0]=N=>c.value=N)},{default:e.withCtx(()=>[e.createVNode(d,{label:"订单信息",name:"basic"}),g.value&&g.value>0?(e.openBlock(),e.createBlock(d,{key:0,label:"物流信息",name:"delivery"})):e.createCommentVNode("",!0)]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(t[c.value]),{key:a.value,order:p.value,reload:m},null,8,["order"]))])}}}),{divTenThousand:Y}=be(),{toClipboard:X}=ue();new Ce;const De={待支付:1,待审核:2,待发货:2,部分发货:2,待入库:3,已完成:3,已关闭:1},Oe={OFFLINE:"线下支付",BALANCE:"余额支付"},$e=(o,c)=>{const t=e.ref(),a=e.ref(null);t.value=o;const l=e.reactive({statusText:"",activeStep:0}),p=l.statusText=Re(o);l.activeStep=De[p];const m=d=>{X(d).then(()=>{P.ElMessage.success("复制成功")}).catch(()=>P.ElMessage.error("复制失败"))},g=()=>{var h,N,A,_,y,D,O,x,C,V,b,u,E;const d=`
            收货人姓名：${(A=(N=(h=t==null?void 0:t.value)==null?void 0:h.extra)==null?void 0:N.receiver)==null?void 0:A.name}

            联系人电话：${(D=(y=(_=t==null?void 0:t.value)==null?void 0:_.extra)==null?void 0:y.receiver)==null?void 0:D.mobile}

            收货地址：${Ie.AddressFn(Ee.regionData,((x=(O=t==null?void 0:t.value)==null?void 0:O.extra)==null?void 0:x.receiver.areaCode)||[])}${(b=(V=(C=t==null?void 0:t.value)==null?void 0:C.extra)==null?void 0:V.receiver)==null?void 0:b.address}

            采购备注：${(E=(u=t==null?void 0:t.value)==null?void 0:u.extra)==null?void 0:E.remark}
        `;X(d).then(()=>P.ElMessage.success("复制成功")).catch(()=>P.ElMessage.error("复制失败"))},i=e.computed(()=>d=>Be(d)),s=e.computed(()=>d=>Ae(d));return{orderDetails:t,stepInfo:l,payTypeMap:Oe,divTenThousand:Y,computedCalculateFreight:i,computedCalculateCommodityPrice:s,payOrderRef:a,copyOrderNo:m,handleCopyReceiver:g}},Be=(o=[])=>o.reduce((c,t)=>c.plus(new k(Y(t.freightPrice))),new k(0)),Ae=(o=[])=>o.reduce((c,t)=>c.plus(new k(Y(t.salePrice).mul(new k(t.num)))),new k(0)),Re=o=>{if(!o)return"";if(o.status==="UNPAID")return"待支付";if(o.status==="PAYMENT_AUDIT")return"待审核";const c=["WAITING_FOR_DELIVER","WAITING_FOR_RECEIVE","COMPLETED"],t={WAITING_FOR_DELIVER:"待发货",WAITING_FOR_RECEIVE:"待入库",COMPLETED:"已完成"};if(o.status==="PAID"){let a="COMPLETED";for(const p of o.orderItems){const m=c.findIndex(g=>g===a);if(p.packageStatus==="WAITING_FOR_DELIVER"&&m>0){a="WAITING_FOR_DELIVER";continue}p.packageStatus==="WAITING_FOR_RECEIVE"&&m>1&&(a="WAITING_FOR_RECEIVE")}let l=t[a];return l==="待发货"&&o.orderItems.find(m=>m.packageStatus!=="WAITING_FOR_DELIVER")&&(l="部分发货"),l}return"已关闭"},qe=()=>{const o=e.ref(!1),c=e.ref("");return{showProof:o,goToShowProof:a=>{var l,p;c.value=((p=(l=a==null?void 0:a.extra)==null?void 0:l.pay)==null?void 0:p.proof)||"",o.value=!0},currentProof:c}},B=o=>(e.pushScopeId("data-v-7632e011"),o=o(),e.popScopeId(),o),Fe={class:"details"},we={class:"details__status"},Me={class:"details__status--title"},Le={class:"details__status--steps"},je={class:"details__userInfo"},We={class:"details__userInfo--left"},ze=B(()=>e.createElementVNode("div",{class:"details__userInfo--title"},"订单信息",-1)),Ge=B(()=>e.createElementVNode("div",null,"配送方式：快递配送",-1)),Ue={class:"details__userInfo--left"},He={class:"details__userInfo--title"},Qe=B(()=>e.createElementVNode("span",null,"收货人信息",-1)),Ye={class:"details__userInfo--right"},Je=B(()=>e.createElementVNode("div",{class:"details__userInfo--title"},"供应商信息",-1)),Ke={class:"details__table"},Xe={class:"commodity"},Ze={class:"commodity__info"},ve={class:"commodity__info--title"},et={class:"commodity__info--spec"},tt={class:"details__subtotal"},ot=B(()=>e.createElementVNode("div",{class:"details__subtotal--title"},"订单总计",-1)),nt={class:"details__subtotal--line"},at={class:"details__subtotal--line"},st={class:"text-red"},lt={class:"details__subtotal--line"},it={class:"text-red"},rt={class:"details__subtotal--line pay-price"},ct={class:"text-red"},dt=["src"],pt={class:"dialog-footer"},_t=e.defineComponent({__name:"basic",props:{order:{type:Object,default:()=>({})},reload:{type:Function,default:()=>({})}},setup(o){const c=o,{orderDetails:t,stepInfo:a,payTypeMap:l,divTenThousand:p,computedCalculateFreight:m,computedCalculateCommodityPrice:g,copyOrderNo:i,handleCopyReceiver:s}=$e(c.order,c.reload),{showProof:d,goToShowProof:h,currentProof:N}=qe();return(A,_)=>{var u,E,R,q,F,w,I,$,M,L,j,W,z,G,r,T,v,ee,te,oe,ne,ae,se,le,ie,re,ce,de,pe,_e,me,fe,ge,he;const y=e.resolveComponent("el-step"),D=e.resolveComponent("el-steps"),O=e.resolveComponent("el-image"),x=e.resolveComponent("el-table-column"),C=e.resolveComponent("el-table"),V=e.resolveComponent("el-button"),b=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",Fe,[e.createElementVNode("div",we,[e.createElementVNode("div",Me,[e.createElementVNode("p",null,e.toDisplayString(e.unref(a).statusText),1)]),e.createElementVNode("div",Le,[e.createVNode(D,{active:e.unref(a).activeStep,"align-center":"","finish-status":"finish"},{default:e.withCtx(()=>{var n,f,U,H,xe,ye,Ne;return[e.createVNode(y,{description:(n=e.unref(t))==null?void 0:n.createTime,title:"买家下单"},null,8,["description"]),e.createVNode(y,{description:(U=(f=e.unref(t))==null?void 0:f.timeNodes)==null?void 0:U.payTime,title:"买家付款"},null,8,["description"]),e.createVNode(y,{description:(xe=(H=e.unref(t))==null?void 0:H.timeNodes)==null?void 0:xe.deliveryTime,title:"卖家发货"},null,8,["description"]),e.createVNode(y,{description:(Ne=(ye=e.unref(t))==null?void 0:ye.timeNodes)==null?void 0:Ne.receiveTime,title:"买家收货"},null,8,["description"])]}),_:1},8,["active"])])]),e.createElementVNode("div",je,[e.createElementVNode("div",We,[ze,e.createElementVNode("div",null,[e.createElementVNode("span",null,"订单编号："+e.toDisplayString((u=e.unref(t))==null?void 0:u.no),1),e.createElementVNode("span",{class:"copy",onClick:_[0]||(_[0]=n=>{var f;return e.unref(i)(((f=e.unref(t))==null?void 0:f.no)||"")})},"复制")]),e.createElementVNode("div",null,"下单时间："+e.toDisplayString((E=e.unref(t))==null?void 0:E.createTime),1),e.createElementVNode("div",null,"支付时间："+e.toDisplayString((q=(R=e.unref(t))==null?void 0:R.timeNodes)==null?void 0:q.payTime),1),e.createElementVNode("div",null,[e.createElementVNode("span",null,"支付方式："+e.toDisplayString(e.unref(l)[(I=(w=(F=e.unref(t))==null?void 0:F.extra)==null?void 0:w.pay)==null?void 0:I.payType]),1),((L=(M=($=e.unref(t))==null?void 0:$.extra)==null?void 0:M.pay)==null?void 0:L.payType)==="OFFLINE"?(e.openBlock(),e.createElementBlock("span",{key:0,class:"copy",onClick:_[1]||(_[1]=n=>{var f,U,H;return e.unref(h)({extra:{pay:{proof:((H=(U=(f=e.unref(t))==null?void 0:f.extra)==null?void 0:U.pay)==null?void 0:H.proof)||""}}})})},"付款凭证")):e.createCommentVNode("",!0)]),Ge]),e.createElementVNode("div",Ue,[e.createElementVNode("div",He,[Qe,e.createElementVNode("span",{class:"copy",onClick:_[2]||(_[2]=(...n)=>e.unref(s)&&e.unref(s)(...n))},"复制")]),e.createElementVNode("div",null,"收货人姓名："+e.toDisplayString((z=(W=(j=e.unref(t))==null?void 0:j.extra)==null?void 0:W.receiver)==null?void 0:z.name),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString((T=(r=(G=e.unref(t))==null?void 0:G.extra)==null?void 0:r.receiver)==null?void 0:T.mobile),1),e.createElementVNode("div",null,[e.createTextVNode(" 收货地址： "),e.createVNode(Ve,{address:(ee=(v=e.unref(t))==null?void 0:v.extra)==null?void 0:ee.receiver.areaCode},null,8,["address"]),e.createTextVNode(" "+e.toDisplayString((ne=(oe=(te=e.unref(t))==null?void 0:te.extra)==null?void 0:oe.receiver)==null?void 0:ne.address),1)]),e.createElementVNode("div",null,"采购备注："+e.toDisplayString((se=(ae=e.unref(t))==null?void 0:ae.extra)==null?void 0:se.remark),1)]),e.createElementVNode("div",Ye,[Je,e.createElementVNode("div",null,"供应商名称："+e.toDisplayString((ie=(le=e.unref(t))==null?void 0:le.extraInfo)==null?void 0:ie.supplierName),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString((ce=(re=e.unref(t))==null?void 0:re.extraInfo)==null?void 0:ce.supplierPhone),1)])]),e.createElementVNode("div",Ke,[e.createVNode(C,{data:(de=e.unref(t))==null?void 0:de.orderItems,border:"",class:"details__table--main"},{default:e.withCtx(()=>[e.createVNode(x,{align:"center",label:"商品",width:"460"},{default:e.withCtx(({row:n})=>{var f;return[e.createElementVNode("div",Xe,[e.createVNode(O,{src:n==null?void 0:n.image,fit:"cover",style:{width:"70px",height:"70px"}},null,8,["src"]),e.createElementVNode("div",Ze,[e.createElementVNode("span",ve,e.toDisplayString(n==null?void 0:n.productName),1),e.createElementVNode("span",et,e.toDisplayString((f=n==null?void 0:n.specs)==null?void 0:f.join(";")),1)])])]}),_:1}),e.createVNode(x,{align:"center",label:"采购单价"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(e.unref(p)(n==null?void 0:n.salePrice)),1)]),_:1}),e.createVNode(x,{align:"center",label:"采购数量",prop:"num"}),e.createVNode(x,{align:"center",label:"采购金额"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(e.unref(p)(n==null?void 0:n.salePrice).mul(new e.unref(k)(n.num))),1)]),_:1}),e.createVNode(x,{label:"实际入库量",prop:"used",align:"center"})]),_:1},8,["data"])]),e.createElementVNode("div",tt,[ot,e.createElementVNode("div",nt," 采购总数："+e.toDisplayString((_e=(pe=e.unref(t))==null?void 0:pe.orderItems)==null?void 0:_e.reduce((n,f)=>n+f.num,0)),1),e.createElementVNode("div",at,[e.createTextVNode(" 商品总价："),e.createElementVNode("span",st,e.toDisplayString(e.unref(g)((me=e.unref(t))==null?void 0:me.orderItems)),1)]),e.createElementVNode("div",lt,[e.createTextVNode(" 运费："),e.createElementVNode("span",it,e.toDisplayString(e.unref(m)((fe=e.unref(t))==null?void 0:fe.orderItems)),1)]),e.createElementVNode("div",rt,[e.createTextVNode(" 采购金额("+e.toDisplayString(((ge=e.unref(t))==null?void 0:ge.status)==="PAID"?"已付款":"应付款")+")：",1),e.createElementVNode("span",ct,e.toDisplayString(e.unref(p)((he=e.unref(t))==null?void 0:he.payAmount))+"元",1)])])]),e.createVNode(b,{modelValue:e.unref(d),"onUpdate:modelValue":_[5]||(_[5]=n=>e.isRef(d)?d.value=n:null),title:"付款凭证",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",pt,[e.createVNode(V,{onClick:_[3]||(_[3]=n=>d.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(V,{type:"primary",onClick:_[4]||(_[4]=n=>d.value=!1)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("img",{src:e.unref(N),class:"proof-img"},null,8,dt)]),_:1},8,["modelValue"])])}}}),Dt="",Z=(o,c)=>{const t=o.__vccOpts||o;for(const[a,l]of c)t[a]=l;return t},mt=Object.freeze(Object.defineProperty({__proto__:null,default:Z(_t,[["__scopeId","data-v-7632e011"]])},Symbol.toStringTag,{value:"Module"})),J=o=>(e.pushScopeId("data-v-8f575d92"),o=o(),e.popScopeId(),o),ft={style:{"padding-left":"30px","margin-bottom":"30px"}},gt=J(()=>e.createElementVNode("div",{style:{color:"#000","font-size":"16px","margin-left":"-10px","font-weight":"700","margin-bottom":"10px"}},"收货人信息",-1)),ht={class:"logisticsInfo"},xt={class:"logisticsInfo__left"},yt={style:{display:"flex","align-items":"center"}},Nt={key:0,style:{"margin-top":"20px"}},Vt=J(()=>e.createElementVNode("div",{class:"logisticsInfo__title"},"物流详情",-1)),ut={class:"logisticsInfo__right"},Et=J(()=>e.createElementVNode("div",{class:"logisticsInfo__title"},"物流信息",-1)),It={key:0},Ct={class:"logisticsInfo__text"},bt={class:"logisticsInfo__text"},Tt={class:"logisticsInfo__text"},St={key:1,style:{"text-align":"center",height:"100px","line-height":"100px"}},kt=e.defineComponent({__name:"delivery",props:{order:{type:Object,default(){return{}}}},setup(o){const c=S.useRoute(),t=e.ref([]),a=e.ref([]),l=e.ref(0);p();async function p(){const{code:i,data:s}=await Te(c.query.orderNo);if(i!==200)return P.ElMessage.error("包裹详情获取失败");s.length&&(a.value=s,m())}const m=async()=>{const i=a.value[l.value];if(i.type!=="WITHOUT")try{const{data:s,code:d,msg:h}=await ke(i.express.expressCompanyCode,i.express.expressNo);if(d!==200)return P.ElMessage.error("包裹详情获取失败");if(!s.data){g(s,i);return}t.value=s.data.reverse()}catch(s){t.value=[{status:"包裹异常",time:i==null?void 0:i.createTime,context:s.msg}]}};function g(i,s){switch(i.returnCode){case"401":t.value=[{status:"包裹异常",time:s.createTime,context:i.message}];break;case"400":t.value=[{status:"包裹异常",time:s.createTime,context:i.message}];break;default:t.value=[{status:"包裹异常",time:s.createTime,context:i.message}];break}}return(i,s)=>{var C,V,b,u,E,R,q,F,w;const d=e.resolveComponent("el-image"),h=e.resolveComponent("el-table-column"),N=e.resolveComponent("el-table"),A=e.resolveComponent("el-row"),_=e.resolveComponent("el-timeline-item"),y=e.resolveComponent("el-timeline"),D=e.resolveComponent("el-scrollbar"),O=e.resolveComponent("el-tab-pane"),x=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",ft,[gt,e.createElementVNode("div",null,"收货人姓名："+e.toDisplayString((b=(V=(C=a.value)==null?void 0:C[0])==null?void 0:V.receiver)==null?void 0:b.name),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString((R=(E=(u=a.value)==null?void 0:u[0])==null?void 0:E.receiver)==null?void 0:R.mobile),1),e.createElementVNode("div",null,"收货地址："+e.toDisplayString((w=(F=(q=a.value)==null?void 0:q[0])==null?void 0:F.receiver)==null?void 0:w.address),1)]),e.createVNode(x,{modelValue:l.value,"onUpdate:modelValue":s[0]||(s[0]=I=>l.value=I),type:"card",onTabChange:m},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.value,(I,$)=>(e.openBlock(),e.createBlock(O,{key:$,label:"包裹"+($+1),name:$},{default:e.withCtx(()=>{var M,L,j,W,z,G;return[e.createElementVNode("div",ht,[e.createElementVNode("div",xt,[e.createVNode(N,{data:I.orderItems,border:"",style:{width:"500px"}},{default:e.withCtx(()=>[e.createVNode(h,{label:"商品 ",width:"400px"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",yt,[e.createVNode(d,{src:r==null?void 0:r.image,title:r==null?void 0:r.productName,fits:"cover",shape:"square",size:"large",style:{width:"70px",height:"70px","margin-right":"10px"}},null,8,["src","title"]),e.createElementVNode("div",null,[e.createElementVNode("div",null,e.toDisplayString(r==null?void 0:r.productName),1),e.createElementVNode("div",null,e.toDisplayString(r==null?void 0:r.specs),1)])])]),_:1}),e.createVNode(h,{align:"center",label:"发货数"},{default:e.withCtx(({row:r})=>[e.createTextVNode(e.toDisplayString(r==null?void 0:r.num),1)]),_:1})]),_:2},1032,["data"]),I.type!=="WITHOUT"?(e.openBlock(),e.createElementBlock("div",Nt,[Vt,e.createVNode(D,{ref_for:!0,ref:"scrollbarRef",height:"500px"},{default:e.withCtx(()=>[e.createVNode(y,{class:"logisticsInfo__timeline"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,(r,T)=>(e.openBlock(),e.createBlock(_,{key:T,color:T===0?"#409eff":" ",timestamp:`${r.context}`,class:"logisticsInfo__timeline--item",style:{"padding-bottom":"42px"}},{default:e.withCtx(()=>[e.createVNode(A,null,{default:e.withCtx(()=>[e.createElementVNode("div",{style:e.normalizeStyle({color:T===0?"#409eff":" "}),class:"logisticsInfo__timeline--status"},e.toDisplayString(r.status),5),e.createElementVNode("div",{style:e.normalizeStyle({color:T===0?"#409eff":" "}),class:"logisticsInfo__timeline--time"},e.toDisplayString(r.time),5)]),_:2},1024)]),_:2},1032,["color","timestamp"]))),128))]),_:2},1024)]),_:2},1536)])):e.createCommentVNode("",!0)]),e.createElementVNode("div",ut,[Et,I.type!=="WITHOUT"?(e.openBlock(),e.createElementBlock("div",It,[e.createElementVNode("div",Ct,"收货地址："+e.toDisplayString((L=(M=a.value[l.value])==null?void 0:M.receiver)==null?void 0:L.address),1),e.createElementVNode("div",bt,"物流公司："+e.toDisplayString((W=(j=a.value[l.value])==null?void 0:j.express)==null?void 0:W.expressCompanyName),1),e.createElementVNode("div",Tt,"物流单号："+e.toDisplayString((G=(z=a.value[l.value])==null?void 0:z.express)==null?void 0:G.expressNo),1)])):(e.openBlock(),e.createElementBlock("div",St,"无需物流"))])])]}),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])}}}),$t="",Pt=Object.freeze(Object.defineProperty({__proto__:null,default:Z(kt,[["__scopeId","data-v-8f575d92"]])},Symbol.toStringTag,{value:"Module"}));return Pe});
