(function(e,E){typeof exports=="object"&&typeof module<"u"?module.exports=E(require("vue"),require("@/apis/http"),require("element-plus"),require("decimal.js"),require("@/composables/useConvert"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@/apis/http","element-plus","decimal.js","@/composables/useConvert","vue-router"],E):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopEditConsignmentCommodity=E(e.ShopEditConsignmentCommodityContext.Vue,e.ShopEditConsignmentCommodityContext.Request,e.ShopEditConsignmentCommodityContext.ElementPlus,e.ShopEditConsignmentCommodityContext.Decimal,e.ShopEditConsignmentCommodityContext.UseConvert,e.ShopEditConsignmentCommodityContext.VueRouter))})(this,function(e,E,G,p,ne,z){"use strict";var K=document.createElement("style");K.textContent=`.distribution__title[data-v-a930f6a6]{padding:15px 0}.distribution__container[data-v-a930f6a6]{padding:0 20px}.distribution__btns[data-v-a930f6a6]{margin:30px auto 0;width:30%;display:flex;justify-content:space-between;align-items:center}
`,document.head.appendChild(K);var Y=(n=>(n.UNLIMITED="不限购",n.PRODUCT_LIMITED="商品限购",n.SKU_LIMITED="规格限购",n))(Y||{});const oe=()=>E.get({url:"gruul-mall-addon-platform/platform/shop/signing/category/choosable/list"}),re=n=>E.get({url:"gruul-mall-goods/goods/product/category",params:n}),le=()=>E.get({url:"gruul-mall-goods/consignment/config"}),ie=n=>E.get({url:`gruul-mall-goods/manager/product/consignment/${n}`}),ae=n=>E.post({url:"gruul-mall-goods/manager/product/consignment/update",data:n}),{divTenThousand:I,mulTenThousand:M}=ne(),se={name:"",saleDescribe:"",platformCategoryId:[],categoryId:"",providerId:"",widePic:"",distributionMode:[],videoUrl:"",albumPics:"",productType:"REAL_PRODUCT",specGroups:[],platformCategory:{one:null,two:null,three:null},shopCategory:{one:null,two:null,three:null},skus:[{id:"",image:"",initSalesVolume:0,limitNum:0,limitType:"UNLIMITED",price:0,productId:"",initStock:0,salePrice:0,shopId:"",stockType:"UNLIMITED",weight:0,specs:[]}],productParameters:[],productAttributes:[],serviceIds:[],detail:"",freightTemplateId:"0",status:"SELL_ON",brandId:""},ce=()=>{const n=z.useRoute(),r=z.useRouter(),a=e.ref(null),s=e.ref([]),c=e.ref([]),l=e.reactive({name:"",shopCategory:[],saleDescribe:"",consignmentPriceSetting:{type:"UNIFY",sale:0,scribe:0},unifyPriceSetting:{type:"",sale:"",scribe:""}}),P={name:{required:!0,message:"请输入商品名称",trigger:"blur"},shopCategory:{required:!0,message:"请选择店铺类目",trigger:"change",type:"array"}},$={expandTrigger:"hover",label:"name",value:"id"},k=e.ref(se),m=e.ref([]),_=e.ref([]);async function C(){const{code:d,data:o,success:N}=await oe();if(d!==200){G.ElMessage.error("获取平台分类失败");return}m.value=j(1,o==null?void 0:o.map(V=>({...V,id:V.categoryId})))}async function h(){const{code:d,data:o}=await re({current:1,size:500});if(d!==200){G.ElMessage.error("获取店铺分类失败");return}_.value=j(1,o.records)}const i=async()=>{var V,U,y,B,S,D,L,T,A,F,t,g,b,x,J,O,Q,X,Z,v;const d=n.query.id,{data:o}=await ie(d);l.name=o==null?void 0:o.name,l.saleDescribe=o==null?void 0:o.saleDescribe,k.value.platformCategoryId=[(U=(V=o==null?void 0:o.extra)==null?void 0:V.platformCategory)==null?void 0:U.one,(B=(y=o==null?void 0:o.extra)==null?void 0:y.platformCategory)==null?void 0:B.two,(D=(S=o==null?void 0:o.extra)==null?void 0:S.platformCategory)==null?void 0:D.three],l.consignmentPriceSetting.type=(T=(L=o==null?void 0:o.extra)==null?void 0:L.consignmentPriceSetting)==null?void 0:T.type,l.consignmentPriceSetting.sale=I((F=(A=o==null?void 0:o.extra)==null?void 0:A.consignmentPriceSetting)==null?void 0:F.sale).toNumber(),l.consignmentPriceSetting.scribe=I((g=(t=o==null?void 0:o.extra)==null?void 0:t.consignmentPriceSetting)==null?void 0:g.scribe).toNumber(),l.shopCategory=[(x=(b=o==null?void 0:o.extra)==null?void 0:b.shopCategory)==null?void 0:x.one,(O=(J=o==null?void 0:o.extra)==null?void 0:J.shopCategory)==null?void 0:O.two,(X=(Q=o==null?void 0:o.extra)==null?void 0:Q.shopCategory)==null?void 0:X.three];const N=(Z=o==null?void 0:o.storageSpecSku)==null?void 0:Z[0];s.value=(v=N==null?void 0:N.skus)==null?void 0:v.map(W=>{var ee,te;return{...W,salePrice:(ee=I(W.salePrice))==null?void 0:ee.toString(),price:(te=I(W.price))==null?void 0:te.toString()}}),c.value=(N==null?void 0:N.specGroups)||[],H(l,s)},f=async()=>{const{data:d,code:o}=await le();o===200&&d&&(l.unifyPriceSetting.sale=I(d==null?void 0:d.sale).toString(),l.unifyPriceSetting.scribe=I(d==null?void 0:d.scribe).toString(),l.unifyPriceSetting.type=d==null?void 0:d.type)};e.watch(()=>l.consignmentPriceSetting,()=>H(l,s),{deep:!0});const R=async()=>{C(),h();try{await f()}finally{i()}},q=()=>{r.back()},w=()=>{var d;(d=a.value)==null||d.validate(async o=>{var N,V,U,y;if(o){const B={shopCategory:{one:(N=l==null?void 0:l.shopCategory)==null?void 0:N[0],two:(V=l==null?void 0:l.shopCategory)==null?void 0:V[1],three:(U=l==null?void 0:l.shopCategory)==null?void 0:U[2]},id:(y=n.query)==null?void 0:y.id,name:l.name,saleDescribe:l.saleDescribe},S={};l.consignmentPriceSetting.type==="UNIFY"?(S.type=l.unifyPriceSetting.type,S.sale=M(l.unifyPriceSetting.sale).toString(),S.scribe=M(l.unifyPriceSetting.scribe).toString()):(S.type=l.consignmentPriceSetting.type,S.sale=M(l.consignmentPriceSetting.sale).toString(),S.scribe=M(l.consignmentPriceSetting.scribe).toString()),B.consignmentPriceSetting=S;const{code:D,success:L,msg:T}=await ae(B);D===200&&L?(G.ElMessage.success({message:T||"修改商品信息成功"}),q()):G.ElMessage.error({message:T})}})};return e.onMounted(()=>R()),{formRef:a,distributionFormModel:l,distributionFormRules:P,platformCategoryList:m,currentComodityInfo:k,shopCascaderProps:$,shopCategoryList:_,distributionSkus:s,divTenThousand:I,specGroups:c,cancelDistribution:q,handleConfirmDistribution:w}};function H(n,r){var a,s,c,l,P,$,k;((a=n==null?void 0:n.consignmentPriceSetting)==null?void 0:a.type)==="UNIFY"?((s=n==null?void 0:n.unifyPriceSetting)==null?void 0:s.type)==="RATE"?r.value=(c=r.value)==null?void 0:c.map(m=>{var _,C,h,i;return{...m,actualSalePrice:(C=new p(m.salePrice))==null?void 0:C.mul(new p(1).add(new p(((_=n==null?void 0:n.unifyPriceSetting)==null?void 0:_.sale)||0).div(100))).toString(),actualPrice:(i=new p(m.price))==null?void 0:i.mul(new p(1).add(new p(((h=n==null?void 0:n.unifyPriceSetting)==null?void 0:h.scribe)||0).div(100))).toString()}}):r.value=(l=r.value)==null?void 0:l.map(m=>{var _,C,h,i;return{...m,actualSalePrice:(C=new p(m.salePrice))==null?void 0:C.add(new p(((_=n==null?void 0:n.unifyPriceSetting)==null?void 0:_.sale)||0)).toString(),actualPrice:(i=new p(m.price))==null?void 0:i.add(new p(((h=n==null?void 0:n.unifyPriceSetting)==null?void 0:h.scribe)||0)).toString()}}):((P=n==null?void 0:n.consignmentPriceSetting)==null?void 0:P.type)==="RATE"?r.value=($=r.value)==null?void 0:$.map(m=>{var _,C,h,i;return{...m,actualSalePrice:(C=new p(m.salePrice))==null?void 0:C.mul(new p(1).add(new p((_=n==null?void 0:n.consignmentPriceSetting)==null?void 0:_.sale).div(100))).toString(),actualPrice:(i=new p(m.price))==null?void 0:i.mul(new p(1).add(new p((h=n==null?void 0:n.consignmentPriceSetting)==null?void 0:h.scribe).div(100))).toString()}}):r.value=(k=r.value)==null?void 0:k.map(m=>{var _,C,h,i;return{...m,actualSalePrice:(C=new p(m.salePrice))==null?void 0:C.add(new p((_=n==null?void 0:n.consignmentPriceSetting)==null?void 0:_.sale)).toString(),actualPrice:(i=new p(m.price))==null?void 0:i.add(new p((h=n==null?void 0:n.consignmentPriceSetting)==null?void 0:h.scribe)).toString()}})}function j(n,r){const a=n===3;for(let s=0;s<r.length;){const c=r[s];if(a){c.disabled=!1,s++;continue}const l=c.children||c.secondCategoryVos||c.categoryThirdlyVos;delete c.secondCategoryVos,delete c.categoryThirdlyVos;const P=!l||l.length===0;if(c.disabled=P,P){r.splice(s,1);continue}if(j(n+1,l),l.length===0){r.splice(s,1);continue}c.children=l,s++}return r}const de=e.createElementVNode("span",null,"销售价 = 供货价 + （供货价 *  ",-1),pe=e.createElementVNode("span",null," % ）",-1),me=e.createElementVNode("span",null,"划线价 = 销售价 + （销售价 *  ",-1),ge=e.createElementVNode("span",null," % ）",-1),_e=e.createElementVNode("span",null,"销售价 = 供货价 +  ",-1),ye=e.createElementVNode("span",null," 元",-1),Ce=e.createElementVNode("span",null,"划线价 = 销售价 +  ",-1),he=e.createElementVNode("span",null," 元",-1),Ve=e.defineComponent({__name:"batch-unify-settings",props:{type:{default:""},sale:{default:0},scribe:{default:0}},setup(n){return(r,a)=>{const s=e.resolveComponent("el-input-number"),c=e.resolveComponent("el-form-item");return r.type==="RATE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(c,{prop:"sale"},{default:e.withCtx(()=>[de,e.createVNode(s,{"model-value":r.sale,controls:!1,precision:2,disabled:""},null,8,["model-value"]),pe]),_:1}),e.createVNode(c,{prop:"scribe"},{default:e.withCtx(()=>[me,e.createVNode(s,{"model-value":r.scribe,controls:!1,precision:2,disabled:""},null,8,["model-value"]),ge]),_:1})],64)):r.type==="REGULAR"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createVNode(c,{prop:"sale"},{default:e.withCtx(()=>[_e,e.createVNode(s,{"model-value":r.sale,controls:!1,precision:2,disabled:""},null,8,["model-value"]),ye]),_:1}),e.createVNode(c,{prop:"scribe"},{default:e.withCtx(()=>[Ce,e.createVNode(s,{"model-value":r.scribe,controls:!1,precision:2,disabled:""},null,8,["model-value"]),he]),_:1})],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[],64))}}}),u=n=>(e.pushScopeId("data-v-a930f6a6"),n=n(),e.popScopeId(),n),fe={class:"distribution"},Ne=u(()=>e.createElementVNode("div",{class:"distribution__title"},"基础信息",-1)),Se={class:"distribution__container"},be=u(()=>e.createElementVNode("div",{class:"distribution__title"},"单规格信息",-1)),ue={class:"distribution__container"},xe=u(()=>e.createElementVNode("div",{class:"distribution__title"},"价格设置",-1)),Pe={class:"distribution__container"},Ee={class:"price-settings"},we=u(()=>e.createElementVNode("span",null,"销售价 = 供货价 + （供货价 *  ",-1)),Te=u(()=>e.createElementVNode("span",null," % ）",-1)),Ie=u(()=>e.createElementVNode("span",null,"划线价 = 销售价 + （销售价 *  ",-1)),ke=u(()=>e.createElementVNode("span",null," % ）",-1)),Ue=u(()=>e.createElementVNode("span",null,"销售价 = 供货价 +  ",-1)),Be=u(()=>e.createElementVNode("span",null," 元",-1)),De=u(()=>e.createElementVNode("span",null,"划线价 = 销售价 +  ",-1)),Le=u(()=>e.createElementVNode("span",null," 元",-1)),$e={key:0,class:"distribution__container--actual"},qe={class:"distribution__btns"},Ae=e.defineComponent({__name:"ShopEditConsignmentCommodity",setup(n){const{distributionFormModel:r,distributionSkus:a,distributionFormRules:s,platformCategoryList:c,shopCascaderProps:l,currentComodityInfo:P,shopCategoryList:$,specGroups:k,formRef:m,cancelDistribution:_,handleConfirmDistribution:C}=ce();return(h,i)=>{const f=e.resolveComponent("el-form-item"),R=e.resolveComponent("el-input"),q=e.resolveComponent("el-cascader"),w=e.resolveComponent("el-col"),d=e.resolveComponent("el-row"),o=e.resolveComponent("el-radio"),N=e.resolveComponent("el-radio-group"),V=e.resolveComponent("el-input-number"),U=e.resolveComponent("el-image"),y=e.resolveComponent("el-table-column"),B=e.resolveComponent("el-table"),S=e.resolveComponent("el-form"),D=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",fe,[e.createVNode(S,{ref_key:"formRef",ref:m,model:e.unref(r),rules:e.unref(s)},{default:e.withCtx(()=>{var L,T,A,F;return[Ne,e.createElementVNode("div",Se,[e.createVNode(f,{label:"商品类型"},{default:e.withCtx(()=>[e.createTextVNode("实物商品")]),_:1}),e.createVNode(f,{label:"销售方式"},{default:e.withCtx(()=>[e.createTextVNode("代销商品（采购商品可供各店铺采购，代销商品可以让各店铺帮您将商品销售出去；）")]),_:1}),e.createVNode(f,{label:"商品名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(R,{modelValue:e.unref(r).name,"onUpdate:modelValue":i[0]||(i[0]=t=>e.unref(r).name=t),placeholder:"请输入商品名称"},null,8,["modelValue"])]),_:1}),e.createVNode(f,{label:"平台类目"},{default:e.withCtx(()=>[e.createVNode(q,{ref:"platformCategoryRef",modelValue:e.unref(P).platformCategoryId,"onUpdate:modelValue":i[1]||(i[1]=t=>e.unref(P).platformCategoryId=t),clearable:"",class:"inputWidth",style:{width:"100%"},options:e.unref(c),props:e.unref(l),disabled:"",placeholder:"请选择平台类目","show-all-levels":""},null,8,["modelValue","options","props"])]),_:1}),e.createVNode(f,{label:"店铺类目",prop:"shopCategory"},{default:e.withCtx(()=>[e.createVNode(q,{ref:"shopCategoryRef",modelValue:e.unref(r).shopCategory,"onUpdate:modelValue":i[2]||(i[2]=t=>e.unref(r).shopCategory=t),clearable:"",style:{width:"100%"},class:"inputWidth",options:e.unref($),props:e.unref(l),placeholder:"请选择店铺类目","show-all-levels":""},null,8,["modelValue","options","props"])]),_:1}),e.createVNode(f,{label:"卖点描述",prop:"saleDescribe"},{default:e.withCtx(()=>[e.createVNode(R,{modelValue:e.unref(r).saleDescribe,"onUpdate:modelValue":i[3]||(i[3]=t=>e.unref(r).saleDescribe=t),class:"inputWidth",placeholder:"请填写卖点描述",maxlength:"60"},null,8,["modelValue"])]),_:1})]),e.unref(a).length===1?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[be,e.createElementVNode("div",ue,[e.createVNode(d,{gutter:8},{default:e.withCtx(()=>[e.createVNode(w,{span:6},{default:e.withCtx(()=>[e.createTextVNode("规格类型：单规格")]),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var t,g;return[e.createTextVNode("供货价："+e.toDisplayString((g=(t=e.unref(a))==null?void 0:t[0])==null?void 0:g.salePrice),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var t,g,b,x;return[e.createTextVNode("销量（注水）："+e.toDisplayString(Number(((g=(t=e.unref(a))==null?void 0:t[0])==null?void 0:g.initSalesVolume)||0)+Number(((x=(b=e.unref(a))==null?void 0:b[0])==null?void 0:x.salesVolume)||0)),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var t,g;return[e.createTextVNode("重量："+e.toDisplayString((g=(t=e.unref(a))==null?void 0:t[0])==null?void 0:g.weight),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var t,g,b,x;return[e.createTextVNode("库存数："+e.toDisplayString(((g=(t=e.unref(a))==null?void 0:t[0])==null?void 0:g.stockType)==="UNLIMITED"?"无限库存":(x=(b=e.unref(a))==null?void 0:b[0])==null?void 0:x.stock),1)]}),_:1}),e.createVNode(w,{span:6},{default:e.withCtx(()=>{var t,g;return[e.createTextVNode("限购类型："+e.toDisplayString(e.unref(Y)[(g=(t=e.unref(a))==null?void 0:t[0])==null?void 0:g.limitType]),1)]}),_:1})]),_:1})])],64)):e.createCommentVNode("",!0),xe,e.createElementVNode("div",Pe,[e.createElementVNode("div",Ee,[e.createVNode(N,{modelValue:e.unref(r).consignmentPriceSetting.type,"onUpdate:modelValue":i[4]||(i[4]=t=>e.unref(r).consignmentPriceSetting.type=t)},{default:e.withCtx(()=>[e.createVNode(o,{label:"UNIFY"},{default:e.withCtx(()=>[e.createTextVNode("统一设价")]),_:1}),e.createVNode(o,{label:"RATE"},{default:e.withCtx(()=>[e.createTextVNode("按比例设价")]),_:1}),e.createVNode(o,{label:"REGULAR"},{default:e.withCtx(()=>[e.createTextVNode("固定金额设价")]),_:1})]),_:1},8,["modelValue"]),e.unref(r).consignmentPriceSetting.type==="UNIFY"?(e.openBlock(),e.createBlock(Ve,{key:0,sale:+e.unref(r).unifyPriceSetting.sale,scribe:+e.unref(r).unifyPriceSetting.scribe,type:e.unref(r).unifyPriceSetting.type},null,8,["sale","scribe","type"])):e.unref(r).consignmentPriceSetting.type==="RATE"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createVNode(f,{prop:"consignmentPriceSetting.sale"},{default:e.withCtx(()=>[we,e.createVNode(V,{modelValue:e.unref(r).consignmentPriceSetting.sale,"onUpdate:modelValue":i[5]||(i[5]=t=>e.unref(r).consignmentPriceSetting.sale=t),controls:!1,precision:2,max:100},null,8,["modelValue"]),Te]),_:1}),e.createVNode(f,{prop:"consignmentPriceSetting.scribe"},{default:e.withCtx(()=>[Ie,e.createVNode(V,{modelValue:e.unref(r).consignmentPriceSetting.scribe,"onUpdate:modelValue":i[6]||(i[6]=t=>e.unref(r).consignmentPriceSetting.scribe=t),controls:!1,precision:2,max:100},null,8,["modelValue"]),ke]),_:1})],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:2},[e.createVNode(f,{prop:"consignmentPriceSetting.sale"},{default:e.withCtx(()=>[Ue,e.createVNode(V,{modelValue:e.unref(r).consignmentPriceSetting.sale,"onUpdate:modelValue":i[7]||(i[7]=t=>e.unref(r).consignmentPriceSetting.sale=t),controls:!1,precision:2},null,8,["modelValue"]),Be]),_:1}),e.createVNode(f,{prop:"consignmentPriceSetting.scribe"},{default:e.withCtx(()=>[De,e.createVNode(V,{modelValue:e.unref(r).consignmentPriceSetting.scribe,"onUpdate:modelValue":i[8]||(i[8]=t=>e.unref(r).consignmentPriceSetting.scribe=t),controls:!1,precision:2},null,8,["modelValue"]),Le]),_:1})],64))]),e.unref(a).length===1?(e.openBlock(),e.createElementBlock("div",$e,[e.createElementVNode("span",null,"销售价："+e.toDisplayString((T=(L=e.unref(a))==null?void 0:L[0])==null?void 0:T.actualSalePrice),1),e.createElementVNode("span",null,"划线价："+e.toDisplayString((F=(A=e.unref(a))==null?void 0:A[0])==null?void 0:F.actualPrice),1)])):e.createCommentVNode("",!0),e.createVNode(B,{data:e.unref(a)},{default:e.withCtx(()=>[e.createVNode(y,{fixed:"left",width:"150",label:"规格图"},{default:e.withCtx(({row:t})=>[e.createVNode(U,{src:t==null?void 0:t.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(k),(t,g)=>(e.openBlock(),e.createBlock(y,{key:g,width:"150",label:t==null?void 0:t.name},{default:e.withCtx(({row:b})=>{var x;return[e.createTextVNode(e.toDisplayString(Array.isArray(b.specs)?(x=b.specs)==null?void 0:x[g]:b.specs.name),1)]}),_:2},1032,["label"]))),128)),e.createVNode(y,{width:"150",label:"供货价",prop:"salePrice"}),e.createVNode(y,{width:"150",label:"划线价",prop:"actualPrice"}),e.createVNode(y,{width:"150",label:"销售价",prop:"actualSalePrice"}),e.createVNode(y,{width:"150",label:"库存"},{default:e.withCtx(({row:t})=>[e.createTextVNode(e.toDisplayString((t==null?void 0:t.stockType)==="UNLIMITED"?"无限库存":t==null?void 0:t.initStock),1)]),_:1}),e.createVNode(y,{width:"150",label:"限购类型"},{default:e.withCtx(({row:t})=>[e.createTextVNode(e.toDisplayString(e.unref(Y)[t==null?void 0:t.limitType]),1)]),_:1}),e.createVNode(y,{width:"150",label:"规格限购"},{default:e.withCtx(({row:t})=>[e.createTextVNode(e.toDisplayString((t==null?void 0:t.limitType)!=="UNLIMITED"?String(t.limitNum):""),1)]),_:1}),e.createVNode(y,{width:"150",label:"重量",prop:"weight"}),e.createVNode(y,{width:"150",label:"销量（注水）"},{default:e.withCtx(({row:t})=>[e.createTextVNode(e.toDisplayString(Number((t==null?void 0:t.initSalesVolume)||0)+Number((t==null?void 0:t.salesVolume)||0)),1)]),_:1})]),_:1},8,["data"])])]}),_:1},8,["model","rules"]),e.createElementVNode("div",qe,[e.createVNode(D,{onClick:e.unref(_)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1},8,["onClick"]),e.createVNode(D,{type:"primary",onClick:e.unref(C)},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1},8,["onClick"])])])}}}),Fe="";return((n,r)=>{const a=n.__vccOpts||n;for(const[s,c]of r)a[s]=c;return a})(Ae,[["__scopeId","data-v-a930f6a6"]])});
