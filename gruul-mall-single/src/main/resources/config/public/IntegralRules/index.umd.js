(function(e,g){typeof exports=="object"&&typeof module<"u"?module.exports=g(require("vue"),require("@/components/q-editor/editor.vue"),require("@/apis/http"),require("element-plus"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@/components/q-editor/editor.vue","@/apis/http","element-plus","vue-router"],g):(e=typeof globalThis<"u"?globalThis:e||self,e.IntegralRules=g(e.IntegralRulesContext.Vue,e.IntegralRulesContext.EditorTwo,e.IntegralRulesContext.Request,e.IntegralRulesContext.ElementPlus,e.IntegralRulesContext.VueRouter))})(this,function(e,g,x,V,H){"use strict";var G=document.createElement("style");G.textContent=`.title[data-v-16e20737]{margin:15px 10px;display:flex;justify-content:center;align-items:center;justify-content:flex-start;font-size:14px;color:#2e99f3;font-weight:700}.title[data-v-16e20737]:before{content:"";display:inline-block;height:14px;width:2.5px;background:#2e99f3;margin-right:9px}.use_rules[data-v-16e20737]{font-size:14px;font-family:sans-serif,sans-serif-Normal;font-weight:400;text-align:LEFT;color:#333;line-height:30px}.use_rules__msg[data-v-16e20737]{font-size:14px;text-align:LEFT;color:#d3d3d3}.save[data-v-16e20737]{margin:30px 0;text-align:center}.get_rules[data-v-16e20737]{text-indent:1em;padding:10px 0}.p20[data-v-16e20737]{padding:0 0 0 20px}
`,document.head.appendChild(G);const I="addon-integral/integral/",K=()=>x.get({url:I+"rules/info"}),W=u=>x.post({url:I+"rules/save",data:u}),X=u=>x.post({url:I+"rules/update",data:u}),N=u=>(e.pushScopeId("data-v-16e20737"),u=u(),e.popScopeId(),u),Z=N(()=>e.createElementVNode("div",{class:"title"},"积分使用规则",-1)),ee={class:"use_rules p20"},le={key:0,style:{border:"1px solid #ccc"}},te={key:1},ae=N(()=>e.createElementVNode("div",{class:"title"},"积分有效期",-1)),oe={class:"use_rules p20"},ne={key:1},re=N(()=>e.createElementVNode("div",{class:"title"},"积分值规则",-1)),se={class:"use_rules p20"},ie={key:0,style:{border:"1px solid #ccc"}},de={key:1},ue=N(()=>e.createElementVNode("div",{class:"title"},"积分获取规则",-1)),ce={class:"use_rules p20"},pe=N(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（限制用户首次分享才可获得积分）",-1)),me={class:"get_rules"},Ve=N(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（每日首次登录获得积分，7天中有2个节点可以额外赠送积分，第8天00:00数据清空）",-1)),Ne={class:"get_rules"},fe=N(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（每日签到获得积分，7天中有2个节点可以额外赠送积分，第8天00:00数据清空）",-1)),ge={class:"get_rules"},_e=N(()=>e.createElementVNode("span",{class:"use_rules__msg"},"（成功交易笔数及实付金额只统计订单状态为【已完成】的数值，且将【已结算订单、积分订单、储值充值】剔除）",-1)),ye={class:"save"},xe={key:0},Ie={key:0},ke={key:1},Ge=e.defineComponent({__name:"IntegralRules",setup(u){H.useRouter();const p=e.ref(!1),f=e.ref(!1),o=e.ref({indate:0,useRule:"<div>（1）积分使用过程中不找零、不兑现、不开发票，不可转移至其他账户。</div><div>（2）使用积分进行兑换，兑换申请一经提交, 一律不能退货</div><div>（3）如因积分商品缺货等原因导致的退货退款，积分会原路返还</div><div>（4）兑换礼品涉及运费和配送费由用户自行承担。</div><div>（5）启山智软保留最终解释权。</div>",ruleInfo:"<div>月度滚动过期制。 每个自然月的第1天00：00分自动清零 已满一年的积分。 举例：2022年8月1日开始清除2023年7月31日</div>",integralGainRule:[{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"SHARE",open:!1},{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"LOGIN",open:!1},{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"SING_IN",open:!1},{rulesParameter:{consumeJson:[{consumeGrowthValueType:"",isSelected:!1,orderQuantityAndAmount:"",presentedGrowthValue:""}]},gainRuleType:"CONSUME",open:!1}]}),n=e.ref({LOGIN:[{key:0,value:0},{key:0,value:0}],SING_IN:[{key:0,value:0},{key:0,value:0}],CONSUME:[{key:0,value:0},{key:0,value:0}]});k();const d=e.ref(),y=e.ref("");async function k(){const{code:a,data:l,msg:s}=await K();if(a!==200){V.ElMessage.error(s||"积分规则获取失败");return}if(l&&l.useRule){Ee(l),o.value=l,d.value=l.integralGainRule[3].rulesParameter.consumeJson,l.integralGainRule[3].rulesParameter.consumeJson[0].isSelected===!0?y.value="ORDER_QUANTITY":y.value="ORDER_AMOUNT";return}f.value=!0}const h=(a=!1)=>{p.value=!p.value,a&&k()},he=async()=>{if(!Te())return;const a=we();if(a==="COMPLETE"){Re();const{code:l,msg:s}=await(f.value?W(o.value):X(o.value));if(l!==200){V.ElMessage.error(s||`${f.value?"保存":"修改"}积分规则失败`);return}V.ElMessage.success(`${f.value?"保存":"修改"}积分规则成功`),k(),Ce()}else V.ElMessage.info(`连续${a==="LOGIN"?"登录":"签到"}天数输入重复，请修改`)};function Re(){o.value.integralGainRule.forEach(a=>{let l={};const s=n.value[a.gainRuleType];if(s){for(let r=0;r<s.length;r++){const i=s[r].key,m=s[r].value;l[i]=m}a.rulesParameter.extendValue=l}})}function Ee(a){a.integralGainRule.forEach(l=>{let s=[],r=n.value[l.gainRuleType];if(console.log("integralArray",r),r){for(const i in l.rulesParameter.extendValue)console.log("key",i),s.push({key:i,value:l.rulesParameter.extendValue[i]});n.value[l.gainRuleType]=s}})}function we(){const a=new Map;if(n.value.LOGIN.filter(r=>!a.has(r.key)&&a.set(r.key,r.value)).length!==n.value.LOGIN.length)return"LOGIN";a.clear();const s=n.value.SING_IN.filter(r=>!a.has(r.key)&&a.set(r.key,r.value)).length;return s!==n.value.SING_IN.length?(console.log("singInLen",s),"SING_IN"):"COMPLETE"}function Te(){const{indate:a,useRule:l,ruleInfo:s,integralGainRule:r}=o.value;if(!a)return V.ElMessage.info("请输入积分有效期"),!1;if(!l.trim().length||l==="<p><br></p>")return V.ElMessage.info("请输入积分规则"),!1;if(!s.trim().length||s==="<p><br></p>")return V.ElMessage.info("请输入积分值信息"),!1;if(r.every(_=>!!_.rulesParameter.basicsValue))return V.ElMessage.info("请输入首次赠送积分值信息"),!1;const m=Se();return console.log(m),m?(V.ElMessage.info(m),!1):!0}function Se(){let a="";const l=n.value.LOGIN,s=n.value.SING_IN,r=n.value.SING_IN;if(l.forEach(i=>{if(!i.key)return a="请输入连续登录天数",a;if(!i.value)return a="请输入连续登录赠送积分值",a}),a||(s.forEach(i=>{if(!i.key)return a="请输入连续签到天数",a;if(!i.value)return a="请输入连续登签到赠送积分值",a}),a)||(r.forEach(i=>{if(!i.key)return a;if(!i.value)return a="请输入赠送的积分值",a}),a))return a}function Ce(){p.value=!1,f.value=!1}const Ue=a=>{console.log(a),a==="ORDER_QUANTITY"&&(d.value[0].isSelected=!0,d.value[1].isSelected=!1),a==="ORDER_AMOUNT"&&(d.value[1].isSelected=!0,d.value[0].isSelected=!1)};return(a,l)=>{var S,C,U,O,B,D,b,A,L,v,M,P,Q,q,F,$,j,Y,z,J;const s=e.resolveComponent("el-button"),r=e.resolveComponent("el-input-number"),i=e.resolveComponent("el-checkbox"),m=e.resolveComponent("el-col"),_=e.resolveComponent("el-input"),R=e.resolveComponent("el-radio"),E=e.resolveComponent("el-form-item"),w=e.resolveComponent("el-row"),Oe=e.resolveComponent("el-radio-group"),T=e.resolveDirective("dompurify-html");return e.openBlock(),e.createElementBlock("div",null,[e.createTextVNode(" 11111111111 "),e.withDirectives(e.createVNode(s,{round:"",type:"primary",onClick:l[0]||(l[0]=t=>h(!1))},{default:e.withCtx(()=>[e.createTextVNode("编辑积分规则")]),_:1},512),[[e.vShow,!p.value]]),Z,e.createElementVNode("div",ee,[p.value?(e.openBlock(),e.createElementBlock("div",le,[e.createVNode(g,{modelValue:o.value.useRule,"onUpdate:modelValue":l[1]||(l[1]=t=>o.value.useRule=t),height:200},null,8,["modelValue"])])):e.withDirectives((e.openBlock(),e.createElementBlock("div",te,null,512)),[[T,o.value.useRule]])]),ae,e.createElementVNode("div",oe,[e.createElementVNode("div",null,[e.createTextVNode(" 积分有效期为 "),p.value?(e.openBlock(),e.createBlock(r,{key:0,modelValue:o.value.indate,"onUpdate:modelValue":l[2]||(l[2]=t=>o.value.indate=t),controls:!1,min:1,max:12,style:{width:"50px"}},null,8,["modelValue"])):(e.openBlock(),e.createElementBlock("span",ne,e.toDisplayString(o.value.indate),1)),e.createTextVNode(" 个月 ")])]),re,e.createElementVNode("div",se,[p.value?(e.openBlock(),e.createElementBlock("div",ie,[e.createVNode(g,{modelValue:o.value.ruleInfo,"onUpdate:modelValue":l[3]||(l[3]=t=>o.value.ruleInfo=t)},null,8,["modelValue"])])):e.withDirectives((e.openBlock(),e.createElementBlock("div",de,null,512)),[[T,o.value.ruleInfo]])]),ue,e.createElementVNode("div",ce,[p.value?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(i,{modelValue:o.value.integralGainRule[0].open,"onUpdate:modelValue":l[4]||(l[4]=t=>o.value.integralGainRule[0].open=t)},{default:e.withCtx(()=>[e.createTextVNode("分享")]),_:1},8,["modelValue"]),pe,e.createElementVNode("div",me,[e.createTextVNode(" 每日首次分享获得 "),e.createVNode(r,{modelValue:o.value.integralGainRule[0].rulesParameter.basicsValue,"onUpdate:modelValue":l[5]||(l[5]=t=>o.value.integralGainRule[0].rulesParameter.basicsValue=t),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分 ")]),e.createVNode(i,{modelValue:o.value.integralGainRule[1].open,"onUpdate:modelValue":l[6]||(l[6]=t=>o.value.integralGainRule[1].open=t)},{default:e.withCtx(()=>[e.createTextVNode("登录")]),_:1},8,["modelValue"]),Ve,e.createElementVNode("div",Ne,[e.createTextVNode(" 每日首次登录获得 "),e.createVNode(r,{modelValue:o.value.integralGainRule[1].rulesParameter.basicsValue,"onUpdate:modelValue":l[7]||(l[7]=t=>o.value.integralGainRule[1].rulesParameter.basicsValue=t),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分, 第 "),e.createVNode(r,{model:+n.value.LOGIN[0].key,controls:!1,min:1,max:6,style:{width:"100px"},"onUpdate:modelValue":l[8]||(l[8]=t=>n.value.LOGIN[0].key=t)},null,8,["model"]),e.createTextVNode(" 天连续登录将额外获赠 "),e.createVNode(r,{modelValue:n.value.LOGIN[0].value,"onUpdate:modelValue":l[9]||(l[9]=t=>n.value.LOGIN[0].value=t),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分； 第 "),e.createVNode(r,{model:+n.value.LOGIN[1].key,controls:!1,min:1,max:7,style:{width:"100px"},"onUpdate:modelValue":l[10]||(l[10]=t=>n.value.LOGIN[1].key=t)},null,8,["model"]),e.createTextVNode(" 天连续登录将额外获得 "),e.createVNode(r,{modelValue:n.value.LOGIN[1].value,"onUpdate:modelValue":l[11]||(l[11]=t=>n.value.LOGIN[1].value=t),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分。 ")]),e.createVNode(i,{modelValue:o.value.integralGainRule[2].open,"onUpdate:modelValue":l[12]||(l[12]=t=>o.value.integralGainRule[2].open=t),style:{"margin-right":"30px"}},{default:e.withCtx(()=>[e.createTextVNode("签到")]),_:1},8,["modelValue"]),fe,e.createElementVNode("div",ge,[e.createTextVNode(" 每日首次签到获得 "),e.createVNode(r,{modelValue:o.value.integralGainRule[2].rulesParameter.basicsValue,"onUpdate:modelValue":l[13]||(l[13]=t=>o.value.integralGainRule[2].rulesParameter.basicsValue=t),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分, 第 "),e.createVNode(r,{model:+n.value.SING_IN[0].key,max:6,controls:!1,min:1,style:{width:"100px"},"onUpdate:modelValue":l[14]||(l[14]=t=>n.value.SING_IN[0].key=t)},null,8,["model"]),e.createTextVNode(" 天连续签到将额外获赠 "),e.createVNode(r,{modelValue:n.value.SING_IN[0].value,"onUpdate:modelValue":l[15]||(l[15]=t=>n.value.SING_IN[0].value=t),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode("点积分； 第 "),e.createVNode(r,{model:+n.value.SING_IN[1].key,controls:!1,min:1,style:{width:"100px"},max:7,"onUpdate:modelValue":l[16]||(l[16]=t=>n.value.SING_IN[1].key=t)},null,8,["model"]),e.createTextVNode(" 天连续签到将额外获得 "),e.createVNode(r,{modelValue:n.value.SING_IN[1].value,"onUpdate:modelValue":l[17]||(l[17]=t=>n.value.SING_IN[1].value=t),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),e.createTextVNode(" 点积分。 ")]),e.createVNode(i,{modelValue:o.value.integralGainRule[3].open,"onUpdate:modelValue":l[18]||(l[18]=t=>o.value.integralGainRule[3].open=t),style:{"margin-right":"30px"}},{default:e.withCtx(()=>[e.createTextVNode("消费获得")]),_:1},8,["modelValue"]),_e,e.createVNode(Oe,{modelValue:y.value,"onUpdate:modelValue":l[19]||(l[19]=t=>y.value=t),class:"ml-4",style:{"margin-top":"5px"},onChange:Ue},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.value,(t,Be)=>(e.openBlock(),e.createElementBlock(e.Fragment,{key:Be},[t.consumeGrowthValueType==="ORDER_QUANTITY"?(e.openBlock(),e.createBlock(w,{key:0},{default:e.withCtx(()=>[e.createVNode(m,{span:1}),e.createVNode(m,{span:22},{default:e.withCtx(()=>[e.createVNode(E,null,{default:e.withCtx(()=>[e.createVNode(R,{modelValue:t.isSelected,"onUpdate:modelValue":c=>t.isSelected=c,label:"ORDER_QUANTITY"},{default:e.withCtx(()=>[e.createTextVNode(" 每成功交易(已完成) "),e.createVNode(_,{modelValue:t.orderQuantityAndAmount,"onUpdate:modelValue":c=>t.orderQuantityAndAmount=c,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 笔订单，奖励 "),e.createVNode(_,{modelValue:t.presentedGrowthValue,"onUpdate:modelValue":c=>t.presentedGrowthValue=c,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 点积分 ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)):t.consumeGrowthValueType==="ORDER_AMOUNT"?(e.openBlock(),e.createBlock(w,{key:1},{default:e.withCtx(()=>[e.createVNode(m,{span:1}),e.createVNode(m,{span:22},{default:e.withCtx(()=>[e.createVNode(E,null,{default:e.withCtx(()=>[e.createVNode(R,{modelValue:t.isSelected,"onUpdate:modelValue":c=>t.isSelected=c,label:"ORDER_AMOUNT"},{default:e.withCtx(()=>[e.createTextVNode(" 实付金额(不含运费)，每满 "),e.createVNode(_,{modelValue:t.orderQuantityAndAmount,"onUpdate:modelValue":c=>t.orderQuantityAndAmount=c,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 元，奖励 "),e.createVNode(_,{modelValue:t.presentedGrowthValue,"onUpdate:modelValue":c=>t.presentedGrowthValue=c,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),e.createTextVNode(" 点积分 ")]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)):e.createCommentVNode("",!0)],64))),128))]),_:1},8,["modelValue"]),e.createElementVNode("div",ye,[e.createVNode(s,{round:"",onClick:l[20]||(l[20]=t=>h(!0))},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.createVNode(s,{round:"",type:"primary",onClick:he},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1})])],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[(S=o.value.integralGainRule)!=null&&S[0].open?(e.openBlock(),e.createElementBlock("div",xe," 每日首次分享获得 "+e.toDisplayString(o.value.integralGainRule[0].rulesParameter.basicsValue)+" 点积分 ",1)):e.createCommentVNode("",!0),e.withDirectives(e.createElementVNode("div",null," 每日首次登录获得 "+e.toDisplayString((C=o.value.integralGainRule)==null?void 0:C[1].rulesParameter.basicsValue)+" 点积分, 第 "+e.toDisplayString((U=n.value.LOGIN)==null?void 0:U[0].key)+" 天连续登录将额外获赠 "+e.toDisplayString((O=n.value.LOGIN)==null?void 0:O[0].value)+" 点积分； 第 "+e.toDisplayString((B=n.value.LOGIN)==null?void 0:B[1].key)+" 天连续登录将额外获得 "+e.toDisplayString((D=n.value.LOGIN)==null?void 0:D[1].value)+" 点积分。 ",513),[[e.vShow,(b=o.value.integralGainRule)==null?void 0:b[1].open]]),e.withDirectives(e.createElementVNode("div",null," 每日首次签到获得 "+e.toDisplayString((A=o.value.integralGainRule)==null?void 0:A[2].rulesParameter.basicsValue)+" 点积分, 第 "+e.toDisplayString((L=n.value.SING_IN)==null?void 0:L[0].key)+" 天连续签到将额外获赠 "+e.toDisplayString((v=n.value.SING_IN)==null?void 0:v[0].value)+" 点积分； 第 "+e.toDisplayString((M=n.value.SING_IN)==null?void 0:M[1].key)+" 天连续签到将额外获得 "+e.toDisplayString((P=n.value.SING_IN)==null?void 0:P[1].value)+" 点积分。 ",513),[[e.vShow,(Q=o.value.integralGainRule)==null?void 0:Q[2].open]]),e.withDirectives(e.createElementVNode("div",null,[(q=d.value)!=null&&q[0].isSelected?(e.openBlock(),e.createElementBlock("p",Ie," 每成功交易（已完成），"+e.toDisplayString((F=d.value)==null?void 0:F[0].orderQuantityAndAmount)+" 笔订单，奖励 "+e.toDisplayString(($=d.value)==null?void 0:$[0].presentedGrowthValue)+" 点积分 ",1)):(j=d.value)!=null&&j[1].isSelected?(e.openBlock(),e.createElementBlock("p",ke," 实付金额（不含运费），每满 "+e.toDisplayString((Y=d.value)==null?void 0:Y[1].orderQuantityAndAmount)+" 元，奖励 "+e.toDisplayString((z=d.value)==null?void 0:z[1].presentedGrowthValue)+" 点积分 ",1)):e.createCommentVNode("",!0)],512),[[e.vShow,(J=o.value.integralGainRule)==null?void 0:J[3].open]])],64))])])}}}),De="";return((u,p)=>{const f=u.__vccOpts||u;for(const[o,n]of p)f[o]=n;return f})(Ge,[["__scopeId","data-v-16e20737"]])});
