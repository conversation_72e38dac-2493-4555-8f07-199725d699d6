(function(e,H){typeof exports=="object"&&typeof module<"u"?module.exports=H(require("vue"),require("@/components/PageManage.vue"),require("decimal.js"),require("@/components/MCard.vue"),require("element-plus"),require("@/composables/useConvert"),require("vue-clipboard3"),require("@/utils/date"),require("@element-plus/icons-vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/qszr-core/packages/q-drop-down"),require("@/components/q-editor/q-edit.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/components/PageManage.vue","decimal.js","@/components/MCard.vue","element-plus","@/composables/useConvert","vue-clipboard3","@/utils/date","@element-plus/icons-vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/qszr-core/packages/q-drop-down","@/components/q-editor/q-edit.vue","@/apis/http"],H):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformDistribute=H(e.PlatformDistributeContext.Vue,e.PlatformDistributeContext.PageManageTwo,e.PlatformDistributeContext.Decimal,e.PlatformDistributeContext.MCard,e.PlatformDistributeContext.ElementPlus,e.PlatformDistributeContext.UseConvert,e.PlatformDistributeContext.VueClipboard3,e.PlatformDistributeContext.DateUtil,e.PlatformDistributeContext.ElementPlusIconsVue,e.PlatformDistributeContext.QTable,e.PlatformDistributeContext.QTableColumn,e.PlatformDistributeContext.QDropDown,e.PlatformDistributeContext.QEditor,e.PlatformDistributeContext.Request))})(this,function(e,H,Y,te,T,W,me,fe,he,_e,J,ge,re,L){"use strict";var ie=document.createElement("style");ie.textContent=`@charset "UTF-8";.dis[data-v-ddf08166]{padding:0 30px;position:relative}.table-height-fit[data-v-254900cf]{overflow:auto}.input-with-select .el-input-group__prepend[data-v-254900cf]{background-color:var(--el-fill-color-blank)}.ellipsis[data-v-254900cf]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.dis__header[data-v-254900cf]{margin-bottom:14px}.com[data-v-254900cf]{width:242px;font-size:12px;color:#515151;display:flex;justify-content:space-between;align-items:center}.com__img[data-v-254900cf]{width:36px;height:36px;margin-right:12px}.com__right[data-v-254900cf]{text-align:left}.com__right--name[data-v-254900cf]{width:194px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.dialogCom[data-v-254900cf]{display:flex;justify-content:center;align-items:center}.dialogCom__img[data-v-254900cf]{width:60px;height:60px;margin-right:12px;border-radius:10px}.dialogCom__right--name[data-v-254900cf]{width:210px;font-size:14px;color:#333;line-height:20px}.amount[data-v-254900cf]:before{content:"￥";display:inline-block}.percentage[data-v-254900cf]:after{content:"%";display:inline-block}.head[data-v-df24458a]{display:flex;align-items:center;justify-content:space-around;background-color:#e6f7ff;height:40px;margin-top:15px;font-size:10px}.content[data-v-df24458a]{display:flex}.content__right1[data-v-df24458a]{border:1px solid #ebeef5;display:flex;align-items:center;justify-content:center;line-height:26px;width:470px;padding-left:10px}.content__right1--item[data-v-df24458a]{width:120px}.content__right2[data-v-df24458a]{width:155px;font-size:13px;border:1px solid #ebeef5;display:flex;flex-wrap:wrap;justify-content:center;flex-direction:column;padding-left:5px;line-height:26px}.content__right2--item[data-v-df24458a]{width:100px}.goods[data-v-df24458a]{display:flex;align-items:center}.goods__pic[data-v-df24458a]{margin-right:10px;width:60px;height:50px;position:relative}.goods__pic--state[data-v-df24458a]{background:#7f83f7;position:absolute;width:40px;height:40px;border-radius:50%;top:5px;left:10px;color:#fff;line-height:40px;text-align:center;font-size:10px}.goods__info-flex[data-v-df24458a]{display:flex;align-items:center;justify-content:space-between;margin-top:10px}.goods__info-flex--name[data-v-df24458a]{width:185px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-right:10px}.goods__info-flex--specs[data-v-df24458a]{width:80px;font-size:10px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}[data-v-df24458a] .el-table thead{display:none}.amount[data-v-df24458a]:before{content:"￥";display:inline-block}.percentage[data-v-df24458a]:after{content:"%";display:inline-block}.ellipsis[data-v-df24458a]{width:250px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.count[data-v-4bf7556c]{height:56px;line-height:56px;font-weight:700}.count span[data-v-4bf7556c]{margin-right:30px}.tbhead[data-v-4bf7556c]{display:flex;align-items:center;height:35px;font-weight:700;background-color:#f2f2f280}.tbhead__goods[data-v-4bf7556c]{margin-left:150px}.tbhead__parameter[data-v-4bf7556c]{margin-left:170px}.tbhead__detail[data-v-4bf7556c]{margin-left:185px}.tbhead__total[data-v-4bf7556c]{margin-left:190px}.ml[data-v-4bf7556c]{margin-left:30px}.tool[data-v-b2ee1b9f]{width:100%;height:56px;background:#fff;position:relative}.tool__btn[data-v-b2ee1b9f]{position:absolute;left:0}.tool__btn--drop[data-v-b2ee1b9f]{width:120px}.tool__input[data-v-b2ee1b9f]{width:250px;font-size:14px;position:absolute;right:0;top:50%;margin-top:-24px}.color51[data-v-b2ee1b9f]{color:#515151}.color58[data-v-b2ee1b9f]{color:#586884}.color33[data-v-b2ee1b9f]{color:#333}.column[data-v-b2ee1b9f]{display:flex;flex-direction:column;align-items:flex-start;justify-content:flex-start}.column button[data-v-b2ee1b9f]{margin:0}.ellipsis[data-v-b2ee1b9f]{width:135px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ml[data-v-b2ee1b9f]{margin-left:30px}.col[data-v-8fb47d32]{background:#f6f8fa;height:38px;display:flex;justify-content:flex-start;align-items:center;font-size:13px;color:#515151}.col__icon[data-v-8fb47d32]{width:4px;height:16px;margin:0 17px 0 19px}.save[data-v-8fb47d32]{width:100%;display:flex;justify-content:center;align-items:center;height:62px;background-color:#fff;box-shadow:0 0 2px #00000012}.mb33[data-v-8fb47d32]{margin-bottom:33px}.mb26[data-v-8fb47d32]{margin-bottom:26px}.tip[data-v-8fb47d32]{font-size:13px;color:#d5d5d5}.table-height-fit[data-v-7471ba34]{height:calc(100vh - 225px);overflow:auto}.dis[data-v-7471ba34]{padding:0}.dis__header[data-v-7471ba34]{display:flex;justify-content:space-between;align-items:center;margin-bottom:14px}.com[data-v-7471ba34]{width:242px;font-size:12px;color:#515151;display:flex;justify-content:space-between;align-items:center}.com__img[data-v-7471ba34]{width:56px;height:56px;margin-right:12px}.com__right[data-v-7471ba34]{text-align:left}.com__right--name[data-v-7471ba34]{width:174px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-bottom:10px}.dialogCom[data-v-7471ba34]{display:flex;justify-content:center;align-items:center}.dialogCom__img[data-v-7471ba34]{width:60px;height:60px;margin-right:12px;border-radius:10px}.dialogCom__right--name[data-v-7471ba34]{width:210px;font-size:14px;color:#333;line-height:20px}.tableCom__img[data-v-7471ba34]{width:36px;height:36px;margin-right:12px;border-radius:10px}.color51[data-v-7471ba34]{color:#515151}.colorRed[data-v-7471ba34]{color:#fd0505}.color33[data-v-7471ba34]{color:#333}.column[data-v-7471ba34]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column button[data-v-7471ba34]{margin:0}.flex[data-v-7471ba34]{display:flex;justify-content:flex-start;align-items:center}.amount[data-v-7471ba34]:before{content:"￥";display:inline-block}.percentage[data-v-7471ba34]:after{content:"%";display:inline-block}.tiShi[data-v-7471ba34]{color:#fd0505;font-size:12px;margin:10px 0 20px}
`,document.head.appendChild(ie);const be={class:"dis"},xe=e.defineComponent({__name:"PlatformDistribute",setup(d){const b={distributionCom:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Ye)),distributionOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>wt)),wholeSaler:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Wt)),distributionSet:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ao)),distributionCode:e.defineAsyncComponent(()=>Promise.resolve().then(()=>co))},n=e.ref("distributionCode"),p=e.computed(()=>b[n.value]);return(a,l)=>{const i=e.resolveComponent("el-tab-pane"),r=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",be,[e.createVNode(r,{modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=s=>n.value=s),class:"demo-tabs"},{default:e.withCtx(()=>[e.createVNode(i,{label:"邀请码",name:"distributionCode"}),e.createVNode(i,{label:"分销商品",name:"distributionCom"}),e.createVNode(i,{label:"分销订单",name:"distributionOrder"}),e.createVNode(i,{label:"分销商",name:"wholeSaler"}),e.createVNode(i,{label:"分销设置",name:"distributionSet"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(p.value),{ref:"componentRef",platform:"platform"},null,512))])}}}),fo="",K=(d,b)=>{const n=d.__vccOpts||d;for(const[p,a]of b)n[p]=a;return n},Ne=K(xe,[["__scopeId","data-v-ddf08166"]]),ue=d=>L.put({url:"addon-distribute/distribute/config/",data:d}),Ve=d=>L.post({url:"addon-distribute/distribute/product/page",data:d}),oe=()=>L.get({url:"addon-distribute/distribute/config/"}),Ce=d=>L.get({url:"addon-distribute/distribute/order/",params:d}),we=d=>L.get({url:"addon-distribute/distribute/distributor/",params:d}),ye=d=>L.get({url:"addon-distribute/distribute/distributor/team",params:d}),Ee=d=>L.get({url:"addon-distribute/distribute/distributor/rank",params:d}),de=d=>L.put({url:`addon-distribute/distribute/product/cancel/${d}`}),Te=(d,b)=>L.put({url:`addon-distribute/distribute/distributor/distributor/apply/${d}`,data:b}),Se=d=>L.get({url:"gruul-mall-shop/shop",params:d}),De=d=>L.post({url:"addon-distribute/distribute/in/code/generate",data:d}),ke=d=>L.get({url:"addon-distribute/distribute/in",params:d}),{divTenThousand:G,divHundred:ne}=W();function Ie(d,b,n,p,a){let l=b==null?void 0:b[0],i=b==null?void 0:b[b.length-1];if(d==="FIXED_AMOUNT"){n=+G(n),p=+G(p),a=+G(a);const r=(n+p+a).toFixed(2);return n+" + "+p+" +"+a+" = "+r}else if(l=+G(l),i=+G(i),n=+ne(G(n)),p=+ne(G(p)),a=+ne(G(a)),a)if(p){const r=(i*n+i*p+i*a).toFixed(2),s=(l*n+l*p+l*a).toFixed(2);return l===i?`${l} * ${n} + ${l} * ${p} + ${l} * ${a} = ${s}`:`
                ${l} * ${n} + ${l} * ${p} + ${l} * ${a} = ${s}
                <br />
                ${i} * ${n} + ${i} * ${p} + ${i} * ${a} = ${r}            
            `}else{const r=(i*n).toFixed(2),s=(l*n).toFixed(2);return l===i?l+" * "+n+"   = "+s:`${l} * ${n} = ${s} </br> ${i} * ${n} = ${r}`}else{const r=(i*n+i*p).toFixed(2),s=(l*n+l*p).toFixed(2);return l===i?`${l} * ${n} + ${l} * ${p} = ${s}`:`${l} * ${n} + ${l} * ${p} = ${s} <br /> ${i} * ${n} + ${i} * ${p} = ${r}`}}const $e={class:"ellipsis f12"},Oe={class:"com"},Be={class:"com__right"},Me={class:"com__right--name"},Ue={class:"f12"},ze={class:"f12 color51"},Fe={key:0,class:"f12 color51"},Ae={key:1,class:"f12 color51"},Le={key:0,class:"f12 colorRed"},Pe={key:0},qe={key:1,class:"f12 colorRed"},Re={class:"f12"},je={class:"f12"},He={class:"f12"},Xe=e.defineComponent({__name:"DistributionComP",setup(d){const b=e.ref(!1),n=e.ref("ONE"),p=e.ref({}),{divTenThousand:a,divHundred:l}=W(),i=e.reactive({current:1,size:10,total:0,list:[],distributionStatus:"ALL",productName:"",shopName:""}),r=e.ref([]);g(),C();const s=o=>{i.current=o,C()},N=o=>{i.current=1,i.size=o,C()},$=()=>{i.current=1,C()},x=()=>{i.current=1,i.productName="",i.shopName="",C()},E=async o=>{T.ElMessageBox.confirm("确定取消分销当前商品？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:t,success:w}=await de([o.id]);t===200&&w?(T.ElMessage.success("取消分销成功"),C()):T.ElMessage.error("取消分销失败")})},y=o=>{r.value=o},I=()=>{if(!r.value.length){T.ElMessage.warning("请选择商品");return}T.ElMessageBox.confirm("确定需要取消分销商品？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const o=r.value.map(u=>u.id),{code:t,success:w}=await de(o);t===200&&w?(T.ElMessage.success("取消分销成功"),C()):T.ElMessage.error("取消分销失败")})};async function C(){const{code:o,data:t}=await Ve({current:i.current,size:i.size,productName:i.productName,shopName:i.shopName,distributionStatus:i.distributionStatus==="ALL"?null:i.distributionStatus});o===200&&t?(i.list=t.records,i.total=t.total):T.ElMessage.error("获取分销商品失败")}function P(o){return o==="SELL_ON"?"上架":o==="SELL_OFF"?"下架":"违规禁用"}const A=()=>{i.current=1,C()};function k(o,t){return o==="UNIFIED"&&p.value.shareType==="RATE",a(t)}async function g(){const{code:o,data:t}=await oe();o===200?(n.value=t.level,(t.one||t.two||t.three)&&(t.one=t.shareType==="FIXED_AMOUNT"?String(a(t.one)):String(l(t.one)),t.two=t.shareType==="FIXED_AMOUNT"?String(a(t.two)):String(l(t.two)),t.three=t.shareType==="FIXED_AMOUNT"?String(a(t.three)):String(l(t.three)),p.value=t)):T.ElMessage.error("获取分销配置失败")}const O=(o,t)=>t.shareType!=="UNIFIED"?t.shareType==="RATE"?n.value==="ONE"?o.mul(l(a(t.one))).toFixed(2):n.value==="TWO"?o.mul(l(a(t.one))).add(o.mul(l(a(t.two)))).toFixed(2):o.mul(l(a(t.one))).add(o.mul(l(a(t.two)))).add(o.mul(l(a(t.three)))).toFixed(2):n.value==="ONE"?a(t.one).toFixed(2):n.value==="TWO"?a(t.one).add(a(t.two)).toFixed(2):a(t.one).add(a(t.two)).add(a(t.three)).toFixed(2):p.value.shareType==="RATE"?n.value==="ONE"?o.mul(l(p.value.one)).toFixed(2):n.value==="TWO"?o.mul(l(p.value.one)).add(o.mul(l(p.value.two))).toFixed(2):o.mul(l(p.value.one)).add(o.mul(l(p.value.two))).add(o.mul(l(p.value.three))).toFixed(2):n.value==="ONE"?new Y(p.value.one).toFixed(2):n.value==="TWO"?new Y(p.value.one).add(p.value.two).toFixed(2):new Y(p.value.one).add(p.value.two).add(p.value.three).toFixed(2),S=e.computed(()=>b.value?"calc(100vh - 495px)":"calc(100vh - 395px)");return(o,t)=>{const w=e.resolveComponent("el-input"),u=e.resolveComponent("el-form-item"),q=e.resolveComponent("el-col"),V=e.resolveComponent("el-row"),_=e.resolveComponent("el-button"),D=e.resolveComponent("el-form"),z=e.resolveComponent("el-tab-pane"),B=e.resolveComponent("el-tabs"),U=e.resolveComponent("el-table-column"),m=e.resolveComponent("el-image"),c=e.resolveComponent("el-tooltip"),F=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(te,{modelValue:b.value,"onUpdate:modelValue":t[2]||(t[2]=f=>b.value=f)},{default:e.withCtx(()=>[e.createVNode(D,{ref:"ruleForm",model:i},{default:e.withCtx(()=>[e.createVNode(V,null,{default:e.withCtx(()=>[e.createVNode(q,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{label:"店铺名称",prop:"shopName","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:i.shopName,"onUpdate:modelValue":t[0]||(t[0]=f=>i.shopName=f),placeholder:"请填写店铺名称",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(q,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{label:"商品名称",prop:"productName","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:i.productName,"onUpdate:modelValue":t[1]||(t[1]=f=>i.productName=f),placeholder:"请填写商品名称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(V,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(q,{span:8},{default:e.withCtx(()=>[e.createVNode(_,{type:"primary",round:"",onClick:$},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1}),e.createVNode(_,{type:"primary",round:"",onClick:x},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createVNode(B,{modelValue:i.distributionStatus,"onUpdate:modelValue":t[3]||(t[3]=f=>i.distributionStatus=f),type:"card",class:"demo-tabs",style:{"margin-top":"20px"},onTabChange:A},{default:e.withCtx(()=>[e.createVNode(z,{label:"全部",name:"ALL"}),e.createVNode(z,{label:"分销中",name:"IN_DISTRIBUTION"}),e.createVNode(z,{label:"取消分销",name:"CANCEL_DISTRIBUTION"})]),_:1},8,["modelValue"]),e.createVNode(V,{justify:"space-between",class:"dis__header"},{default:e.withCtx(()=>[e.createVNode(_,{type:"primary",plain:"",round:"",onClick:I},{default:e.withCtx(()=>[e.createTextVNode("批量取消分销")]),_:1})]),_:1}),e.createVNode(F,{data:i.list,class:"table-height-fit","header-cell-style":{background:"#F6F8FA"},style:e.normalizeStyle({height:S.value}),onSelectionChange:y},{default:e.withCtx(()=>[e.createVNode(U,{type:"selection",width:"50"}),e.createVNode(U,{width:"140",label:"店铺信息",align:"center"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",$e,e.toDisplayString(f.shopName),1)]),_:1}),e.createVNode(U,{label:"商品信息",width:"270",align:"center"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",Oe,[e.createVNode(m,{class:"com__img",src:f.pic},null,8,["src"]),e.createElementVNode("div",Be,[e.createElementVNode("div",Me,e.toDisplayString(f.name),1),e.createElementVNode("div",null,"￥"+e.toDisplayString(e.unref(a)(f.salePrices[0])),1)])])]),_:1}),e.createVNode(U,{label:"总库存",width:"100",align:"center"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",Ue,e.toDisplayString(f.stock),1)]),_:1}),e.createVNode(U,{label:"分佣参数",width:"120",align:"center"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",ze,[e.createTextVNode(" 一级:"),e.createElementVNode("span",{class:e.normalizeClass([f.shareType==="UNIFIED"?p.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":f.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(k(f.shareType,f.one)),3)]),n.value!=="ONE"&&f.two?(e.openBlock(),e.createElementBlock("div",Fe,[e.createTextVNode(" 二级:"),e.createElementVNode("span",{class:e.normalizeClass([f.shareType==="UNIFIED"?p.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":f.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(k(f.shareType,f.two)),3)])):e.createCommentVNode("",!0),n.value==="THREE"&&f.three?(e.openBlock(),e.createElementBlock("div",Ae,[e.createTextVNode(" 三级:"),e.createElementVNode("span",{class:e.normalizeClass([f.shareType==="UNIFIED"?p.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":f.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(k(f.shareType,f.three)),3)])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(U,{label:"分销佣金(预计)",width:"160",align:"center"},{default:e.withCtx(f=>{var X,M,v;return[e.createVNode(c,{"raw-content":!0,content:e.unref(Ie)(f.row.shareType,f.row.salePrices,(X=f.row)==null?void 0:X.one,(M=f.row)==null?void 0:M.two,(v=f.row)==null?void 0:v.three),placement:"bottom",effect:"light"},{default:e.withCtx(()=>{var R,ee;return[f.row.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",Le,[f.row.salePrices[0]===((R=f.row.salePrices)==null?void 0:R.pop())?(e.openBlock(),e.createElementBlock("span",Pe,"￥"+e.toDisplayString(O(e.unref(a)(f.row.salePrices[0]),f.row)),1)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[e.createElementVNode("span",null,"￥"+e.toDisplayString(O(e.unref(a)(f.row.salePrices[0]),f.row)),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(O(e.unref(a)((ee=f.row.salePrices)==null?void 0:ee.pop()),f.row)),1)],64))])):(e.openBlock(),e.createElementBlock("div",qe,"￥"+e.toDisplayString(O(e.unref(a)(f.row.salePrices[0]),f.row)),1))]}),_:2},1032,["content"])]}),_:1}),e.createVNode(U,{label:"状态",width:"100",align:"center"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",Re,e.toDisplayString(P(f.status)),1)]),_:1}),e.createVNode(U,{label:"分销状态",width:"100",align:"center"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",je,e.toDisplayString(f.distributionStatus==="IN_DISTRIBUTION"?"分销中":"取消分销"),1)]),_:1}),e.createVNode(U,{label:"添加时间",width:"160",align:"center"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",He,e.toDisplayString(f.createTime),1)]),_:1}),e.createVNode(U,{fixed:"right",label:"操作",width:"110",align:"center"},{default:e.withCtx(({row:f})=>[f.distributionStatus==="IN_DISTRIBUTION"?(e.openBlock(),e.createBlock(_,{key:0,type:"danger",link:"",onClick:X=>E(f)},{default:e.withCtx(()=>[e.createTextVNode("取消分销")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data","style"]),e.createVNode(H,{style:{border:"0"},"page-num":i.current,"page-size":i.size,total:i.total,onHandleCurrentChange:s,onHandleSizeChange:N},null,8,["page-num","page-size","total"])])}}}),ho="",Ye=Object.freeze(Object.defineProperty({__proto__:null,default:K(Xe,[["__scopeId","data-v-254900cf"]])},Symbol.toStringTag,{value:"Module"})),{divTenThousand:j}=W();function se(d,b){return j(b)}const We=()=>({useSingleCalculationFormula:(n,p,a)=>{var s,N;const l=(s=n==null?void 0:n.bonusShare)==null?void 0:s.shareType,i=(N=n==null?void 0:n.bonusShare)==null?void 0:N[p],r=j((n==null?void 0:n.dealPrice)*(n==null?void 0:n.num));return l==="RATE"?`${r} * ${se(l,i)}% = ${a}`:se(l,i)},useTotalPrice:n=>{let p=[],a=[],l=[],i=[];return n.items.forEach(s=>{let N=[];s.orderStatus==="PAID"?N=l:s.orderStatus==="COMPLETED"?N=p:s.orderStatus==="CLOSED"&&(N=a),s.one.bonus&&(i.push(j(s.one.bonus)),N.push(j(s.one.bonus))),s.two.bonus&&(i.push(j(s.two.bonus)),N.push(j(s.two.bonus))),s.three.bonus&&(i.push(j(s.three.bonus)),N.push(j(s.three.bonus))),s.purchase&&s.one.userId&&(i.push(j(s.one.bonus)),N.push(j(s.one.bonus)))}),{completePrice:p.join(" + ")+" = "+p.reduce((s,N)=>s.plus(N),new Y(0)),closedPrice:a.join(" + ")+" = "+a.reduce((s,N)=>s.plus(N),new Y(0)),toSelledPrice:l.join(" + ")+" = "+l.reduce((s,N)=>s.plus(N),new Y(0)),totalPrice:i.join(" + ")+" = "+i.reduce((s,N)=>s.plus(N),new Y(0))}}}),Ge=d=>(e.pushScopeId("data-v-df24458a"),d=d(),e.popScopeId(),d),Je={class:"head"},Ke={class:"ellipsis"},Qe={class:"content"},Ze={class:"goods"},ve={class:"goods__pic"},et=["src"],tt={class:"goods__info"},ot={class:"goods__info-flex"},nt={class:"goods__info-flex--name"},at={class:"goods__info-flex--price"},lt={class:"goods__info-flex"},rt={class:"goods__info-flex--specs"},it={class:"goods__info-flex--num"},dt={class:"f12 color51"},st={class:"content__right1"},ct={class:"content__right1--item"},pt={class:"content__right1--item"},mt={class:"content__right1--item"},ft={class:"content__right2"},ht={key:0,class:"content__right2--item"},_t={key:1,class:"content__right2--item"},gt={key:2,class:"content__right2--item"},bt=Ge(()=>e.createElementVNode("div",{class:"content__right2--item"},null,-1)),xt=e.defineComponent({__name:"DistributionOrderTableP",props:{orderInfo:{type:Object,default(){return{id:null,type:null,name:"",url:"",append:""}}}},setup(d){const{useSingleCalculationFormula:b,useTotalPrice:n}=We(),{toClipboard:p}=me(),{divTenThousand:a}=W(),l=d;function i(x,E){return a(E)}function r(x,E){let y=new Y(0);return"items"in l.orderInfo&&l.orderInfo.items.length&&l.orderInfo.items.forEach(I=>{I[x].bonus&&(y=y.add(a(I[x].bonus))),E&&I[x].userId&&(y=y.add(a(I[x].bonus)))}),y}function s(x){let E=new Y(0);return"items"in l.orderInfo&&l.orderInfo.items.length&&l.orderInfo.items.forEach(y=>{x&&y.orderStatus!==x||(y.one.bonus&&(E=E.add(a(y.one.bonus))),y.two.bonus&&(E=E.add(a(y.two.bonus))),y.three.bonus&&(E=E.add(a(y.three.bonus))),y.purchase&&y.one.userId&&(E=E.add(a(y.one.bonus))))}),E.toNumber()}function N(x){return x==="COMPLETED"?"已赚":x==="CLOSED"?"已失效":"待结算"}async function $(x){try{await p(x),T.ElMessage.success("复制成功")}catch{T.ElMessage.error("复制失败")}}return(x,E)=>{var P,A,k;const y=e.resolveComponent("el-table-column"),I=e.resolveComponent("el-table"),C=e.resolveComponent("el-tooltip");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",Je,[e.createElementVNode("div",null,[e.createTextVNode(" 订单号："+e.toDisplayString(d.orderInfo.orderNo),1),e.createElementVNode("span",{style:{"margin-left":"10px",color:"#1890ff",cursor:"pointer"},onClick:E[0]||(E[0]=g=>$(d.orderInfo.orderNo))},"复制")]),e.createElementVNode("div",null,"下单时间："+e.toDisplayString(d.orderInfo.createTime),1),e.createElementVNode("div",null,"买家："+e.toDisplayString(d.orderInfo.buyerNickname),1),e.createElementVNode("div",null,"实付款："+e.toDisplayString(e.unref(a)(d.orderInfo.payAmount).toFixed(2))+" 元",1),e.createElementVNode("div",Ke,"所属店铺："+e.toDisplayString(d.orderInfo.shopName),1)]),e.createElementVNode("div",Qe,[e.createVNode(I,{data:d.orderInfo.items,border:"",width:"100%","row-style":{height:"110px"}},{default:e.withCtx(()=>[e.createVNode(y,{prop:"name"},{default:e.withCtx(({row:g})=>[e.createElementVNode("div",Ze,[e.createElementVNode("div",ve,[e.createElementVNode("img",{src:g.image,style:{width:"60px",height:"50px"}},null,8,et),e.createElementVNode("div",{class:"goods__pic--state",style:e.normalizeStyle(g.orderStatus==="CLOSED"?"background:#9A9A9A;":g.orderStatus==="COMPLETED"?"background:#FD0505 ;":"")},e.toDisplayString(N(g.orderStatus)),5)]),e.createElementVNode("div",tt,[e.createElementVNode("div",ot,[e.createElementVNode("div",nt,e.toDisplayString(g.productName),1),e.createElementVNode("div",at,e.toDisplayString(g.num)+"件",1)]),e.createElementVNode("div",lt,[e.createElementVNode("div",rt,e.toDisplayString(g.specs&&g.specs.join("-")),1),e.createElementVNode("div",it,"￥"+e.toDisplayString(e.unref(a)(g.dealPrice)),1)])])])]),_:1}),e.createVNode(y,{prop:"address",width:"125px"},{default:e.withCtx(({row:g})=>[e.createElementVNode("div",dt,[e.createTextVNode(" 金额:"),e.createElementVNode("span",{class:e.normalizeClass([g.bonusShare.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(i(g.bonusShare.shareType,g.bonusShare.one)),3)])]),_:1})]),_:1},8,["data"]),e.createElementVNode("div",st,[e.createElementVNode("div",ct,[e.createElementVNode("div",null,e.toDisplayString(d.orderInfo.items[0].one.name),1),e.createElementVNode("div",null,e.toDisplayString(d.orderInfo.items[0].one.mobile),1),e.createElementVNode("div",null,[e.createVNode(C,{"raw-content":!0,content:d.orderInfo.items[0].one.userId?e.unref(b)((P=d.orderInfo.items)==null?void 0:P[0],"one",r("one")):"该层级无分销员，不计算对应佣金",placement:"bottom",effect:"light"},null,8,["content"])])]),e.createElementVNode("div",pt,[e.createElementVNode("div",null,e.toDisplayString(d.orderInfo.items[0].two.name),1),e.createElementVNode("div",null,e.toDisplayString(d.orderInfo.items[0].two.mobile),1),e.createVNode(C,{"raw-content":!0,content:d.orderInfo.items[0].two.userId?e.unref(b)((A=d.orderInfo.items)==null?void 0:A[0],"two",r("two")):"该层级无分销员，不计算对应佣金",placement:"bottom",effect:"light"},null,8,["content"])]),e.createElementVNode("div",mt,[e.createElementVNode("div",null,e.toDisplayString(d.orderInfo.items[0].three.name),1),e.createElementVNode("div",null,e.toDisplayString(d.orderInfo.items[0].three.mobile),1),e.createVNode(C,{"raw-content":!0,content:d.orderInfo.items[0].three.userId?e.unref(b)((k=d.orderInfo.items)==null?void 0:k[0],"three",r("three")):"该层级无分销员，不计算对应佣金",placement:"bottom",effect:"light"},null,8,["content"])])]),e.createElementVNode("div",ft,[s("PAID")?(e.openBlock(),e.createElementBlock("div",ht,[e.createVNode(C,{"raw-content":!0,content:e.unref(n)(d.orderInfo).toSelledPrice.toString(),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("待结算："+e.toDisplayString(s("PAID")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),s("COMPLETED")?(e.openBlock(),e.createElementBlock("div",_t,[e.createVNode(C,{"raw-content":!0,content:e.unref(n)(d.orderInfo).completePrice.toString(),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("已赚："+e.toDisplayString(s("COMPLETED")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),s("CLOSED")?(e.openBlock(),e.createElementBlock("div",gt,[e.createVNode(C,{"raw-content":!0,content:e.unref(n)(d.orderInfo).closedPrice.toString(),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("已失效："+e.toDisplayString(s("CLOSED")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),bt])])],64)}}}),bo="",Nt=K(xt,[["__scopeId","data-v-df24458a"]]),ut={class:"count"},Vt=e.createStaticVNode('<div class="tbhead" data-v-4bf7556c><div class="tbhead__goods" data-v-4bf7556c>商品</div><div class="tbhead__parameter" data-v-4bf7556c>分佣参数</div><div class="tbhead__detail" data-v-4bf7556c>分佣详情</div><div class="tbhead__total" data-v-4bf7556c>佣金结算</div></div>',1),Ct=e.defineComponent({__name:"DistributionOrderP",setup(d){const b=new fe,{divTenThousand:n}=W(),p=e.ref(!1),a=e.reactive({orderNo:"",productName:"",shopName:"",buyerNickname:"",date:"",startTime:"",endTime:"",status:""}),l=e.reactive({current:1,size:10,total:0}),i=e.ref([]),r=e.ref({total:"",unsettled:"",invalid:""});s();async function s(){const{code:k,data:g,msg:O}=await Ce({current:l.current,size:l.size,...a});k===200&&g?(i.value=g.page.records,r.value=g.statistic,l.total=g.page.total):T.ElMessage.error(O||"获取分销订单失败")}const N=()=>{l.current=1,(a==null?void 0:a.date.length)>0&&(a.startTime=a.date[0],a.endTime=a.date[1]),s()},$=()=>{a.orderNo="",a.productName="",a.shopName="",a.buyerNickname="",a.date="",a.startTime="",a.endTime="",l.current=1,s()},x=k=>{l.current=k,s()},E=k=>{l.current=1,l.size=k,s()},y=e.ref(" "),I=e.ref("全部订单"),C=k=>{a.status=k,s()},P=k=>{if(y.value=" ",I.value=k,I.value==="近一个月订单"){const g=b.getLastMonth(new Date);A(g)}else if(I.value==="近三个月订单"){const g=b.getLastThreeMonth(new Date);A(g)}else a.startTime="",a.endTime="",s()},A=async k=>{const g=b.getYMDs(new Date);a.startTime=k,a.endTime=g,s()};return(k,g)=>{const O=e.resolveComponent("el-input"),S=e.resolveComponent("el-form-item"),o=e.resolveComponent("el-col"),t=e.resolveComponent("el-date-picker"),w=e.resolveComponent("el-row"),u=e.resolveComponent("el-button"),q=e.resolveComponent("el-form"),V=e.resolveComponent("el-icon"),_=e.resolveComponent("el-dropdown-item"),D=e.resolveComponent("el-dropdown-menu"),z=e.resolveComponent("el-dropdown"),B=e.resolveComponent("el-tab-pane"),U=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(te,{modelValue:p.value,"onUpdate:modelValue":g[5]||(g[5]=m=>p.value=m)},{default:e.withCtx(()=>[e.createVNode(q,{ref:"ruleForm",model:a},{default:e.withCtx(()=>[e.createVNode(w,null,{default:e.withCtx(()=>[e.createVNode(o,{span:8},{default:e.withCtx(()=>[e.createVNode(S,{label:"订单号",prop:"orderNo","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(O,{modelValue:a.orderNo,"onUpdate:modelValue":g[0]||(g[0]=m=>a.orderNo=m),placeholder:"请填写订单号",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(o,{span:8},{default:e.withCtx(()=>[e.createVNode(S,{label:"商品名称",prop:"productName","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(O,{modelValue:a.productName,"onUpdate:modelValue":g[1]||(g[1]=m=>a.productName=m),placeholder:"请填写商品名称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(o,{span:8},{default:e.withCtx(()=>[e.createVNode(S,{label:"店铺名称",prop:"shopName","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(O,{modelValue:a.shopName,"onUpdate:modelValue":g[2]||(g[2]=m=>a.shopName=m),placeholder:"请填写店铺名称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(o,{span:8},{default:e.withCtx(()=>[e.createVNode(S,{label:"买家昵称",prop:"buyerNickname","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(O,{modelValue:a.buyerNickname,"onUpdate:modelValue":g[3]||(g[3]=m=>a.buyerNickname=m),placeholder:"请填写买家昵称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(o,{span:16},{default:e.withCtx(()=>[e.createVNode(S,{label:"下单时间",prop:"date","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(t,{modelValue:a.date,"onUpdate:modelValue":g[4]||(g[4]=m=>a.date=m),clearable:!1,type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","value-format":"YYYY-MM-DD HH:mm:ss","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(w,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(o,{span:8},{default:e.withCtx(()=>[e.createVNode(u,{type:"primary",round:"",onClick:N},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1}),e.createVNode(u,{type:"primary",round:"",onClick:$},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createElementVNode("div",ut,[e.createElementVNode("span",null,"累计总佣金： ￥ "+e.toDisplayString(e.unref(n)(r.value.total)),1),e.createElementVNode("span",null,"待结算总佣金： ￥ "+e.toDisplayString(e.unref(n)(r.value.unsettled)),1),e.createElementVNode("span",null,"已失效总佣金： ￥"+e.toDisplayString(e.unref(n)(r.value.invalid)),1)]),Vt,e.createVNode(U,{modelValue:y.value,"onUpdate:modelValue":g[6]||(g[6]=m=>y.value=m),onTabChange:C},{default:e.withCtx(()=>[e.createVNode(B,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(I.value),1),e.createVNode(z,{placement:"bottom-end",trigger:"click",onCommand:P},{dropdown:e.withCtx(()=>[e.createVNode(D,null,{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(["近一个月订单","近三个月订单","全部订单"],m=>e.createVNode(_,{key:m,command:m},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(m),1)]),_:2},1032,["command"])),64))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(V,{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(V,null,{default:e.withCtx(()=>[e.createVNode(e.unref(he.ArrowDownBold))]),_:1})]),_:1})])]),_:1})]),_:1}),e.createVNode(B,{label:"已付款",name:"PAID"}),e.createVNode(B,{label:"已完成",name:"COMPLETED"}),e.createVNode(B,{label:"已关闭",name:"CLOSED"})]),_:1},8,["modelValue"]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value,m=>(e.openBlock(),e.createBlock(Nt,{key:m.orderNo,"order-info":m},null,8,["order-info"]))),128)),e.createVNode(H,{"page-num":l.current,"page-size":l.size,total:l.total,onHandleCurrentChange:x,onHandleSizeChange:E},null,8,["page-num","page-size","total"])],64)}}}),xo="",wt=Object.freeze(Object.defineProperty({__proto__:null,default:K(Ct,[["__scopeId","data-v-4bf7556c"]])},Symbol.toStringTag,{value:"Module"})),yt=d=>(e.pushScopeId("data-v-b2ee1b9f"),d=d(),e.popScopeId(),d),Et={class:"tool"},Tt={class:"tool__btn"},St={style:{width:"190px"}},Dt={key:0,style:{width:"135px"},class:"ml"},kt={key:1,style:{width:"200px"},class:"ml"},It=["onClick"],$t={class:"f12 color51",style:{"margin-left":"6px"}},Ot={class:"ellipsis"},Bt={key:0},Mt={style:{display:"flex","align-items":"center","justify-content":"space-around",width:"260px"}},Ut={style:{"font-size":"20px"}},zt=["onClick"],Ft={class:"f12 color58",style:{"margin-bottom":"20px"}},At={class:"f12",style:{display:"flex"}},Lt={class:"ml"},Pt={class:"ml"},qt={class:"f12 color33"},Rt={class:"f12 color33"},jt=yt(()=>e.createElementVNode("div",null,"审核结果：拒绝",-1)),Ht={class:"dialog-footer"},Xt={class:"dialog-footer"},Yt=e.defineComponent({__name:"WholeSalerP",setup(d){const b=e.ref(!1),n=e.reactive({name:"",mobile:"",date:"",startTime:"",endTime:"",status:"SUCCESS"}),{divTenThousand:p}=W(),a=[{label:"拒绝",name:"refuse"}],l=e.reactive({current:1,size:10,total:0}),i=e.ref([]),r=e.ref([]),s=e.ref(!1),N=e.reactive({current:1,total:0,size:10,list:[],userId:""}),$=e.ref(!1),x=e.reactive({current:1,total:0,size:10,list:[],userId:"",rank:""});D();const E=()=>{D()},y=m=>{l.current=m,D()},I=m=>{l.current=1,l.size=m,D()},C=()=>{l.current=1,n.date.length>0&&(console.log("searchConfig.date",n.date),n.startTime=n.date[0],n.endTime=n.date[1]),D()},P=m=>{O(!0,[m])},A=()=>{r.value.length?O(!0,r.value.map(m=>m.userId)):T.ElMessage.warning("请勾选分销商")},k=m=>{O(!1,[m])},g=()=>{r.value.length?O(!1,r.value.map(m=>m.userId)):T.ElMessage.warning("请勾选分销商")};async function O(m,c){T.ElMessageBox.confirm("确定需要继续操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:F}=await Te(m,c);F&&F===200?(T.ElMessage.success("操作成功"),D()):T.ElMessage.error("操作失败")})}const S=m=>{n.status==="SUCCESS"&&(s.value=!0,N.userId=m,z(m))},o=m=>{n.status==="SUCCESS"&&($.value=!0,x.userId=m,B(m))},t=()=>{N.current=1,N.size=10,N.list=[],N.userId=""},w=()=>{x.current=1,x.size=10,x.list=[],x.userId="",x.rank=""},u=m=>{N.current=m,z()},q=m=>{N.current=1,N.size=m,z()},V=m=>{x.current=m,B()},_=m=>{x.current=1,x.size=m,B()};async function D(){const{code:m,data:c}=await we({...n,...l});m===200?(i.value=c.records,l.total=c.total):T.ElMessage.error("获取分销商失败")}async function z(m){const{code:c,data:F}=await ye({userId:m||N.userId,current:N.current,size:N.size});c&&c===200?(N.list=F.records,N.total=F.total,s.value=!0):(T.ElMessage.error("获取下线失败"),s.value=!1)}async function B(m){const{code:c,data:F}=await Ee({userId:m||x.userId,current:x.current,size:x.size});c&&c===200?(x.list=F.records,x.total=F.total,x.rank=F.rank,$.value=!0):(T.ElMessage.error("获取排行失败"),$.value=!1)}const U=()=>{l.current=1,n.date="",n.endTime="",n.startTime="",n.mobile="",n.name="",D()};return(m,c)=>{const F=e.resolveComponent("el-input"),f=e.resolveComponent("el-form-item"),X=e.resolveComponent("el-col"),M=e.resolveComponent("el-date-picker"),v=e.resolveComponent("el-row"),R=e.resolveComponent("el-button"),ee=e.resolveComponent("el-form"),ae=e.resolveComponent("el-tab-pane"),po=e.resolveComponent("el-tabs"),mo=e.resolveComponent("el-image"),Q=e.resolveComponent("el-table-column"),ce=e.resolveComponent("el-table"),pe=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(te,{modelValue:b.value,"onUpdate:modelValue":c[3]||(c[3]=h=>b.value=h)},{default:e.withCtx(()=>[e.createVNode(ee,{ref:"ruleForm",model:n},{default:e.withCtx(()=>[e.createVNode(v,null,{default:e.withCtx(()=>[e.createVNode(X,{span:8},{default:e.withCtx(()=>[e.createVNode(f,{label:"姓名",prop:"name","label-width":"50px"},{default:e.withCtx(()=>[e.createVNode(F,{modelValue:n.name,"onUpdate:modelValue":c[0]||(c[0]=h=>n.name=h),placeholder:"请填写姓名检索",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(X,{span:8},{default:e.withCtx(()=>[e.createVNode(f,{label:"手机号",prop:"mobile","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(F,{modelValue:n.mobile,"onUpdate:modelValue":c[1]||(c[1]=h=>n.mobile=h),placeholder:"请填写手机号检索",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(X,{span:16},{default:e.withCtx(()=>[e.createVNode(f,{label:"申请时间",prop:"date","label-width":"75px"},{default:e.withCtx(()=>[e.createVNode(M,{modelValue:n.date,"onUpdate:modelValue":c[2]||(c[2]=h=>n.date=h),type:"datetimerange","range-separator":"-",clearable:!1,"start-placeholder":"开始时间","value-format":"YYYY-MM-DD HH:mm:ss","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(v,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(X,{span:8},{default:e.withCtx(()=>[e.createVNode(R,{type:"primary",round:"",onClick:C},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1}),e.createVNode(R,{type:"primary",round:"",onClick:U},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createVNode(po,{modelValue:n.status,"onUpdate:modelValue":c[4]||(c[4]=h=>n.status=h),type:"card",style:{"margin-top":"20px"},class:"demo-tabs",onTabChange:E},{default:e.withCtx(()=>[e.createVNode(ae,{label:"分销列表",name:"SUCCESS"}),e.createVNode(ae,{label:"待审核",name:"APPLYING"}),e.createVNode(ae,{label:"已关闭",name:"CLOSED"})]),_:1},8,["modelValue"]),e.createElementVNode("div",Et,[e.createElementVNode("div",Tt,[n.status==="APPLYING"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(R,{type:"primary",plain:"",round:"",onClick:A},{default:e.withCtx(()=>[e.createTextVNode("批量同意")]),_:1}),e.createVNode(R,{type:"primary",plain:"",round:"",onClick:g},{default:e.withCtx(()=>[e.createTextVNode("批量拒绝")]),_:1})],64)):e.createCommentVNode("",!0)])]),e.createVNode(e.unref(_e),{"checked-item":r.value,"onUpdate:checkedItem":c[5]||(c[5]=h=>r.value=h),data:i.value,selection:!0},{header:e.withCtx(({row:h})=>[e.createElementVNode("div",St,"申请时间："+e.toDisplayString(h.createTime),1),n.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",Dt,"审核人员："+e.toDisplayString(h.auditor),1)):e.createCommentVNode("",!0),n.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",kt,"审核(通过)："+e.toDisplayString(h.passTime),1)):e.createCommentVNode("",!0),n.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",{key:2,style:{"margin-left":"200px",color:"#0f40f5",cursor:"pointer"},onClick:le=>o(h.userId)}," 佣金排行榜 ",8,It)):e.createCommentVNode("",!0)]),default:e.withCtx(()=>[e.createVNode(J,{label:"分销商信息",width:"90"},{default:e.withCtx(({row:h})=>[e.createElementVNode("div",null,[e.createVNode(v,{justify:"space-between"},{default:e.withCtx(()=>[e.createVNode(mo,{src:h.avatar,style:{width:"40px",height:"40px","border-radius":"4px"}},null,8,["src"]),e.createElementVNode("div",$t,[e.createElementVNode("div",Ot,e.toDisplayString(h.name),1),e.createElementVNode("div",null,e.toDisplayString(h.mobile),1),n.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",Bt,"上级 ："+e.toDisplayString(h.referrer||"平台"),1)):e.createCommentVNode("",!0)])]),_:2},1024)])]),_:1}),n.status==="SUCCESS"?(e.openBlock(),e.createBlock(J,{key:0,label:"团队成员",width:"100"},{default:e.withCtx(({row:h})=>[e.createElementVNode("div",Mt,[e.createElementVNode("div",null,[e.createTextVNode(" 总人数： "),e.createElementVNode("span",Ut,e.toDisplayString(Number(h.one)+Number(h.two)+Number(h.three)),1)]),e.createElementVNode("div",{class:"column",onClick:le=>S(h.userId)},[e.createVNode(R,{type:"primary",link:""},{default:e.withCtx(()=>[e.createTextVNode("成员："+e.toDisplayString(h.one),1)]),_:2},1024)],8,zt)])]),_:1})):e.createCommentVNode("",!0),n.status==="SUCCESS"?(e.openBlock(),e.createBlock(J,{key:1,label:"佣金",width:"150"},{default:e.withCtx(({row:h})=>[e.createElementVNode("div",null,[e.createElementVNode("div",Ft,"累计佣金："+e.toDisplayString(e.unref(p)(h.total)),1),e.createElementVNode("div",At,[e.createElementVNode("div",null,"待提现佣金："+e.toDisplayString(e.unref(p)(h.undrawn)),1),e.createElementVNode("div",Lt,"待结算佣金："+e.toDisplayString(e.unref(p)(h.unsettled)),1),e.createElementVNode("div",Pt,"已失效佣金："+e.toDisplayString(e.unref(p)(h.invalid)),1)])])]),_:1})):e.createCommentVNode("",!0),n.status!=="SUCCESS"?(e.openBlock(),e.createBlock(J,{key:2,label:"上级分销商",width:"60"},{default:e.withCtx(({row:h})=>[e.createElementVNode("div",qt,e.toDisplayString(h.referrer||"平台"),1)]),_:1})):e.createCommentVNode("",!0),n.status!=="SUCCESS"?(e.openBlock(),e.createBlock(J,{key:3,label:"累计消费(元)",width:"60"},{default:e.withCtx(({row:h})=>[e.createElementVNode("div",Rt,e.toDisplayString(e.unref(p)(h.consumption)),1)]),_:1})):e.createCommentVNode("",!0),n.status==="APPLYING"?(e.openBlock(),e.createBlock(J,{key:4,label:"操作",width:"60"},{default:e.withCtx(({row:h})=>[e.createVNode(e.unref(ge),{title:"同意","command-list":a,onClick:le=>P(h.userId),onCommand:le=>k(h.userId)},null,8,["onClick","onCommand"])]),_:1})):e.createCommentVNode("",!0),n.status==="CLOSED"?(e.openBlock(),e.createBlock(J,{key:5,label:"操作",width:"60"},{default:e.withCtx(({row:h})=>[e.createElementVNode("div",null,[e.createElementVNode("div",null,"审核时间："+e.toDisplayString(h.passTime),1),e.createElementVNode("div",null,"审核员："+e.toDisplayString(h.auditor),1),jt])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["checked-item","data"]),e.createVNode(H,{"page-num":l.current,"page-size":l.size,total:l.total,onHandleCurrentChange:y,onHandleSizeChange:I},null,8,["page-num","page-size","total"]),e.createVNode(pe,{modelValue:s.value,"onUpdate:modelValue":c[8]||(c[8]=h=>s.value=h),width:"554px",title:"团队成员",onClose:t},{footer:e.withCtx(()=>[e.createElementVNode("span",Ht,[e.createVNode(R,{onClick:c[6]||(c[6]=h=>s.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(R,{type:"primary",onClick:c[7]||(c[7]=h=>s.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(ce,{data:N.list,"header-cell-style":{background:"#f6f8fa",fontSize:"12px",color:"#909399","font-weight":"bold"},"cell-style":{fontSize:"12px",color:"#333333"},height:"400"},{default:e.withCtx(()=>[e.createVNode(Q,{label:"姓名",align:"center"},{default:e.withCtx(({row:h})=>[e.createTextVNode(e.toDisplayString(h.name?h.name:h.nickname),1)]),_:1}),e.createVNode(Q,{label:"层级",align:"center"},{default:e.withCtx(({row:h})=>[e.createTextVNode(e.toDisplayString(h.level==="ONE"?"一级":h.level==="TWO"?"二级":"三级"),1)]),_:1}),e.createVNode(Q,{label:"累计消费",align:"center",prop:"consumption"},{default:e.withCtx(({row:h})=>[e.createTextVNode(e.toDisplayString(e.unref(p)(h.consumption)),1)]),_:1}),e.createVNode(Q,{label:"订单数",align:"center",prop:"orderCount"})]),_:1},8,["data"]),e.createVNode(H,{"page-num":N.current,"page-size":N.size,total:N.total,onHandleCurrentChange:u,onHandleSizeChange:q},null,8,["page-num","page-size","total"])]),_:1},8,["modelValue"]),e.createVNode(pe,{modelValue:$.value,"onUpdate:modelValue":c[11]||(c[11]=h=>$.value=h),width:"554px",title:"佣金排行榜",onClose:w},{footer:e.withCtx(()=>[e.createElementVNode("span",Xt,[e.createVNode(R,{onClick:c[9]||(c[9]=h=>$.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(R,{type:"primary",onClick:c[10]||(c[10]=h=>$.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("div",null,"您的累计佣金排名为："+e.toDisplayString(x.rank.rank),1),e.createVNode(ce,{data:x.list,"header-cell-style":{background:"#f6f8fa",fontSize:"12px",color:"#909399","font-weight":"bold"},"cell-style":{fontSize:"12px",color:"#333333"},height:"400"},{default:e.withCtx(()=>[e.createVNode(Q,{label:"名次",align:"center",type:"index"}),e.createVNode(Q,{label:"姓名",align:"center",prop:"name"}),e.createVNode(Q,{label:"累计佣金",align:"center",prop:"total"},{default:e.withCtx(({row:h})=>[e.createTextVNode(e.toDisplayString(e.unref(p)(h.total)),1)]),_:1})]),_:1},8,["data"]),e.createVNode(H,{"page-num":x.current,"page-size":x.size,total:x.total,onHandleCurrentChange:V,onHandleSizeChange:_},null,8,["page-num","page-size","total"])]),_:1},8,["modelValue"])],64)}}}),uo="",Wt=Object.freeze(Object.defineProperty({__proto__:null,default:K(Yt,[["__scopeId","data-v-b2ee1b9f"]])},Symbol.toStringTag,{value:"Module"})),Z=d=>(e.pushScopeId("data-v-8fb47d32"),d=d(),e.popScopeId(),d),Gt=Z(()=>e.createElementVNode("div",{class:"col mb26"},[e.createElementVNode("div",{class:"col__icon",style:{background:"#08CC00"}}),e.createElementVNode("div",null,"基础设置")],-1)),Jt=Z(()=>e.createElementVNode("br",null,null,-1)),Kt=Z(()=>e.createElementVNode("span",{style:{"margin-left":"30px",color:"#9a9a9a"}},"开启后分销员自己购买分销商品可获得一级佣金",-1)),Qt=Z(()=>e.createElementVNode("span",{style:{"margin-left":"30px",color:"#9a9a9a"}},"控制是否在商品详情页内展示【预计赚】",-1)),Zt=Z(()=>e.createElementVNode("div",{class:"col mb26"},[e.createElementVNode("div",{class:"col__icon",style:{background:"#F57373"}}),e.createElementVNode("div",null,"佣金设置")],-1)),vt=Z(()=>e.createElementVNode("span",{style:{"margin-left":"30px",color:"#9a9a9a"}},"百分比佣金 = 商品实售价 * 购买商品数 * 百分比；固定金额佣金 = 固定金额 * 购买商品数",-1)),eo={class:"save"},to={class:"dialog-footer"},oo={class:"dialog-footer"},no=e.defineComponent({__name:"DistributionSetP",setup(d){const{divHundred:b,mulHundred:n,mulTenThousand:p,divTenThousand:a}=W(),l=e.ref(!1),i=e.ref(!1),r=e.reactive({config:{level:"ONE",condition:{types:["APPLY","CONSUMPTION"],requiredAmount:0},precompute:"DISTRIBUTOR",protocol:"111",playMethods:"333",purchase:!1,shareType:"FIXED_AMOUNT",one:0,two:0,three:0}}),s=e.computed(()=>r.config.level==="ONE"?1:r.config.level==="TWO"?2:3),N=e.reactive({poster:[{required:!0,message:"请设置推广海报",trigger:"blur"}],condition:[{required:!0,validator:C,trigger:"blur"}],protocol:[{required:!0,message:"请设置协议",trigger:"blur"}],playMethods:[{required:!0,message:"请设置攻略玩法",trigger:"blur"}],one:[{required:!0,validator:P,trigger:"change"}],two:[{required:!0,validator:A,trigger:"change"}],three:[{required:!0,validator:k,trigger:"change"}]}),$=e.ref();O();const x=()=>{l.value=!0},E=()=>{i.value=!0},y=async()=>{$.value&&$.value.validate(async(S,o)=>{if(S){const t=JSON.parse(JSON.stringify(r.config));t.condition.requiredAmount=p(t.condition.requiredAmount),r.config.shareType==="RATE"?(t.one=n(t.one),t.two=n(t.two),t.three=n(t.three)):(t.one=p(t.one),t.two=p(t.two),t.three=p(t.three));const{code:w}=await ue(g(t));w===200?T.ElMessage.success("保存成功"):T.ElMessage.error("保存失败")}})},I=()=>{r.config.one=0,r.config.two=0,r.config.three=0};function C(S,o,t){if(o.types.length){if(o.types.includes("CONSUMPTION")&&Number(o.requiredAmount)<=0)return t(new Error("设置金额应大于零"))}else return t(new Error("请选择分销商条件"));t()}function P(S,o,t){const w=r.config.shareType;if(w==="UNIFIED")return t();const u=Number(o);if(u<=0)return t(new Error("一级佣金应大于等于零"));if(r.config.level!=="ONE"&&r.config.two&&u<=Number(r.config.two))return t(new Error("一级佣金值应大于二级佣金"));if(w==="RATE"&&(u>100||u<0))return t(new Error("一级佣金比例应设置在0-100之间"));if(w==="FIXED_AMOUNT"&&(u>9e3||u<0))return t(new Error("一级佣金应设置在0-9000之间"));t()}function A(S,o,t){const w=r.config.shareType;if(w==="UNIFIED"||r.config.level==="ONE")return t();const u=Number(o);if(w==="RATE"&&(u>100||u<0))return t(new Error("一级佣金比例应设置在0-100之间"));if(w==="FIXED_AMOUNT"&&(u>9e3||u<0))return t(new Error("一级佣金应设置在0-9000之间"));if(r.config.one&&u>=Number(r.config.one))return t(new Error("二级佣金应小于一级佣金"));if(r.config.level==="THREE"&&r.config.three&&u<=Number(r.config.three))return t(new Error("二级佣金应大于三级佣金"));t()}function k(S,o,t){const w=r.config.shareType;if(w==="UNIFIED"||r.config.level!=="THREE")return t();const u=Number(o);if(w==="RATE"&&(u>100||u<0))return t(new Error("一级佣金比例应设置在1-100之间"));if(w==="FIXED_AMOUNT"&&(u>9e3||u<0))return t(new Error("一级佣金应设置在0-9000之间"));if(r.config.two&&u>=Number(r.config.two))return t(new Error("三级佣金应小于二级佣金"));t()}function g(S){return S.level==="ONE"?(delete S.two,delete S.three):S.level==="TWO"&&delete S.three,S}async function O(){const{code:S,data:o}=await oe();S===200?(o.shareType==="RATE"?(o.one=b(o.one),o.two=b(o.two),o.three=b(o.three)):(o.one=a(o.one),o.two=a(o.two),o.three=a(o.three)),o.condition.requiredAmount=a(o.condition.requiredAmount),r.config=o):T.ElMessage.error("获取分销配置失败")}return(S,o)=>{const t=e.resolveComponent("el-radio"),w=e.resolveComponent("el-radio-group"),u=e.resolveComponent("el-form-item"),q=e.resolveComponent("el-checkbox"),V=e.resolveComponent("el-col"),_=e.resolveComponent("el-input"),D=e.resolveComponent("el-row"),z=e.resolveComponent("el-checkbox-group"),B=e.resolveComponent("el-button"),U=e.resolveComponent("el-form"),m=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(U,{ref_key:"formRef",ref:$,model:r.config,"label-width":"120",rules:N},{default:e.withCtx(()=>[Gt,e.createVNode(u,{label:"分销层级",class:"mb33",prop:"level"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:r.config.level,"onUpdate:modelValue":o[0]||(o[0]=c=>r.config.level=c)},{default:e.withCtx(()=>[e.createVNode(t,{label:"ONE"},{default:e.withCtx(()=>[e.createTextVNode("一级")]),_:1}),e.createVNode(t,{label:"TWO"},{default:e.withCtx(()=>[e.createTextVNode("二级")]),_:1}),e.createVNode(t,{label:"THREE"},{default:e.withCtx(()=>[e.createTextVNode("三级")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(u,{label:"成为分销商条件",class:"mb33",prop:"condition"},{default:e.withCtx(()=>[e.createVNode(z,{modelValue:r.config.condition.types,"onUpdate:modelValue":o[2]||(o[2]=c=>r.config.condition.types=c)},{default:e.withCtx(()=>[e.createVNode(q,{label:"APPLY"},{default:e.withCtx(()=>[e.createTextVNode("申请")]),_:1}),Jt,e.createVNode(q,{label:"CONSUMPTION"},{default:e.withCtx(()=>[e.createVNode(D,{justify:"start",align:"middle",style:{width:"240px"}},{default:e.withCtx(()=>[e.createVNode(V,{span:12},{default:e.withCtx(()=>[e.createTextVNode("累计金额大于等于")]),_:1}),e.createVNode(V,{span:12},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:r.config.condition.requiredAmount,"onUpdate:modelValue":o[1]||(o[1]=c=>r.config.condition.requiredAmount=c),type:"number",controls:!1,style:{width:"142px"}},{append:e.withCtx(()=>[e.createTextVNode("元")]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(u,{label:"分销内购",class:"mb33"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:r.config.purchase,"onUpdate:modelValue":o[3]||(o[3]=c=>r.config.purchase=c)},{default:e.withCtx(()=>[e.createVNode(t,{label:!1},{default:e.withCtx(()=>[e.createTextVNode("关闭")]),_:1}),e.createVNode(t,{label:!0},{default:e.withCtx(()=>[e.createTextVNode("开启")]),_:1})]),_:1},8,["modelValue"]),Kt]),_:1}),e.createVNode(u,{label:"预计赚",class:"mb33"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:r.config.precompute,"onUpdate:modelValue":o[4]||(o[4]=c=>r.config.precompute=c)},{default:e.withCtx(()=>[e.createVNode(t,{label:"DISTRIBUTOR"},{default:e.withCtx(()=>[e.createTextVNode("只对分销商(员)展示【预计赚】")]),_:1}),e.createVNode(t,{label:"NEVER"},{default:e.withCtx(()=>[e.createTextVNode("对所有人都不展示")]),_:1}),e.createVNode(t,{label:"ALL"},{default:e.withCtx(()=>[e.createTextVNode("对所有人展示【预计赚】")]),_:1})]),_:1},8,["modelValue"]),Qt]),_:1}),e.createVNode(u,{label:"协议内容"},{default:e.withCtx(()=>[e.createVNode(B,{link:"",type:"primary",onClick:x},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:1})]),_:1}),e.createVNode(u,{label:"赚钱攻略"},{default:e.withCtx(()=>[e.createVNode(B,{link:"",type:"primary",onClick:E},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:1})]),_:1}),Zt,e.createVNode(u,{label:"佣金设置"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:r.config.shareType,"onUpdate:modelValue":o[5]||(o[5]=c=>r.config.shareType=c),onChange:I},{default:e.withCtx(()=>[e.createVNode(t,{label:"FIXED_AMOUNT"},{default:e.withCtx(()=>[e.createTextVNode("固定金额")]),_:1}),e.createVNode(t,{label:"RATE"},{default:e.withCtx(()=>[e.createTextVNode("百分比")]),_:1})]),_:1},8,["modelValue"]),vt]),_:1}),s.value>=1?(e.openBlock(),e.createBlock(u,{key:0,label:"一级佣金",prop:"one"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:r.config.one,"onUpdate:modelValue":o[6]||(o[6]=c=>r.config.one=c),type:"number",style:{width:"200px"}},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r.config.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0),s.value>=2?(e.openBlock(),e.createBlock(u,{key:1,label:"二级佣金",prop:"two"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:r.config.two,"onUpdate:modelValue":o[7]||(o[7]=c=>r.config.two=c),type:"number",style:{width:"200px"}},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r.config.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0),s.value===3?(e.openBlock(),e.createBlock(u,{key:2,label:"三级佣金",prop:"three"},{default:e.withCtx(()=>[e.createVNode(_,{modelValue:r.config.three,"onUpdate:modelValue":o[8]||(o[8]=c=>r.config.three=c),type:"number",style:{width:"200px"}},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r.config.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model","rules"]),e.createElementVNode("div",eo,[e.createVNode(B,{type:"primary",round:"",onClick:y},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1})]),e.createVNode(m,{modelValue:l.value,"onUpdate:modelValue":o[12]||(o[12]=c=>l.value=c),title:"编辑协议"},{footer:e.withCtx(()=>[e.createElementVNode("span",to,[e.createVNode(B,{onClick:o[10]||(o[10]=c=>l.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(B,{type:"primary",onClick:o[11]||(o[11]=c=>l.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(re,{content:r.config.protocol,"onUpdate:content":o[9]||(o[9]=c=>r.config.protocol=c)},null,8,["content"])]),_:1},8,["modelValue"]),e.createVNode(m,{modelValue:i.value,"onUpdate:modelValue":o[16]||(o[16]=c=>i.value=c),title:"编辑攻略"},{footer:e.withCtx(()=>[e.createElementVNode("span",oo,[e.createVNode(B,{onClick:o[14]||(o[14]=c=>i.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(B,{type:"primary",onClick:o[15]||(o[15]=c=>i.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(re,{content:r.config.playMethods,"onUpdate:content":o[13]||(o[13]=c=>r.config.playMethods=c)},null,8,["content"])]),_:1},8,["modelValue"])],64)}}}),Co="",ao=Object.freeze(Object.defineProperty({__proto__:null,default:K(no,[["__scopeId","data-v-8fb47d32"]])},Symbol.toStringTag,{value:"Module"})),lo={class:"dis"},ro={class:"dis__header"},io={class:"dis__header-left"},so=e.defineComponent({__name:"DistributionCode",props:{platform:{type:String,default:""}},setup(d){const b=e.reactive({list:[],current:1,pages:1,total:0,size:10,productName:"",distributionStatus:"ALL"}),{mulTenThousand:n,divTenThousand:p,mulHundred:a,divHundred:l}=W(),i=e.ref("add"),r=e.ref(!1),s=e.ref("ONE"),N=e.ref({}),$=e.ref([]),x=e.ref({id:"",name:""}),E=e.ref(),y=e.computed(()=>i.value==="add"?"新增":i.value==="edit"?"编辑":"查看"),I=d;setTimeout(()=>{console.log(I.platform)},1e3),e.reactive({pages:1,current:1,list:[],total:0,size:10,name:"",loading:!1,excludeProductIds:[]});const C=e.reactive({quantity:null,shopId:null}),P=e.reactive({quantity:[{required:!0,message:"请填写数量",trigger:"blur"}],shopId:[{required:!0,message:"请选择绑定的店铺",trigger:"blur"}]}),A=()=>{Se({}).then(V=>{var _;V.code===200?$.value=((_=V==null?void 0:V.data)==null?void 0:_.records)??[]:($.value=[],T.ElMessage.error((V==null?void 0:V.msg)??"获取商店列表失败！"))})};let k=e.computed(()=>V=>{const _=$.value.filter(D=>D.id==V);return _.length>0?_[0].name:""});const g=()=>{const V=JSON.parse(localStorage.getItem("storlocalshopStore"));x.value={id:V.value.shopId,name:V.value.name}};q(),I.platform=="shop"?g():A(),setTimeout(()=>{u()},200);const O=()=>{i.value="add",r.value=!0},S=()=>{E.value&&E.value.validate(async V=>{if(V){console.log(C,"distributeForm");const _={num:Number(C.quantity),shopId:I.platform=="shop"?x.value.id:C.shopId};console.log(_,"param");const D=await De(_);D.code===200?(T.ElMessage.success("激活码创建成功！"),u(),r.value=!1,C.quantity=null,C.shopId=null):T.ElMessage.error((D==null?void 0:D.msg)??"创建邀请码失败")}})},o=V=>{b.current=V,u()},t=V=>{b.size=V,u()},w=()=>{};e.ref("");async function u(){const{code:V,data:_,msg:D}=await ke({size:b.size,current:b.current,shopId:I.platform=="shop"?x.value.id:null});V===200&&_?(b.list=_.records,b.total=_.total):T.ElMessage.error(D??"获取邀请码列表失败")}async function q(){const{code:V,data:_}=await oe();V===200?(s.value=_.level,(_.one||_.two||_.three)&&(_.one=_.shareType==="FIXED_AMOUNT"?String(p(_.one)):String(l(_.one)),_.two=_.shareType==="FIXED_AMOUNT"?String(p(_.two)):String(l(_.two)),_.three=_.shareType==="FIXED_AMOUNT"?String(p(_.three)):String(l(_.three)),N.value=_)):T.ElMessage.error("获取分销配置失败")}return(V,_)=>{const D=e.resolveComponent("el-button"),z=e.resolveComponent("el-table-column"),B=e.resolveComponent("el-table"),U=e.resolveComponent("el-input"),m=e.resolveComponent("el-form-item"),c=e.resolveComponent("el-option"),F=e.resolveComponent("el-select"),f=e.resolveComponent("el-form"),X=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",lo,[e.createElementVNode("div",ro,[e.createElementVNode("div",io,[e.createVNode(D,{type:"primary",round:"",style:{width:"100px",height:"36px"},onClick:O},{default:e.withCtx(()=>[e.createTextVNode("新增邀请码")]),_:1})])]),e.createVNode(B,{data:b.list,"header-cell-style":{color:"#909399",fontSize:"12px",background:"#F6F8FA"},class:"table-height-fit"},{default:e.withCtx(()=>[e.createVNode(z,{label:"邀请码",width:"220",align:"center",prop:"code"}),e.createVNode(z,{label:"状态",width:"100",align:"center",prop:"isUse"},{default:e.withCtx(M=>[e.createTextVNode(e.toDisplayString(new Date(M.row.useEndDate).getTime()<new Date().getTime()?"已过期":M.row.isUse==!0?"已使用":"待使用"),1)]),_:1}),e.createVNode(z,{label:"所属店铺",width:"100",align:"center",prop:"shopId"},{default:e.withCtx(M=>[e.createTextVNode(e.toDisplayString(I.platform=="shop"?x.value.name:e.unref(k)(M.row.shopId)),1)]),_:1}),e.createVNode(z,{label:"创建时间",width:"240",align:"center",prop:"createTime"}),e.createVNode(z,{label:"过期时间",width:"240",align:"center",prop:"useEndDate"})]),_:1},8,["data"]),e.createVNode(H,{"page-size":b.size,"page-num":b.current,total:b.total,onHandleCurrentChange:o,onHandleSizeChange:t},null,8,["page-size","page-num","total"]),e.createVNode(X,{modelValue:r.value,"onUpdate:modelValue":_[3]||(_[3]=M=>r.value=M),title:y.value,width:"30%",onClose:w},{footer:e.withCtx(()=>[e.createVNode(D,{onClick:_[2]||(_[2]=M=>r.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(D,{type:"primary",onClick:S},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})]),default:e.withCtx(()=>[e.createVNode(f,{ref_key:"formRef",ref:E,model:C,rules:P},{default:e.withCtx(()=>[e.createVNode(m,{label:"数量",prop:"quantity"},{default:e.withCtx(()=>[e.createVNode(U,{modelValue:C.quantity,"onUpdate:modelValue":_[0]||(_[0]=M=>C.quantity=M),style:{width:"70%"},min:1,type:"number"},null,8,["modelValue"])]),_:1}),I.platform!="shop"?(e.openBlock(),e.createBlock(m,{key:0,label:"店铺",prop:"shopId"},{default:e.withCtx(()=>[e.createVNode(F,{modelValue:C.shopId,"onUpdate:modelValue":_[1]||(_[1]=M=>C.shopId=M),style:{width:"70%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList($.value,M=>(e.openBlock(),e.createBlock(c,{key:M.id+"$shop",label:M.name,value:M.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}}),yo="",co=Object.freeze(Object.defineProperty({__proto__:null,default:K(so,[["__scopeId","data-v-7471ba34"]])},Symbol.toStringTag,{value:"Module"}));return Ne});
