(function(e,f){typeof exports=="object"&&typeof module<"u"?module.exports=f(require("vue"),require("@vueuse/core"),require("@/apis/http"),require("vue-router"),require("@/composables/useConvert"),require("@/components/PageManage.vue"),require("@/store/modules/shopInfo"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@/apis/http","vue-router","@/composables/useConvert","@/components/PageManage.vue","@/store/modules/shopInfo","element-plus"],f):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopApplyDiscount=f(e.ShopApplyDiscountContext.Vue,e.ShopApplyDiscountContext.VueUse,e.ShopApplyDiscountContext.Request,e.ShopApplyDiscountContext.VueRouter,e.ShopApplyDiscountContext.UseConvert,e.ShopApplyDiscountContext.PageManageTwo,e.ShopApplyDiscountContext.ShopInfoStore,e.ShopApplyDiscountContext.ElementPlus))})(this,function(e,f,g,V,b,D,E,_){"use strict";var C=document.createElement("style");C.textContent=`.fullcolumn[data-v-7574456b]{width:966px;height:144px;background:#f9f9f9;margin-bottom:10px;padding:10px;display:flex;justify-content:center;align-items:center}.fullcolumn__left[data-v-7574456b]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-around;align-items:flex-start;width:50%;height:100%;font-size:12px;color:#333}.fullcolumn__left--title[data-v-7574456b]{font-size:14px}.fullcolumn__left--statistical[data-v-7574456b]{width:100%;color:#a9a9a9;display:flex;justify-content:center;align-items:center;padding-right:40px;justify-content:space-between}.fullcolumn__center[data-v-7574456b]{width:20%;height:100%}.fullcolumn__center--title[data-v-7574456b]{font-size:14px}.fullcolumn__right[data-v-7574456b]{width:30%;height:100%;display:flex;justify-content:center;align-items:center}.nots[data-v-7574456b]{color:#2e99f3}.ongoing[data-v-7574456b]{color:#f57373}.hasEnded[data-v-7574456b],.off[data-v-7574456b],.suspended[data-v-7574456b]{color:#a9a9a9}.container[data-v-a93b1229]{overflow-y:scroll}
`,document.head.appendChild(C);const N=e.defineComponent({__name:"selectType",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Array,default:()=>[{label:"1",value:"2"}]}},emits:["update:modelValue","change"],setup(t,{emit:r}){const s=t,c=f.useVModel(s,"modelValue",r);return(n,d)=>{const o=e.resolveComponent("el-option"),p=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(p,{modelValue:e.unref(c),"onUpdate:modelValue":d[0]||(d[0]=u=>e.isRef(c)?c.value=u:null),placeholder:s.placeholder,style:{width:"150px"},onChange:d[1]||(d[1]=u=>r("change",u))},{default:e.withCtx(()=>[e.renderSlot(n.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.list,(u,l)=>(e.openBlock(),e.createBlock(o,{key:l,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),w=e.defineComponent({__name:"head-search",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},leftBtnText:{type:String,default:"leftBtnText"}},emits:["update:modelValue","leftBtnClick","search"],setup(t,{emit:r}){const s=t,c=[{value:"NOT_STARTED",label:"未开始"},{value:"PROCESSING",label:"进行中"},{value:"OVER",label:"已结束"},{value:"ILLEGAL_SELL_OFF",label:"违规下架"}],n=f.useVModel(s,"modelValue",r);return(d,o)=>{const p=e.resolveComponent("el-button"),u=e.resolveComponent("el-option"),l=e.resolveComponent("el-col"),i=e.resolveComponent("el-date-picker"),m=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(m,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px",width:"100%"}},{default:e.withCtx(()=>[e.createVNode(l,{span:14},{default:e.withCtx(()=>[e.createVNode(p,{round:"",type:"primary",onClick:o[0]||(o[0]=a=>r("leftBtnClick"))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(s.leftBtnText),1)]),_:1}),e.createVNode(N,{modelValue:e.unref(n).fullReductionStatus,"onUpdate:modelValue":o[1]||(o[1]=a=>e.unref(n).fullReductionStatus=a),style:{"margin-left":"15px"},list:c,onChange:o[2]||(o[2]=a=>r("search"))},{default:e.withCtx(()=>[e.createVNode(u,{label:"全部状态",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(l,{span:8},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:e.unref(n).date,"onUpdate:modelValue":o[3]||(o[3]=a=>e.unref(n).date=a),style:{width:"300px"},format:"YYYY/MM/DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:o[4]||(o[4]=a=>r("search",a))},null,8,["modelValue"])]),_:1})]),_:1})}}}),R=t=>g.get({url:"addon-full-reduction/fullReduction/",params:t}),k=t=>g.del({url:`addon-full-reduction/fullReduction/${t}`}),x={NOT_STARTED:{title:"未开始",class:"nots"},PROCESSING:{title:"进行中",class:"ongoing"},OVER:{title:"已结束",class:"hasEnded"},ILLEGAL_SELL_OFF:{title:"违规下架",class:"off"}},B={class:"fullcolumn"},T={class:"fullcolumn__left"},A={class:"fullcolumn__left--title"},L={class:"fullcolumn__left--statistical"},z={class:"fullcolumn__center"},M={class:"fullcolumn__right"},O=e.defineComponent({__name:"column",props:{item:{type:Object,required:!0}},emits:["del"],setup(t,{emit:r}){const{divTenThousand:s}=b(),c=V.useRouter(),n=o=>{c.push({name:"applyDiscountBaseinfo",query:{id:o}})},d=e.computed(()=>o=>Number(o)===0?"全部":o+"件");return(o,p)=>{const u=e.resolveComponent("el-button"),l=e.resolveComponent("el-button-group");return e.openBlock(),e.createElementBlock("div",B,[e.createElementVNode("div",T,[e.createElementVNode("h1",A,e.toDisplayString(t.item.fullReductionName),1),e.createElementVNode("time",null,"活动时间："+e.toDisplayString(t.item.fullReductionStartTime)+"至"+e.toDisplayString(t.item.fullReductionEndTime),1),e.createElementVNode("div",null,"活动商品："+e.toDisplayString(d.value(t.item.productNum)),1),e.createElementVNode("div",L,[e.createElementVNode("span",null,"参加人数："+e.toDisplayString(t.item.peopleNum||0),1),e.createElementVNode("span",null,"支付单数："+e.toDisplayString(t.item.payOrder||0),1),e.createElementVNode("span",null,"应收金额："+e.toDisplayString(t.item.amountReceivable&&e.unref(s)(t.item.amountReceivable)||0),1)])]),e.createElementVNode("div",z,[e.createElementVNode("h1",{class:e.normalizeClass(["fullcolumn__center--title",e.unref(x)[t.item.fullReductionStatus].class])},e.toDisplayString(e.unref(x)[t.item.fullReductionStatus].title),3)]),e.createElementVNode("div",M,[e.createVNode(l,null,{default:e.withCtx(()=>[e.createVNode(u,{round:"",onClick:p[0]||(p[0]=i=>n(t.item.id))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(t.item.fullReductionStatus!=="NOT_STARTED"?"查看":"编辑")+"活动 ",1)]),_:1}),e.createVNode(u,{round:"",onClick:p[1]||(p[1]=i=>r("del",t.item.id))},{default:e.withCtx(()=>[e.createTextVNode("删除活动")]),_:1})]),_:1})])])}}}),q="",S=(t,r)=>{const s=t.__vccOpts||t;for(const[c,n]of r)s[c]=n;return s},I=S(O,[["__scopeId","data-v-7574456b"]]),j=e.defineComponent({__name:"ShopApplyDiscount",setup(t){const r=V.useRouter(),s=e.ref([]),c=e.reactive({keyword:"",date:"",fullReductionStatus:""}),n=e.reactive({size:10,current:1,total:0});e.onBeforeMount(()=>{d()});async function d(){const{date:l,fullReductionStatus:i}=c,m={fullReductionStartTime:"",fullReductionEndTime:""};Array.isArray(l)&&l.length===2&&(m.fullReductionStartTime=l[0],m.fullReductionEndTime=l[1]);const a={...n,...m,shopId:E.useShopInfoStore().shopInfo.id,fullReductionStatus:i},{code:y,data:h}=await R(a);if(y!==200)return _.ElMessage.error("获取活动列表失败");s.value=h.records,n.current=h.current,n.size=h.size,n.total=h.total}const o=async l=>{try{if(!await _.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:m,data:a}=await k(l);if(m!==200){_.ElMessage.error("删除失败");return}_.ElMessage.success("删除成功"),s.value=s.value.filter(y=>y.id!==l),n.total--}catch(i){console.log("isValidate",i)}},p=l=>{n.size=l,d()},u=l=>{n.current=l,d()};return(l,i)=>{const m=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(w,{modelValue:c,"onUpdate:modelValue":i[0]||(i[0]=a=>c=a),"left-btn-text":"新增满减活动",onSearch:d,onLeftBtnClick:i[1]||(i[1]=a=>e.unref(r).push({name:"applyDiscountBaseinfo"}))},null,8,["modelValue"]),e.createElementVNode("div",{style:e.normalizeStyle({height:"calc(100vh - 220px)"}),class:"container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value,a=>(e.openBlock(),e.createBlock(I,{key:a.id,item:a,onDel:o},null,8,["item"]))),128))],4),e.createVNode(m,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:n,"onUpdate:modelValue":i[2]||(i[2]=a=>n=a),"load-init":!0,"page-size":n.size,total:n.total,onReload:d,onHandleSizeChange:p,onHandleCurrentChange:u},null,8,["modelValue","page-size","total"])]),_:1})])}}}),$="";return S(j,[["__scopeId","data-v-a93b1229"]])});
