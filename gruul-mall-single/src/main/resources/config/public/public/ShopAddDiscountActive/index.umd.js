(function(e,_){typeof exports=="object"&&typeof module<"u"?module.exports=_(require("vue"),require("vue-router"),require("@/apis/http"),require("@/apis/decoration"),require("@/store/modules/shopInfo"),require("@/utils/date"),require("decimal.js"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("element-plus"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/apis/http","@/apis/decoration","@/store/modules/shopInfo","@/utils/date","decimal.js","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","element-plus","@/composables/useConvert"],_):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopAddDiscountActive=_(e.ShopAddDiscountActiveContext.Vue,e.ShopAddDiscountActiveContext.VueRouter,e.ShopAddDiscountActiveContext.Request,e.ShopAddDiscountActiveContext.DecorationAPI,e.ShopAddDiscountActiveContext.ShopInfoStore,e.ShopAddDiscountActiveContext.DateUtil,e.ShopAddDiscountActiveContext.Decimal,e.ShopAddDiscountActiveContext.QChooseGoodsPopup,e.ShopAddDiscountActiveContext.ElementPlus,e.ShopAddDiscountActiveContext.UseConvert))})(this,function(e,_,D,F,O,q,E,B,m,Y){"use strict";var I=document.createElement("style");I.textContent=`@charset "UTF-8";.add[data-v-5eef2e5d]{margin:-20px -15px;height:calc(100vh - 85px);overflow-y:auto;scrollbar-width:none;-ms-overflow-style:none}.add[data-v-5eef2e5d]::-webkit-scrollbar{display:none}.title[data-v-5eef2e5d]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-5eef2e5d]{font-size:12px;margin-left:12px;color:#c4c4c4}.rules[data-v-5eef2e5d]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-5eef2e5d]{width:300px;display:flex}.text[data-v-5eef2e5d]{font-size:14px;color:#333}.goodsData[data-v-5eef2e5d]{border:1px solid #ccc}.goods-list[data-v-5eef2e5d]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-5eef2e5d]{display:flex}.goods-list__goods-list__info-name[data-v-5eef2e5d]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-5eef2e5d]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-5eef2e5d]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-5eef2e5d]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-5eef2e5d]{font-size:16px}.ruleform-date[data-v-5eef2e5d]{width:100%;display:flex;align-items:center}.flex[data-v-5eef2e5d]{margin-top:10px;height:50px}.flex-item[data-v-5eef2e5d]{width:40%}.coupon-rules[data-v-5eef2e5d]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-5eef2e5d]{position:fixed;left:50%;bottom:30px}.commodityForm[data-v-5eef2e5d]{box-sizing:border-box;padding-bottom:62px}.commodityForm__tool[data-v-5eef2e5d]{width:1010px;align-items:center;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;margin-left:-40px;z-index:100}
`,document.head.appendChild(I);const z=[{label:"满X元减",value:"FULL_REDUCTION"},{label:"满X元折",value:"FULL_DISCOUNT"}],G=c=>D.post({url:"addon-full-reduction/fullReduction/",data:c}),$=c=>D.get({url:`addon-full-reduction/fullReduction/${c.shopId}/${c.fullReductionId}`}),p=c=>(e.pushScopeId("data-v-5eef2e5d"),c=c(),e.popScopeId(),c),H={style:{padding:"40px","padding-bottom":"52px"},class:"add"},j=p(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),X=p(()=>e.createElementVNode("span",{class:"msg"},"活动名称不超过5个字",-1)),Q={class:"ruleform-date"},W=p(()=>e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1)),J=p(()=>e.createElementVNode("span",{class:"msg"},"(限定10条)",-1)),K={key:0,class:"flex",style:{width:"100%"}},Z=p(()=>e.createElementVNode("span",null,"满",-1)),v=p(()=>e.createElementVNode("span",null,"元,打",-1)),ee=p(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"折",-1)),te={key:1,class:"flex",style:{width:"100%"}},oe=p(()=>e.createElementVNode("span",null,"满",-1)),ne=p(()=>e.createElementVNode("span",null,"元,减",-1)),le=p(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"元",-1)),de={class:"goods-list__info"},ie={class:"goods-list__goods-list__info-name"},ae={class:"goods-list__goods-list__info-name--name"},se={class:"goods-list__goods-list__info-name--price"},re={key:0,class:"text"},ce={class:"commodityForm__tool"},ue=e.defineComponent({__name:"ShopAddDiscountActive",setup(c){const R=_.useRouter(),h=_.useRoute(),C=new q,V=O.useShopInfoStore(),S=V.shopInfo.id,y=e.reactive({form:{fullReductionId:"",fullReductionName:"",fullReductionStatus:"ILLEGAL_SELL_OFF",fullReductionStartTime:"",fullReductionEndTime:"",fullReductionRules:[{fullReductionRule:"",conditionAmount:1,discountAmount:1,discountRatio:.1}],shopId:S,shopName:V.shopInfo.name,productType:"ALL_PRODUCT",productIds:[],isUpdate:!1,productNum:0},isEditDisable:!1,goodsTotal:0,fullReductionTime:[],rules:{fullReductionName:[{required:!0,message:"请输入活动名称",trigger:"blur"}],type:[{required:!0,message:"请选择优惠券类型",trigger:["blur","change"]}],productType:[{required:!0,message:"请输入满减规则",trigger:["blur","change"]}],fullReductionStartTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]}],fullReductionEndTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]}]},chooseGoodsPopup:!1,chooseGoodsList:[]}),{form:l,isEditDisable:a,rules:pe,chooseGoodsPopup:N,chooseGoodsList:r,goodsTotal:fe}=e.toRefs(y),x=e.ref(),{mulTenThousand:me,divTenThousand:U}=Y();_e();async function _e(){if(!h.query.id)return;l.value.isUpdate=!0,l.value.fullReductionId=h.query.id.toString();const{code:o,data:t,msg:i}=await $({shopId:S,fullReductionId:h.query.id.toString()});if(o!==200){m.ElMessage.error(i||"获取活动信息失败");return}xe(t.fullReductionStatus),t.fullReductionRules=L(t.fullReductionRules,Ne);for(const d in t){const u=t[d];y.form[d]=u}he(t)}async function he(o){if(o.productIds&&o.productIds.length){const{code:t,data:i}=await F.doGetRetrieveCommodity({productId:o.productIds});if(t!==200){m.ElMessage.error("获取商品信息失败");return}r.value=i.list.map(d=>({...d,isCheck:!0}))}}function xe(o){o&&o!=="NOT_STARTED"&&(a.value=!0)}const ge=async()=>{if(a.value){k();return}if(!x.value||!await x.value.validate())return;if(!Re(l.value.fullReductionRules)){m.ElMessage.error("满减规则输入有误");return}if(["SPECIFIED_PRODUCT_PARTICIPATE","SPECIFIED_PRODUCT_NOT_PARTICIPATE"].includes(l.value.productType)&&!r.value.length){m.ElMessage.error("请选择商品");return}l.value.productIds=r.value.map(f=>f.productId),l.value.productNum=Ce[l.value.productType]();const t=L(l.value.fullReductionRules,Ve),{code:i,data:d,msg:u}=await G({...y.form,fullReductionRules:t});if(i!==200){const f=["fullReductionEndTime:需要是一个将来的时间","fullReductionStartTime:需要是一个将来的时间"].includes(u)?"请选择一个将来的时间":u;m.ElMessage.error(f||"活动创建失败");return}m.ElMessage.success("活动创建成功"),Te(),k()};function k(){R.push({name:"applyDiscountIndex"})}function Re(o){return o.every(t=>["FULL_DISCOUNT","FULL_REDUCTION"].includes(t.fullReductionRule)?t.fullReductionRule==="FULL_DISCOUNT"?t.conditionAmount&&t.discountRatio&&t.discountRatio>0&&t.discountRatio<=9.9:t.conditionAmount&&t.discountAmount&&t.conditionAmount>=t.discountAmount:!1)}function L(o,t){return o.map(i=>{const{fullReductionRule:d,conditionAmount:u,discountAmount:f,discountRatio:T}=i;return i.fullReductionRule==="FULL_DISCOUNT"?{fullReductionRule:d,conditionAmount:t(u),discountRatio:T}:i.fullReductionRule==="FULL_REDUCTION"?{fullReductionRule:d,conditionAmount:t(u),discountAmount:t(f)}:i})}const Ce={ALL_PRODUCT:()=>0,SPECIFIED_PRODUCT_PARTICIPATE:()=>{var o;return((o=l.value.productIds)==null?void 0:o.length)||0},SPECIFIED_PRODUCT_NOT_PARTICIPATE:()=>{var o;return fe.value-(((o=l.value.productIds)==null?void 0:o.length)||0)}};function Ve(o){return o?me(o).toNumber():0}function Ne(o){return o?U(o).toNumber():0}const be=o=>{r.value=o.tempGoods},ye=async o=>{await m.ElMessageBox.confirm("确定移除该商品?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&(r.value=r.value.filter(i=>i.productId!==o))};function Te(){x.value&&(x.value.resetFields(),r.value=[])}const Ae=o=>{l.value.fullReductionRules.splice(o,1)},we=()=>{l.value.fullReductionRules.push({fullReductionRule:"",conditionAmount:0,discountAmount:0,discountRatio:0})},P=o=>{const t=C.getYMDs(o),i=C.getYMDs(new Date);return new E(new Date(t).getTime()).lessThan(new Date(i).getTime())||new E(new Date(o).getTime()).greaterThanOrEqualTo(new Date(De(6)).getTime())};function De(o){let t=new Date;return t.setMonth(t.getMonth()+Number(o)),t.toLocaleString().replace(/\//g,"-")}function Ee(o){const t=o.map(u=>U(u).toNumber()),i=Math.min(...t),d=Math.max(...t);return t.length>1?`${i}~${d}`:t[0]}return(o,t)=>{const i=e.resolveComponent("el-input"),d=e.resolveComponent("el-form-item"),u=e.resolveComponent("el-date-picker"),f=e.resolveComponent("el-link"),T=e.resolveComponent("el-option"),Ie=e.resolveComponent("el-select"),g=e.resolveComponent("el-table-column"),b=e.resolveComponent("el-input-number"),M=e.resolveComponent("el-table"),A=e.resolveComponent("el-radio"),Se=e.resolveComponent("el-radio-group"),Ue=e.resolveComponent("el-image"),w=e.resolveComponent("el-button"),ke=e.resolveComponent("el-row"),Le=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",H,[j,e.createVNode(Le,{ref_key:"ruleFormRef",ref:x,model:e.unref(l),rules:e.unref(pe),"label-width":"auto","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(d,{label:"活动名称",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:e.unref(l).fullReductionName,"onUpdate:modelValue":t[0]||(t[0]=n=>e.unref(l).fullReductionName=n),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"5",placeholder:"请输入活动名称",disabled:e.unref(a)},null,8,["modelValue","disabled"]),X]),_:1}),e.createVNode(d,{label:"活动时间",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",Q,[e.createVNode(d,{prop:"fullReductionStartTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:e.unref(l).fullReductionStartTime,"onUpdate:modelValue":t[1]||(t[1]=n=>e.unref(l).fullReductionStartTime=n),type:"datetime",disabled:e.unref(a),placeholder:"请选择开始时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":P},null,8,["modelValue","disabled"]),W]),_:1}),e.createVNode(d,{prop:"fullReductionEndTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:e.unref(l).fullReductionEndTime,"onUpdate:modelValue":t[2]||(t[2]=n=>e.unref(l).fullReductionEndTime=n),disabled:e.unref(a),type:"datetime",placeholder:"请选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":P},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(d,{"label-width":"80px",style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(f,{underline:!1,type:"primary",disabled:e.unref(a)||e.unref(l).fullReductionRules.length>9,onClick:we},{default:e.withCtx(()=>[e.createTextVNode("添加规则 ")]),_:1},8,["disabled"]),J]),_:1}),e.createVNode(d,{label:"活动规则"},{default:e.withCtx(()=>[e.createVNode(M,{data:e.unref(l).fullReductionRules,style:{width:"80%"},border:"","header-cell-style":{textAlign:"center",fontSize:"14px",color:"#606266"},"cell-style":{height:"60px"}},{default:e.withCtx(()=>[e.createVNode(g,{label:"满减条件",width:"170"},{default:e.withCtx(({row:n})=>[e.createVNode(d,{prop:"fullReductionRule"},{default:e.withCtx(()=>[e.createVNode(Ie,{modelValue:n.fullReductionRule,"onUpdate:modelValue":s=>n.fullReductionRule=s,disabled:e.unref(a),placeholder:"全部类型"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(z),s=>(e.openBlock(),e.createBlock(T,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1024)]),_:1}),e.createVNode(g,{label:"满减规则",width:"300"},{default:e.withCtx(({row:n})=>[n.fullReductionRule==="FULL_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",K,[e.createVNode(d,{prop:"conditionAmount","label-width":"0%"},{default:e.withCtx(()=>[Z,e.createVNode(b,{modelValue:n.conditionAmount,"onUpdate:modelValue":s=>n.conditionAmount=s,modelModifiers:{number:!0},style:{width:"60%",margin:"0 5px"},disabled:e.unref(a),controls:!1,max:99999,min:1},null,8,["modelValue","onUpdate:modelValue","disabled"]),v]),_:2},1024),e.createVNode(d,{prop:"discountRatio","label-width":"0%"},{default:e.withCtx(()=>[e.createVNode(b,{modelValue:n.discountRatio,"onUpdate:modelValue":s=>n.discountRatio=s,modelModifiers:{number:!0},disabled:e.unref(a),style:{width:"80%"},controls:!1,max:9.9,precision:1,min:.1},null,8,["modelValue","onUpdate:modelValue","disabled"]),ee]),_:2},1024)])):e.createCommentVNode("",!0),n.fullReductionRule==="FULL_REDUCTION"?(e.openBlock(),e.createElementBlock("div",te,[e.createVNode(d,{prop:"requiredAmount","label-width":0},{default:e.withCtx(()=>[oe,e.createVNode(b,{modelValue:n.conditionAmount,"onUpdate:modelValue":s=>n.conditionAmount=s,modelModifiers:{number:!0},disabled:e.unref(a),controls:!1,style:{width:"90px",margin:"0 5px"},max:999999,min:1},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1024),e.createVNode(d,{prop:"discountAmount","label-width":0},{default:e.withCtx(()=>[ne,e.createVNode(b,{modelValue:n.discountAmount,"onUpdate:modelValue":s=>n.discountAmount=s,modelModifiers:{number:!0},disabled:e.unref(a),style:{width:"90px",margin:"0 5px"},controls:!1,max:n.conditionAmount,min:1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]),le]),_:2},1024)])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(g,{label:"操作",align:"center"},{default:e.withCtx(({$index:n})=>[e.createVNode(f,{underline:!1,type:"danger",disabled:e.unref(a),onClick:s=>Ae(n)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e.createVNode(d,{label:"商品选择",prop:"productType"},{default:e.withCtx(()=>[e.createVNode(Se,{modelValue:e.unref(l).productType,"onUpdate:modelValue":t[3]||(t[3]=n=>e.unref(l).productType=n),disabled:e.unref(a)},{default:e.withCtx(()=>[e.createVNode(A,{label:"ALL_PRODUCT"},{default:e.withCtx(()=>[e.createTextVNode("全部商品参与")]),_:1}),e.createVNode(A,{label:"SPECIFIED_PRODUCT_PARTICIPATE"},{default:e.withCtx(()=>[e.createTextVNode("指定商品参与")]),_:1}),e.createVNode(A,{label:"SPECIFIED_PRODUCT_NOT_PARTICIPATE"},{default:e.withCtx(()=>[e.createTextVNode("指定商品不参与")]),_:1})]),_:1},8,["modelValue","disabled"]),e.unref(l).productType!=="ALL_PRODUCT"&&e.unref(r).length?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass(["goods-list",e.unref(r).length&&"goodsData"])},[e.createVNode(M,{style:{width:"100%"},data:e.unref(r),height:"260px","header-cell-style":{fontSize:"14px",color:"#606266",background:"#f2f2f2",height:"54px",fontWeight:"normal"}},{default:e.withCtx(()=>[e.createVNode(g,{label:"商品信息"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",de,[e.createVNode(Ue,{style:{width:"60px",height:"60px"},"preview-teleported":!0,src:n.pic,fit:"","preview-src-list":[n.pic]},null,8,["src","preview-src-list"]),e.createElementVNode("div",ie,[e.createElementVNode("div",ae,e.toDisplayString(n.productName),1),e.createElementVNode("div",se,e.toDisplayString(Ee(n.salePrices)),1)])])]),_:1}),e.createVNode(g,{label:"操作",width:"80px"},{default:e.withCtx(({row:n})=>[e.createVNode(f,{underline:!1,type:"primary",disabled:e.unref(a),onClick:s=>ye(n.productId)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])],2)):e.createCommentVNode("",!0),e.unref(l).productType!=="ALL_PRODUCT"?(e.openBlock(),e.createBlock(ke,{key:1,justify:"space-between",style:{width:"90%","margin-top":"10px"}},{default:e.withCtx(()=>[e.createVNode(w,{type:"primary",round:"",plain:"",disabled:e.unref(a),onClick:t[4]||(t[4]=n=>N.value=!0)},{default:e.withCtx(()=>[e.createTextVNode(" 选择商品 ")]),_:1},8,["disabled"]),e.unref(r).length?(e.openBlock(),e.createElementBlock("span",re,"已选择"+e.toDisplayString(e.unref(r).length)+"款商品",1)):e.createCommentVNode("",!0)]),_:1})):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["model","rules"]),e.createElementVNode("div",ce,[e.createVNode(w,{round:"",plain:"",onClick:t[5]||(t[5]=n=>e.unref(R).back())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.createVNode(w,{type:"primary",round:"",onClick:ge},{default:e.withCtx(()=>[e.createTextVNode("保存")]),_:1})]),e.createVNode(B,{modelValue:e.unref(N),"onUpdate:modelValue":t[6]||(t[6]=n=>e.isRef(N)?N.value=n:null),"point-goods-list":e.unref(r),onOnConfirm:be},null,8,["modelValue","point-goods-list"])])}}}),Pe="";return((c,R)=>{const h=c.__vccOpts||c;for(const[C,V]of R)h[C]=V;return h})(ue,[["__scopeId","data-v-5eef2e5d"]])});
