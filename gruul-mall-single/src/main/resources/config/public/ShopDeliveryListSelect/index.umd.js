(function(e,l){typeof exports=="object"&&typeof module<"u"?module.exports=l(require("vue"),require("@/components/q-address"),require("@/apis/http"),require("element-china-area-data"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@/components/q-address","@/apis/http","element-china-area-data","element-plus"],l):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopDeliveryListSelect=l(e.ShopDeliveryListSelectContext.Vue,e.ShopDeliveryListSelectContext.QAddressIndex,e.ShopDeliveryListSelectContext.Request,e.ShopDeliveryListSelectContext.ElementChinaAreaData,e.ShopDeliveryListSelectContext.ElementPlus))})(this,function(e,l,c,g,m){"use strict";const u=n=>c.get({url:"gruul-mall-shop/shop/logistics/address/list",params:n}),h=n=>c.get({url:"gruul-mall-freight/logistics/express/usable/list",params:n});return e.defineComponent({__name:"ShopDeliveryListSelect",props:{properties:{type:Object,default:{}}},setup(n){const r=n,t=e.reactive(r.properties.deliverDialogFormData||{deliverType:"WITHOUT",printId:"",receiver:{name:"",mobile:"",address:""},expressCompany:{logisticsCompanyCode:"",logisticsCompanyName:"",expressNo:""},addressaddress:""}),C=e.ref(r.properties.expressCompanyMap||new Map),y=e.ref([]),d=e.ref([]);x(),D();async function D(){const{data:a,code:s}=await u({});s!==200?m.ElMessage({message:"请刷新重试...",type:"warning"}):d.value=a.records;const i=d.value.find(p=>p.defSend==="YES");i&&(t.addressaddress=i.id,r.properties.loadDeliverDialogFormData(t),r.properties.loadDeliveryAddressData(d.value))}async function x(){const{code:a,data:s}=await h({size:1e3,current:1});if(a!==200){m.ElMessage.error("获取物流公司失败");return}s.records.length&&(t.expressCompany.logisticsCompanyCode=s.records[0].logisticsCompanyCode,r.properties.loadDeliverDialogFormData(t)),y.value=s.records,r.properties.loadCompanySelectListData(s.records)}const S=a=>{if(t.deliverType==="EXPRESS"){for(const s of C.value)s[1].logisticsCompanyCode=a;r.properties.loadexpressCompanyMapData(C.value)}r.properties.loadDeliverDialogFormData(t)},_=()=>{r.properties.loadDeliverDialogFormData(t)};return(a,s)=>{const i=e.resolveComponent("el-option"),p=e.resolveComponent("el-select"),f=e.resolveComponent("el-form-item");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.withDirectives(e.createVNode(f,{label:"物流服务",prop:"logisticsCompanyCode","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:t.expressCompany.logisticsCompanyCode,"onUpdate:modelValue":s[0]||(s[0]=o=>t.expressCompany.logisticsCompanyCode=o),placeholder:"请选择物流服务",style:{width:"444px"},onChange:S},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value,o=>(e.openBlock(),e.createBlock(i,{key:o.logisticsCompanyName,label:o.logisticsCompanyName,value:o.logisticsCompanyCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},512),[[e.vShow,t.deliverType!=="WITHOUT"]]),t.deliverType==="PRINT_EXPRESS"?(e.openBlock(),e.createBlock(f,{key:0,label:"发货地址",prop:"addressaddress","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:t.addressaddress,"onUpdate:modelValue":s[1]||(s[1]=o=>t.addressaddress=o),placeholder:"请选择发货地址",style:{width:"444px"},onChange:_},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.value,o=>(e.openBlock(),e.createBlock(i,{key:o.id,value:o.id,label:`${e.unref(l.AddressFn)(e.unref(g.regionData),[o.provinceCode,o.cityCode,o.regionCode])}${o.address}`},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0)],64)}}})});
