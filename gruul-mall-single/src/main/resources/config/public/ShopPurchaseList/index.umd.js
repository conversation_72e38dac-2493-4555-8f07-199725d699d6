(function(e,X){typeof exports=="object"&&typeof module<"u"?module.exports=X(require("vue"),require("vue-router"),require("@/components/MCard.vue"),require("lodash"),require("@/components/pageManage/PageManage.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("decimal.js"),require("element-plus"),require("@/store/modules/shopInfo"),require("@/components/q-upload/q-upload.vue"),require("@/composables/useConvert"),require("@vueuse/core"),require("@/utils/date"),require("vue-clipboard3"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/MCard.vue","lodash","@/components/pageManage/PageManage.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","decimal.js","element-plus","@/store/modules/shopInfo","@/components/q-upload/q-upload.vue","@/composables/useConvert","@vueuse/core","@/utils/date","vue-clipboard3","@/apis/http"],X):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopPurchaseList=X(e.ShopPurchaseListContext.Vue,e.ShopPurchaseListContext.VueRouter,e.ShopPurchaseListContext.MCard,e.ShopPurchaseListContext.Lodash,e.ShopPurchaseListContext.PageManage,e.ShopPurchaseListContext.QTable,e.ShopPurchaseListContext.QTableColumn,e.ShopPurchaseListContext.Decimal,e.ShopPurchaseListContext.ElementPlus,e.ShopPurchaseListContext.ShopInfoStore,e.ShopPurchaseListContext.QUpload,e.ShopPurchaseListContext.UseConvert,e.ShopPurchaseListContext.VueUse,e.ShopPurchaseListContext.DateUtil,e.ShopPurchaseListContext.VueClipboard3,e.ShopPurchaseListContext.Request))})(this,function(e,X,ce,v,ie,_e,j,H,O,de,Be,ee,$e,Pe,Le,M){"use strict";var ge=document.createElement("style");ge.textContent=`@charset "UTF-8";.commodity-info[data-v-a3a2d900]{display:flex;align-items:center}.commodity-info img[data-v-a3a2d900]{width:60px;height:60px}.commodity-info span[data-v-a3a2d900]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.purchase__batch[data-v-a3a2d900]{display:flex;justify-content:flex-end}.purchase__remark[data-v-a3a2d900]{padding:15px 0}.purchase__total[data-v-a3a2d900]{display:flex;flex-direction:column;align-items:flex-end;line-height:1.5}.purchase__total--title[data-v-a3a2d900]{font-size:1.2em;font-weight:600}.batch__commodity[data-v-1d9e47e3]{display:flex;align-items:center}.batch__commodity img[data-v-1d9e47e3]{width:60px;height:60px;flex-shrink:0}.batch__commodity span[data-v-1d9e47e3]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.batch__expand[data-v-1d9e47e3]{margin-left:20px;margin-right:20px;padding:10px;border:1px solid #efefef}.batch__form[data-v-1d9e47e3]{display:flex;justify-content:flex-end}.batch__pagination[data-v-1d9e47e3]{padding:15px 0;display:flex;justify-content:flex-end;align-items:center}.batch__total[data-v-1d9e47e3]{display:flex;flex-direction:column;align-items:flex-end;line-height:1.5}.batch__total--title[data-v-1d9e47e3]{font-size:1.2em;font-weight:600}[data-v-1d9e47e3] .el-table thead .el-table__cell{background-color:var(--el-fill-color-light)}.tools[data-v-48d75a25]{padding:15px 0}.commodity-info[data-v-48d75a25]{display:flex;align-items:center}.commodity-info img[data-v-48d75a25]{width:60px;height:60px}.commodity-info span[data-v-48d75a25]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.m__table{width:100%;table-layout:fixed;border-collapse:separate;font-size:12px}.m__table .hide{display:none}.m__table--container{background:#eef1f6;padding:11px 8px}.m__table--container.single{background:none}.m__table--container.single .m__table--head th:first-child{border-radius:10px 0 0}.m__table--container.single .m__table--head th:last-child{border-radius:0 10px 0 0}.m__table--container.single .m__table--head:after{display:none}.m__table--shrink{flex:1;height:100%}.m__table--center{display:flex;align-items:center}.m__table .close{background:#f5f5f5!important}.m__table .hover--class:hover .body--header{border-color:#bebebe!important;position:relative}.m__table .hover--class:hover .body--content td,.m__table .hover--class:hover .body--content td:first-child{border-color:#bebebe!important}.m__table .hover--class:hover .body--content td:last-child{border-color:#bebebe!important}.m__table .ordinary--class:hover .body--header,.m__table .ordinary--class:hover .body--content td{border-color:#bcdfff}.m__table .ordinary--class:hover .body--content td:last-child{border-color:#bcdfff}.m__table .need--border .body--content td{border-right:1px solid #bcdfff}.m__table .need--border .body--content td:last-child{border-color:#bcdfff}.m__table--empty .empty__td{width:960px;height:80px;background-color:#fff;margin-left:-15px;font-size:14px;color:#b3b3b3}.m__table--head th{background:#fff;padding:12px 10px;text-align:center;color:#515a6e;border-top:1px solid #d8eaf9;border-bottom:1px solid #d8eaf9;vertical-align:middle;font-size:14px;font-weight:400;color:#586884}.m__table--head th:first-child{border-left:1px solid #d8eaf9}.m__table--head th:last-child{border-right:1px solid #d8eaf9}.m__table--head:after{content:"-";display:block;line-height:14px;color:transparent}.m__table--head.padding:after{content:"-";display:block;color:transparent}.m__table--body .body--header{display:flex;justify-content:flex-start;align-items:center;padding:0 10px;border:1px solid #d8eaf9;font-size:13px;border-radius:10px 10px 0 0;height:55px;vertical-align:middle;background:#fff}.m__table--body.default .body--content .m__table--item:first-child{border-radius:0}.m__table--body.default .body--content .m__table--item:last-child{border-radius:0}.m__table--body.default .m__table--item{border-top:1px solid #d8eaf9;vertical-align:middle}.m__table--body .body--content td{padding:8px 0;border-top:0px;border-bottom:1px solid #d8eaf9;border-right:0px;font-size:12px;color:#50596d;background:#fff;vertical-align:middle}.m__table--body .body--content td .item__content{display:flex;justify-content:center;align-items:center}.m__table--body .body--content td .selection__checkbox{display:inline-block;width:100%;height:100%}.m__table--body .body--content td .selection__checkbox.selection{display:flex;justify-content:flex-start;align-items:center}.m__table--body .body--content td:nth-child(1){padding-left:10px}.m__table--body .body--content td:first-child{border-left:1px solid #d8eaf9}.m__table--body .body--content td:last-child{border-right:1px solid #d8eaf9}.m__table--body .body--content.is--multiple td:first-child{border-right:1px solid #d8eaf9!important}.m__table--body:after{content:"-";display:block;line-height:14px;color:transparent;width:100%}.m__table .el-checkbox{margin-right:10px!important;float:left}.count-down[data-v-0823a7bd]{color:red}.invoice-dialog[data-v-023b6aef]{color:#000}.invoice-dialog-main[data-v-023b6aef]{display:flex;justify-content:center;align-items:center;justify-content:space-between}.invoice-dialog-main__content[data-v-023b6aef]{margin:10px 0;display:flex;justify-content:center;align-items:center;justify-content:space-between}.invoice-dialog-main__note[data-v-023b6aef]{display:flex;justify-content:center;align-items:center;align-items:flex-start;justify-content:flex-start}.invoice-dialog-main__type[data-v-023b6aef]{margin-top:10px;display:flex;justify-content:center;align-items:center;justify-content:space-between}.invoice-dialog-main--price[data-v-023b6aef]{color:#fd0505;font-size:14px;font-weight:700}.invoice-dialog-main--price[data-v-023b6aef]:before{content:"￥";font-size:10px;font-weight:400}.invoice-dialog-main--msg[data-v-023b6aef]{color:#9a9a9a;margin:10px 0;font-size:12px}.batch__commodity[data-v-047df9dd]{display:flex;align-items:center}.batch__commodity img[data-v-047df9dd]{width:60px;height:60px;flex-shrink:0}.batch__commodity span[data-v-047df9dd]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.batch__form[data-v-047df9dd]{display:flex;justify-content:flex-end}.batch__commodity[data-v-da16f025]{display:flex;align-items:center}.batch__commodity img[data-v-da16f025]{width:60px;height:60px;flex-shrink:0}.batch__commodity span[data-v-da16f025]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.batch__form[data-v-da16f025]{display:flex;justify-content:flex-end}.order-table[data-v-09d5a239]{overflow-x:auto;height:calc(100vh - 520px);transition:height .5s;word-break:break-all}.order-table__commodity[data-v-09d5a239]{width:280px;display:flex;justify-content:space-between}.order-table__commodity--name[data-v-09d5a239]{flex:1;overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin:0 10px}.order-table__commodity--info[data-v-09d5a239]{flex-shrink:0;display:flex;flex-direction:column;align-items:flex-end}.order-table__supplier[data-v-09d5a239]{display:flex;justify-content:flex-start;align-items:center;flex-direction:column;line-height:1.5}.order-table__actions[data-v-09d5a239]{display:flex;flex-wrap:wrap}.order-table__actions .el-link+.el-link[data-v-09d5a239]{margin-left:8px}.order-table__header[data-v-09d5a239]{font-size:11px;display:flex;justify-content:space-around;align-items:center;width:100%}.invoice-title[data-v-09d5a239]{display:flex;justify-content:center;align-items:center}.invoice-desc[data-v-09d5a239]{display:flex;justify-content:center;align-items:center;color:#fd0505;margin-top:7px}.proof-img[data-v-09d5a239]{width:350px;height:350px;object-fit:contain}.copy[data-v-09d5a239]{color:#1890ff;margin-left:8px;cursor:pointer}.text-red[data-v-09d5a239]{color:red}.good[data-v-1ce42707]{padding:0 30px 0 45px;box-sizing:border-box;font-size:12px;font-family:Microsoft YaHei,Microsoft YaHei-Normal;font-weight:400;color:#000}.good__info[data-v-1ce42707]{margin-bottom:24px}.good__img[data-v-1ce42707]{display:flex;justify-content:flex-start;align-items:flex-start}.good__spec[data-v-1ce42707]{width:758px}.b[data-v-1ce42707]{font-size:12px;color:#000}.commodity-info[data-v-c6c58e29]{display:flex;align-items:center}.commodity-info img[data-v-c6c58e29]{width:60px;height:60px}.commodity-info span[data-v-c6c58e29]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.good[data-v-1d2d7e08]{padding:0 30px 0 45px;box-sizing:border-box;font-size:12px;font-family:Microsoft YaHei,Microsoft YaHei-Normal;font-weight:400;color:#000}.good__info[data-v-1d2d7e08]{margin-bottom:24px}.good__img[data-v-1d2d7e08]{display:flex;justify-content:flex-start;align-items:flex-start}.good__spec[data-v-1d2d7e08]{width:758px}.b[data-v-1d2d7e08]{font-size:12px;color:#000}img[data-v-eb8affdf]{vertical-align:top}.commodity-info[data-v-f0aff17e]{display:flex;align-items:center;justify-content:flex-start;width:100%}.commodity-info img[data-v-f0aff17e]{width:60px;height:60px}.commodity-info span[data-v-f0aff17e]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}
`,document.head.appendChild(ge);const Oe=e.defineComponent({__name:"ShopPurchaseList",setup(t){const o=e.ref("supply"),a=[{name:"supply",label:"货源"},{name:"purchaseOrder",label:"采购订单"},{name:"waitingPublish",label:"待发布"},{name:"release",label:"已发布"}],n={supply:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Gt)),purchaseOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ja)),waitingPublish:e.defineAsyncComponent(()=>Promise.resolve().then(()=>sn)),release:e.defineAsyncComponent(()=>Promise.resolve().then(()=>On))};return(i,_)=>{const c=e.resolveComponent("el-tab-pane"),d=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(d,{modelValue:o.value,"onUpdate:modelValue":_[0]||(_[0]=s=>o.value=s),style:{"margin-top":"15px"}},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(a,(s,l)=>e.createVNode(c,{key:l,label:s.label,name:s.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(n[o.value])))])}}}),Re=t=>M.get({url:"addon-supplier/supplier/manager/product/getSupplyList",params:{...t}}),ue=t=>M.post({url:"gruul-mall-order/order/distribution/cost/",data:t}),ye=(t={})=>M.post({url:"addon-supplier/supplier/order",data:t}),Me=t=>M.get({url:`addon-supplier/supplier/order/creation/${t}`}),Ae=(t={})=>M.put({url:"addon-supplier/supplier/order/pay",data:t}),Ue=t=>M.put({url:`addon-supplier/supplier/order/close/${t}`}),xe=t=>M.get({url:`addon-supplier/supplier/order/storage/${t}`}),Fe=t=>M.put({url:"addon-supplier/supplier/order/storage",data:t}),ze=t=>M.put({url:`addon-supplier/supplier/order/complete/${t}`}),He=t=>M.get({url:"addon-supplier/supplier/order/publish",params:t}),Ne=(t,o)=>M.post({url:`gruul-mall-carrier-pigeon/pigeon/group-chat-rooms/${t}/${o}`}),je=t=>M.get({url:"gruul-mall-goods/manager/product/purchase/issue/products",params:t}),Ge=t=>M.put({url:`gruul-mall-goods/manager/product/purchase/issue/product/updateStatus/${t}`}),pe=(t={})=>M.get({url:"gruul-mall-shop/shop/info/getSupplierInfo",params:t}),qe=(t={})=>M.get({url:"addon-supplier/supplier/order",params:t}),Ve=(t,o)=>M.get({url:`gruul-mall-storage/storage/shop/${t}/product/${o}`}),fe=()=>M.get({url:"gruul-mall-addon-platform/platform/shop/signing/category/choosable/list"}),be=()=>M.get({url:"gruul-mall-shop/shop/logistics/address/list",params:{current:1,size:1e3}}),Ye=()=>M.get({url:"addon-invoice/invoice/invoice-headers/pageInvoiceHeader",params:{size:500,ownerType:"SHOP"}}),We="addon-invoice/invoice/",Ke=t=>M.post({url:`${We}invoiceRequest/`,data:t}),Ce=t=>M.get({url:"addon-invoice/invoice/invoiceSettings/",params:t}),Qe=t=>M.get({url:`addon-invoice/invoice/invoiceRequest/${t}`}),Xe=t=>M.get({url:"addon-invoice/invoice/invoiceRequest/pre-request",params:t}),Je=t=>M.put({url:`addon-invoice/invoice/invoiceRequest/${t}`}),Ze=t=>M.post({url:"addon-invoice/invoice/invoiceAttachment/re-send",data:t}),Se=()=>M.get({url:"addon-invoice/invoice/invoice-headers/getDefaultInvoiceHeader",params:{invoiceHeaderOwnerType:"SHOP"}}),ve={style:{background:"#f9f9f9"}},et=e.defineComponent({__name:"search",emits:["changeShow","search"],setup(t,{emit:o}){const a=e.ref(!1),n=e.ref([]),i=e.ref([]);e.watch(()=>a.value,l=>o("changeShow",l));const _=async()=>{const{data:l,code:k}=await fe();i.value=l};e.onMounted(()=>_());const c=e.reactive({supplierId:"",platformCategoryParentId:"",supplierGoodsName:""}),d=()=>{const l=v.cloneDeep(c);l.platformCategoryParentId=Array.isArray(l.platformCategoryParentId)?l.platformCategoryParentId.pop():"",o("search",l)},s=async(l="")=>{var m;const k=await pe({supplierName:l});k.data&&((m=k.data)!=null&&m.length)&&(n.value=k.data)};return(l,k)=>{const m=e.resolveComponent("el-option"),p=e.resolveComponent("el-select"),r=e.resolveComponent("el-form-item"),x=e.resolveComponent("el-col"),y=e.resolveComponent("el-cascader"),T=e.resolveComponent("el-input"),w=e.resolveComponent("el-row"),I=e.resolveComponent("el-button"),C=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",ve,[e.createVNode(ce,{modelValue:a.value,"onUpdate:modelValue":k[3]||(k[3]=P=>a.value=P)},{default:e.withCtx(()=>[e.createVNode(C,{ref:"form",model:c,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(w,null,{default:e.withCtx(()=>[e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(r,{label:"供应商"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:c.supplierId,"onUpdate:modelValue":k[0]||(k[0]=P=>c.supplierId=P),"remote-method":s,clearable:"",filterable:"",placeholder:"请选择供应商",remote:""},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,P=>(e.openBlock(),e.createBlock(m,{key:P.id,label:P.name,value:P.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(r,{label:"平台类目"},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:c.platformCategoryParentId,"onUpdate:modelValue":k[1]||(k[1]=P=>c.platformCategoryParentId=P),options:i.value,props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"},clearable:"",placeholder:"请选择平台类目","show-all-levels":"",style:{width:"62.5%"}},null,8,["modelValue","options"])]),_:1})]),_:1}),e.createVNode(x,{span:8},{default:e.withCtx(()=>[e.createVNode(r,{label:"商品名称"},{default:e.withCtx(()=>[e.createVNode(T,{modelValue:c.supplierGoodsName,"onUpdate:modelValue":k[2]||(k[2]=P=>c.supplierGoodsName=P),maxlength:"20",placeholder:"请输入商品名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(r,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(I,{class:"from_btn",round:"",type:"primary",onClick:d},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),tt=t=>{const o=e.ref([]),a=e.ref(0),n=1e4,i=e.computed(()=>{var x;return(x=t.value.storageSkus)==null?void 0:x.reduce((y,T)=>y+=T.purchaseNum||0,0)}),_=e.computed(()=>{var x;return((x=t.value.storageSkus)==null?void 0:x.reduce((y,T)=>{var I;const w=T.purchaseNum?(I=new H(T.salePrice))==null?void 0:I.div(1e4).mul(new H(T.purchaseNum)):new H(0);return new H(y).plus(w)},new H(0)))||new H(0)}),c=e.computed(()=>{var y;const x=((y=t.value.storageSkus)==null?void 0:y.map(T=>(T==null?void 0:T.stockType)==="UNLIMITED"?n:Number((T==null?void 0:T.stock)||0)))||[];return Math.min(...x)}),d=e.computed(()=>_.value.plus(new H(a.value))),s=x=>{var y;(y=t.value.storageSkus)==null||y.forEach(T=>T.purchaseNum=x)},l=async()=>{var y;let x=[];try{const T=await be();x=(y=T==null?void 0:T.data)==null?void 0:y.records}finally{o.value=x}},k=async(x=["320000","320100","320101"])=>{var T;let y=0;try{const w=await ue({receiverAreaCode:x,freeRight:!1,distributionMode:["EXPRESS"],address:"江苏省 南京市 市辖区收拾收拾",shopFreights:[t.value].map(C=>{var P;return{shopId:C.shopId,freights:[{templateId:C.freightTemplateId,skuInfos:(P=C.storageSkus)==null?void 0:P.map(R=>({price:R.salePrice,skuId:R.id,num:R.purchaseNum||0,weight:R.weight}))}]}})});y=Object.values(((T=w==null?void 0:w.data)==null?void 0:T.EXPRESS)||{}).reduce((C,P)=>C+=P,0)}finally{a.value=y}};e.watch(()=>i.value,()=>k());const m=e.reactive({remark:"",receiveId:""});return{receiveList:o,freightTotal:a,maxUnlimitedNum:n,totalNum:i,totalPrice:_,maxBatchNum:c,totalOrderPrice:d,changeBatchPurchaseNum:s,fetchReceiveAddress:l,purchaseFormModel:m,getOrderConfig:()=>{const x=at(o.value,m.receiveId),y="PURCHASE",T=m.remark,w=ot(t);return{receiver:x,sellType:y,remark:T,suppliers:w}},handleRemove:x=>{O.ElMessageBox.confirm("确认移除当前规格信息").then(()=>{var y;(y=t.value.storageSkus)==null||y.splice(x,1)})}}},ot=t=>{var a;const o={supplierId:t.value.shopId,productSkus:{}};return(a=t.value.storageSkus)==null||a.forEach(n=>{n.purchaseNum&&(o.productSkus[t.value.id],o.productSkus[t.value.id]||(o.productSkus[t.value.id]=[]),o.productSkus[t.value.id].push({skuId:n.id,num:n.purchaseNum}))}),[o]},at=(t,o="")=>{const a=t.find(n=>n.id===o);return a?{name:a.contactName,mobile:a.contactPhone,areaCode:[a==null?void 0:a.provinceCode,a==null?void 0:a.cityCode,a==null?void 0:a.regionCode],address:a==null?void 0:a.address}:{name:"",mobile:"",areaCode:[],address:""}},nt=t=>(e.pushScopeId("data-v-a3a2d900"),t=t(),e.popScopeId(),t),lt={class:"purchase"},rt={key:0,class:"purchase__batch"},st={class:"commodity-info"},ct=["src"],it={class:"purchase__total"},dt=nt(()=>e.createElementVNode("div",{class:"purchase__total--title"},"订单合计",-1)),pt={class:"purchase__total--line"},mt={class:"purchase__total--line"},_t={class:"purchase__total--line"},ft={class:"purchase__total--line"},ht=e.defineComponent({__name:"purchase",props:{lines:{default:()=>({id:"",albumPics:"",productName:"",salePrices:[],sellType:"PURCHASE",shopId:"",shopName:"",shopOwnProductStockNum:0,storageSkus:[]})}},emits:["update:lines"],setup(t,{expose:o,emit:a}){const n=t,i=e.ref(0),_=e.computed({get(){return n.lines},set(I){a("update:lines",I)}}),{maxUnlimitedNum:c,totalNum:d,freightTotal:s,totalPrice:l,maxBatchNum:k,totalOrderPrice:m,changeBatchPurchaseNum:p,receiveList:r,fetchReceiveAddress:x,purchaseFormModel:y,getOrderConfig:T,handleRemove:w}=tt(_);return e.onMounted(()=>x()),o({getOrderConfig:T}),(I,C)=>{const P=e.resolveComponent("el-input-number"),R=e.resolveComponent("el-form-item"),g=e.resolveComponent("el-form"),V=e.resolveComponent("el-table-column"),h=e.resolveComponent("el-button"),f=e.resolveComponent("el-table"),b=e.resolveComponent("el-option"),D=e.resolveComponent("el-select"),B=e.resolveComponent("el-input");return e.openBlock(),e.createElementBlock("div",lt,[_.value.storageSkus&&_.value.storageSkus.length>1?(e.openBlock(),e.createElementBlock("div",rt,[e.createVNode(g,{"show-message":!1,inline:""},{default:e.withCtx(()=>[e.createVNode(R,{label:"采购数(批量)"},{default:e.withCtx(()=>[e.createVNode(P,{modelValue:i.value,"onUpdate:modelValue":C[0]||(C[0]=N=>i.value=N),max:e.unref(k),min:0,onChange:e.unref(p)},null,8,["modelValue","max","onChange"])]),_:1})]),_:1})])):e.createCommentVNode("",!0),e.createVNode(f,{data:_.value.storageSkus},{default:e.withCtx(()=>{var N;return[((N=_.value.storageSkus)==null?void 0:N.length)===1?(e.openBlock(),e.createBlock(V,{key:0,label:"商品名称","min-width":"250"},{default:e.withCtx(()=>[e.createElementVNode("div",st,[e.createElementVNode("img",{src:_.value.albumPics},null,8,ct),e.createElementVNode("span",null,e.toDisplayString(_.value.productName),1)])]),_:1})):(e.openBlock(),e.createBlock(V,{key:1,label:"商品规格","min-width":"250"},{default:e.withCtx(({row:L})=>{var U;return[e.createTextVNode(e.toDisplayString(((U=L==null?void 0:L.specs)==null?void 0:U.join(","))||"单规格"),1)]}),_:1})),e.createVNode(V,{align:"center",label:"供货价",width:"150"},{default:e.withCtx(({row:L})=>[e.createTextVNode(" ￥"+e.toDisplayString((L==null?void 0:L.salePrice)/1e4),1)]),_:1}),e.createVNode(V,{align:"center",label:"起批数",prop:"minimumPurchase",width:"80"}),e.createVNode(V,{align:"center",label:"供应商库存",width:"100"},{default:e.withCtx(({row:L})=>[e.createTextVNode(e.toDisplayString((L==null?void 0:L.stockType)==="UNLIMITED"?"不限购":L.stock),1)]),_:1}),e.createVNode(V,{align:"center",label:"自有库存",prop:"shopOwnProductStockNum",width:"80"}),e.createVNode(V,{align:"center",label:"采购数",width:"180"},{default:e.withCtx(({row:L})=>[e.createVNode(P,{modelValue:L.purchaseNum,"onUpdate:modelValue":U=>L.purchaseNum=U,max:(L==null?void 0:L.stockType)==="UNLIMITED"?e.unref(c):L.stock,min:L.minimumPurchase,placeholder:"请输入"},null,8,["modelValue","onUpdate:modelValue","max","min"])]),_:1}),e.createVNode(V,{fixed:"right",label:"操作",width:"80"},{default:e.withCtx(({$index:L})=>[e.createVNode(h,{link:"",size:"small",type:"danger",onClick:U=>e.unref(w)(L)},{default:e.withCtx(()=>[e.createTextVNode("移除")]),_:2},1032,["onClick"])]),_:1})]}),_:1},8,["data"]),e.createVNode(g,{"show-message":!1,class:"purchase__remark"},{default:e.withCtx(()=>[e.createVNode(R,{label:"收货人信息"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:e.unref(y).receiveId,"onUpdate:modelValue":C[1]||(C[1]=N=>e.unref(y).receiveId=N),style:{width:"100%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(r),N=>(e.openBlock(),e.createBlock(b,{key:N.id,label:N.contactName+"  "+N.contactPhone+"   "+N.address,value:N.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(R,{label:"采购备注"},{default:e.withCtx(()=>[e.createVNode(B,{modelValue:e.unref(y).remark,"onUpdate:modelValue":C[2]||(C[2]=N=>e.unref(y).remark=N),placeholder:"请输入采购备注",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e.createElementVNode("div",it,[dt,e.createElementVNode("div",pt,"采购数量："+e.toDisplayString(e.unref(d)),1),e.createElementVNode("div",mt,"商品总价："+e.toDisplayString(e.unref(l)),1),e.createElementVNode("div",_t,"运费："+e.toDisplayString(e.unref(s).toFixed(2)),1),e.createElementVNode("div",ft,"采购金额(应付款)：￥"+e.toDisplayString(e.unref(m)),1)])])}}}),Gn="",G=(t,o)=>{const a=t.__vccOpts||t;for(const[n,i]of o)a[n]=i;return a},gt=G(ht,[["__scopeId","data-v-a3a2d900"]]),ut=t=>{const o=e.reactive({page:1,pageSize:5,total:0}),a=e.reactive({remark:"",receiveId:""}),n=e.ref(0),i=t,_=e.ref(0),c=e.ref([]),d=e.ref([]),s=(V,h)=>{c.value=h.map(f=>f.id)},l=V=>{var b,D;const h=i.value.findIndex(B=>B.id===V),f=(D=(b=i.value[h])==null?void 0:b.storageSkus)==null?void 0:D.reduce((B,N)=>B+Number(N.purchaseNum||0),0);i.value[h].totalPurchase=f,I()},k=e.computed(()=>V=>{var f;let h=0;return(f=V==null?void 0:V.skus)==null||f.forEach(b=>{b.lotStartingNum>h&&(h=b.lotStartingNum)}),h}),m=e.computed(()=>V=>{var f,b,D;let h=(b=(f=V==null?void 0:V.skus)==null?void 0:f[0])==null?void 0:b.supplierInventory;return(D=V==null?void 0:V.skus)==null||D.forEach(B=>{B.supplierInventory<h&&(h=B.supplierInventory)}),h}),p=e.computed(()=>V=>{const h=((V==null?void 0:V.salePrices)||[]).map(D=>Number(D)),f=Math.min(...h),b=Math.max(...h);return f===b?b/1e4:`${f/1e4}~${b/1e4}`}),r=e.computed(()=>(console.log(i.value,"totalNum"),i.value.reduce((V,h)=>{var f;return V+(((f=h.storageSkus)==null?void 0:f.reduce((b,D)=>b+=D.purchaseNum||0,0))||0)},0))),x=e.computed(()=>(console.log(i.value,"totalPrice"),i.value.reduce((V,h)=>{var f;return V.add(((f=h.storageSkus)==null?void 0:f.reduce((b,D)=>{var N;const B=D.purchaseNum?(N=new H(D.salePrice))==null?void 0:N.div(1e4).mul(new H(D.purchaseNum||0)):new H(0);return new H(b).plus(B)},new H(0)))||new H(0))},new H(0)))),y=e.computed(()=>x.value.add(new H(_.value))),T=e.computed(()=>V=>V==null?void 0:V.reduce((h,f)=>h+=Number(f.stock),0)),w=(V,h)=>{var f,b;if(typeof h=="number"){const D=i.value.findIndex(B=>B.id===V);D>-1&&((b=(f=i.value[D])==null?void 0:f.storageSkus)==null||b.forEach(B=>{B.purchaseNum=h}),l(V))}},I=async(V=["320000","320100","320101"])=>{var f;let h=0;try{const b=await ue({receiverAreaCode:V,freeRight:!1,distributionMode:["EXPRESS"],address:"江苏省 南京市 市辖区收拾收拾",shopFreights:i.value.map(B=>{var N;return{shopId:B.shopId,freights:[{templateId:B.freightTemplateId,skuInfos:(N=B.storageSkus)==null?void 0:N.map(L=>({price:L.salePrice,skuId:L.id,num:L.purchaseNum||0,weight:L.weight}))}]}})});h=Object.values(((f=b==null?void 0:b.data)==null?void 0:f.EXPRESS)||{}).reduce((B,N)=>B+=N,0)}finally{_.value=h}};return{totalPrice:x,totalNum:r,totalOrderPrice:y,freightTotal:_,purchaseFormModel:a,tableData:i,expandRowKeys:c,expandOpen:s,handleChangePurchaseNum:l,minRowBatchNum:k,maxRowBatchNum:m,computedSalePrice:p,computedSuplier:T,changeRowBatchNum:w,getFreightCount:I,receiveList:d,fetchReceiveAddress:async()=>{var h;let V=[];try{const f=await be();V=(h=f==null?void 0:f.data)==null?void 0:h.records}finally{d.value=V}},getOrderConfig:()=>{const V=xt(d.value,a.receiveId),h="PURCHASE",f=a.remark,b=yt(i);return{receiver:V,sellType:h,remark:f,suppliers:b}},handleRemoveBatch:V=>{O.ElMessageBox.confirm("确认移除当前商品信息").then(()=>{t.value.splice(V,1),o.total=t.value.length,n.value=Date.now()})},handleRemoveSku:(V,h)=>{O.ElMessageBox.confirm("确认移除当前规格信息").then(()=>{var f,b,D;(D=(b=(f=t.value)==null?void 0:f[h])==null?void 0:b.storageSkus)==null||D.splice(V,1),t.value=t.value.filter(B=>B.storageSkus&&B.storageSkus.length>0),nextTick(()=>{o.total=t.value.length}),n.value=Date.now()})},refreshKey:n,paginationOptions:o}},yt=t=>{const o=[];return t.value.forEach(a=>{var i;let n=o.findIndex(_=>_.supplierId===a.shopId);n===-1&&(n=o.push({supplierId:a.shopId,productSkus:{}})-1),(i=a.storageSkus)==null||i.forEach(_=>{_.purchaseNum&&(o[n].productSkus[a.id],o[n].productSkus[a.id]||(o[n].productSkus[a.id]=[]),o[n].productSkus[a.id].push({skuId:_.id,num:_.purchaseNum}))})}),o.filter(a=>Object.keys(a==null?void 0:a.productSkus).length>0)},xt=(t,o="")=>{const a=t.find(n=>n.id===o);return a?{name:a.contactName,mobile:a.contactPhone,areaCode:[a==null?void 0:a.provinceCode,a==null?void 0:a.cityCode,a==null?void 0:a.regionCode],address:a==null?void 0:a.address}:{name:"",mobile:"",areaCode:[],address:""}},Nt=t=>(e.pushScopeId("data-v-1d9e47e3"),t=t(),e.popScopeId(),t),Vt={class:"batch"},bt={class:"batch__expand"},Ct={class:"batch__form"},St={class:"batch__commodity"},kt=["src"],Et={class:"batch__commodity--name"},Tt={class:"batch__pagination"},wt={class:"batch__total"},It=Nt(()=>e.createElementVNode("div",{class:"batch__total--title"},"订单合计",-1)),Dt={class:"batch__total--line"},Bt={class:"batch__total--line"},$t={class:"batch__total--line"},Pt={class:"batch__total--line"},Lt=1e5,Ot=e.defineComponent({__name:"batch-purchase",props:{lines:{default:()=>[]}},emits:["update:lines"],setup(t,{expose:o,emit:a}){const n=t,i=e.computed({get(){return n.lines},set(D){a("update:lines",D)}}),_=e.computed(()=>i.value.filter((D,B)=>B>=(h.page-1)*h.pageSize&&B<h.page*h.pageSize)),{receiveList:c,fetchReceiveAddress:d,purchaseFormModel:s,expandRowKeys:l,expandOpen:k,handleChangePurchaseNum:m,minRowBatchNum:p,maxRowBatchNum:r,computedSalePrice:x,computedSuplier:y,changeRowBatchNum:T,freightTotal:w,totalNum:I,totalPrice:C,totalOrderPrice:P,getOrderConfig:R,handleRemoveBatch:g,handleRemoveSku:V,paginationOptions:h}=ut(i),f=D=>{h.pageSize=D},b=D=>{h.page=D};return e.onMounted(()=>{h.total=i.value.length,d()}),o({getOrderConfig:R}),(D,B)=>{const N=e.resolveComponent("el-option"),L=e.resolveComponent("el-select"),U=e.resolveComponent("el-form-item"),q=e.resolveComponent("el-input"),K=e.resolveComponent("el-form"),J=e.resolveComponent("el-input-number"),z=e.resolveComponent("el-table-column"),Z=e.resolveComponent("el-link"),oe=e.resolveComponent("el-table"),ae=e.resolveComponent("el-pagination");return e.openBlock(),e.createElementBlock("div",Vt,[e.createVNode(K,{"show-message":!1,class:"batch__remark"},{default:e.withCtx(()=>[e.createVNode(U,{label:"收货人信息"},{default:e.withCtx(()=>[e.createVNode(L,{modelValue:e.unref(s).receiveId,"onUpdate:modelValue":B[0]||(B[0]=S=>e.unref(s).receiveId=S),style:{width:"100%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(c),S=>(e.openBlock(),e.createBlock(N,{key:S.id,label:S.contactName+"  "+S.contactPhone+"   "+S.address,value:S.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(U,{label:"采购备注"},{default:e.withCtx(()=>[e.createVNode(q,{modelValue:e.unref(s).remark,"onUpdate:modelValue":B[1]||(B[1]=S=>e.unref(s).remark=S),placeholder:"请输入采购备注",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(oe,{data:_.value,"expand-row-keys":e.unref(l),"row-key":"id",onExpandChange:e.unref(k)},{default:e.withCtx(()=>[e.createVNode(z,{type:"expand"},{default:e.withCtx(({row:S,$index:$})=>[e.createElementVNode("div",bt,[e.createElementVNode("div",Ct,[e.createVNode(K,null,{default:e.withCtx(()=>[e.createVNode(U,{label:"采购数(批量)"},{default:e.withCtx(()=>[e.createVNode(J,{modelValue:S.batchNum,"onUpdate:modelValue":E=>S.batchNum=E,max:e.unref(r)(S),min:e.unref(p)(S),precision:0,onChange:E=>e.unref(T)(S.id,E)},null,8,["modelValue","onUpdate:modelValue","max","min","onChange"])]),_:2},1024)]),_:2},1024)]),e.createVNode(oe,{data:S.storageSkus,"row-key":"id"},{default:e.withCtx(()=>[e.createVNode(z,{label:"商品规格"},{default:e.withCtx(({row:E})=>{var A;return[e.createTextVNode(e.toDisplayString(((A=E==null?void 0:E.specs)==null?void 0:A.join(","))||"单规格"),1)]}),_:1}),e.createVNode(z,{label:"供货价",width:"130"},{default:e.withCtx(({row:E})=>[e.createTextVNode(" ￥"+e.toDisplayString((E==null?void 0:E.salePrice)/1e4),1)]),_:1}),e.createVNode(z,{label:"起批数",prop:"minimumPurchase",width:"80"}),e.createVNode(z,{label:"供应商库存",prop:"stock",width:"100"},{default:e.withCtx(({row:E})=>[e.createTextVNode(e.toDisplayString((E==null?void 0:E.stockType)==="UNLIMITED"?"不限购":E.stock),1)]),_:1}),e.createVNode(z,{label:"自有库存",prop:"shopOwnProductStockNum",width:"80"}),e.createVNode(z,{label:"采购数",prop:"purchaseNum"},{default:e.withCtx(({row:E})=>[e.createVNode(J,{modelValue:E.purchaseNum,"onUpdate:modelValue":A=>E.purchaseNum=A,max:E.stockType==="UNLIMITED"?Lt:E.stock,min:E.minimumPurchase,precision:0,onChange:A=>e.unref(m)(S.id)},null,8,["modelValue","onUpdate:modelValue","max","min","onChange"])]),_:2},1024),e.createVNode(z,{fixed:"right",label:"操作",width:"80"},{default:e.withCtx(({$index:E})=>[e.createVNode(Z,{type:"danger",onClick:A=>e.unref(V)(E,$)},{default:e.withCtx(()=>[e.createTextVNode("移出")]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])]),_:1}),e.createVNode(z,{label:"供应商",prop:"supplierName"}),e.createVNode(z,{label:"商品名称",width:"250"},{default:e.withCtx(({row:S})=>{var $;return[e.createElementVNode("div",St,[e.createElementVNode("img",{src:($=S.albumPics)==null?void 0:$.split(",").shift()},null,8,kt),e.createElementVNode("span",Et,e.toDisplayString(S.productName),1)])]}),_:1}),e.createVNode(z,{label:"供货价"},{default:e.withCtx(({row:S})=>[e.createTextVNode("￥"+e.toDisplayString(e.unref(x)(S)),1)]),_:1}),e.createVNode(z,{label:"供应商库存"},{default:e.withCtx(({row:S})=>[e.createTextVNode(e.toDisplayString(e.unref(y)(S==null?void 0:S.storageSkus)),1)]),_:1}),e.createVNode(z,{label:"自有库存",prop:"shopOwnProductStockNum"}),e.createVNode(z,{label:"采购数",prop:"totalPurchase"}),e.createVNode(z,{label:"操作"},{default:e.withCtx(({$index:S})=>[e.createVNode(Z,{type:"danger",onClick:$=>e.unref(g)(S)},{default:e.withCtx(()=>[e.createTextVNode("移出")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","expand-row-keys","onExpandChange"]),e.createElementVNode("div",Tt,[e.createVNode(ae,{"current-page":e.unref(h).page,"page-size":e.unref(h).pageSize,"page-sizes":[5,10,15,20],total:e.unref(h).total,layout:"total, prev, pager, next, sizes",onSizeChange:f,onCurrentChange:b},null,8,["current-page","page-size","total"])]),e.createElementVNode("div",wt,[It,e.createElementVNode("div",Dt,"采购数量："+e.toDisplayString(e.unref(I)),1),e.createElementVNode("div",Bt,"商品总价："+e.toDisplayString(e.unref(C)),1),e.createElementVNode("div",$t,"运费："+e.toDisplayString(e.unref(w).toFixed(2)),1),e.createElementVNode("div",Pt,"采购金额(应付款)：￥"+e.toDisplayString(e.unref(P)),1)])])}}}),Yn="",Rt=G(Ot,[["__scopeId","data-v-1d9e47e3"]]),Mt=()=>{const t=e.ref(!1),o=e.ref([]);return{batchPurchaseLines:o,showBatchDialog:t,openBatchDialog:(i=[])=>{o.value=i,t.value=!0},setBatchPurchaseLines:(i=[])=>{o.value=i}}},At={class:"tools"},Ut={class:"commodity-info"},Ft=["src"],zt={class:"dialog-footer"},Ht={class:"dialog-footer"},jt=e.defineComponent({__name:"index",setup(t){const o=e.ref([]),a=e.ref({}),n=e.ref("calc(100vh - 350px)"),i=e.ref([]),_=e.ref(!1),c=X.useRouter(),d=e.reactive({page:{current:1,size:10},total:0}),s=(f={})=>{a.value=f,d.page.current=1,k()},l=e.ref(),k=async()=>{const f=await Re({...d.page,sellType:"PURCHASE",...a.value});f.data&&(d.total=Number(f.data.total),i.value=f.data.records)},m=f=>{n.value=f?"calc(100vh - 450px)":"calc(100vh - 350px)"},p=f=>{var b,D,B;if(f)l.value={...f,storageSkus:((D=(b=f.storageSkus)==null?void 0:b.map(N=>({...N,purchaseNum:0})))==null?void 0:D.filter(N=>N.stockType==="LIMITED"&&Number(N.stock||0)>N.minimumPurchase||N.stockType==="UNLIMITED"))||[]},_.value=!0;else{if(o.value.length===0){O.ElMessage.error({message:"请选择需要采购的商品"});return}C((B=o.value)==null?void 0:B.map(N=>{var L;return{...N,storageSkus:(L=N.storageSkus)==null?void 0:L.filter(U=>U.stockType==="LIMITED"&&Number(U.stock||0)>U.minimumPurchase||U.stockType==="UNLIMITED")}}))}},r=f=>{Ne(de.useShopInfoStore().shopInfo.id,f.shopId).then(({code:b})=>{b===200&&c.push({path:"/message/customer/supplierService",query:{id:f.shopId}})})},x=e.computed(()=>f=>{const b=((f==null?void 0:f.salePrices)||[]).map(N=>Number(N)),D=Math.min(...b),B=Math.max(...b);return D===B?B/1e4:`${D/1e4}~${B/1e4}`}),y=e.computed(()=>f=>f==null?void 0:f.reduce((b,D)=>b+=Number(D.stock),0)),T=e.ref(null),w=e.ref(null),{showBatchDialog:I,openBatchDialog:C,batchPurchaseLines:P}=Mt();let R=0;const g=async()=>{var D,B,N;R=0;const f=(D=T.value)==null?void 0:D.getOrderConfig();if(!(f!=null&&f.receiver.mobile))return O.ElMessage.error("请选择收货人信息");const b=await ye(f);b.code===200&&(B=b.data)!=null&&B.mainNo&&V((N=b.data)==null?void 0:N.mainNo)},V=async f=>{if(R++,R===20)return;(await Me(f)).data?(O.ElMessage.success("采购订单创建成功"),_.value=!1,I.value=!1,k()):setTimeout(()=>{V(f)},1e3)},h=async()=>{var D,B,N;const f=(D=w.value)==null?void 0:D.getOrderConfig();if(!(f!=null&&f.receiver.mobile))return O.ElMessage.error("请选择收货人信息");const b=await ye(f);b.code===200&&(B=b.data)!=null&&B.mainNo&&V((N=b.data)==null?void 0:N.mainNo)};return(f,b)=>{const D=e.resolveComponent("el-button"),B=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(et,{onSearch:s,onChangeShow:m}),e.createElementVNode("div",At,[e.createVNode(D,{onClick:b[0]||(b[0]=N=>p())},{default:e.withCtx(()=>[e.createTextVNode("一键采购")]),_:1})]),e.createVNode(e.unref(_e),{checkedItem:o.value,"onUpdate:checkedItem":b[1]||(b[1]=N=>o.value=N),data:i.value,selection:!0,style:e.normalizeStyle({height:n.value,overflowY:"auto"}),class:"table"},{default:e.withCtx(()=>[e.createVNode(j,{align:"center",label:"供应商",prop:"supplierName",width:"130"}),e.createVNode(j,{align:"left",label:"商品名称"},{default:e.withCtx(({row:N})=>{var L,U;return[e.createElementVNode("div",Ut,[e.createElementVNode("img",{src:(U=(L=N.albumPics)==null?void 0:L.split(","))==null?void 0:U.shift()},null,8,Ft),e.createElementVNode("span",null,e.toDisplayString(N.productName),1)])]}),_:1}),e.createVNode(j,{align:"center",label:"供货价",width:"150"},{default:e.withCtx(({row:N})=>[e.createTextVNode(" ￥"+e.toDisplayString(x.value(N)),1)]),_:1}),e.createVNode(j,{align:"center",label:"供应商库存",width:"100"},{default:e.withCtx(({row:N})=>[e.createTextVNode(e.toDisplayString(y.value(N==null?void 0:N.storageSkus)),1)]),_:1}),e.createVNode(j,{align:"center",label:"自有库存",prop:"shopOwnProductStockNum",width:"80"},{default:e.withCtx(({row:N})=>{var L;return[e.createTextVNode(e.toDisplayString((L=N==null?void 0:N.storageSkus)==null?void 0:L.reduce((U,q)=>U+Number((q==null?void 0:q.shopOwnProductStockNum)||0),0)),1)]}),_:1}),e.createVNode(j,{fixed:"right",label:"操作",width:"150"},{default:e.withCtx(({row:N})=>[e.createElementVNode("div",null,[e.createVNode(D,{link:"",size:"small",type:"primary",onClick:L=>p(N)},{default:e.withCtx(()=>[e.createTextVNode("一键采购")]),_:2},1032,["onClick"]),e.createVNode(D,{link:"",size:"small",type:"primary",onClick:L=>r(N)},{default:e.withCtx(()=>[e.createTextVNode("联系卖家")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["checkedItem","data","style"]),e.createVNode(ie,{modelValue:d.page,"onUpdate:modelValue":b[2]||(b[2]=N=>d.page=N),total:d.total,"load-init":"",onReload:k},null,8,["modelValue","total"]),e.createVNode(B,{modelValue:_.value,"onUpdate:modelValue":b[5]||(b[5]=N=>_.value=N),"close-on-click-modal":!1,"destroy-on-close":"",title:"一键采购",width:"1000px"},{footer:e.withCtx(()=>[e.createElementVNode("span",zt,[e.createVNode(D,{onClick:b[4]||(b[4]=N=>_.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(D,{type:"primary",onClick:g},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(gt,{ref_key:"purchaseRef",ref:T,lines:l.value,"onUpdate:lines":b[3]||(b[3]=N=>l.value=N)},null,8,["lines"])]),_:1},8,["modelValue"]),e.createVNode(B,{modelValue:e.unref(I),"onUpdate:modelValue":b[8]||(b[8]=N=>e.isRef(I)?I.value=N:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"一键采购",width:"1000px"},{footer:e.withCtx(()=>[e.createElementVNode("span",Ht,[e.createVNode(D,{onClick:b[7]||(b[7]=N=>I.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(D,{type:"primary",onClick:h},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(Rt,{ref_key:"batchPurchaseRef",ref:w,lines:e.unref(P),"onUpdate:lines":b[6]||(b[6]=N=>e.isRef(P)?P.value=N:null)},null,8,["lines"])]),_:1},8,["modelValue"])],64)}}}),Kn="",Gt=Object.freeze(Object.defineProperty({__proto__:null,default:G(jt,[["__scopeId","data-v-48d75a25"]])},Symbol.toStringTag,{value:"Module"})),qt={style:{background:"#f9f9f9"}},Yt=e.defineComponent({__name:"search",emits:["changeShow","search"],setup(t,{emit:o}){const a=e.ref(!1),n=e.ref([]);e.watch(()=>a.value,d=>o("changeShow",d));const i=e.reactive({supplierId:"",no:"",date:""}),_=()=>{var s,l;const d=v.cloneDeep(i);d.startTime=Array.isArray(d.date)?(s=d.date)==null?void 0:s[0]:void 0,d.endTime=Array.isArray(d.date)?(l=d.date)==null?void 0:l[1]:void 0,delete d.date,o("search",d)},c=async(d="")=>{var l;const s=await pe({supplierName:d});s.data&&((l=s.data)!=null&&l.length)&&(n.value=s.data)};return(d,s)=>{const l=e.resolveComponent("el-input"),k=e.resolveComponent("el-form-item"),m=e.resolveComponent("el-col"),p=e.resolveComponent("el-option"),r=e.resolveComponent("el-select"),x=e.resolveComponent("el-date-picker"),y=e.resolveComponent("el-row"),T=e.resolveComponent("el-button"),w=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",qt,[e.createVNode(ce,{modelValue:a.value,"onUpdate:modelValue":s[3]||(s[3]=I=>a.value=I)},{default:e.withCtx(()=>[e.createVNode(w,{ref:"form",model:i,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(y,null,{default:e.withCtx(()=>[e.createVNode(m,{span:8},{default:e.withCtx(()=>[e.createVNode(k,{label:"订单号"},{default:e.withCtx(()=>[e.createVNode(l,{modelValue:i.no,"onUpdate:modelValue":s[0]||(s[0]=I=>i.no=I),placeholder:"请输入订单号"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(m,{span:8},{default:e.withCtx(()=>[e.createVNode(k,{label:"供应商"},{default:e.withCtx(()=>[e.createVNode(r,{modelValue:i.supplierId,"onUpdate:modelValue":s[1]||(s[1]=I=>i.supplierId=I),"remote-method":c,clearable:"",filterable:"",placeholder:"请选择供应商",remote:""},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,I=>(e.openBlock(),e.createBlock(p,{key:I.id,label:I.name,value:I.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e.createVNode(m,{span:8},{default:e.withCtx(()=>[e.createVNode(k,{label:"下单时间"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:i.date,"onUpdate:modelValue":s[2]||(s[2]=I=>i.date=I),"end-placeholder":"结束时间",format:"YYYY/MM/DD","start-placeholder":"开始时间",type:"daterange","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(k,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(T,{class:"from_btn",round:"",type:"primary",onClick:_},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Wt=()=>({data:{type:Array,default(){return[]}},selection:{type:Boolean,default:!1},custom:{type:Boolean,default:!1},needBorder:{type:Boolean,default:!1},columns:{type:Array,default(){return[]}},tableHeaderClass:{type:String,default:""},rowHeaderClass:{type:String,default:""},needHoverBorder:{type:Boolean,default:!1},multipleKey:{type:String,default:""}}),Kt=e.defineComponent({props:Wt(),emits:["check"],setup(t,{slots:o,emit:a}){const n=e.ref([]),i=e.computed({get:()=>!!(c.value.length&&c.value.every(p=>p.checked)),set:p=>{l(p),a("check",k())}}),_=e.computed(()=>{const p=c.value.filter(r=>r.checked).length;return p!==0&&p!==c.value.length}),c=e.computed(()=>{const p=n.value;return p?d(p):[]}),d=p=>p?(p.forEach(r=>{const x=r,y=x.orderItems;x.packageMap=y.reduce((T,w,I)=>{var R;const P=String(I);return T.has(P)?(R=T.get(P))==null||R.push(w):T.set(P,[w]),T},new Map)}),p):[];e.watch(t,p=>{n.value=p.data},{immediate:!0});const s=(p,r)=>{Object.assign(p.props?p.props:{},r)};function l(p){c.value=c.value.map(r=>(r.checked=p,r))}function k(){return c.value.filter(p=>p.checked)}const m=(p,r)=>{const x=[],y=p.packageMap;let T=!0;return y.forEach((w,I)=>{x.push(e.createVNode("tr",{class:"body--content"},[o.default&&o.default().map((C,P)=>{var g;s(C,{row:p,packageId:I,shopOrderItems:w});const R=(g=C.props)==null?void 0:g["is-mixed"];if(R&&T||!R)return e.createVNode("td",{class:["o_table--item",!C&&"hide"],rowspan:R?y.size:void 0},[e.createVNode("div",{class:["selection__checkbox",t.selection&&P===0&&"selection"]},[e.createVNode("div",{class:["o_table--shrink"]},[C])])])})])),T=!1}),x};return()=>e.createVNode("table",{class:["m__table"],cellpadding:"0",cellspacing:"0"},[e.createVNode("colgroup",null,[t.columns.map(p=>e.createVNode("col",{width:p.width},null))]),e.createVNode("thead",{class:["m__table--head",t.tableHeaderClass,o.header&&"padding"]},[e.createVNode("tr",{class:"m__tr"},[t.columns.map((p,r)=>e.createVNode("th",{style:p.customStyle,class:r===0&&t.selection&&["m__table--center"]},[r===0&&t.selection&&e.createVNode(e.resolveComponent("el-checkbox"),{indeterminate:_.value,modelValue:i.value,"onUpdate:modelValue":x=>i.value=x,onChange:l.bind(this,i.value)},null),e.createVNode("div",{class:["m__table--shrink"]},[p.label])]))])]),c.value.length?c.value.map((p,r)=>e.createVNode("tbody",{class:["m__table--body",t.custom?"custom":"default",t.needBorder&&"need--border",t.needHoverBorder&&p.close?"hover--class":"ordinary--class"]},[o.header&&e.createVNode("tr",null,[e.createVNode("td",{colspan:t.columns.length},[e.createVNode("div",{class:["body--header",t.rowHeaderClass,{close:p.close}]},[o.header({row:p,index:r})])])]),m(p)])):e.createVNode("tbody",{class:"m__table--empty"},[e.createVNode("tr",null,[e.createVNode("td",{class:"empty__td",colspan:t.columns.length,align:"center"},[e.createTextVNode("暂无数据~")])])])])}}),Xn="",Qt=()=>({data:{type:Array,default(){return[]}},selection:{type:Boolean,default:!1},custom:{type:Boolean,default:!1},needBorder:{type:Boolean,default:!1},columns:{type:Array,default(){return[]}},tableHeaderClass:{type:String,default:""},rowHeaderClass:{type:String,default:""},needHoverBorder:{type:Boolean,default:!1},multipleKey:{type:String,default:""}}),Xt=e.defineComponent({props:Qt(),emits:["update:checkedItem"],setup(t,{slots:o,emit:a}){const n=e.ref(),i=e.ref(),_=()=>t.columns.length>0&&t.columns||o.default&&o.default().map(s=>{const l=s.props;return{label:l==null?void 0:l.label,width:l==null?void 0:l.width,prop:l==null?void 0:l.prop,customStyle:l==null?void 0:l.customStyle}}),c=s=>{a("update:checkedItem",s)},d=()=>{const s={default:()=>o.default&&o.default()};if(!o.default&&!t.columns.length&&!o.custom)throw new Error("请传入MTableColumn");return o.header&&(s.header=l=>o.header&&o.header(l)),s};return e.watch(t,s=>{i.value=v.cloneDeep(s.data)},{immediate:!0}),()=>e.createVNode("div",{class:["m__table--container"]},[e.createVNode(Kt,{onCheck:c,columns:_(),data:i.value,custom:t.custom,selection:t.selection,tableHeaderClass:t.tableHeaderClass,rowHeaderClass:t.rowHeaderClass,needHoverBorder:t.needHoverBorder,multipleKey:t.multipleKey,needBorder:t.needBorder,ref:n},d())])}}),te=e.defineComponent({__name:"split-table-column",props:{prop:{type:String,default:""},align:{type:String,default:"center"},row:{type:Object,default(){return{}}},packageId:{type:String,default(){}},shopOrderItems:{type:Object,default(){return[]}}},setup(t){const o=t,a=n=>{switch(n){case"left":return"flex-start";case"right":return"flex-end";default:return"center"}};return(n,i)=>(e.openBlock(),e.createElementBlock("div",{class:"item__content",style:e.normalizeStyle({justifyContent:a(o.align)})},[e.renderSlot(n.$slots,"default",{row:o.row,shopOrderItems:o.shopOrderItems})],4))}}),Jt=(t,o)=>{t?o.value="calc(100vh - 450px)":o.value="calc(100vh - 350px)"},ke={UNPAID:"待支付",PAYMENT_AUDIT:"待审核",WAITING_FOR_DELIVER:"待发货",WAITING_FOR_PUTIN:"待入库",FINISHED:"已完成",CLOSED:"已关闭"};function Zt(t,o=1e3,a={}){const{immediate:n=!0,immediateCallback:i=!1}=a;let _=null;const c=e.ref(!1);function d(){_&&(clearInterval(_),_=null)}function s(){c.value=!1,d()}function l(){o<=0||(c.value=!0,i&&t(),d(),_=setInterval(t,o))}return n&&l(),{isActive:c,pause:s,resume:l,clean:d}}const vt=(t,o)=>{const a=e.reactive({day:"00",hours:"00",minutes:"00",seconds:"00"});if(!o)return{...e.toRefs(a)};const _=new Date(t.replaceAll("-","/")).getTime()+parseInt(o)*1e3-new Date().getTime(),{day:c,hours:d,minutes:s,seconds:l}=eo(_);return a.day=c,a.hours=d,a.minutes=s,a.seconds=l,{...e.toRefs(a)}},eo=t=>{let i=0,_=0,c=0,d=0;for(;t>864e5;)t-=864e5,i++;for(;t>36e5;)t-=36e5,_++;for(;t>6e4;)t-=6e4,c++;for(;t>1e3;)t-=1e3,d++;return{day:String(i).padStart(2,"0"),hours:String(_).padStart(2,"0"),minutes:String(c).padStart(2,"0"),seconds:String(d).padStart(2,"0")}},to={class:"count-down"},oo={key:0},ao=e.defineComponent({__name:"index",props:{createTime:{default:""},payTimeout:{default:""}},setup(t){const o=t,a=e.reactive({day:"00",hours:"00",minutes:"00",seconds:"00"}),n=e.ref(!0),{clean:i}=Zt(()=>{const{day:_,hours:c,minutes:d,seconds:s}=vt(o.createTime,o.payTimeout);a.day=_.value,a.hours=c.value,a.minutes=d.value,a.seconds=s.value,_.value==="00"&&c.value==="00"&&d.value==="00"&&s.value==="00"?(n.value=!0,e.nextTick(()=>i())):n.value=!1},1e3,{immediate:!0,immediateCallback:!0});return e.onBeforeUnmount(()=>i()),(_,c)=>(e.openBlock(),e.createElementBlock("div",to,[a.day!=="00"?(e.openBlock(),e.createElementBlock("span",oo,e.toDisplayString(a.day)+"天",1)):e.createCommentVNode("",!0),e.createElementVNode("span",null,e.toDisplayString(a.hours)+":"+e.toDisplayString(a.minutes)+":"+e.toDisplayString(a.seconds),1)]))}}),Jn="",no=G(ao,[["__scopeId","data-v-0823a7bd"]]),lo=()=>{const t=e.ref(!1),o=e.ref("");return{showProof:t,goToShowProof:n=>{var i,_;o.value=((_=(i=n==null?void 0:n.extra)==null?void 0:i.pay)==null?void 0:_.proof)||"",t.value=!0},currentProof:o}},ro=e.defineComponent({__name:"pay-order",props:{price:{default:()=>new H(0)}},setup(t,{expose:o}){const a=t,n=e.reactive({payType:"",proof:""}),i={payType:[{required:!0,message:"请选择支付方式",trigger:"blur"}],proof:[{validator:(d,s,l)=>{n.payType==="OFFLINE"&&(s||l(new Error("请上传凭证"))),l()},trigger:"change"}]},_=e.ref(null);return o({getPayOrderFormModel:()=>new Promise((d,s)=>{_.value?_.value.validate(l=>{l?d(n):s("valid error")}):s("none instance")})}),(d,s)=>{const l=e.resolveComponent("el-form-item"),k=e.resolveComponent("el-radio"),m=e.resolveComponent("el-radio-group"),p=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(p,{ref_key:"formRef",ref:_,model:n,rules:i},{default:e.withCtx(()=>[e.createVNode(l,{label:"应付款(元)"},{default:e.withCtx(()=>[e.createTextVNode("￥"+e.toDisplayString(a.price),1)]),_:1}),e.createVNode(l,{label:"支付方式",prop:"payType"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:n.payType,"onUpdate:modelValue":s[0]||(s[0]=r=>n.payType=r)},{default:e.withCtx(()=>[e.createVNode(k,{label:"OFFLINE"},{default:e.withCtx(()=>[e.createTextVNode("线下付款")]),_:1}),e.createVNode(k,{label:"BALANCE"},{default:e.withCtx(()=>[e.createTextVNode("店铺余额")]),_:1})]),_:1},8,["modelValue"])]),_:1}),n.payType==="OFFLINE"?(e.openBlock(),e.createBlock(l,{key:0,label:"付款凭证",prop:"proof"},{default:e.withCtx(()=>[e.createVNode(Be,{src:n.proof,"onUpdate:src":s[1]||(s[1]=r=>n.proof=r),format:{size:1},height:100,width:100},null,8,["src"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model"])}}}),so={PERSONAL:"个人",ENTERPRISE:"企业"},co={VAT_GENERAL:"增值税电子普通发票",VAT_SPECIAL:"增值税电子专用发票"},me=t=>(e.pushScopeId("data-v-023b6aef"),t=t(),e.popScopeId(),t),io={style:{padding:"0 30px"},class:"invoice-dialog"},po={class:"invoice-dialog-main"},mo={class:"invoice-dialog-main--name"},_o={class:"invoice-dialog-main--price"},fo={class:"invoice-dialog-main--number"},ho=me(()=>e.createElementVNode("p",{class:"invoice-dialog-main--msg"}," 1、开票金额为消费者实付款金额，红包、优惠、购物券、消费返利等不在开票范围 2、如订单发生退货退款、退款，开票金额也将对应退款金额扣除。 ",-1)),go={class:"invoice-dialog-main__note"},uo=me(()=>e.createElementVNode("span",{style:{"flex-shrink":"0"}},"开票备注：",-1)),yo={class:"invoice-dialog-main__type"},xo=me(()=>e.createElementVNode("span",{style:{"margin-right":"10px"}},"发票类型：",-1)),No={key:1},Vo={key:0},bo=me(()=>e.createElementVNode("span",null,"抬头选择：",-1)),Co={class:"invoice-dialog-main__content"},So={style:{flex:"3"}},ko={style:{flex:"3"}},Eo={style:{flex:"3"}},To={class:"invoice-dialog-main__content"},wo={style:{flex:"3"}},Io={style:{flex:"3"}},Do={style:{flex:"3"}},Bo={class:"invoice-dialog-main__content"},$o={style:{flex:"3"}},Po={style:{flex:"6"}},Lo={key:0,class:"invoice-dialog-main__content"},Oo=e.defineComponent({__name:"invoice-dialog-main",props:{invoiceDetail:{type:Object,default:()=>({})},invoiceSetType:{type:String,default:""}},emits:["update:invoiceDetail"],setup(t,{emit:o}){const a=t,n=$e.useVModel(a,"invoiceDetail",o),{divTenThousand:i}=ee(),_=e.ref([]),c=e.computed(()=>a.invoiceDetail.invoiceStatus!=="START"?a.invoiceDetail:_.value.find(s=>s.id===n.value.invoiceHeaderId));e.watchEffect(()=>{n.value.invoiceStatus==="START"&&d()});async function d(){const{data:s,msg:l,code:k}=await Ye();k===200&&(_.value=s.records)}return(s,l)=>{var y,T,w,I,C,P,R,g,V,h;const k=e.resolveComponent("el-input"),m=e.resolveComponent("el-radio"),p=e.resolveComponent("el-radio-group"),r=e.resolveComponent("el-option"),x=e.resolveComponent("el-select");return e.openBlock(),e.createElementBlock("div",io,[e.createElementVNode("div",po,[e.createElementVNode("div",mo,[e.createTextVNode(" 供应商名称："),e.createElementVNode("span",null,e.toDisplayString(t.invoiceDetail.shopSupplierName),1)]),e.createElementVNode("div",null,[e.createTextVNode(" 开票金额 "),e.createElementVNode("span",_o,e.toDisplayString(e.unref(i)(t.invoiceDetail.invoiceAmount).toFixed(2)),1)]),e.createElementVNode("div",fo,[e.createTextVNode(" 订单号： "),e.createElementVNode("span",null,e.toDisplayString(t.invoiceDetail.orderNo),1)])]),ho,e.createElementVNode("div",go,[uo,t.invoiceDetail.invoiceStatus!=="START"?(e.openBlock(),e.createBlock(k,{key:0,"model-value":t.invoiceDetail.billingRemarks,rows:2,style:{"margin-left":"10px"},disabled:"",type:"textarea",maxlength:"100"},null,8,["model-value"])):(e.openBlock(),e.createBlock(k,{key:1,modelValue:e.unref(n).billingRemarks,"onUpdate:modelValue":l[0]||(l[0]=f=>e.unref(n).billingRemarks=f),rows:2,style:{"margin-left":"10px"},type:"textarea",maxlength:"100",placeholder:"选填"},null,8,["modelValue"]))]),e.createElementVNode("div",yo,[e.createElementVNode("div",null,[xo,t.invoiceDetail.invoiceStatus==="START"?(e.openBlock(),e.createBlock(p,{key:0,modelValue:e.unref(n).invoiceType,"onUpdate:modelValue":l[1]||(l[1]=f=>e.unref(n).invoiceType=f)},{default:e.withCtx(()=>[t.invoiceSetType==="VAT_GENERAL"||t.invoiceSetType==="VAT_COMBINED"?(e.openBlock(),e.createBlock(m,{key:0,label:"VAT_GENERAL"},{default:e.withCtx(()=>[e.createTextVNode("增值税电子普通发票")]),_:1})):e.createCommentVNode("",!0),t.invoiceSetType==="VAT_SPECIAL"||t.invoiceSetType==="VAT_COMBINED"?(e.openBlock(),e.createBlock(m,{key:1,label:"VAT_SPECIAL"},{default:e.withCtx(()=>[e.createTextVNode("增值税电子专用发票")]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["modelValue"])):(e.openBlock(),e.createElementBlock("span",No,e.toDisplayString(e.unref(co)[t.invoiceDetail.invoiceType]),1))]),t.invoiceDetail.invoiceStatus==="START"?(e.openBlock(),e.createElementBlock("div",Vo,[bo,e.createVNode(x,{modelValue:e.unref(n).invoiceHeaderId,"onUpdate:modelValue":l[2]||(l[2]=f=>e.unref(n).invoiceHeaderId=f),placeholder:"请选择抬头",size:"small"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(_.value,f=>(e.openBlock(),e.createBlock(r,{key:f.id,label:f.header,value:f.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])):e.createCommentVNode("",!0)]),e.createElementVNode("div",Co,[e.createElementVNode("div",So,[e.createTextVNode(" 抬头类型： "),e.createElementVNode("span",null,e.toDisplayString(((y=c.value)==null?void 0:y.invoiceHeaderType)&&e.unref(so)[c.value.invoiceHeaderType]),1)]),e.createElementVNode("div",ko,"发票抬头： "+e.toDisplayString((T=c.value)==null?void 0:T.header),1),e.createElementVNode("div",Eo,"税号："+e.toDisplayString((w=c.value)==null?void 0:w.taxIdentNo),1)]),e.createElementVNode("div",To,[e.createElementVNode("div",wo,"开户行："+e.toDisplayString((I=c.value)==null?void 0:I.openingBank),1),e.createElementVNode("div",Io,"银行账号："+e.toDisplayString((C=c.value)==null?void 0:C.bankAccountNo),1),e.createElementVNode("div",Do,"企业电话："+e.toDisplayString((P=c.value)==null?void 0:P.enterprisePhone),1)]),e.createElementVNode("div",Bo,[e.createElementVNode("div",$o,"邮箱地址："+e.toDisplayString((R=c.value)==null?void 0:R.email),1),e.createElementVNode("div",Po,"企业地址："+e.toDisplayString((g=c.value)==null?void 0:g.enterpriseAddress),1)]),(V=t.invoiceDetail)!=null&&V.denialReason?(e.openBlock(),e.createElementBlock("div",Lo,[e.createElementVNode("div",null,"拒绝原因："+e.toDisplayString((h=t.invoiceDetail)==null?void 0:h.denialReason),1)])):e.createCommentVNode("",!0)])}}}),vn="",Ro=G(Oo,[["__scopeId","data-v-023b6aef"]]),Mo=t=>{const o=e.ref(""),a=e.ref([]),n=e.ref([]),i=(m,p)=>{n.value=p.map(r=>r.id)},_=e.computed(()=>m=>{var r,x,y,T,w;let p=((x=(r=m==null?void 0:m.skus)==null?void 0:r[0])==null?void 0:x.num)-((T=(y=m==null?void 0:m.skus)==null?void 0:y[0])==null?void 0:T.used);return(w=m==null?void 0:m.skus)==null||w.forEach(I=>{const C=(I==null?void 0:I.num)-(I==null?void 0:I.used);C<p&&(p=C)}),p}),c=(m,p)=>{var r;if(typeof p=="number"){const x=a.value.findIndex(y=>y.productId===m);x>-1&&((r=a.value[x])==null||r.skus.forEach(y=>y.inStorageNum=p))}},d=e.computed(()=>m=>{const p=a.value.findIndex(r=>r.productId===m);return p>-1?a.value[p].skus.reduce((x,y)=>x+Number(y.inStorageNum),0):0});return{remark:o,tableData:a,expandRowKeys:n,expandOpen:i,maxRowBatchNum:_,changeRowBatchNum:c,computedActualNum:d,handleRemoveCommodity:m=>{O.ElMessageBox.confirm("确认移除当前商品信息").then(()=>{a.value.splice(m,1)})},handleRemoveSku:(m,p)=>{O.ElMessageBox.confirm("确认移除当前规格信息").then(()=>{var r,x,y;(y=(x=(r=a.value)==null?void 0:r[m])==null?void 0:x.skus)==null||y.splice(p,1)})},initialInstorageData:async()=>{var p,r;const m=await xe(t.orderNo);m.data&&(o.value=(p=m==null?void 0:m.data)==null?void 0:p.remark,a.value=(r=m.data)==null?void 0:r.products)}}},Ao={class:"batch"},Uo={class:"batch__form"},Fo={class:"batch__commodity"},zo=["src"],Ho={class:"batch__commodity--name"},jo=e.defineComponent({__name:"in-storage",props:{orderNo:{default:""}},setup(t,{expose:o}){const a=t,{remark:n,tableData:i,expandRowKeys:_,expandOpen:c,maxRowBatchNum:d,changeRowBatchNum:s,computedActualNum:l,handleRemoveCommodity:k,handleRemoveSku:m,initialInstorageData:p}=Mo(a);return e.onMounted(()=>p()),o({tableData:i,remark:n}),(r,x)=>{const y=e.resolveComponent("el-input"),T=e.resolveComponent("el-form-item"),w=e.resolveComponent("el-form"),I=e.resolveComponent("el-input-number"),C=e.resolveComponent("el-table-column"),P=e.resolveComponent("el-link"),R=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",Ao,[e.createVNode(w,{"show-message":!1,class:"purchase__remark"},{default:e.withCtx(()=>[e.createVNode(T,{label:"入库备注"},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:e.unref(n),"onUpdate:modelValue":x[0]||(x[0]=g=>e.isRef(n)?n.value=g:null),placeholder:"请输入入库备注",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(R,{data:e.unref(i),"expand-row-keys":e.unref(_),"row-key":"id",onExpandChange:e.unref(c)},{default:e.withCtx(()=>[e.createVNode(C,{type:"expand"},{default:e.withCtx(({row:g,$index:V})=>[e.createElementVNode("div",Uo,[e.createVNode(w,null,{default:e.withCtx(()=>[e.createVNode(T,{label:"入库数(批量)"},{default:e.withCtx(()=>[e.createVNode(I,{modelValue:g.batchNum,"onUpdate:modelValue":h=>g.batchNum=h,max:e.unref(d)(g),min:0,precision:0,onChange:h=>e.unref(s)(g.productId,h)},null,8,["modelValue","onUpdate:modelValue","max","onChange"])]),_:2},1024)]),_:2},1024)]),e.createVNode(R,{data:g.skus,"row-key":"id"},{default:e.withCtx(()=>[e.createVNode(C,{label:"商品规格"},{default:e.withCtx(({row:h})=>{var f;return[e.createTextVNode(e.toDisplayString((f=h==null?void 0:h.specs)==null?void 0:f.join(";")),1)]}),_:1}),e.createVNode(C,{label:"采购数",prop:"num",width:"80"}),e.createVNode(C,{label:"已入库数",prop:"used",width:"100"}),e.createVNode(C,{label:"剩余入库数",width:"100"},{default:e.withCtx(({row:h})=>[e.createTextVNode(e.toDisplayString((h==null?void 0:h.num)-(h==null?void 0:h.used)),1)]),_:1}),e.createVNode(C,{label:"实际入库数（本次）",prop:"inStorageNum",width:"180"},{default:e.withCtx(({row:h})=>[e.createVNode(I,{modelValue:h.inStorageNum,"onUpdate:modelValue":f=>h.inStorageNum=f,max:(h==null?void 0:h.num)-(h==null?void 0:h.used),min:0,precision:0},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),e.createVNode(C,{fixed:"right",label:"操作",width:"80"},{default:e.withCtx(({$index:h})=>[e.createVNode(P,{type:"danger",onClick:f=>e.unref(m)(V,h)},{default:e.withCtx(()=>[e.createTextVNode("移出")]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])]),_:1}),e.createVNode(C,{label:"商品名称",width:"250"},{default:e.withCtx(({row:g})=>[e.createElementVNode("div",Fo,[e.createElementVNode("img",{src:g.image},null,8,zo),e.createElementVNode("span",Ho,e.toDisplayString(g.productName),1)])]),_:1}),e.createVNode(C,{label:"采购数"},{default:e.withCtx(({row:g})=>{var V;return[e.createTextVNode(e.toDisplayString((V=g==null?void 0:g.skus)==null?void 0:V.reduce((h,f)=>h+f.num,0)),1)]}),_:1}),e.createVNode(C,{label:"已入库数"},{default:e.withCtx(({row:g})=>{var V;return[e.createTextVNode(e.toDisplayString((V=g==null?void 0:g.skus)==null?void 0:V.reduce((h,f)=>h+f.used,0)),1)]}),_:1}),e.createVNode(C,{label:"剩余入库数"},{default:e.withCtx(({row:g})=>{var V,h;return[e.createTextVNode(e.toDisplayString(((V=g==null?void 0:g.skus)==null?void 0:V.reduce((f,b)=>f+b.num,0))-((h=g==null?void 0:g.skus)==null?void 0:h.reduce((f,b)=>f+b.used,0))),1)]}),_:1}),e.createVNode(C,{label:"实际入库数（本次）"},{default:e.withCtx(({row:g})=>[e.createTextVNode(e.toDisplayString(e.unref(l)(g.id)),1)]),_:1}),e.createVNode(C,{label:"操作"},{default:e.withCtx(({$index:g})=>[e.createVNode(P,{type:"danger",onClick:V=>e.unref(k)(g)},{default:e.withCtx(()=>[e.createTextVNode("移出")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","expand-row-keys","onExpandChange"])])}}}),tl="",Go=G(jo,[["__scopeId","data-v-047df9dd"]]),qo=t=>{const o=e.reactive({orderNo:"",supplierId:"",remark:""}),a=e.ref([]);return{tableData:a,extraData:o,initialInstorageData:async()=>{var _;const i=await xe(t.orderNo);i.data&&(a.value=(_=i.data)==null?void 0:_.products,Object.keys(o).forEach(c=>{var s;const d=c;o[d]=(s=i==null?void 0:i.data)==null?void 0:s[c]}))}}},Yo={class:"batch"},Wo={class:"batch__commodity"},Ko=["src"],Qo={class:"batch__commodity--name"},Xo=e.defineComponent({__name:"storage-details",props:{orderNo:{default:""}},setup(t){const o=t,{tableData:a,initialInstorageData:n,extraData:i}=qo(o);return e.onMounted(()=>n()),(_,c)=>{const d=e.resolveComponent("el-form-item"),s=e.resolveComponent("el-form"),l=e.resolveComponent("el-table-column"),k=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",Yo,[e.createVNode(s,{"show-message":!1,class:"purchase__remark"},{default:e.withCtx(()=>[e.createVNode(d,{label:"采购备注"},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(i).remark),1)]),_:1})]),_:1}),e.createVNode(k,{data:e.unref(a),"row-key":"id"},{default:e.withCtx(()=>[e.createVNode(l,{type:"expand"},{default:e.withCtx(({row:m})=>[e.createVNode(k,{data:m.skus,"row-key":"id"},{default:e.withCtx(()=>[e.createVNode(l,{label:"商品规格"},{default:e.withCtx(({row:p})=>{var r;return[e.createTextVNode(e.toDisplayString((r=p==null?void 0:p.specs)==null?void 0:r.join(";")),1)]}),_:1}),e.createVNode(l,{label:"采购数",prop:"num",width:"80"}),e.createVNode(l,{label:"已入库数",prop:"used",width:"100"}),e.createVNode(l,{label:"剩余入库数",width:"100"},{default:e.withCtx(({row:p})=>[e.createTextVNode(e.toDisplayString((p==null?void 0:p.num)-(p==null?void 0:p.used)),1)]),_:1})]),_:2},1032,["data"])]),_:1}),e.createVNode(l,{label:"商品名称",width:"250"},{default:e.withCtx(({row:m})=>[e.createElementVNode("div",Wo,[e.createElementVNode("img",{src:m.image},null,8,Ko),e.createElementVNode("span",Qo,e.toDisplayString(m.productName),1)])]),_:1}),e.createVNode(l,{label:"采购数"},{default:e.withCtx(({row:m})=>{var p;return[e.createTextVNode(e.toDisplayString((p=m==null?void 0:m.skus)==null?void 0:p.reduce((r,x)=>r+x.num,0)),1)]}),_:1}),e.createVNode(l,{label:"已入库数"},{default:e.withCtx(({row:m})=>{var p;return[e.createTextVNode(e.toDisplayString((p=m==null?void 0:m.skus)==null?void 0:p.reduce((r,x)=>r+x.used,0)),1)]}),_:1}),e.createVNode(l,{label:"剩余入库数"},{default:e.withCtx(({row:m})=>{var p,r;return[e.createTextVNode(e.toDisplayString(((p=m==null?void 0:m.skus)==null?void 0:p.reduce((x,y)=>x+y.num,0))-((r=m==null?void 0:m.skus)==null?void 0:r.reduce((x,y)=>x+y.used,0))),1)]}),_:1})]),_:1},8,["data"])])}}}),al="",Jo=G(Xo,[["__scopeId","data-v-da16f025"]]),Zo=()=>{const t=e.ref(""),o=e.ref(!1),a=e.ref(null);return{inStorageOrderNo:t,showInStorageDialog:o,openInStorageDialog:i=>{t.value=i,o.value=!0},inStorageRefs:a}},vo=()=>{const t=e.ref(""),o=e.ref(!1);return{storageDetailsOrderNo:t,showStorageDetailsDialog:o,openStorageDetailsDialog:n=>{t.value=n,o.value=!0}}},ea=["近一个月订单","近三个月订单","全部订单"],ta={START:{title:"申请开票",btnConfig:{text:"",type:""},describe:""},SUCCESSFULLY_INVOICED:{title:"开票成功",btnConfig:{text:"重新发送发票到邮箱",type:"primary"},describe:"开票成功后，发票将发送至您的邮箱地址，请注意查看邮件~"},REQUEST_IN_PROCESS:{title:"开票中",btnConfig:{text:"撤销开票申请",type:"danger"},describe:"开票成功后，发票将发送至您的邮箱地址，请注意查看邮件~"},FAILED_INVOICE_REQUEST:{title:"开票失败",btnConfig:{text:"重新申请开票",type:"primary"},describe:"开票失败后，您可以重新申请开票~"}},{divTenThousand:oa}=ee(),{toClipboard:aa}=Le(),he=new Pe,na=()=>{const{showInStorageDialog:t,inStorageOrderNo:o,openInStorageDialog:a,inStorageRefs:n}=Zo(),{storageDetailsOrderNo:i,showStorageDetailsDialog:_,openStorageDetailsDialog:c}=vo(),d={OFFLINE:"线下支付",BALANCE:"余额支付"},s=X.useRouter(),l=e.ref(!1),k=e.ref(!1),m=e.ref({applicantId:"",applicantShopId:"",invoiceOwnerType:"SHOP",shopSupplierName:"",applicationTime:"",orderNo:"",invoiceAmount:"",invoiceType:"",invoiceStatus:"START",billingRemarks:"",denialReason:"",invoiceHeaderId:""}),p=e.ref("VAT_COMBINED"),r=e.ref(),x=e.ref(null),y=e.reactive({status:"",startTime:"",endTime:"",purchaser:"",no:""}),T=e.reactive({page:{current:1,size:10},total:0}),w=e.ref(" "),I=e.ref("全部订单"),C=e.ref([]),P=S=>{y.status=S,h()},R=e.computed(()=>S=>ra(S)),g=e.computed(()=>S=>Ee(S)),V=e.computed(()=>S=>sa(S)),h=async()=>{var E,A;let S=[],$=0;try{const Y=await qe({...T.page,...y});S=(E=Y.data)==null?void 0:E.records,$=((A=Y.data)==null?void 0:A.total)||0}finally{C.value=S,T.total=Number($)}},f=S=>{if(w.value=" ",I.value=S,I.value==="近一个月订单"){const $=he.getLastMonth(new Date);D($)}else if(I.value==="近三个月订单"){const $=he.getLastThreeMonth(new Date);D($)}else y.startTime="",y.endTime="",h()},b=S=>{y.startTime=S.startTime,y.endTime=S.endTime,y.purchaser=S.purchaser,y.no=S.no,T.page.current=1,h()},D=async S=>{const $=he.getYMDs(new Date);y.startTime=S,y.endTime=$,h()},B=()=>{switch(m.value.invoiceStatus){case"START":L();break;case"REQUEST_IN_PROCESS":U();break;case"SUCCESSFULLY_INVOICED":q();break;case"FAILED_INVOICE_REQUEST":K();break}},N=()=>{m.value={applicantId:"",applicantShopId:"",invoiceOwnerType:"SHOP",shopSupplierName:"",applicationTime:"",orderNo:"",invoiceAmount:"",invoiceType:"",invoiceStatus:"START",billingRemarks:"",denialReason:"",invoiceHeaderId:""}},L=async()=>{if(!m.value.invoiceHeaderId)return O.ElMessage.error("请选择抬头");if(!m.value.invoiceType)return O.ElMessage.error("请选择发票类型");const{code:S,data:$,msg:E}=await Ke(m.value);if(S!==200)return O.ElMessage.error(E||"申请开票失败");O.ElMessage.success("申请开票成功"),k.value=!1},U=async()=>{const{data:S,code:$,msg:E}=await Je(m.value.id);if($!==200)return O.ElMessage.error(E||"撤销开票失败");O.ElMessage.success("撤销开票成功"),k.value=!1},q=async()=>{const{data:S,code:$,msg:E}=await Ze({invoiceRequestId:m.value.id,shopId:m.value.applicantShopId});if($!==200)return O.ElMessage.error(E||"重发发票失败");O.ElMessage.success("重发发票成功"),k.value=!1},K=async()=>{m.value={...m.value,applicantId:"",applicationTime:"",invoiceType:"",invoiceStatus:"START",billingRemarks:"",denialReason:"",invoiceHeaderId:""},Se().then(S=>{m.value.invoiceHeaderId=S.data.id}).catch(()=>{O.ElMessage.error("获取默认抬头失败")}),Ce({invoiceSettingsClientType:"SUPPLIER",shopId:m.value.applicantShopId}).then(S=>{p.value=S.data.invoiceSetupValue[0].invoiceType}).catch(()=>{O.ElMessage.error("获取发票设置失败")})},J=async(S,$)=>{switch(S){case"cancel":O.ElMessageBox.confirm("请确认是否将订单取消？？").then(async()=>{const{code:E,msg:A}=await Ue($.no);E===200?(O.ElMessage.success({message:A||"取消成功"}),h()):O.ElMessage.error({message:A||"取消失败"})});break;case"details":s.push({path:"/goods/purchase/detail",query:{orderNo:$.no}});break;case"pay":r.value=$,l.value=!0;break;case"finish":O.ElMessageBox.confirm("完成后将无法入库，请确认订单是否已完成？？").then(async()=>{const{code:E,msg:A}=await ze($.no);E===200?(O.ElMessage.success({message:A||"订单入库已完成"}),h()):O.ElMessage.error({message:A||"订单完成失败"})});break;case"inStorage":a($.no);break;case"storageDetails":c($.no);break;case"contact":Ne($.shopId,$.supplierId).then(({code:E})=>{E===200&&s.push({path:"/message/customer/supplierService",query:{id:$.supplierId}})});break;case"invoice":z($);break}},z=async S=>{const{data:$,code:E,msg:A}=await Xe({invoiceOwnerType:"SHOP",applicantId:de.useShopInfoStore().shopInfo.id,applicantShopId:S.supplierId,orderNo:S.no});if(E!==200||!$)return O.ElMessage.error({message:A||"获取开票信息失败"});if($.invoiceStatus==="REQUEST_HAS_EXPIRED")return O.ElMessage.error({message:A||"已超出可开票时间"});if($.invoiceStatus==="SERVER_NOT_SUPPORTED")return O.ElMessage.error({message:A||"供应商不支持开票"});k.value=!0,$.invoiceStatus!=="ALLOWED_INVOICING"?Qe($.id).then(Y=>{m.value=Y.data}).catch(()=>{O.ElMessage.error("获取发票详情失败")}):(m.value.shopSupplierName=S.extraInfo.supplierName,m.value.applicantShopId=S.supplierId,m.value.orderNo=S.no,m.value.invoiceAmount=$.billMoney,Se().then(Y=>{m.value.invoiceHeaderId=Y.data.id}).catch(()=>{O.ElMessage.error("获取默认抬头失败")}),Ce({invoiceSettingsClientType:"SUPPLIER",shopId:S.supplierId}).then(Y=>{p.value=Y.data.invoiceSetupValue[0].invoiceType}).catch(()=>{O.ElMessage.error("获取发票设置失败")}))};return{handleTabChange:P,pagination:T,initOrderList:h,orderDataList:C,handleQuickSearchCommand:f,handleSearch:b,quickSearchTabName:I,quickSearchTabNames:ea,activeTabName:w,getMainOrderStatusText:g,computedBtnList:R,handleDispatchEvent:J,copyOrderNo:async S=>{try{await aa(S),O.ElMessage.success("复制成功")}catch{O.ElMessage.error("复制失败")}},computedCalculateFreight:V,showPayDialog:l,currentRow:r,handlePayOrder:async()=>{var A,Y;const S=await((A=x.value)==null?void 0:A.getPayOrderFormModel()),{code:$,msg:E}=await Ae({...S,orderNo:(Y=r.value)==null?void 0:Y.no});$===200?(O.ElMessage.success("支付成功"),h(),l.value=!1):O.ElMessage.error(E||"支付失败")},payOrderRef:x,payTypeMap:d,showInStorageDialog:t,inStorageOrderNo:o,inStorageRefs:n,storageDetailsOrderNo:i,showStorageDetailsDialog:_,handleConfirmInStorage:async()=>{const S=n.value;if(S){const $={skuStorages:la(S.tableData),remark:S.remark,orderNo:o.value},E=await Fe($);E.code===200?(O.ElMessage.success({message:E.msg||"入库成功"}),t.value=!1,h()):O.ElMessage.error({message:E.msg||"入库失败"})}},showInvoiceDialog:k,InvoiceStatusHander:ta,invoiceDetail:m,handleInvoiceConfig:B,handleCloseInvoiceDialog:N,invoiceSetType:p}},la=t=>{const o=[];return t.forEach(a=>{const n=a.productId;a.skus.forEach(_=>{const c=_.skuId,d=_.inStorageNum||0;o.push({key:{productId:n,skuId:c},value:d})})}),o},Ee=t=>{if(t.status==="UNPAID")return"待支付";if(t.status==="PAYMENT_AUDIT")return"待审核";const o=["WAITING_FOR_DELIVER","WAITING_FOR_RECEIVE","COMPLETED"],a={WAITING_FOR_DELIVER:"待发货",WAITING_FOR_RECEIVE:"待入库",COMPLETED:"已完成"};if(t.status==="PAID"){let n="COMPLETED";for(const _ of t.orderItems){const c=o.findIndex(d=>d===n);if(_.packageStatus==="WAITING_FOR_DELIVER"&&c>0){n="WAITING_FOR_DELIVER";continue}if(_.packageStatus==="WAITING_FOR_RECEIVE"&&c>1){n="WAITING_FOR_RECEIVE";continue}}let i=a[n];return i==="待发货"&&t.orderItems.find(c=>c.packageStatus!=="WAITING_FOR_DELIVER")&&(i="部分发货"),i}return"已关闭"},ra=t=>{const o=[],a=Ee(t);return o.push({action:"details",text:"查看详情",type:"primary"}),o.push({action:"contact",text:"联系卖家",type:"primary"}),a==="待支付"?(o.push({action:"pay",text:"去支付",type:"primary"}),o.push({action:"cancel",text:"取消订单",type:"danger"})):["待入库","部分发货"].includes(a)?(o.push({action:"inStorage",text:"入库",type:"primary"}),o.push({action:"finish",text:"完成",type:"primary"}),o.push({action:"storageDetails",text:"入库详情",type:"primary"})):a==="已完成"&&(o.push({action:"storageDetails",text:"入库详情",type:"primary"}),o.push({action:"invoice",text:"申请开票",type:"primary"})),o},sa=t=>t.reduce((o,a)=>o.plus(new H(oa(a.freightPrice))),new H(0)),Te=t=>(e.pushScopeId("data-v-09d5a239"),t=t(),e.popScopeId(),t),ca=["onClick"],ia={class:"order-table__header"},da={class:"order-table__no"},pa=["onClick"],ma={class:"order-table__freight"},_a={key:0,class:"order-table__pay"},fa={style:{"font-size":"14px"}},ha=Te(()=>e.createElementVNode("span",{style:{"font-size":"12px"}},"￥",-1)),ga={key:1,class:"order-table__pay"},ua={style:{"font-size":"14px"}},ya=Te(()=>e.createElementVNode("span",{style:{"font-size":"12px"}},"￥",-1)),xa={class:"order-table__mode"},Na=["onClick"],Va={class:"order-table__pay-time"},ba={class:"order-table__order-time"},Ca={class:"order-table__commodity"},Sa={style:{flex:"1"}},ka={class:"order-table__commodity--name",style:{"font-weight":"bold"}},Ea={class:"order-table__commodity--name",style:{"margin-top":"10px"}},Ta={class:"order-table__commodity--info"},wa={class:"order-table__amount"},Ia={class:"order-table__amount--price"},Da={style:{color:"#f00","font-size":"14px"}},Ba={class:"order-table__amount--num",style:{"text-align":"center"}},$a={class:"order-table__supplier"},Pa={class:"order-table__actions"},La={class:"dialog-footer"},Oa=["src"],Ra={class:"dialog-footer"},Ma={class:"dialog-footer"},Aa={class:"invoice-title"},Ua=["id"],Fa={class:"invoice-desc"},za={class:"dialog-footer"},Ha=e.defineComponent({__name:"index",setup(t){const{handleTabChange:o,pagination:a,initOrderList:n,orderDataList:i,handleQuickSearchCommand:_,handleSearch:c,quickSearchTabName:d,quickSearchTabNames:s,activeTabName:l,getMainOrderStatusText:k,computedBtnList:m,handleDispatchEvent:p,copyOrderNo:r,computedCalculateFreight:x,showPayDialog:y,showInvoiceDialog:T,handlePayOrder:w,payOrderRef:I,payTypeMap:C,currentRow:P,showInStorageDialog:R,inStorageOrderNo:g,storageDetailsOrderNo:V,showStorageDetailsDialog:h,handleConfirmInStorage:f,inStorageRefs:b,InvoiceStatusHander:D,invoiceDetail:B,handleInvoiceConfig:N,handleCloseInvoiceDialog:L,invoiceSetType:U}=na(),{divTenThousand:q}=ee(),{showProof:K,goToShowProof:J,currentProof:z}=lo(),Z=e.ref("calc(100vh - 350px)"),oe=$=>Jt($,Z),ae=[];return(()=>{Object.keys(ke).forEach($=>{ae.push([$,ke[$]])})})(),n(),($,E)=>{const A=e.resolveComponent("i-ep-arrow-down"),Y=e.resolveComponent("el-icon"),Rn=e.resolveComponent("el-dropdown-item"),Mn=e.resolveComponent("el-dropdown-menu"),An=e.resolveComponent("el-dropdown"),De=e.resolveComponent("el-tab-pane"),Un=e.resolveComponent("el-tabs"),Fn=e.resolveComponent("el-image"),zn=e.resolveComponent("el-link"),Q=e.resolveComponent("el-button"),ne=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(Yt,{onChangeShow:oe,onSearch:e.unref(c)},null,8,["onSearch"]),e.createVNode(Un,{modelValue:e.unref(l),"onUpdate:modelValue":E[0]||(E[0]=u=>e.isRef(l)?l.value=u:null),onTabChange:e.unref(o)},{default:e.withCtx(()=>[e.createVNode(De,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(d)),1),e.createVNode(An,{placement:"bottom-end",trigger:"click",onCommand:e.unref(_)},{dropdown:e.withCtx(()=>[e.createVNode(Mn,null,{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(s),u=>(e.openBlock(),e.createBlock(Rn,{key:u,command:u},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(u),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(Y,{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(A)]),_:1})],8,ca)]),_:1},8,["onCommand"])]),_:1}),(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(ae,u=>e.createVNode(De,{key:u[0],label:u[1],name:u[0]},null,8,["label","name"])),64))]),_:1},8,["modelValue","onTabChange"]),e.createVNode(e.unref(Xt),{class:"order-table",style:e.normalizeStyle({height:Z.value}),data:e.unref(i)},{header:e.withCtx(({row:u})=>{var F,W,le,re,se;return[e.createElementVNode("div",ia,[e.createElementVNode("span",da,[e.createElementVNode("span",null,e.toDisplayString(u.no),1),e.createElementVNode("span",{class:"copy",onClick:Hn=>e.unref(r)(u.no)},"复制",8,pa)]),e.createElementVNode("span",ma,"运费(元)："+e.toDisplayString(e.unref(x)(u==null?void 0:u.orderItems)),1),e.unref(k)(u)==="待发货"||e.unref(k)(u)==="待入库"||e.unref(k)(u)==="已完成"?(e.openBlock(),e.createElementBlock("span",_a,[e.createTextVNode("已付款："),e.createElementVNode("span",fa,[ha,e.createTextVNode(e.toDisplayString(e.unref(q)(u.payAmount)),1)])])):(e.openBlock(),e.createElementBlock("span",ga,[e.createTextVNode("应付款："),e.createElementVNode("span",ua,[ya,e.createTextVNode(e.toDisplayString(e.unref(q)(u.payAmount)),1)])])),e.createElementVNode("span",xa,e.toDisplayString(e.unref(C)[(W=(F=u==null?void 0:u.extra)==null?void 0:F.pay)==null?void 0:W.payType]),1),(re=(le=u==null?void 0:u.extra)==null?void 0:le.pay)!=null&&re.proof?(e.openBlock(),e.createElementBlock("span",{key:2,class:"order-table__proof",onClick:Hn=>e.unref(J)(u)},"付款凭证",8,Na)):e.createCommentVNode("",!0),e.createElementVNode("span",Va,"支付："+e.toDisplayString((se=u==null?void 0:u.timeNodes)==null?void 0:se.payTime),1),e.createElementVNode("span",ba,"下单："+e.toDisplayString(u.createTime),1)])]}),default:e.withCtx(()=>[e.createVNode(te,{label:"商品",width:"280px"},{default:e.withCtx(({shopOrderItems:u})=>{var F,W,le,re,se;return[e.createElementVNode("div",Ca,[e.createVNode(Fn,{style:{width:"63px",height:"63px"},fits:"cover",src:(F=u==null?void 0:u[0])==null?void 0:F.image},null,8,["src"]),e.createElementVNode("div",Sa,[e.createElementVNode("span",ka,e.toDisplayString((W=u==null?void 0:u[0])==null?void 0:W.productName),1),e.createElementVNode("span",Ea,e.toDisplayString((le=u==null?void 0:u[0])==null?void 0:le.specs.join(",")),1)]),e.createElementVNode("div",Ta,[e.createElementVNode("span",null,"￥"+e.toDisplayString(e.unref(q)((re=u==null?void 0:u[0])==null?void 0:re.salePrice)),1),e.createElementVNode("span",null,e.toDisplayString((se=u==null?void 0:u[0])==null?void 0:se.num)+" 件",1)])])]}),_:1}),e.createVNode(te,{label:"采购金额",width:"150px","is-mixed":!0},{default:e.withCtx(({row:u})=>[e.createElementVNode("div",wa,[e.createElementVNode("div",Ia,[e.createTextVNode(" ￥"),e.createElementVNode("span",Da,e.toDisplayString(e.unref(q)(u.payAmount)),1)]),e.createElementVNode("div",Ba," 共 "+e.toDisplayString(u.orderItems.reduce((F,W)=>F+W.num,0))+" 件 ",1)])]),_:1}),e.createVNode(te,{label:"供应商",width:"150px","is-mixed":!0},{default:e.withCtx(({row:u})=>{var F,W;return[e.createElementVNode("div",$a,[e.createElementVNode("span",null,e.toDisplayString((F=u==null?void 0:u.extraInfo)==null?void 0:F.supplierName),1),e.createElementVNode("span",null,e.toDisplayString((W=u==null?void 0:u.extraInfo)==null?void 0:W.supplierPhone),1)])]}),_:1}),e.createVNode(te,{label:"订单状态",width:"150px","is-mixed":!0},{default:e.withCtx(({row:u})=>{var F;return[e.createElementVNode("span",{class:e.normalizeClass({"text-red":e.unref(k)(u)==="待支付"})},[e.createTextVNode(e.toDisplayString(e.unref(k)(u))+" ",1),e.unref(k)(u)==="待支付"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createTextVNode(" ("),e.createVNode(no,{"create-time":u==null?void 0:u.createTime,"pay-timeout":(F=u==null?void 0:u.extra)==null?void 0:F.payTimeout},null,8,["create-time","pay-timeout"]),e.createTextVNode(") ")],64)):e.createCommentVNode("",!0)],2)]}),_:1}),e.createVNode(te,{label:"操作",width:"150px","is-mixed":!0},{default:e.withCtx(({row:u})=>[e.createElementVNode("div",Pa,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(m)(u),F=>(e.openBlock(),e.createBlock(zn,{key:F.action,style:{margin:"0 6px 6px"},type:F.type,onClick:W=>e.unref(p)(F.action,u)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(F.text),1)]),_:2},1032,["type","onClick"]))),128))])]),_:1})]),_:1},8,["style","data"]),e.createVNode(ie,{modelValue:e.unref(a).page,"onUpdate:modelValue":E[1]||(E[1]=u=>e.unref(a).page=u),"load-init":"",total:e.unref(a).total,onReload:e.unref(n)},null,8,["modelValue","total","onReload"]),e.createVNode(ne,{modelValue:e.unref(y),"onUpdate:modelValue":E[3]||(E[3]=u=>e.isRef(y)?y.value=u:null),title:"支付订单",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",La,[e.createVNode(Q,{onClick:E[2]||(E[2]=u=>y.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(Q,{type:"primary",onClick:e.unref(w)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1},8,["onClick"])])]),default:e.withCtx(()=>{var u;return[e.createVNode(ro,{ref_key:"payOrderRef",ref:I,price:e.unref(q)((u=e.unref(P))==null?void 0:u.payAmount)},null,8,["price"])]}),_:1},8,["modelValue"]),e.createVNode(ne,{modelValue:e.unref(K),"onUpdate:modelValue":E[6]||(E[6]=u=>e.isRef(K)?K.value=u:null),title:"付款凭证",width:"500px"},{footer:e.withCtx(()=>[e.createElementVNode("span",Ra,[e.createVNode(Q,{onClick:E[4]||(E[4]=u=>K.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(Q,{type:"primary",onClick:E[5]||(E[5]=u=>K.value=!1)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("img",{src:e.unref(z),class:"proof-img"},null,8,Oa)]),_:1},8,["modelValue"]),e.createVNode(ne,{modelValue:e.unref(R),"onUpdate:modelValue":E[8]||(E[8]=u=>e.isRef(R)?R.value=u:null),title:"入库",width:"1150px"},{footer:e.withCtx(()=>[e.createElementVNode("span",Ma,[e.createVNode(Q,{onClick:E[7]||(E[7]=u=>R.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(Q,{type:"primary",onClick:e.unref(f)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1},8,["onClick"])])]),default:e.withCtx(()=>[e.unref(R)?(e.openBlock(),e.createBlock(Go,{key:0,ref_key:"inStorageRefs",ref:b,"order-no":e.unref(g)},null,8,["order-no"])):e.createCommentVNode("",!0)]),_:1},8,["modelValue"]),e.createVNode(ne,{modelValue:e.unref(h),"onUpdate:modelValue":E[9]||(E[9]=u=>e.isRef(h)?h.value=u:null),title:"入库详情",width:"1150px"},{default:e.withCtx(()=>[e.createVNode(Jo,{"order-no":e.unref(V)},null,8,["order-no"])]),_:1},8,["modelValue"]),e.createVNode(ne,{modelValue:e.unref(T),"onUpdate:modelValue":E[11]||(E[11]=u=>e.isRef(T)?T.value=u:null),width:"1200px",center:"",onClose:e.unref(L)},{header:e.withCtx(({titleId:u,titleClass:F})=>[e.createElementVNode("div",Aa,[e.createElementVNode("h5",{id:u,class:e.normalizeClass(F)},e.toDisplayString(e.unref(D)[e.unref(B).invoiceStatus].title),11,Ua)]),e.createElementVNode("div",Fa,e.toDisplayString(e.unref(D)[e.unref(B).invoiceStatus].describe),1)]),footer:e.withCtx(()=>[e.createElementVNode("span",za,[e.unref(B).invoiceStatus==="START"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(Q,{onClick:E[10]||(E[10]=u=>T.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(Q,{type:"primary",onClick:e.unref(N)},{default:e.withCtx(()=>[e.createTextVNode(" 确认 ")]),_:1},8,["onClick"])],64)):(e.openBlock(),e.createBlock(Q,{key:1,type:e.unref(D)[e.unref(B).invoiceStatus].btnConfig.type,onClick:e.unref(N)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(D)[e.unref(B).invoiceStatus].btnConfig.text),1)]),_:1},8,["type","onClick"]))])]),default:e.withCtx(()=>[e.createVNode(Ro,{"invoice-detail":e.unref(B),invoiceSetType:e.unref(U)},null,8,["invoice-detail","invoiceSetType"])]),_:1},8,["modelValue","onClose"])],64)}}}),ll="",ja=Object.freeze(Object.defineProperty({__proto__:null,default:G(Ha,[["__scopeId","data-v-09d5a239"]])},Symbol.toStringTag,{value:"Module"})),Ga=()=>{const t=e.ref(!1),o=e.ref([]),a=e.ref([]),n=e.reactive({supplierId:"",productName:"",categoryId:""});async function i(){const{data:c,code:d}=await fe();a.value=c}const _=async(c="")=>{const d=await pe({supplierName:c});o.value=(d==null?void 0:d.data)||[]};return e.onMounted(()=>{i()}),{isShow:t,searchType:n,supplierList:o,platformCategoryList:a,fetchSupplierList:_}},qa={style:{background:"#f9f9f9"}},Ya=e.defineComponent({__name:"search",emits:["changeShow","search"],setup(t,{emit:o}){const{isShow:a,searchType:n,supplierList:i,platformCategoryList:_,fetchSupplierList:c}=Ga();e.watch(()=>a.value,s=>{o("changeShow",s)});const d=()=>{const s=v.cloneDeep(n);s.categoryId=Array.isArray(s.categoryId)?s.categoryId.pop():"",o("search",s)};return(s,l)=>{const k=e.resolveComponent("el-option"),m=e.resolveComponent("el-select"),p=e.resolveComponent("el-form-item"),r=e.resolveComponent("el-col"),x=e.resolveComponent("el-cascader"),y=e.resolveComponent("el-input"),T=e.resolveComponent("el-row"),w=e.resolveComponent("el-button"),I=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",qa,[e.createVNode(ce,{modelValue:e.unref(a),"onUpdate:modelValue":l[3]||(l[3]=C=>e.isRef(a)?a.value=C:null)},{default:e.withCtx(()=>[e.createVNode(I,{ref:"form",model:e.unref(n),"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(T,null,{default:e.withCtx(()=>[e.createVNode(r,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"供应商"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:e.unref(n).supplierId,"onUpdate:modelValue":l[0]||(l[0]=C=>e.unref(n).supplierId=C),remote:"",filterable:"",clearable:"","remote-method":e.unref(c),placeholder:"请选择供应商"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(i),C=>(e.openBlock(),e.createBlock(k,{key:C.id,value:C.id,label:C.name},null,8,["value","label"]))),128))]),_:1},8,["modelValue","remote-method"])]),_:1})]),_:1}),e.createVNode(r,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"平台类目"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:e.unref(n).categoryId,"onUpdate:modelValue":l[1]||(l[1]=C=>e.unref(n).categoryId=C),clearable:"",style:{width:"62.5%"},options:e.unref(_),placeholder:"请选择平台类目","show-all-levels":"",props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"}},null,8,["modelValue","options"])]),_:1})]),_:1}),e.createVNode(r,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"商品名称"},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:e.unref(n).productName,"onUpdate:modelValue":l[2]||(l[2]=C=>e.unref(n).productName=C),placeholder:"请输入商品名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(p,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(w,{class:"from_btn",type:"primary",round:"",onClick:d},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Wa=()=>{const t=e.ref("calc(100vh - 300px)");return{tableHeight:t,changeSearchBarShow:a=>{t.value=a?"calc(100vh - 400px)":"calc(100vh - 300px)"}}},Ka=()=>{const t=e.reactive({supplierId:"",categoryId:"",productName:""}),o=e.reactive({page:{current:1,size:10},total:0}),a=e.ref([]),n=async()=>{const{code:c,data:d}=await He({...o.page,...t});c===200&&d&&(a.value=(d==null?void 0:d.records)||[],o.total=Number(d==null?void 0:d.total))},i=c=>{Object.keys(c).forEach(d=>{const s=d;t[s]=c[d]}),o.page.current=1,n()},_=e.computed(()=>c=>{const d=((c==null?void 0:c.prices)||[]).map(k=>Number(k)),s=Math.min(...d),l=Math.max(...d);return s===l?l/1e4:`${s/1e4}~${l/1e4}`});return{handleSearch:i,waitingPublishData:a,pagination:o,initData:n,computedSalePrice:_}},we=t=>(e.pushScopeId("data-v-1ce42707"),t=t(),e.popScopeId(),t),Qa={class:"good"},Xa={class:"good__info"},Ja={class:"good__info"},Za={class:"good__img"},va=we(()=>e.createElementVNode("text",null,"商品图片：",-1)),en=we(()=>e.createElementVNode("div",null,"规格：",-1)),tn=e.defineComponent({__name:"preview",props:{currentProduct:{type:Object,default:()=>({})}},setup(t){const o=t,{divTenThousand:a}=ee(),n=e.ref([]),i=e.ref([]),_=async()=>{const{data:c}=await Ve(de.useShopInfoStore().shopInfo.id,o.currentProduct.productId);n.value=(c==null?void 0:c.skus)||[],i.value=(c==null?void 0:c.specGroups)||[]};return e.onMounted(()=>_()),(c,d)=>{var m,p;const s=e.resolveComponent("el-image"),l=e.resolveComponent("el-table-column"),k=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",Qa,[e.createElementVNode("div",Xa,"店铺名称："+e.toDisplayString((m=o.currentProduct)==null?void 0:m.supplierName),1),e.createElementVNode("div",Ja,"商品名称："+e.toDisplayString((p=o.currentProduct)==null?void 0:p.productName),1),e.createElementVNode("div",Za,[va,e.createVNode(s,{"preview-src-list":[o.currentProduct.image],src:o.currentProduct.image,style:{width:"100px",height:"100px"}},null,8,["preview-src-list","src"])]),en,e.createVNode(k,{data:n.value,"header-row-style":{"font-size":"12px",color:"#000000"},height:"350",stripe:""},{default:e.withCtx(()=>[i.value.length?(e.openBlock(),e.createBlock(l,{key:0,align:"center",label:"规格"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString((r==null?void 0:r.specs)&&(r==null?void 0:r.specs.join("-"))),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(l,{align:"center",label:"sku图"},{default:e.withCtx(({row:r})=>[e.createVNode(s,{src:r==null?void 0:r.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e.createVNode(l,{align:"center",label:"实售价(元)"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(r.salePrice&&e.unref(a)(r.salePrice)),1)]),_:1}),e.createVNode(l,{align:"center",label:"指导价(元)",prop:"originalPrice"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(r.price&&e.unref(a)(r.price)),1)]),_:1}),e.createVNode(l,{align:"center",label:"重量(kg)",prop:"weight"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(r.weight),1)]),_:1})]),_:1},8,["data"])])}}}),dl="",on=G(tn,[["__scopeId","data-v-1ce42707"]]),an=()=>{const t=e.ref(),o=e.ref(!1);return{currentRow:t,showPreviewDialog:o,openPreviewDialog:n=>{t.value=n,o.value=!0}}},nn={class:"commodity-info"},ln=["src"],rn=e.defineComponent({__name:"index",setup(t){const{tableHeight:o,changeSearchBarShow:a}=Wa(),{handleSearch:n,waitingPublishData:i,pagination:_,initData:c,computedSalePrice:d}=Ka(),{currentRow:s,showPreviewDialog:l,openPreviewDialog:k}=an(),m=X.useRouter(),p=r=>{m.push({path:"/goods/purchase/release",query:{id:r==null?void 0:r.productId,supplierId:r==null?void 0:r.supplierId}})};return(r,x)=>{const y=e.resolveComponent("el-button"),T=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(Ya,{onSearch:e.unref(n),onChangeShow:e.unref(a)},null,8,["onSearch","onChangeShow"]),e.createVNode(e.unref(_e),{data:e.unref(i),style:e.normalizeStyle({height:e.unref(o),overflowY:"auto"})},{default:e.withCtx(()=>[e.createVNode(j,{label:"供应商",prop:"supplierName",width:"120"}),e.createVNode(j,{label:"商品名称"},{default:e.withCtx(({row:w})=>[e.createElementVNode("div",nn,[e.createElementVNode("img",{src:w==null?void 0:w.image},null,8,ln),e.createElementVNode("span",null,e.toDisplayString(w==null?void 0:w.productName),1)])]),_:1}),e.createVNode(j,{align:"center",label:"供货价",width:"150"},{default:e.withCtx(({row:w})=>[e.createTextVNode(" ￥"+e.toDisplayString(e.unref(d)(w)),1)]),_:1}),e.createVNode(j,{align:"center",label:"采购数",prop:"num",width:"100"}),e.createVNode(j,{fixed:"right",label:"操作",width:"120"},{default:e.withCtx(({row:w})=>[e.createElementVNode("div",null,[e.createVNode(y,{link:"",size:"small",type:"primary",onClick:I=>e.unref(k)(w)},{default:e.withCtx(()=>[e.createTextVNode("查看")]),_:2},1032,["onClick"]),e.createVNode(y,{link:"",size:"small",type:"primary",onClick:I=>p(w)},{default:e.withCtx(()=>[e.createTextVNode("发布")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data","style"]),e.createVNode(ie,{modelValue:e.unref(_).page,"onUpdate:modelValue":x[0]||(x[0]=w=>e.unref(_).page=w),total:e.unref(_).total,"load-init":"",onReload:e.unref(c)},null,8,["modelValue","total","onReload"]),e.createVNode(T,{modelValue:e.unref(l),"onUpdate:modelValue":x[1]||(x[1]=w=>e.isRef(l)?l.value=w:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"查看",width:"1000px"},{default:e.withCtx(()=>[e.createVNode(on,{"current-product":e.unref(s)},null,8,["current-product"])]),_:1},8,["modelValue"])],64)}}}),ml="",sn=Object.freeze(Object.defineProperty({__proto__:null,default:G(rn,[["__scopeId","data-v-c6c58e29"]])},Symbol.toStringTag,{value:"Module"})),cn=()=>{const t=e.ref(!1),o=e.ref([]),a=e.ref([]),n=e.reactive({supplierId:"",productName:"",shopCategoryId:""});async function i(){const{data:c,code:d}=await fe();a.value=c}const _=async(c="")=>{var s;const d=await pe({supplierName:c});d.data&&((s=d.data)!=null&&s.length)&&(o.value=d.data)};return e.onMounted(()=>{i()}),{isShow:t,searchType:n,supplierList:o,platformCategoryList:a,fetchSupplierList:_}},dn={style:{background:"#f9f9f9"}},pn=e.defineComponent({__name:"search",emits:["changeShow","search"],setup(t,{emit:o}){const{isShow:a,searchType:n,supplierList:i,platformCategoryList:_,fetchSupplierList:c}=cn();e.watch(()=>a.value,s=>{o("changeShow",s)});const d=()=>{const s=v.cloneDeep(n);s.shopCategoryId=Array.isArray(s.shopCategoryId)?s.shopCategoryId.pop():"",o("search",s)};return(s,l)=>{const k=e.resolveComponent("el-option"),m=e.resolveComponent("el-select"),p=e.resolveComponent("el-form-item"),r=e.resolveComponent("el-col"),x=e.resolveComponent("el-cascader"),y=e.resolveComponent("el-input"),T=e.resolveComponent("el-row"),w=e.resolveComponent("el-button"),I=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",dn,[e.createVNode(ce,{modelValue:e.unref(a),"onUpdate:modelValue":l[3]||(l[3]=C=>e.isRef(a)?a.value=C:null)},{default:e.withCtx(()=>[e.createVNode(I,{ref:"form",model:e.unref(n),"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(T,null,{default:e.withCtx(()=>[e.createVNode(r,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"供应商"},{default:e.withCtx(()=>[e.createVNode(m,{modelValue:e.unref(n).supplierId,"onUpdate:modelValue":l[0]||(l[0]=C=>e.unref(n).supplierId=C),remote:"",filterable:"",clearable:"","remote-method":e.unref(c),placeholder:"请选择供应商"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(i),C=>(e.openBlock(),e.createBlock(k,{key:C.id,value:C.id,label:C.name},null,8,["value","label"]))),128))]),_:1},8,["modelValue","remote-method"])]),_:1})]),_:1}),e.createVNode(r,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"平台类目"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:e.unref(n).shopCategoryId,"onUpdate:modelValue":l[1]||(l[1]=C=>e.unref(n).shopCategoryId=C),clearable:"",style:{width:"62.5%"},options:e.unref(_),placeholder:"请选择平台类目","show-all-levels":"",props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"}},null,8,["modelValue","options"])]),_:1})]),_:1}),e.createVNode(r,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{label:"商品名称"},{default:e.withCtx(()=>[e.createVNode(y,{modelValue:e.unref(n).productName,"onUpdate:modelValue":l[2]||(l[2]=C=>e.unref(n).productName=C),placeholder:"请输入商品名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(p,{style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(w,{class:"from_btn",type:"primary",round:"",onClick:d},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),mn=()=>{const t=e.ref(),o=e.ref(!1);return{currentRow:t,showPreviewDialog:o,openPreviewDialog:n=>{t.value=n,o.value=!0}}},_n=()=>{const t=e.ref("calc(100vh - 300px)");return{tableHeight:t,changeSearchBarShow:a=>{t.value=a?"calc(100vh - 400px)":"calc(100vh - 300px)"}}},fn=()=>{const t=e.reactive({page:{current:1,size:10},total:0}),o=e.reactive({supplierId:"",productName:"",shopCategoryId:"",status:""}),a=e.ref([]),n=async()=>{let d=[],s=0;try{const l=await je({...t.page,...o});l.code===200&&l.data&&(d=l.data.records,s=Number(l.data.total))}finally{a.value=d,t.total=s}};return{handleSearch:d=>{Object.keys(d).forEach(s=>{const l=s;o[l]=d[l]||""}),t.page.current=1,n()},releaseData:a,pagination:t,initData:n,handleSaleOn:d=>{O.ElMessageBox.confirm("确认上架当前商品").then(()=>{Ge(d).then(({code:s,msg:l})=>{s===200?(O.ElMessage.success({message:l||"上架成功"}),n()):O.ElMessage.error({message:l||"上架失败"})})})},searchType:o,changeStatus:d=>{o.status=d,t.page.current=1,n()}}},Ie=t=>(e.pushScopeId("data-v-1d2d7e08"),t=t(),e.popScopeId(),t),hn={class:"good"},gn={class:"good__info"},un={class:"good__info"},yn={class:"good__img"},xn=Ie(()=>e.createElementVNode("text",null,"商品图片：",-1)),Nn=Ie(()=>e.createElementVNode("div",null,"规格：",-1)),Vn=e.defineComponent({__name:"preview",props:{currentProduct:{type:Object,default:()=>({})}},setup(t){const o=t,{divTenThousand:a}=ee(),n=e.ref([]),i=e.ref([]),_=async()=>{const{data:c}=await Ve(de.useShopInfoStore().shopInfo.id,o.currentProduct.id);n.value=(c==null?void 0:c.skus)||[],i.value=(c==null?void 0:c.specGroups)||[]};return e.onMounted(()=>_()),(c,d)=>{var m,p;const s=e.resolveComponent("el-image"),l=e.resolveComponent("el-table-column"),k=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",hn,[e.createElementVNode("div",gn,"店铺名称："+e.toDisplayString((m=o.currentProduct)==null?void 0:m.supplierName),1),e.createElementVNode("div",un,"商品名称："+e.toDisplayString((p=o.currentProduct)==null?void 0:p.name),1),e.createElementVNode("div",yn,[xn,e.createVNode(s,{"preview-src-list":[o.currentProduct.pic],src:o.currentProduct.pic,style:{width:"100px",height:"100px"}},null,8,["preview-src-list","src"])]),Nn,e.createVNode(k,{data:n.value,"header-row-style":{"font-size":"12px",color:"#000000"},height:"350",stripe:""},{default:e.withCtx(()=>[i.value.length?(e.openBlock(),e.createBlock(l,{key:0,align:"center",label:"规格"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString((r==null?void 0:r.specs)&&(r==null?void 0:r.specs.join("-"))),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(l,{align:"center",label:"sku图"},{default:e.withCtx(({row:r})=>[e.createVNode(s,{src:r==null?void 0:r.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e.createVNode(l,{align:"center",label:"实售价(元)"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(r.salePrice&&e.unref(a)(r.salePrice)),1)]),_:1}),e.createVNode(l,{align:"center",label:"指导价(元)",prop:"originalPrice"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(r.price&&e.unref(a)(r.price)),1)]),_:1}),e.createVNode(l,{align:"center",label:"重量(kg)",prop:"weight"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(r.weight),1)]),_:1})]),_:1},8,["data"])])}}}),yl="",bn=G(Vn,[["__scopeId","data-v-1d2d7e08"]]),Cn=()=>{const t=e.ref(!1),o=e.ref();return{violationReason:o,showViolationReason:t,openViolationReasonDialog:n=>{o.value=n,t.value=!0}}},Sn={style:{"line-height":"30px"}},kn=["src"],En=e.defineComponent({__name:"violation-reason",props:{productViolation:{type:Object,default:()=>({})}},setup(t){const o=t,a={PROHIBITED:"违禁品",COUNTERFEIT:"假冒伪劣",EXCESSIVE_PLATFORM_INTERVENTION:"平台介入率太高",TITLE_IRREGULARITY:"标题有问题",OTHER:"其他"},n=e.computed(()=>{var i;return{...o.productViolation,violationType:a[o.productViolation.violationType],violationEvidence:(i=o.productViolation.violationEvidence)==null?void 0:i.split(",")}});return(i,_)=>{const c=e.resolveComponent("el-col"),d=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",Sn,[e.createVNode(d,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:12},{default:e.withCtx(()=>[e.createElementVNode("div",null,"检查员："+e.toDisplayString(n.value.rummager),1)]),_:1}),e.createVNode(c,{span:12},{default:e.withCtx(()=>[e.createElementVNode("div",null,"检查时间："+e.toDisplayString(n.value.examineDateTime),1)]),_:1})]),_:1}),e.createElementVNode("div",null,"类型："+e.toDisplayString(n.value.violationType),1),e.createElementVNode("div",null,"原因："+e.toDisplayString(n.value.violationExplain),1),e.createElementVNode("div",null,[e.createTextVNode(" 相关证据： "),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value.violationEvidence,(s,l)=>(e.openBlock(),e.createElementBlock("img",{key:l,src:s,class:"violation-evidence"},null,8,kn))),128))])])}}}),Nl="",Tn=G(En,[["__scopeId","data-v-eb8affdf"]]),wn={class:"commodity-info"},In=["src"],Dn={key:0},Bn={key:1},$n={key:2},Pn={key:3},Ln=e.defineComponent({__name:"index",setup(t){const{tableHeight:o,changeSearchBarShow:a}=_n(),{handleSearch:n,releaseData:i,pagination:_,initData:c,handleSaleOn:d,searchType:s,changeStatus:l}=fn(),{currentRow:k,showPreviewDialog:m,openPreviewDialog:p}=mn(),{violationReason:r,showViolationReason:x,openViolationReasonDialog:y}=Cn();return(T,w)=>{const I=e.resolveComponent("el-tab-pane"),C=e.resolveComponent("el-tabs"),P=e.resolveComponent("el-button"),R=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(pn,{onSearch:e.unref(n),onChangeShow:e.unref(a)},null,8,["onSearch","onChangeShow"]),e.createVNode(C,{"model-value":e.unref(s).status,"onUpdate:modelValue":e.unref(l)},{default:e.withCtx(()=>[e.createVNode(I,{label:"全部",name:""}),e.createVNode(I,{label:"已上架",name:"SELL_ON"}),e.createVNode(I,{label:"已下架",name:"SELL_OFF"}),e.createVNode(I,{label:"违规下架",name:"PLATFORM_SELL_OFF"})]),_:1},8,["model-value","onUpdate:modelValue"]),e.createVNode(e.unref(_e),{data:e.unref(i),style:e.normalizeStyle({height:e.unref(o),overflowY:"auto"})},{default:e.withCtx(()=>[e.createVNode(j,{label:"供应商",prop:"supplierName",width:"120"}),e.createVNode(j,{align:"left",label:"商品名称"},{default:e.withCtx(({row:g})=>[e.createElementVNode("div",wn,[e.createElementVNode("img",{src:g==null?void 0:g.pic},null,8,In),e.createElementVNode("span",null,e.toDisplayString(g==null?void 0:g.name),1)])]),_:1}),e.createVNode(j,{label:"库存",width:"150"},{default:e.withCtx(({row:g})=>{var V;return[e.createTextVNode(e.toDisplayString((V=g==null?void 0:g.storageSkus)==null?void 0:V.reduce((h,f)=>h+Number(f.stock),0)),1)]}),_:1}),e.createVNode(j,{align:"center",label:"状态",width:"100"},{default:e.withCtx(({row:g})=>[g!=null&&g.delete?(e.openBlock(),e.createElementBlock("span",Dn,"下架")):(g==null?void 0:g.status)==="SELL_ON"?(e.openBlock(),e.createElementBlock("span",Bn,"已上架")):(g==null?void 0:g.status)==="SELL_OFF"?(e.openBlock(),e.createElementBlock("span",$n,"已下架")):(g==null?void 0:g.status)==="PLATFORM_SELL_OFF"?(e.openBlock(),e.createElementBlock("span",Pn,"违规下架")):e.createCommentVNode("",!0)]),_:1}),e.createVNode(j,{fixed:"right",label:"操作",width:"120"},{default:e.withCtx(({row:g})=>[e.createElementVNode("div",null,[e.createVNode(P,{link:"",size:"small",type:"primary",onClick:V=>e.unref(p)(g)},{default:e.withCtx(()=>[e.createTextVNode("查看")]),_:2},1032,["onClick"]),g!=null&&g.delete||(g==null?void 0:g.status)==="SELL_OFF"?(e.openBlock(),e.createBlock(P,{key:0,link:"",size:"small",type:"primary",onClick:V=>e.unref(d)(g==null?void 0:g.id)},{default:e.withCtx(()=>[e.createTextVNode("上架 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),(g==null?void 0:g.status)==="PLATFORM_SELL_OFF"?(e.openBlock(),e.createBlock(P,{key:1,link:"",size:"small",type:"primary",onClick:V=>{var h;return e.unref(y)((h=g==null?void 0:g.extra)==null?void 0:h.productViolation)}},{default:e.withCtx(()=>[e.createTextVNode("违规原因 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["data","style"]),e.createVNode(ie,{modelValue:e.unref(_).page,"onUpdate:modelValue":w[0]||(w[0]=g=>e.unref(_).page=g),total:e.unref(_).total,"load-init":"",onReload:e.unref(c)},null,8,["modelValue","total","onReload"]),e.createVNode(R,{modelValue:e.unref(m),"onUpdate:modelValue":w[1]||(w[1]=g=>e.isRef(m)?m.value=g:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"查看",width:"1000px"},{default:e.withCtx(()=>[e.createVNode(bn,{"current-product":e.unref(k)},null,8,["current-product"])]),_:1},8,["modelValue"]),e.createVNode(R,{modelValue:e.unref(x),"onUpdate:modelValue":w[2]||(w[2]=g=>e.isRef(x)?x.value=g:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"违规原因",width:"600px"},{default:e.withCtx(()=>[e.createVNode(Tn,{"product-violation":e.unref(r)},null,8,["product-violation"])]),_:1},8,["modelValue"])],64)}}}),Vl="",On=Object.freeze(Object.defineProperty({__proto__:null,default:G(Ln,[["__scopeId","data-v-f0aff17e"]])},Symbol.toStringTag,{value:"Module"}));return Oe});
