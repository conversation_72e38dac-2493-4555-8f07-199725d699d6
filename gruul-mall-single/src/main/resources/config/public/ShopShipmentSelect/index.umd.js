(function(e,n){typeof exports=="object"&&typeof module<"u"?module.exports=n(require("vue"),require("@/components/q-address"),require("@/apis/http"),require("element-china-area-data"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@/components/q-address","@/apis/http","element-china-area-data","element-plus"],n):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopShipmentSelect=n(e.ShopShipmentSelectContext.Vue,e.ShopShipmentSelectContext.QAddressIndex,e.ShopShipmentSelectContext.Request,e.ShopShipmentSelectContext.ElementChinaAreaData,e.ShopShipmentSelectContext.ElementPlus))})(this,function(e,n,x,y,_){"use strict";const g=s=>x.get({url:"gruul-mall-shop/shop/logistics/address/list",params:s}),S=s=>x.get({url:"gruul-mall-freight/logistics/express/usable/list",params:s}),w=e.createElementVNode("div",{class:"send"},"物流服务",-1),u=e.createElementVNode("div",null,"物流单号",-1),V=e.createElementVNode("div",null,"发货地址",-1);return e.defineComponent({__name:"ShopShipmentSelect",props:{properties:{type:Object,default:{}}},setup(s){const a=s,o=e.reactive(a.properties.deliverDialogFormData||{deliverType:"WITHOUT",receiver:{name:"",mobile:"",address:""},expressCompany:"",addressaddress:"",expressNo:""}),r=e.ref([]),f=e.ref([]);k(),N();async function N(){const{code:d,data:l}=await S({size:1e3,current:1});if(d!==200){_.ElMessage.error("获取物流公司失败");return}f.value=l.records,a.properties.loadCompanySelectListData(l.records)}async function k(){const{data:d,code:l}=await g({});l!==200?_.ElMessage({message:"请刷新重试...",type:"warning"}):r.value=d.records,console.log("deliveryAddressData.value",r.value);const i=r.value.find(p=>p.defSend==="YES");o.addressaddress=i.id,a.properties.loadDeliverDialogFormData(o),a.properties.loadDeliveryAddressData(r.value)}const c=()=>{a.properties.loadDeliverDialogFormData(o)};return(d,l)=>{const i=e.resolveComponent("el-option"),p=e.resolveComponent("el-select"),m=e.resolveComponent("el-col"),h=e.resolveComponent("el-row"),C=e.resolveComponent("el-form-item"),D=e.resolveComponent("el-input");return e.openBlock(),e.createElementBlock(e.Fragment,null,[o.deliverType!=="WITHOUT"?(e.openBlock(),e.createBlock(C,{key:0,"label-width":"90px",prop:""},{label:e.withCtx(()=>[w]),default:e.withCtx(()=>[e.createVNode(h,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{span:20},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:o.expressCompany,"onUpdate:modelValue":l[0]||(l[0]=t=>o.expressCompany=t),class:"m-2",placeholder:"请选择物流服务",style:{width:"100%",height:"30px"},onChange:c},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,t=>(e.openBlock(),e.createBlock(i,{key:t.logisticsCompanyName,label:t.logisticsCompanyName,value:t.logisticsCompanyCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0),o.deliverType==="EXPRESS"?(e.openBlock(),e.createBlock(C,{key:1,"label-width":"90px",prop:"expressNo"},{label:e.withCtx(()=>[u]),default:e.withCtx(()=>[e.createVNode(h,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{span:20},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:o.expressNo,"onUpdate:modelValue":l[1]||(l[1]=t=>o.expressNo=t),placeholder:"",style:{width:"100%",height:"30px"},maxlength:"40",onChange:c},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0),o.deliverType==="PRINT_EXPRESS"?(e.openBlock(),e.createBlock(C,{key:2,"label-width":"90px",prop:""},{label:e.withCtx(()=>[V]),default:e.withCtx(()=>[e.createVNode(h,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{span:20},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:o.addressaddress,"onUpdate:modelValue":l[2]||(l[2]=t=>o.addressaddress=t),placeholder:"选择发货地址",style:{width:"100%",height:"30px"},onChange:c},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(r.value,t=>(e.openBlock(),e.createBlock(i,{key:t.id,value:t.id,label:`${e.unref(n.AddressFn)(e.unref(y.regionData),[t.provinceCode,t.cityCode,t.regionCode])}${t.address}`},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0)],64)}}})});
