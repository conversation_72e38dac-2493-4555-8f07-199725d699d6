(function(e,p){typeof exports=="object"&&typeof module<"u"?module.exports=p(require("vue"),require("@/apis/http"),require("@/composables/useConvert"),require("vue-router"),require("@/components/PageManage.vue"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@/apis/http","@/composables/useConvert","vue-router","@/components/PageManage.vue","element-plus"],p):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopHelpInfo=p(e.ShopHelpInfoContext.Vue,e.ShopHelpInfoContext.Request,e.ShopHelpInfoContext.UseConvert,e.ShopHelpInfoContext.VueRouter,e.ShopHelpInfoContext.PageManageTwo,e.ShopHelpInfoContext.ElementPlus))})(this,function(e,p,m,f,u,b){"use strict";var x=document.createElement("style");x.textContent=`.bangain_info_box[data-v-bd65015e]{width:956px;border:1px solid #d5d5d5;padding:25px 20px 0;margin-bottom:10px}.bangain_info_box__item[data-v-bd65015e]{margin-bottom:25px;font-size:14px;color:#333;width:100%}.bargain_sponsor[data-v-bd65015e],.bargain_sponsor__info[data-v-bd65015e],.bargain_origin[data-v-f1ca05c7]{display:flex;justify-content:center;align-items:center;justify-content:flex-start}.bargain_origin--name[data-v-f1ca05c7]{width:120px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.bargain_origin--shop_name[data-v-f1ca05c7]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(x);const N="addon-bargain/bargainOrder/",y=t=>p.get({url:N+`detail/${t}`}),V=t=>p.get({url:"addon-bargain/bargainHelpPeople/",params:t}),C={BARGAINING:"砍价中",FAILED_TO_BARGAIN:"砍价失败",SUCCESSFUL_BARGAIN:"砍价成功"},I={class:"bangain_info_box"},D={class:"bangain_info_box__item bargain_sponsor"},E={class:"bargain_sponsor__info"},S={class:"bargain_origin--name",style:{"margin-left":"10px","font-size":"14px",color:"#515151"}},w={class:"bangain_info_box__item"},T={class:"bangain_info_box__item"},O={class:"bangain_info_box__item"},k={class:"bangain_info_box__item bargain_sponsor"},B={class:"bargain_sponsor__info"},P={class:"bargain_origin--name",style:{"margin-left":"10px","font-size":"14px",color:"#515151"}},H={class:"bangain_info_box__item"},R={class:"bangain_info_box__item"},A={class:"bangain_info_box__item"},z={key:0,class:"bangain_info_box__item"},q=e.defineComponent({__name:"helpInfoTop",props:{bargainOrderDetail:{type:Object,required:!0}},setup(t){const d=f.useRouter(),{divTenThousand:l}=m(),n=o=>{o&&d.push({name:"detailsIndex",query:{orderNo:o}})};return(o,c)=>{var a;const s=e.resolveComponent("el-image"),i=e.resolveComponent("el-link");return e.openBlock(),e.createElementBlock("div",I,[e.createElementVNode("div",D,[e.createTextVNode(" 开团发起人： "),e.createElementVNode("div",E,[e.createVNode(s,{style:{width:"40px",height:"40px"},fit:"",src:(a=t.bargainOrderDetail)==null?void 0:a.userHeadPortrait},null,8,["src"]),e.createElementVNode("div",S,e.toDisplayString(t.bargainOrderDetail.userNickname),1)])]),e.createElementVNode("p",w,"活动名称："+e.toDisplayString(t.bargainOrderDetail.activityName),1),e.createElementVNode("p",T,[e.createTextVNode(" 发起时间："),e.createElementVNode("time",null,e.toDisplayString(t.bargainOrderDetail.publishBargainingTime),1)]),e.createElementVNode("p",O,"砍价人数："+e.toDisplayString(t.bargainOrderDetail.bargainingPeople),1),e.createElementVNode("div",k,[e.createTextVNode(" 砍价商品： "),e.createElementVNode("div",B,[e.createVNode(s,{style:{width:"40px",height:"40px"},fit:"",src:t.bargainOrderDetail.productPic},null,8,["src"]),e.createElementVNode("div",P,e.toDisplayString(t.bargainOrderDetail.productName),1)])]),e.createElementVNode("p",H,"底价："+e.toDisplayString(t.bargainOrderDetail.floorPrice&&e.unref(l)(t.bargainOrderDetail.floorPrice)),1),e.createElementVNode("p",R,[e.createTextVNode(" 结束时间："),e.createElementVNode("time",null,e.toDisplayString(t.bargainOrderDetail.endTime),1)]),e.createElementVNode("p",A,"砍价状态："+e.toDisplayString(e.unref(C)[t.bargainOrderDetail.bargainStatus]),1),t.bargainOrderDetail.orderNo?(e.openBlock(),e.createElementBlock("p",z,[e.createTextVNode(" 订单信息："),e.createVNode(i,{underline:!1,type:"primary",onClick:c[0]||(c[0]=_=>n(t.bargainOrderDetail.orderNo))},{default:e.withCtx(()=>[e.createTextVNode("查看详情")]),_:1})])):e.createCommentVNode("",!0)])}}}),J="",h=(t,d)=>{const l=t.__vccOpts||t;for(const[n,o]of d)l[n]=o;return l},G=h(q,[["__scopeId","data-v-bd65015e"]]),$={class:"bargain_origin"},L={class:"bargain_origin--name",style:{width:"200px","margin-left":"10px"}},M=e.defineComponent({__name:"bargainInfoList",emits:["reload"],setup(t,{expose:d,emit:l}){const n=e.reactive({size:10,current:1,total:0}),o=e.ref([]),{divTenThouMillion:c}=m();async function s(i){const{code:a,data:_,msg:g}=await V({...i,...n});if(a!==200){b.ElMessage.error(g||"查询砍价帮砍列表失败");return}n.current=_.current,n.size=_.size,n.total=_.total,o.value=_.records}return d({getBargainHelpPeople:s}),(i,a)=>{const _=e.resolveComponent("el-image"),g=e.resolveComponent("el-table-column"),U=e.resolveComponent("el-table"),F=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(U,{ref:"multipleTableRef",data:o.value,stripe:"",style:e.normalizeStyle({height:"calc(100vh - 650px)"}),"header-row-style":{fontSize:"12px",color:"#909399"},"header-cell-style":{background:"#f6f8fa"},"cell-style":{fontSize:"12px",color:"#333333"}},{default:e.withCtx(()=>[e.createVNode(g,{label:"帮砍人信息"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",$,[e.createVNode(_,{style:{width:"40px",height:"40px"},fit:"",src:r.userHeadPortrait},null,8,["src"]),e.createElementVNode("div",L,e.toDisplayString(r.userNickName),1)])]),_:1}),e.createVNode(g,{label:"帮砍金额"},{default:e.withCtx(({row:r})=>[e.createElementVNode("span",null,e.toDisplayString(r.helpCutAmount&&e.unref(c)(r.helpCutAmount)),1)]),_:1}),e.createVNode(g,{label:"帮砍时间",width:"200px",align:"center"},{default:e.withCtx(({row:r})=>[e.createElementVNode("time",null,e.toDisplayString(r.helpCutTime),1)]),_:1})]),_:1},8,["data","style"]),e.createVNode(F,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:n,"onUpdate:modelValue":a[0]||(a[0]=r=>n=r),"page-size":n.size,total:n.total,onReload:a[1]||(a[1]=r=>l("reload"))},null,8,["modelValue","page-size","total"])]),_:1})],64)}}}),K="",j=h(M,[["__scopeId","data-v-f1ca05c7"]]);return e.defineComponent({__name:"ShopHelpInfo",setup(t){const d=f.useRoute(),l=e.ref(),n=e.ref({activityId:"",bargainStatus:"BARGAINING",bargainingPeople:0,endTime:"",floorPrice:"",id:"",productId:"",productName:"",productPic:"",publishBargainingTime:"",shopId:"",skuId:"",sponsorId:"",userHeadPortrait:"",userNickname:"",salePrice:"",createTime:"",updateTime:""});o();async function o(){if(d.query.id){const{code:s,data:i,msg:a}=await y(d.query.id+"");if(s!==200){b.ElMessage.error(a||"获取砍价订单详情失败");return}if(!i)return;n.value=i,c()}}function c(){var a;const{id:s,activityId:i}=n.value;(a=l.value)==null||a.getBargainHelpPeople({activityId:i,bargainOrderId:s})}return(s,i)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode(G,{"bargain-order-detail":n.value},null,8,["bargain-order-detail"]),e.createVNode(j,{ref_key:"bargainListRef",ref:l,onReload:c},null,512)]))}})});
