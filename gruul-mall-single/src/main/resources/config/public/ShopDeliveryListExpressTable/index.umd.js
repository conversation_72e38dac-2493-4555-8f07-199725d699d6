(function(e,a){typeof exports=="object"&&typeof module<"u"?module.exports=a(require("vue"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/qszr-core/packages/q-table/QTable","@/composables/useConvert"],a):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopDeliveryListExpressTable=a(e.ShopDeliveryListExpressTableContext.Vue,e.ShopDeliveryListExpressTableContext.QTableColumn,e.ShopDeliveryListExpressTableContext.QTable,e.ShopDeliveryListExpressTableContext.UseConvert))})(this,function(e,a,y,b){"use strict";var m=document.createElement("style");m.textContent=`.orderIndex-table[data-v-d647db28]{margin-bottom:100px}.orderIndex-table__img-box[data-v-d647db28]{width:200px;display:flex;justify-content:space-between}.orderIndex-table__img[data-v-d647db28]{flex-shrink:0;border-radius:5px;position:relative}.orderIndex-table__img-mask[data-v-d647db28]{display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:12px;color:#000}.is-complete[data-v-d647db28]{background:#eef1f6}.header-table[data-v-d647db28]{width:100%;display:flex;justify-content:space-between;align-items:center}.money_text[data-v-d647db28]{font-size:12px;color:#000}
`,document.head.appendChild(m);const f={style:{"margin-right":"36px"}},g={class:"orderIndex-table__img-box"},C={class:"orderIndex-table__img-mask"},V={style:{color:"#838383","font-size":"10px"}},u={class:"avatar_text avatar_text__bottom money_text"},N={style:{color:"#2e99f3","margin-right":"10px"}},k={style:{padding:"0 10px 0"},class:"money_text"},E=e.defineComponent({__name:"ShopDeliveryListExpressTable",props:{properties:{type:Object,default:{}}},setup(p){const s=p,{divTenThousand:d}=b(),l=e.ref(s.properties.expressCompanyMap||new Map);e.watch(()=>s.properties.expressCompanyMap,o=>{o&&(l.value=o)});const c=e.computed(()=>o=>d(o.shopOrders[0].shopOrderItems[0].dealPrice)),D=e.computed(()=>o=>o.shopOrders[0].shopOrderItems.reduce((r,i)=>i.num+r,0));e.watch(()=>s.properties.logisticsCompanyCode,o=>{if(o)for(const r in s.properties.tableData){const i=s.properties.tableData[r].no;l.value.get(i).logisticsCompanyCode=o}});const _=o=>{const r=o.shopOrders[0].orderReceiver;return r||o.orderReceiver};return(o,r)=>{const i=e.resolveComponent("el-button"),x=e.resolveComponent("el-row"),S=e.resolveComponent("el-image"),T=e.resolveComponent("el-option"),q=e.resolveComponent("el-select"),h=e.resolveComponent("el-form-item"),w=e.resolveComponent("el-input");return e.openBlock(),e.createBlock(e.unref(y),{data:s.properties.tableData,class:"orderIndex-table"},{header:e.withCtx(({row:t})=>[e.createElementVNode("div",f,"订单号:"+e.toDisplayString(t.no),1),e.createElementVNode("div",null,"创建时间:"+e.toDisplayString(t.createTime),1),e.createVNode(x,{style:{flex:"1"},justify:"end"},{default:e.withCtx(()=>[e.createVNode(i,{type:"primary",link:"",size:"small",onClick:n=>s.properties.filterOrderList(t)},{default:e.withCtx(()=>[e.createTextVNode("移除")]),_:2},1032,["onClick"])]),_:2},1024)]),default:e.withCtx(()=>[e.createVNode(a,{prop:"name",label:"商品"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",g,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.shopOrders[0].shopOrderItems.slice(0,2),n=>(e.openBlock(),e.createBlock(S,{key:n.id,fits:"cover",style:{width:"63px",height:"63px"},shape:"square",size:"large",src:n.image,title:n.productName},null,8,["src","title"]))),128)),e.createElementVNode("div",C,[e.createElementVNode("span",null,"￥"+e.toDisplayString(c.value(t)),1),e.createElementVNode("span",V,"共"+e.toDisplayString(D.value(t))+"件",1)])])]),_:1}),e.createVNode(a,{prop:"age",label:"客户"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",u,[e.createElementVNode("span",N,"买家昵称 : "+e.toDisplayString(t.buyerNickname),1),e.createElementVNode("div",k," (收货人："+e.toDisplayString(_(t).name)+","+e.toDisplayString(_(t).mobile)+") ",1)])]),_:1}),e.createVNode(a,{prop:"sex",label:"操作",align:"right"},{default:e.withCtx(({row:t})=>[e.createVNode(x,null,{default:e.withCtx(()=>[e.createVNode(h,{label:"物流服务",style:{width:"230px"}},{default:e.withCtx(()=>[e.createVNode(q,{modelValue:l.value.get(t.no).logisticsCompanyCode,"onUpdate:modelValue":n=>l.value.get(t.no).logisticsCompanyCode=n,placeholder:"请选择物流服务"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.properties.companySelectList,n=>(e.openBlock(),e.createBlock(T,{key:n.logisticsCompanyName,label:n.logisticsCompanyName,value:n.logisticsCompanyCode},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),e.createVNode(h,{label:"运单号码",style:{width:"230px"}},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:l.value.get(t.no).expressNo,"onUpdate:modelValue":n=>l.value.get(t.no).expressNo=n,style:{height:"28px"},maxlength:"40"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:1})]),_:1},8,["data"])}}}),L="";return((p,s)=>{const d=p.__vccOpts||p;for(const[l,c]of s)d[l]=c;return d})(E,[["__scopeId","data-v-d647db28"]])});
