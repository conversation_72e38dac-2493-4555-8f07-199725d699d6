tx:
  lbs:
    key: xxx
server:
  port: 9998
spring:
  messages:
    basename: i18n/messages
    encoding: UTF-8
    use-code-as-default-message: true
  mvc:
    static-path-pattern: /public/**
  web:
    resources:
      static-locations: classpath:config/public/
  servlet:
    multipart:
      # 文件上传大小限制
      max-file-size: 20MB
      max-request-size: 25MB
  application:
    name: gruul-mall-single
  main:
    allow-circular-references: true
  profiles:
    active: dev
  datasource:
    dynamic:
      strict: false
      hikari:
        connection-timeout: 15000
        idle-timeout: 0
        max-lifetime: 28000000
        max-pool-size: 5
      datasource:
        master:
          url: *************************************************************************************************************************
          username: mall
          password: mall_All@202429
  # redis 缓存
  cache:
    type: redis
  redis:
    database: 15
    host: ************
    password: <PERSON><PERSON><PERSON><PERSON>@Dev2023
    port: 6379
    timeout: 60s
    lettuce:
      pool:
        max-idle: 3
        max-active: 3
  # security 安全配置
  security:
    oauth2:
      authorizationserver:
        token:
          token-validity-seconds: 14400
          refresh-token-validity-seconds: 14400
        key:
          public-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNBFp4Tv5RwqPfcP7wP/UpBFipASOYfr0DY25dfF8TaleFK9q23Ia20mayMUg7S+YlAPt7VrUlnFGQvOy/UsgRv3Wa2mKxmtPG0qWRMWTbGl72NexMt695+Q2VIoru0qkF2Vd03DxdDE4AEFhqUr3SAZNTnzyOXqQ0yhKRQVXkAwIDAQAB
          private-key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAI0EWnhO/lHCo99w/vA/9SkEWKkBI5h+vQNjbl18XxNqV4Ur2rbchrbSZrIxSDtL5iUA+3tWtSWcUZC87L9SyBG/dZraYrGa08bSpZExZNsaXvY17Ey3r3n5DZUiiu7SqQXZV3TcPF0MTgAQWGpSvdIBk1OfPI5epDTKEpFBVeQDAgMBAAECgYABvPx/1XrFYjtSjUTZ4Z1G2KgJHpukN70NC3mNoHNW522AauZ4G5FziSC9ReXoiTUjM/cQRvorpljLtRshTpwVteXteTkk/uC0y6n1pvhdJS9LjaU5E5ZI1u7ZY8pjNp6ZqDc3QzcWPqcIlF7uDGqbpc2hZK2XOKp7+Hccvq0mnQJBAMbOejERtz/aAr0VOk1EzwvK49umWXW0za6Ijm4SREV3IcH7JLvkuTYsAtvxQ6zXORCzslGlXAp20f+dLVgKdyUCQQC1ldgz3lmud4TniGwlPhAbv59WfRKjT4TfxWmV2w++viLKIXuQJin3bKaCx3XeQkQX7BXSzE8IRKJzSR3WoXoHAkBe4odBSjJK4FN0SWBlJpUnDLPJztBdtGiCh5xq/n7lJKHRAnuazeRz7XeD39er86DBzvIps6GFQQajWDIfFgedAkAl9Mt7lBfE60DBsPeCFWYCltx0lLJHpsCMkdRPOboh0datz6/nYsLU8EYiAhV2Pv3CzluFc6V1gfs/A+KT0a8TAkEAp2fg6TwtDe6GjkwNAGUzJ5UhpkIcQaZJ9MPU6oP6pJ8ry/7grdD55JWjCDYOXO5GP09rVBK+5wA3qVJJmw5Qkw==
      resourceserver:
        authorization-server: true
        key:
          public-key: ${spring.security.oauth2.authorizationserver.key.public-key}
        opaquetoken:
          client-id: gruul
          client-secret: gruul
  # mq
  rabbitmq:
    host: ***************
    port: 5673
    #    username: ${spring.profiles.active}
    username: prodstomp
    #    password: ${spring.profiles.active}admin
    password: prodstompadmin
    #    virtual-host: /${spring.profiles.active}
    virtual-host: /prodstomp
    listener:
      simple:
        retry:
          enabled: false # 开启消费者进行重试
          max-attempts: 1 # 最大重试次数
          initial-interval: 3000 # 重试时间间隔
        acknowledge-mode: manual #消费手动应答
    stomp:
      host: ${spring.rabbitmq.host}
      username: prodstomp
      password: prodstompadmin
      virtual-host: /prodstomp
#      username: ${spring.profiles.active}stomp
#      password: ${spring.profiles.active}stompadmin
#      virtual-host: /${spring.profiles.active}stomp
#mybatis plus
mybatis-plus:
  global-config:
    banner: false
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
sms:
  conf: ALI
easy-es:
  keep-alive-millis: 18000 # 心跳策略时间 单位:ms
  connect-timeout: 5000
  socket-timeout: 5000
  connection-request-timeout: 5000
  max-conn-total: 100
  max-conn-per-route: 100
  banner: false
  enable: true #是否开启EE自动配置
  address: ***************:9199 # es连接地址+端口 格式必须为ip:port,如果是集群则可用逗号隔开
  schema: http # 默认为http
  username: elastic #如果无账号密码则可不配置此行
  password: Test@2023 #如果无账号密码则可不配置此行
  global-config:
    distributed: true
    db-config:
      table-prefix: ${spring.profiles.active}_ #索引名前缀
      field-strategy: not_null
logging:
  config: classpath:config/logback-spring.xml
  level:
    com.medusa.gruul: debug
    com.ibatis: debug

#医护配置
yihuzhijia:
  api:
    domain: https://www.yihuyihu.com/yhzj
    appId: YH_ZYYPSC_CF
    appKey: c27dda71d6d3414d62a65cac1bf48ff8


gruul:
  h5:
    url: http://**************:7777/h5/
  shop:
    # 弃用
    defaultShopId:
  # 单体架构
  single: true
  single-config:
    addons:
      - addon-coupon
      - gruul-mall-addon-platform
      - addon-bargain
      - addon-live
      - addon-invoice
      - addon-shop-store
      - addon-team
      - addon-distribute
      - addon-coupon
      - addon-matching-treasure
      - addon-rebate
      - addon-integral
      - addon-supplier
      - addon-full-reduction
      - addon-intra-city-distribution
      - gruul-mall-freight
      - addon-member
  #xxl-job
  xxl-job:
    admin:
      admin-addresses: http://***************:9010/xxl-job-admin
    executor:
      id: 15
      access-token: 3l8jomjpbjti
      router-address: http://**************:2${server.port}
      log-path: /tmp/logs/${gruul.xxl-job.executor.app-name}/xxl-job
      log-retention-days: 30
  #小程序配置
  wechat:
    app-id: wx7165bad92421a96a
    app-secret: 70e172df7beddf65a720932d1c74d84e
    mpAppId: wx7165bad92421a96a
    mpAppSecret: 70e172df7beddf65a720932d1c74d84e
  #昱极订单状态对比任务状态
  yuji:
    order:
      taskFlag: false
  tenant:
    enable-multi-shop: true
    ignore-shop-id-tables:
      - t_shop
      - t_auth_client
      - t_menu
      - t_menu_addon
      - t_user
      - t_user_data
      - t_user_account
      - t_storage_sku
      - t_storage_sku_stock_record
      - t_storage_spec
      - t_storage_spec_group
      - t_file
      - t_member_rights
      - t_user_address
      - t_user_collect
      - t_user_deal_detail
      - t_user_foot_mark
      - t_user_free_member
      - t_user_member_card
      - t_user_member_relevancy_rights
      - t_user_saving_rule
      - t_user_tag
      - t_user_tag_group
      - t_order
      - t_order_payment
      - t_order_receiver
      - t_order_discount
      - t_order_discount_item
      - t_shop_follow
      - t_shop_follow_new_products
      - t_basics_info
      - t_file
      - t_sms_conf
      - t_system_conf
      - t_afs_history
      - t_afs_history_package
      - t_afs_order_item
      - t_afs_order_receiver
      - t_afs_package
      - t_pigeon_message
      - t_message_user
      - t_sms_template
      - t_sms_sign
      - t_sms_record
      - t_deal_ranking
      - t_overview_distributor
      - t_overview_shop
      - t_overview_shop_balance
      - t_overview_statement
      - t_overview_user
      - t_overview_withdraw
      - t_platform_decoration
      - t_platform_decoration_details
      - t_platform_category
      - t_platform_client_config
      - merchant_details
      - t_payment_record
      - t_payment_history
      - t_payment_info
      - t_payment_merchant_config
      - t_payment_refund
      - t_coupon
      - t_coupon_calculate
      - t_coupon_order_record
      - t_coupon_product
      - t_coupon_user
      - t_distribute_conf
      - t_distribute_product
      - t_distribute_shop
      - t_distributor
      - t_distributor_order
      - t_seckill_product
      - t_seckill_promotion
      - t_paid_member
      - t_paid_member_relevancy_rights
      - t_live_goods_examine
      - t_live_member
      - t_live_room
      - t_wechat_room
      - t_wechat_call_number
      - t_live_room_goods
      - t_order_timeout
      - t_search_brand
      - t_shop_store
      - t_platform_shop_signing_category
      - t_platform_category
      - t_integral_rules
      - t_user_growth_value_settings
      - t_rebate_order
      - t_full_reduction
      - t_shop_user_account
      - t_rebate_transactions
      - t_supplier_goods
      - t_supplier_goods_publish
      - t_supplier_info
      - t_supplier_order
      - t_supplier_order_item
      - t_supplier_order_package
      - t_supplier_storage_record
      - t_rebate_accounts
      - t_rebate_conf
      - t_rebate_details
      - t_rebate_order
      - t_rebate_payment
      - t_rebate_transactions
      - t_bargain
      - t_bargain_help_people
      - t_bargain_order
      - t_bargain_payment_info
      - t_bargain_product
      - t_full_reduction
      - t_full_reduction_payment_info
      - t_full_reduction_product
      - t_integral_behavior
      - t_integral_consume_order
      - t_integral_order
      - t_integral_order_payment
      - t_integral_order_receiver
      - t_integral_product
      - t_integral_rules
      - t_integral_setting
      - t_intra_city_distribution_config
      - t_anchor
      - t_anchor_follow
      - t_base_live
      - t_live_audience
      - t_live_extend
      - t_live_order
      - t_live_product
      - t_product_explanation
      - t_prohibited
      - t_reservation
      - t_set_meal
      - t_set_meal_payment_info
      - t_set_meal_product
      - t_paid_member
      - t_paid_member_relevancy_rights
      - t_platform_category
      - t_platform_client_config
      - t_platform_decoration
      - t_platform_decoration_details
      - t_platform_privacy_agreement
      - t_platform_shop_signing_category
      - t_rebate_accounts
      - t_rebate_conf
      - t_rebate_details
      - t_rebate_order
      - t_rebate_payment
      - t_rebate_transactions
      - t_seckill_payment_info
      - t_seckill_product
      - t_seckill_promotion
      - t_shop_assistant
      - t_shop_store
      - t_shop_store_order
      - t_supplier_goods
      - t_supplier_goods_publish
      - t_supplier_info
      - t_supplier_order
      - t_supplier_order_item
      - t_supplier_order_package
      - t_supplier_storage_record
      - t_team_activity
      - t_team_order
      - t_team_product
      - t_search_brand
      - t_search_brand_follow
      - t_member_rights
      - t_shop_user_account
      - t_user_account
      - t_user_address
      - t_user_collect
      - t_user_deal_detail
      - t_user_foot_mark
      - t_user_free_member
      - t_user_growth_value_settings
      - t_user_integral_detail
      - t_user_member_card
      - t_user_member_relevancy_rights
      - t_user_saving_rule
      - t_user_tag
      - t_user_tag_group
      - t_invoice_attachment
      - t_invoice_header
      - t_invoice_request
      - t_invoice_settings
      - t_payment_sharing_record
      - t_shop_bank_account
      - t_overview_withdraw_accounts
      - t_user_identity
      - t_payment_user_account_record
      - t_supplier
      - t_distributor_in_code
      - t_product_sync_record
      - t_distributor_affair_records
      - t_supplier_merchant
      - t_supplier_request_record
      - t_supplier_product
      - t_supplier_product_order
      - t_hot_product
      - t_supplier_notify_record
      - t_supplier_user
      - t_supplier_user_card
      - t_supplier_right
      - t_gift_order_record
      - t_gift_product
      - t_gift
      - t_shop_order_gift
      - t_notify
      - t_notify_record
      - t_shop_data_sync_record
      - t_shop_group
      - t_shop_group_mapping
      - t_shop_paid_member
      - t_shop_paid_member_relevancy_rights
      - t_supplier_diagnose_record
      - t_supplier_product_list
      - t_supplier_stock_sync_record
      - t_yy_request_record
      - t_yy_order
      - t_yy_settings
      - t_oss_conf