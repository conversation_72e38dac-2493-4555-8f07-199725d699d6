package com.medusa.gruul.carrier.pigeon.service.modules.oss.config;

import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class TencentStorageConfig implements StorageConf {

    private static final long serialVersionUID = 6697917083773365808L;
    /**
     * 腾讯云绑定的域名
     */
    @NotBlank(message = "腾讯云绑定的域名不能为空")
    @URL(message = "腾讯云绑定的域名格式不正确")
    private String qcloudDomain;
    /**
     * 腾讯云路径前缀
     */
    private String qcloudPrefix;
    /**
     * 腾讯云AppId
     */
    @NotNull(message = "腾讯云AppId不能为空")
    private Integer qcloudAppId;
    /**
     * 腾讯云SecretId
     */
    @NotBlank(message = "腾讯云SecretId不能为空")
    private String qcloudSecretId;
    /**
     * 腾讯云SecretKey
     */
    @NotBlank(message = "腾讯云SecretKey")
    private String qcloudSecretKey;
    /**
     * 腾讯云BucketName
     */
    @NotBlank(message = "腾讯云BucketName")
    private String qcloudBucketName;
    /**
     * 腾讯云COS所属地区
     */
    @NotBlank(message = "腾讯云COS所属地区")
    private String qcloudRegion;
}
