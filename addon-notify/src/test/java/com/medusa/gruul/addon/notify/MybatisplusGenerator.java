package com.medusa.gruul.addon.notify;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.medusa.gruul.common.mp.model.BaseEntity;

import java.util.HashMap;

/**
 * <AUTHOR>
 * date 2022/2/24
 */
public class MybatisplusGenerator {

    public static void main(String[] args) {
        FastAutoGenerator.create(
                        "************************************************************************************************************************",
                        "root",
                        "Test@yhzj_17"
                )
                .globalConfig(builder -> {
                    builder.author("张天赐")
                            //.enableSwagger()
                            //.fileOverride()
                            .outputDir("F:/company/project/Cshop/gruul-mall/gruul-mall-goods/gruul-mall-goods-service/src/main/java");
                })
                .packageConfig(builder -> {
                    builder.parent("com.medusa.gruul.goods.service")
                            .moduleName("mp")
                            .pathInfo(
                                    new HashMap<OutputFile, String>() {
                                        private static final long serialVersionUID = 4195354013431294015L;

                                        {
                                            put(OutputFile.xml, "F:/company/project/Cshop/gruul-mall/gruul-mall-goods/gruul-mall-goods-service/src/main/resources/mapper");
                                        }
                                    }
                            );
                })
                .strategyConfig(builder -> {
                    builder.addInclude("t_product_tag")
                            .addTablePrefix("t_")
                            .entityBuilder()
                            .enableLombok()
                            .superClass(BaseEntity.class);
                }).execute();
    }
}
