package com.medusa.gruul.addon.notify.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 通知订单状态
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum NotifyOrderType {

    /**
     * 未支付
     */
    CREATED(1),

    /**
     * 已支付
     */
    PAID(2),

    /**
     * 发货
     */
    DELIVER(3),
    /**
     * 确认收货
     */
    CONFIRM(4),

    /**
     * 关闭订单
     */
    CLOSED(5),

    /**
     * 订单完成
     */
    ACCOMPLISH(6),

    //------------------售后状态

    /**
     * 退货退款已同意
     */
    RETURN_REFUND_AGREE(7),

    /**
     * 退货退款已拒绝
     */
    RETURN_REFUND_REJECT(8),

    /**
     * 已退货退款 已完成
     */
    RETURNED_REFUNDED(13),

    ;

    @EnumValue
    private final Integer value;

}
