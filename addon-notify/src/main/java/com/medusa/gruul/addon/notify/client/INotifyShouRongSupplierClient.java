package com.medusa.gruul.addon.notify.client;


import com.medusa.gruul.addon.notify.mp.entity.Notify;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.model.dto.SupplierOrderPayDTO;

/**
 * 供应商昱极接口
 */
public interface INotifyShouRongSupplierClient {
    /**
     * 通知订单
     */
    Result<Boolean> notifyOrder(Notify notify, SupplierOrderPayDTO payDTO);

    default String convertRequestUrl(String baseUrl, String apiUrl) {
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        if (!apiUrl.startsWith("/")) {
            baseUrl += "/";
        }
        return baseUrl + apiUrl;
    }
}
