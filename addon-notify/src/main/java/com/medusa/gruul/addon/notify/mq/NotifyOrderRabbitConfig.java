package com.medusa.gruul.addon.notify.mq;
import com.medusa.gruul.afs.api.enums.AfsRabbit;
import com.medusa.gruul.order.api.enums.OrderRabbit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * date 2023/2/2
 * time 22:42
 **/

@Slf4j
@Configuration
@RequiredArgsConstructor
public class NotifyOrderRabbitConfig {
    /**
     * 订单支付完成交换机
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(name = "notifyOrderExchange")
    public Exchange notifyOrderExchange() {
        return ExchangeBuilder.directExchange(OrderRabbit.ORDER_DELIVER_GOODS.exchange())
                .durable(true)
                .delayed().build();
    }

    /**
     *订单发货通知
     */
    @Bean
    public Queue notifyOrdeDeliverCallbackQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_ORDER_DELIVER_CALLBACK_QUEUE)
                .build();
    }

    @Bean
    public Binding notifyOrderDeliverCallbackBinding() {
        return BindingBuilder.bind(notifyOrdeDeliverCallbackQueue())
                .to(notifyOrderExchange())
                .with(OrderRabbit.ORDER_DELIVER_GOODS.routingKey())
                .noargs();
    }

    /**
     *订单创建完成
     */
    @Bean
    public Queue notifyOrdeCreatedCallbackQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_ORDER_CREATED_CALLBACK_QUEUE)
                .build();
    }
    @Bean
    public Binding notifyOrderCreatedCallbackBinding() {
        return BindingBuilder.bind(notifyOrdeCreatedCallbackQueue())
                .to(notifyOrderExchange())
                .with(OrderRabbit.ORDER_CREATED.routingKey())
                .noargs();
    }

    /**
     *订单支付完成
     */
    @Bean
    public Queue notifyOrdePaidCallbackQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_ORDER_PAID_CALLBACK_QUEUE)
                .build();
    }
    @Bean
    public Binding notifyOrderPaidCallbackBinding() {
        return BindingBuilder.bind(notifyOrdePaidCallbackQueue())
                .to(notifyOrderExchange())
                .with(OrderRabbit.ORDER_PAID_BROADCAST.routingKey())
                .noargs();
    }

    /**
     *订单确认收货
     */
    @Bean
    public Queue notifyOrdeConfirmCallbackQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_ORDER_CONFIRM_CALLBACK_QUEUE)
                .build();
    }
    @Bean
    public Binding notifyOrderConfirmCallbackBinding() {
        return BindingBuilder.bind(notifyOrdeConfirmCallbackQueue())
                .to(notifyOrderExchange())
                //此ORDER_AUTO_DO_SHARING为确认收货之后会发出的消息
                .with(OrderRabbit.ORDER_DO_CONFIRM.routingKey())
                .noargs();
    }

    /**
     *订单关闭
     */
    @Bean
    public Queue notifyOrdeCloseCallbackQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_ORDER_CLOSE_CALLBACK_QUEUE)
                .build();
    }
    @Bean
    public Binding notifyOrderCloseCallbackBinding() {
        return BindingBuilder.bind(notifyOrdeCloseCallbackQueue())
                .to(notifyOrderExchange())
                .with(OrderRabbit.ORDER_CLOSE.routingKey())
                .noargs();
    }

    /**
     *订单完成
     */
//    @Bean
//    public Queue notifyOrdeAccomplishCallbackQueue() {
//        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_ORDER_ACCOMPLISH_CALLBACK_QUEUE)
//                .build();
//    }
//    @Bean
//    public Binding notifyOrderAccomplishCallbackBinding() {
//        return BindingBuilder.bind(notifyOrdeAccomplishCallbackQueue())
//                .to(orderExchange())
//                .with(OrderRabbit.ORDER_ACCOMPLISH.routingKey())
//                .noargs();
//    }

    /**
     *订单已同意售后
     */
    @Bean
    public Queue notifyOrdeAfsAgreeCallbackQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_AFS_AGREE_CALLBACK_QUEUE)
                .build();
    }
    @Bean
    public Binding notifyOrderAfsAgreeCallbackBinding() {
        return BindingBuilder.bind(notifyOrdeAfsAgreeCallbackQueue())
                .to(afsExchange())
                .with(AfsRabbit.AFS_AGREE_REQUEST.routingKey())
                .noargs();
    }


    @Bean
    @ConditionalOnMissingBean(name = "afsExchange")
    public Exchange afsExchange() {
        return ExchangeBuilder.directExchange(AfsRabbit.EXCHANGE)
                .durable(Boolean.TRUE)
                .delayed()
                .build();
    }
    /**
     * 退款回调队列
     */
    @Bean
    public Queue notifyOrderAfsRefundedQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_AFS_REFUND_CALLBACK_QUEUE)
                .build();
    }
    @Bean
    public Binding notifyOrderAfsRefundedBinding() {
        return BindingBuilder.bind(notifyOrderAfsRefundedQueue())
                .to(afsExchange())
                .with(AfsRabbit.AFS_REFUND_CALLBACK.routingKey())
                .noargs();
    }

    /**
     *订单已拒绝售后
     */
//    @Bean
//    public Queue notifyOrdeAfsRejectCallbackQueue() {
//        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_AFS_REJECT_CALLBACK_QUEUE)
//                .build();
//    }
//    @Bean
//    public Binding notifyOrderAfsRejectCallbackBinding() {
//        return BindingBuilder.bind(notifyOrdeAfsRejectCallbackQueue())
//                .to(orderExchange())
//                .with(AfsRabbit.AFS_REJECT_REQUEST.routingKey())
//                .noargs();
//    }

}
