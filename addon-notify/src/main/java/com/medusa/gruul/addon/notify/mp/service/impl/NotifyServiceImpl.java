package com.medusa.gruul.addon.notify.mp.service.impl;

import com.medusa.gruul.addon.notify.mp.entity.Notify;
import com.medusa.gruul.addon.notify.mp.mapper.NotifyMapper;
import com.medusa.gruul.addon.notify.mp.service.INotifyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 通知供应商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Service
public class NotifyServiceImpl extends ServiceImpl<NotifyMapper, Notify> implements INotifyService {

}
