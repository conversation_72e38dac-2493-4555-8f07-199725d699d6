package com.medusa.gruul.addon.notify.mq;
import com.medusa.gruul.afs.api.enums.AfsRabbit;
import com.medusa.gruul.order.api.enums.OrderRabbit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * date 2023/2/2
 * time 22:42
 **/

@Slf4j
@Configuration
@RequiredArgsConstructor
public class NotifyYujiRabbitConfig {
    /**
     * 订单支付完成交换机
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(name = "orderExchange")
    public Exchange orderExchange() {
        return ExchangeBuilder.directExchange(OrderRabbit.ORDER_DELIVER_GOODS.exchange())
                .durable(true)
                .delayed().build();
    }

    /**
     *订单支付完成
     */
    @Bean
    public Queue notifyYujiOrdePaidCallbackQueue() {
        return QueueBuilder.durable(NotifyOrderQueueNames.NOTIFY_YUJI_ORDER_PAID_CALLBACK_QUEUE)
                .build();
    }
    @Bean
    public Binding notifyYujiOrderPaidCallbackBinding() {
        return BindingBuilder.bind(notifyYujiOrdePaidCallbackQueue())
                .to(orderExchange())
                .with(OrderRabbit.ORDER_PAID_BROADCAST.routingKey())
                .noargs();
    }

}
