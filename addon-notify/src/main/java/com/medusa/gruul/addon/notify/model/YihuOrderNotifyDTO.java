package com.medusa.gruul.addon.notify.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class YihuOrderNotifyDTO {

    /**
     * 医护权益卡id
     */
    private Long cardId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 保单号/卡密
     */
    private String policyNo;

    /**
     * 订单号
     */
    private String bizCode;

    /**
     * 母订单号
     */
    private String parentBizCode;
    /**
     * 供应商单号
     */
    private String supplierOrderNo;
    /**
     * 供应商类型
     */
    private String supplierType;

    /**
     * 申请时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 取消时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;

    /**
     * 优惠金额
     */
    private BigDecimal couponAmt;

    /**
     * 快递公司
     */
    private String courierCompany;

    /**
     * 快递公司编码
     */
    private String courierCompanyCode;

    /**
     * 快递单号
     */
    private String courierNumber;

    /**
     * 减免配送费
     */
    private String discountPostageFee;

    /**
     * 是否含有处方药
     */
    private String hasPrescription;

    /**
     * 配送费
     */
    private BigDecimal postageFee;

    /**
     * 处方链接
     */
    private String prescriptionPdf;

    /**
     * 实际金额=总金额-优惠金额+配送费-减免配送费
     */
    private BigDecimal realAmt;

    /**
     * 退卡时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;

    /**
     * 下单时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 总金额
     */
    private BigDecimal totalAmt;

    /**
     * 订单状态【1-待付款, 2-待补方, 3-待收货, 4-已完成, 5-已退款, 6-已失效,9-待发货，10-退款中】
     */
    private String status;

    /**
     * 药品明细
     */
    private List<YihuOrderDetailDTO> thirdOrderDetailList;

    /**
     * 收货地址信息
     */
    private YihuOrderUserAddressDTO thirdUserAddressInfo;
    /**
     * 店铺优惠券码
     */
    private String shopCouponId;

    /**
     * 包裹Id
     */
    private Long packageId;

    /**
     * 售后工单号
     */
    private String afsNo;

}
