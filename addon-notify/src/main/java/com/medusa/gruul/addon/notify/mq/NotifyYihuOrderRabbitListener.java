package com.medusa.gruul.addon.notify.mq;

import cn.hutool.core.thread.ThreadUtil;
import com.medusa.gruul.addon.notify.model.enums.NotifyOrderType;
import com.medusa.gruul.addon.notify.service.SendNotifyService;
import com.medusa.gruul.afs.api.model.AfsMqMessageDTO;
import com.medusa.gruul.order.api.enums.OrderCloseType;
import com.medusa.gruul.order.api.model.OrderCreatedDTO;
import com.medusa.gruul.order.api.model.OrderPackageKeyDTO;
import com.medusa.gruul.order.api.model.OrderPaidBroadcastDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.payment.api.model.dto.RefundNotifyResultDTO;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Strings;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 医护订单通知
 * <AUTHOR>
 **/

@Slf4j
@Component
@RequiredArgsConstructor
public class NotifyYihuOrderRabbitListener {


    private final SendNotifyService sendNotifyService;

    /**
     * 订单创建完成
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_ORDER_CREATED_CALLBACK_QUEUE)
    public void notifyCreated(OrderCreatedDTO orderCreated, Channel channel, Message message) throws IOException {
        log.info("医护订单创建完成通知：orderCreated：{}", orderCreated);
        //确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        sendNotifyService.sendManySupper(NotifyOrderType.CREATED, orderCreated.getOrderNo(),0L,null);
    }

    /**
     * 订单支付完成
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_ORDER_PAID_CALLBACK_QUEUE)
    public void notifyPaid(OrderPaidBroadcastDTO orderPaidBroadcastDTO, Channel channel, Message message) throws IOException {
        log.info("医护订单支付完成通知：orderPaidBroadcastDTO：{}", orderPaidBroadcastDTO);
        //确认消息
        ThreadUtil.sleep(1000);
        String orderNo = orderPaidBroadcastDTO.getOrderNo();
        sendNotifyService.sendManySupper(NotifyOrderType.PAID, orderNo,0L,null);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    /**
     * 订单确认收货
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_ORDER_CONFIRM_CALLBACK_QUEUE)
    public void notifyConfirm(OrderPackageKeyDTO orderPackageKey, Channel channel, Message message) throws IOException {
        log.info("医护订单确认收货通知：orderPackageKey：{}", orderPackageKey);
        //确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        sendNotifyService.sendManySupper(NotifyOrderType.CONFIRM, orderPackageKey.getOrderNo(),0L,null);
    }

    /**
     * 订单关闭
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_ORDER_CLOSE_CALLBACK_QUEUE)
    public void notifyClose(OrderInfo orderInfo, Channel channel, Message message) throws IOException {
        log.info("医护订单关闭通知：orderInfo：{}", orderInfo);
        //医护不推送售后关闭
//        if (OrderCloseType.AFS.name().equals(orderInfo.getCloseType().name())) {
//            //确认消息
//            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//            return;
//        }
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        sendNotifyService.sendManySupper(NotifyOrderType.CLOSED, orderInfo.getOrderNo(),0L,orderInfo.getAfs()!=null?orderInfo.getAfs().getAfsNo():null);
    }

    /**
     * 订单完成
     */
//    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_ORDER_ACCOMPLISH_CALLBACK_QUEUE)
//    public void notifyAccomplish(OrderCompletedDTO orderCompletedDTO, Channel channel, Message message) throws IOException {
//        log.info("订单完成通知：orderCompletedDTO：{}", orderCompletedDTO);
//        //确认消息
//        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        sendNotifyService.send(NotifyOrderType.ACCOMPLISH, orderCompletedDTO.getOrderNo());
//    }

    /**
     * 订单发货
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_ORDER_DELIVER_CALLBACK_QUEUE)
    public void notifyDeliver(OrderPackageKeyDTO orderPackageKeyDTO, Channel channel, Message message) throws IOException {
        log.info("医护订单发货通知：orderPackageKeyDTO：{}", orderPackageKeyDTO);
        //确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        sendNotifyService.sendManySupper(NotifyOrderType.DELIVER, orderPackageKeyDTO.getOrderNo(),orderPackageKeyDTO.getPackageId(),null);
    }

    /**
     * 订单同意售后
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_AFS_AGREE_CALLBACK_QUEUE)
    public void notifyAfsAgree(AfsMqMessageDTO afsMqMessageDTO, Channel channel, Message message) throws IOException {
        log.info("医护订单同意售后通知：afsMqMessageDTO：{}", afsMqMessageDTO);
        //确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        sendNotifyService.sendManySupper(NotifyOrderType.RETURN_REFUND_AGREE, afsMqMessageDTO.getOrderNo(),0L,afsMqMessageDTO.getAfsNo());
    }

    /**
     * 订单拒绝售后
     */
//    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_AFS_REJECT_CALLBACK_QUEUE)
//    public void notifyAfsReject(AfsMqMessageDTO afsMqMessageDTO, Channel channel, Message message) throws IOException {
//        log.info("订单拒绝售后通知：afsMqMessageDTO：{}", afsMqMessageDTO);
//        //确认消息
//        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        sendNotifyService.send(NotifyOrderType.RETURN_REFUND_REJECT, afsMqMessageDTO.getOrderNo());
//    }

    /**
     * 订单已退款成功
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_AFS_REFUND_CALLBACK_QUEUE)
    public void notifyAfsReject(RefundNotifyResultDTO refundNotifyResultDTO, Channel channel, Message message) throws IOException {
        log.info("医护订单已退款成功通知：refundNotifyResultDTO：{}", refundNotifyResultDTO);
        //确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        String orderNo = Strings.isNullOrEmpty(refundNotifyResultDTO.getOrderNo()) ? refundNotifyResultDTO.getOutTradeNo() : refundNotifyResultDTO.getOrderNo();
        sendNotifyService.sendManySupper(NotifyOrderType.RETURNED_REFUNDED, orderNo,0L,refundNotifyResultDTO.getAfsNum());
    }


}
