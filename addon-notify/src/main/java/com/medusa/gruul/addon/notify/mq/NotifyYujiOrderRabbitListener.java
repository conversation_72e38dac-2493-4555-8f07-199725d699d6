package com.medusa.gruul.addon.notify.mq;

import com.medusa.gruul.addon.notify.model.enums.NotifyOrderType;
import com.medusa.gruul.addon.notify.service.SendNotifyService;
import com.medusa.gruul.afs.api.model.AfsMqMessageDTO;
import com.medusa.gruul.order.api.model.OrderCreatedDTO;
import com.medusa.gruul.order.api.model.OrderPackageKeyDTO;
import com.medusa.gruul.order.api.model.OrderPaidBroadcastDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.payment.api.model.dto.RefundNotifyResultDTO;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 昱极订单通知
 * <AUTHOR>
 **/

@Slf4j
@Component
@RequiredArgsConstructor
public class NotifyYujiOrderRabbitListener {


    private final SendNotifyService sendNotifyService;

    /**
     * 订单支付完成
     */
    @RabbitListener(queues = NotifyOrderQueueNames.NOTIFY_YUJI_ORDER_PAID_CALLBACK_QUEUE)
    public void notifyPaid(OrderPaidBroadcastDTO orderPaidBroadcastDTO, Channel channel, Message message) throws IOException {
        log.info("供应商订单支付完成通知：orderPaidBroadcastDTO：{}", orderPaidBroadcastDTO);
        String orderNo = orderPaidBroadcastDTO.getOrderNo();
        sendNotifyService.supplierSend(NotifyOrderType.PAID, orderNo);
        //确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }



}
