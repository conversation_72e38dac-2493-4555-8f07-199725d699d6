package com.medusa.gruul.addon.notify.service;

import com.medusa.gruul.addon.notify.model.enums.NotifyOrderType;
import com.medusa.gruul.order.api.entity.Order;

/**
 * <p>
 * 通知供应商
 * </p>
 *
 * <AUTHOR>
 */
public interface SendNotifyService {

    void send(NotifyOrderType notifyOrderType, String orderNo);

    void yujiSend(NotifyOrderType notifyOrderType, String orderNo);

    void supplierSend(NotifyOrderType notifyOrderType, String orderNo);

    void sendSync(NotifyOrderType notifyOrderType, Order order);

    //多供应商
    void sendManySupper(NotifyOrderType notifyOrderType, String orderNo,Long packageId,String afsNo);

}
