package com.medusa.gruul.addon.notify.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.addon.notify.model.enums.NotifyStatus;
import com.medusa.gruul.common.mp.model.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_notify_record")
public class NotifyRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 重试id
     */
    private Long retryId;

    /**
     * 请求url
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String sendParam;

    /**
     * 响应结果
     */
    private String response;

    /**
     * 状态
     */
    private NotifyStatus status;
}
