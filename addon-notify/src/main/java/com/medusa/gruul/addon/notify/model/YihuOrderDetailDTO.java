package com.medusa.gruul.addon.notify.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class YihuOrderDetailDTO {

    /**
     * 关联订单表id
     */
    private Long orderId;

    /**
     * 订单项id
     */
    private Long itemId;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 优惠单价
     */
    private BigDecimal discountAmt;

    /**
     * 药品编号
     */
    private String drugCode;

    /**
     * 药品名称
     */
    private String drugName;

    /**
     * 销售价格
     */
    private BigDecimal price;

    /**
     * 实际需支付金额
     */
    private BigDecimal realAmt;

    /**
     * 总金额
     */
    private BigDecimal totalAmt;

    /**
     * 总优惠金额
     */
    private BigDecimal totalDiscountAmt;

    /**
     * 售后状态
     */
    private String status;

    /**
     * 快递公司
     */
    private String courierCompany;


    /**
     * 快递单号
     */
    private String courierNumber;

    /**
     * 快递公司编码
     */
    private String courierCompanyCode;


    /**
     * 包裹状态
     */
    private String packageStatus;


    /**
     * 供应商订单号
     */
    private String supplierOrderNo;

    /**
     * 供应商类型
     */
    private String supplierType;


    /**
     * 包裹Id
     */
    private Long packageId;

    /**
     * 售后工单号
     */
    private String afsNo;
}
