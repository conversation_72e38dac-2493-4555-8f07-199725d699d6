package com.medusa.gruul.addon.notify.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.medusa.gruul.addon.notify.client.INotifyShouRongSupplierClient;
import com.medusa.gruul.addon.notify.client.INotifyYihuSupplierClient;
import com.medusa.gruul.addon.notify.client.INotifyYujiSupplierClient;
import com.medusa.gruul.addon.notify.model.YihuOrderDetailDTO;
import com.medusa.gruul.addon.notify.model.YihuOrderNotifyDTO;
import com.medusa.gruul.addon.notify.model.YihuOrderUserAddressDTO;
import com.medusa.gruul.addon.notify.model.enums.NotifyOrderType;
import com.medusa.gruul.addon.notify.model.enums.NotifyStatus;
import com.medusa.gruul.addon.notify.mp.entity.Notify;
import com.medusa.gruul.addon.notify.service.SendNotifyService;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.goods.api.model.dto.SupplierMerchantDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierOrderCreateDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierOrderPayDTO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.DiscountSourceType;
import com.medusa.gruul.order.api.enums.SupplierProductOrderStatusEnum;
import com.medusa.gruul.order.api.model.ItemExtra;
import com.medusa.gruul.order.api.model.SupplierProductOrderKeyDTO;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendNotifyServiceImpl implements SendNotifyService {

    private final INotifyYihuSupplierClient yihuSupplierClient;
    private final INotifyYujiSupplierClient yujiSupplierClient;
    private final INotifyShouRongSupplierClient shouRongSupplierClient;

    private final OrderRpcService orderRpcService;
    private final GoodsRpcService goodsRpcService;

    @Override
    public void sendManySupper(NotifyOrderType notifyOrderType, String orderNo, Long packageId, String afsNo) {
        Notify notify = new Notify();
        notify.setStatus(NotifyStatus.INIT).setRequestNum(1).setOrderNo(orderNo);
        Order orderDetail = orderRpcService.getOrderDetail(orderNo);
        if (null == orderDetail) {
            log.error("医护通知没有找到对应订单， orderNo: " + orderNo);
            return;
        }
        //订单转换
        List<YihuOrderNotifyDTO> YihuOrderNotifyDTOList = convertOrderToNotifyList(notifyOrderType, orderDetail);
        // 经过合并操作得到的订单列表
        List<YihuOrderNotifyDTO> mergedResultOrders = YihuOrderNotifyDTOList;

        // 经过拆分操作得到的订单列表
        List<YihuOrderNotifyDTO> splitResultOrders = YihuOrderNotifyDTOList;
        // 合并订单列表
        List<YihuOrderNotifyDTO> mergedOrders1 = mergeOrders(new ArrayList<>(mergedResultOrders),packageId);
        if (notifyOrderType.getValue() == 7 || notifyOrderType.getValue() == 13 || notifyOrderType.getValue() == 5) {
            if (areThirdOrderDetailStatusesSame(mergedOrders1)) {
                for (YihuOrderNotifyDTO YihuOrderNotifyDTO1 : mergedOrders1) {
                    yihuSupplierClient.notifyOrder(notify, YihuOrderNotifyDTO1);
                }
            }
        }else {
            for (YihuOrderNotifyDTO YihuOrderNotifyDTO1 : mergedOrders1) {
                yihuSupplierClient.notifyOrder(notify, YihuOrderNotifyDTO1);
            }
        }


        List<YihuOrderNotifyDTO> splitOrderList = splitOrders(splitResultOrders);
        //如果同一个供应商不拆分
        if(!checkSameSupplierType(splitOrderList)){
            for (YihuOrderNotifyDTO dto : splitOrderList) {
                // 情况1：判断那个包裹
                if (packageId != 0L) {
                    if (packageId.equals(dto.getPackageId())) {
                        yihuSupplierClient.notifyOrder(notify, dto);
                    }
                }
                // 情况2：判断哪个售后单
                else if (afsNo != null) {
                    if (afsNo.equals(dto.getAfsNo())) {
                        yihuSupplierClient.notifyOrder(notify, dto);
                    }
                }
                // 情况3：两个值都无值（可选，根据业务是否需要处理）
                else {
                    // 例如：两个条件都无值时，是否对所有对象执行操作？
                    yihuSupplierClient.notifyOrder(notify, dto);
                }
            }
        }
    }

    public static boolean checkSameSupplierType(List<YihuOrderNotifyDTO> orderList) {
        if (orderList == null || orderList.isEmpty()) {
            return true; // 空列表视为相同
        }

        String firstSupplierType = orderList.get(0).getSupplierType();

        for (YihuOrderNotifyDTO order : orderList) {
            String currentType = order.getSupplierType();
            // 处理null情况并比较
            if (firstSupplierType == null) {
                if (currentType != null) {
                    return false;
                }
            } else if (!firstSupplierType.equals(currentType)) {
                return false;
            }
        }

        return true;
    }

    @Override
    public void send(NotifyOrderType notifyOrderType, String orderNo) {
        Notify notify = new Notify();
        notify.setStatus(NotifyStatus.INIT).setRequestNum(1).setOrderNo(orderNo);
        Order orderDetail = orderRpcService.getOrderDetail(orderNo);
        if (null == orderDetail) {
            log.error("医护通知没有找到对应订单， orderNo: " + orderNo);
            return;
        }
        //订单转换
        YihuOrderNotifyDTO yihuOrderNotifyDTO = convertOrderToNotify(notifyOrderType, orderDetail);
        //供应商订单
        if (notifyOrderType.getValue() != NotifyOrderType.CREATED.getValue() &&
                notifyOrderType.getValue() != NotifyOrderType.CLOSED.getValue()) {
            SupplierProductOrderKeyDTO supplierProductOrderKeyDTO = new SupplierProductOrderKeyDTO();
            supplierProductOrderKeyDTO.setOrderNo(orderNo).setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS);
            List<SupplierProductOrder> supplierProductOrder = orderRpcService.getSupplierProductOrder(supplierProductOrderKeyDTO);
            //暂时固定昱极
            if (!supplierProductOrder.isEmpty()) {
                yihuOrderNotifyDTO.setSupplierOrderNo(supplierProductOrder.get(0).getSupplierOrderNo())
                        .setSupplierType("YH_YJYK");
            }
        }
        yihuSupplierClient.notifyOrder(notify, yihuOrderNotifyDTO);
    }


    public static boolean areThirdOrderDetailStatusesSame(List<YihuOrderNotifyDTO> mergedOrders1) {
        if (mergedOrders1 == null || mergedOrders1.isEmpty()) {
            return false; // 空列表默认返回false，可根据需求调整
        }
        for (YihuOrderNotifyDTO order : mergedOrders1) {
            List<YihuOrderDetailDTO> details = order.getThirdOrderDetailList();
            if (details == null || details.size() <= 1) {
                continue; // 空列表或单个元素视为状态相同
            }
            String baseStatus = details.get(0).getStatus();
            for (int i = 1; i < details.size(); i++) {
                String currStatus = details.get(i).getStatus();
                if (!equals(baseStatus, currStatus)) {
                    return false; // 存在不同状态，直接返回false
                }
            }
        }
        return true; // 所有明细状态均相同
    }

    // 工具方法：处理null值的equals比较
    private static boolean equals(String a, String b) {
        return a == b || (a != null && a.equals(b));
    }

    // 处理订单拆分的核心方法
    public static List<YihuOrderNotifyDTO> splitOrders(List<YihuOrderNotifyDTO> originalOrders) {
        List<YihuOrderNotifyDTO> splitOrders = new ArrayList<>();
        if (originalOrders == null || originalOrders.isEmpty()) {
            return splitOrders;
        }

        // 遍历原始订单列表
        for (YihuOrderNotifyDTO original : originalOrders) {
            List<YihuOrderDetailDTO> details = original.getThirdOrderDetailList();
            if (details == null || details.isEmpty()) {
                // 无子项的订单直接保留（按业务需求可调整）
                splitOrders.add(copyOrder(original, null));
                continue;
            }

            // 有子项：每个子项生成一个新订单
            for (YihuOrderDetailDTO detail : details) {
                if (detail == null || detail.getItemId() == null) {
                    continue; // 跳过无效子项
                }
                // 复制原始订单信息，并用当前子项生成新订单
                YihuOrderNotifyDTO newOrder = copyOrder(original, detail);
                splitOrders.add(newOrder);
            }
        }
        return splitOrders.stream()
                // 按bizCode分组：key为bizCode，value为同bizCode的元素列表
                .collect(Collectors.groupingBy(YihuOrderNotifyDTO::getBizCode))
                // 处理每个分组，只保留符合条件的一个元素
                .values().stream()
                .map(group -> {
                    // 筛选出supplierOrderNo不为null的元素
                    List<YihuOrderNotifyDTO> nonNullSupplierOrders = group.stream()
                            .filter(dto -> dto.getSupplierOrderNo() != null)
                            .collect(Collectors.toList());

                    if (!nonNullSupplierOrders.isEmpty()) {
                        // 若存在非null的supplierOrderNo，保留第一个
                        return nonNullSupplierOrders.get(0);
                    } else {
                        // 若都为null，保留组内第一个元素
                        return group.get(0);
                    }
                })
                .collect(Collectors.toList());
    }

    // 复制原始订单信息，并根据子项设置关键字段
    private static YihuOrderNotifyDTO copyOrder(YihuOrderNotifyDTO original, YihuOrderDetailDTO detail) {
        YihuOrderNotifyDTO newOrder = new YihuOrderNotifyDTO();

        // 1. 基础字段继承原始订单
        newOrder.setCardId(original.getCardId());
        newOrder.setUserId(original.getUserId());
        newOrder.setPolicyNo(original.getPolicyNo());
        newOrder.setApplyTime(original.getApplyTime());
        newOrder.setCancelTime(original.getCancelTime());
        newOrder.setCouponAmt(original.getCouponAmt());
        newOrder.setDiscountPostageFee(original.getDiscountPostageFee());
        newOrder.setHasPrescription(original.getHasPrescription());
        newOrder.setPostageFee(original.getPostageFee());
        newOrder.setPrescriptionPdf(original.getPrescriptionPdf());
        newOrder.setRealAmt(original.getRealAmt());
        newOrder.setRefundTime(original.getRefundTime());
        newOrder.setSubmitTime(original.getSubmitTime());
        newOrder.setTotalAmt(original.getTotalAmt());
        newOrder.setStatus(original.getStatus());
        newOrder.setThirdUserAddressInfo(copyAddress(original.getThirdUserAddressInfo())); // 深拷贝地址
//        newOrder.setShopCouponId(original.getShopCouponId());

        // 2. 关键字段设置（根据子项）
        if (detail != null && detail.getItemId() != null) {
            newOrder.setBizCode(detail.getItemId().toString()); // 新订单bizCode = 子项itemId
            newOrder.setParentBizCode(original.getBizCode());   // 母订单号 = 原始订单bizCode
            // 子项列表仅保留当前子项（深拷贝）
            List<YihuOrderDetailDTO> singleDetail = new ArrayList<>();
            singleDetail.add(copyDetail(detail));
            newOrder.setThirdOrderDetailList(singleDetail);
            newOrder.setCourierCompany(detail.getCourierCompany());
            newOrder.setCourierCompanyCode(detail.getCourierCompanyCode());
            newOrder.setCourierNumber(detail.getCourierNumber());
            newOrder.setSupplierOrderNo(detail.getSupplierOrderNo());
            newOrder.setSupplierType(detail.getSupplierType());
            newOrder.setPackageId(detail.getPackageId());
            newOrder.setAfsNo(detail.getAfsNo());
        } else {
            // 无子项时的默认处理（可根据业务调整）
            newOrder.setBizCode(original.getBizCode());
            newOrder.setParentBizCode(null);
            newOrder.setThirdOrderDetailList(new ArrayList<>());
        }

        return newOrder;
    }

    // 深拷贝子项（避免引用原始对象）
    private static YihuOrderDetailDTO copyDetail(YihuOrderDetailDTO detail) {
        YihuOrderDetailDTO newDetail = new YihuOrderDetailDTO();
        newDetail.setOrderId(detail.getOrderId());
        newDetail.setItemId(detail.getItemId());
        newDetail.setQuantity(detail.getQuantity());
        newDetail.setDiscountAmt(detail.getDiscountAmt());
        newDetail.setDrugCode(detail.getDrugCode());
        newDetail.setDrugName(detail.getDrugName());
        newDetail.setPrice(detail.getPrice());
        newDetail.setRealAmt(detail.getRealAmt());
        newDetail.setTotalAmt(detail.getTotalAmt());
        newDetail.setTotalDiscountAmt(detail.getTotalDiscountAmt());
        return newDetail;
    }

    // 深拷贝地址信息（避免引用原始对象）
    private static YihuOrderUserAddressDTO copyAddress(YihuOrderUserAddressDTO address) {
        if (address == null) {
            return null;
        }
        YihuOrderUserAddressDTO newAddress = new YihuOrderUserAddressDTO();
        newAddress.setName(address.getName());
        newAddress.setProvince(address.getProvince());
        newAddress.setCity(address.getCity());
        newAddress.setDistrict(address.getDistrict());
        newAddress.setMobile(address.getMobile());
        newAddress.setAddress(address.getAddress());
        return newAddress;
    }

    @Override
    public void yujiSend(NotifyOrderType notifyOrderType, String orderNo) {
        Notify notify = new Notify();
        notify.setStatus(NotifyStatus.INIT).setRequestNum(1).setOrderNo(orderNo);
        SupplierProductOrderKeyDTO orderKeyDTO = new SupplierProductOrderKeyDTO();
        orderKeyDTO.setOrderNo(orderNo).setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS);
        List<SupplierProductOrder> supplierProductOrder = orderRpcService.getSupplierProductOrder(orderKeyDTO);
        if (supplierProductOrder.isEmpty()) {
            log.error("没有找到昱极订单:" + orderNo);
        } else {
            SupplierOrderPayDTO payDTO = new SupplierOrderPayDTO();
            payDTO.setOrderNo(supplierProductOrder.get(0).getSupplierOrderNo()).setOrderStatus(200);
            yujiSupplierClient.notifyOrder(notify, payDTO);
        }

    }

    public void supplierSend(NotifyOrderType notifyOrderType, String orderNo) {
        Notify notify = new Notify();
        notify.setStatus(NotifyStatus.INIT).setRequestNum(1).setOrderNo(orderNo);
        SupplierProductOrderKeyDTO orderKeyDTO = new SupplierProductOrderKeyDTO();
        orderKeyDTO.setOrderNo(orderNo).setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS);
        List<SupplierProductOrder> supplierProductOrder = orderRpcService.getSupplierProductOrder(orderKeyDTO);
        if (supplierProductOrder.isEmpty()) {
            log.error("没有找到供应商订单:" + orderNo);
            return;
        }
        //根据供应商拆分下单
        Map<Long, List<SupplierProductOrder>> supplierProductOrderMap = supplierProductOrder.stream().filter(s -> null != s.getSupplierId()).collect(Collectors.groupingBy(SupplierProductOrder::getSupplierId));
        supplierProductOrderMap.forEach((supplierId, supplierOrders) -> {
            SupplierMerchantDTO supplierMerchantBySupplierId = goodsRpcService.getSupplierMerchantBySupplierId(supplierId);
            if (null == supplierMerchantBySupplierId || null == supplierMerchantBySupplierId.getApiExtra() || !supplierMerchantBySupplierId.getApiExtra().isEnableApi()) {
                return;
            }
            Result<Boolean> booleanResult = null;
            SupplierProductOrderKeyDTO supplierProductOrderKeyDTO = new SupplierProductOrderKeyDTO();
            supplierProductOrderKeyDTO.setOrderNo(supplierOrders.get(0).getOrderNo());
            supplierProductOrderKeyDTO.setPayStatus(Boolean.TRUE);
            if ("YH_SR".equals(supplierMerchantBySupplierId.getAppId())) {
                log.debug("通知首荣成功订单:" + orderNo);
                SupplierOrderPayDTO payDTO = new SupplierOrderPayDTO();
                payDTO.setOrderNo(supplierOrders.get(0).getSupplierOrderNo()).setOrderStatus(200);
                booleanResult = shouRongSupplierClient.notifyOrder(notify, payDTO);
                supplierProductOrderKeyDTO.setSupplierStatus("200");
            } else if ("YH_YJYK".equals(supplierMerchantBySupplierId.getAppId())) {
                log.debug("通知昱极成功订单:" + orderNo);
                SupplierOrderPayDTO payDTO = new SupplierOrderPayDTO();
                payDTO.setOrderNo(supplierOrders.get(0).getSupplierOrderNo()).setOrderStatus(200);
                booleanResult = yujiSupplierClient.notifyOrder(notify, payDTO);
            }
            orderRpcService.updateSupplierProductOrderStatus(supplierProductOrderKeyDTO);
        });

    }

    @Override
    public void sendSync(NotifyOrderType notifyOrderType, Order order) {
        String orderNo = order.getNo();

        Notify notify = new Notify();
        notify.setStatus(NotifyStatus.INIT).setRequestNum(1).setOrderNo(orderNo);
        //订单转换
        YihuOrderNotifyDTO yihuOrderNotifyDTO = convertOrderToNotify(notifyOrderType, order);
        //供应商订单
        if (notifyOrderType.getValue() != NotifyOrderType.CREATED.getValue() &&
                notifyOrderType.getValue() != NotifyOrderType.CLOSED.getValue()) {
            SupplierProductOrderKeyDTO supplierProductOrderKeyDTO = new SupplierProductOrderKeyDTO();
            supplierProductOrderKeyDTO.setOrderNo(orderNo).setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS);
            List<SupplierProductOrder> supplierProductOrder = orderRpcService.getSupplierProductOrder(supplierProductOrderKeyDTO);
            //暂时固定昱极
            yihuOrderNotifyDTO.setSupplierOrderNo(supplierProductOrder.get(0).getSupplierOrderNo())
                    .setSupplierType("YH_YJYK");
        }
        yihuSupplierClient.notifyOrder(notify, yihuOrderNotifyDTO);
    }

    private YihuOrderNotifyDTO convertOrderToNotify(NotifyOrderType notifyOrderType, Order order) {
        //优惠项
        Map<Long, OrderDiscountItem> discountItemMap = convertOrderDiscountToMap(order.getOrderDiscounts());
        //包裹信息,只取第一个
        ShopOrderPackage shopOrderPackage = order.getShopOrderPackages() != null && !order.getShopOrderPackages().isEmpty() ? order.getShopOrderPackages().get(0) : null;
        //收货地址
        YihuOrderUserAddressDTO thirdUserAddressInfo = new YihuOrderUserAddressDTO();
        thirdUserAddressInfo
                .setName(order.getOrderReceiver().getName())
                .setProvince(Long.valueOf(order.getOrderReceiver().getAreaCode().get(0)))
                .setCity(Long.valueOf(order.getOrderReceiver().getAreaCode().get(1)))
                .setDistrict(Long.valueOf(order.getOrderReceiver().getAreaCode().get(2)))
                .setMobile(order.getOrderReceiver().getMobile())
                .setAddress(order.getOrderReceiver().getAddress());
        //是否包含处方
        AtomicReference<Boolean> isIncludePrescription = new AtomicReference<>(false);
        //子订单转换
        List<YihuOrderDetailDTO> thirdOrderDetailList = Lists.newArrayList();
        order.getShopOrders().get(0).getShopOrderItems().forEach(shopOrderItem -> {
            ItemExtra extra = shopOrderItem.getExtra();
            if (null != extra.getIsPrescriptionMedicine() && extra.getIsPrescriptionMedicine()) {
                isIncludePrescription.set(true);
            }
            YihuOrderDetailDTO yihuOrderDetailDTO = new YihuOrderDetailDTO();
            yihuOrderDetailDTO.setOrderId(order.getId())
                    .setQuantity(shopOrderItem.getNum())
                    .setDiscountAmt(BigDecimal.ZERO)
                    .setDrugCode(shopOrderItem.getProductNo())
                    .setDrugName(shopOrderItem.getProductName())
                    .setPrice(AmountCalculateHelper.toYuan(shopOrderItem.getSalePrice()))
                    .setRealAmt(AmountCalculateHelper.toYuan(shopOrderItem.getDealPrice()))
                    .setTotalAmt(AmountCalculateHelper.toYuan(shopOrderItem.getSalePrice() * shopOrderItem.getNum()))
                    .setTotalDiscountAmt(BigDecimal.ZERO);
            OrderDiscountItem orderDiscountItem = discountItemMap.get(shopOrderItem.getId());
            if (null != orderDiscountItem) {
                yihuOrderDetailDTO.setDiscountAmt(AmountCalculateHelper.toYuan(orderDiscountItem.getDiscountAmount() / shopOrderItem.getNum()))
                        .setTotalDiscountAmt(AmountCalculateHelper.toYuan(orderDiscountItem.getDiscountAmount()));
            }

            thirdOrderDetailList.add(yihuOrderDetailDTO);
        });

        //Order 转为YihuOrderNotifyDTO
        YihuOrderNotifyDTO yihuOrderNotifyDTO = new YihuOrderNotifyDTO();
        yihuOrderNotifyDTO.setCardId(null)
                .setUserId(order.getBuyerId())
                .setPolicyNo(order.getBuyerIdCode())
                .setBizCode(order.getNo())
                .setApplyTime(null)
                .setCancelTime(null)
                .setCouponAmt(AmountCalculateHelper.toYuan(order.getOrderPayment().getDiscountAmount()))
                .setCourierNumber("")
                .setCourierCompanyCode("")
                .setDiscountPostageFee("0")
                .setHasPrescription(isIncludePrescription.get() ? "Y" : "N")
                .setPostageFee(AmountCalculateHelper.toYuan(order.getOrderPayment().getFreightAmount()))
                .setPrescriptionPdf("")
                .setRealAmt(AmountCalculateHelper.toYuan(order.getOrderPayment().getPayAmount()))
                .setRefundTime(null)
                .setSubmitTime(order.getCreateTime())
                .setTotalAmt(AmountCalculateHelper.toYuan(
                        order.getOrderPayment().getTotalAmount() + order.getOrderPayment().getFreightAmount())
                )
                .setStatus("")
                .setThirdOrderDetailList(thirdOrderDetailList)
                .setThirdUserAddressInfo(thirdUserAddressInfo);
        //如果存在快递
        if (shopOrderPackage != null) {
            yihuOrderNotifyDTO.setCourierCompany(shopOrderPackage.getExpressCompanyName())
                    .setCourierNumber(shopOrderPackage.getExpressNo())
                    .setCourierNumber(shopOrderPackage.getExpressNo());
        }
        //店铺优惠券码,一单最多一张
        if (null != order.getOrderDiscounts() && !order.getOrderDiscounts().isEmpty()) {
            List<OrderDiscount> shopCoupon = order.getOrderDiscounts().stream().filter(orderDiscount -> DiscountSourceType.SHOP_COUPON.getValue().equals(orderDiscount.getSourceType().getValue())).toList();
            if (!shopCoupon.isEmpty()) {
                yihuOrderNotifyDTO.setShopCouponId(shopCoupon.get(0).getSourceId().toString());
            }
        }
        convertOrderType(notifyOrderType, order, yihuOrderNotifyDTO);
        return yihuOrderNotifyDTO;
    }

    // 支持多供应商
    private List<YihuOrderNotifyDTO> convertOrderToNotifyList(NotifyOrderType notifyOrderType, Order order) {
        // 优惠项
        Map<Long, OrderDiscountItem> discountItemMap = convertOrderDiscountToMap(order.getOrderDiscounts());

        // 收货地址
        YihuOrderUserAddressDTO thirdUserAddressInfo = new YihuOrderUserAddressDTO();
        thirdUserAddressInfo
                .setName(order.getOrderReceiver().getName())
                .setProvince(Long.valueOf(order.getOrderReceiver().getAreaCode().get(0)))
                .setCity(Long.valueOf(order.getOrderReceiver().getAreaCode().get(1)))
                .setDistrict(Long.valueOf(order.getOrderReceiver().getAreaCode().get(2)))
                .setMobile(order.getOrderReceiver().getMobile())
                .setAddress(order.getOrderReceiver().getAddress());

        // 是否包含处方
        AtomicReference<Boolean> isIncludePrescription = new AtomicReference<>(false);

        // 获取所有包裹信息 (已查询好的列表)
        List<ShopOrderPackage> shopOrderPackages = order.getShopOrderPackages() != null
                && !order.getShopOrderPackages().isEmpty()
                ? order.getShopOrderPackages()
                : Collections.emptyList();

        // 构建包裹ID到包裹对象的映射
        Map<Long, ShopOrderPackage> packageIdMap = shopOrderPackages.stream()
                .collect(Collectors.toMap(ShopOrderPackage::getId, pkg -> pkg));

        // 按供应商ID和包裹ID分组处理子订单
        Map<Long, Map<Long, List<ShopOrderItem>>> supplierPackageItemsMap = new HashMap<>();

        // 处理所有店铺订单中的商品项
        for (ShopOrder shopOrder : order.getShopOrders()) {
            for (ShopOrderItem item : shopOrder.getShopOrderItems()) {
                Long supplierId = item.getSupplierId();
                Long packageId = item.getPackageId(); // 假设ShopOrderItem有getPackageId()方法

                // 初始化供应商分组
                supplierPackageItemsMap.computeIfAbsent(supplierId, k -> new HashMap<>());

                // 初始化包裹分组
                supplierPackageItemsMap.get(supplierId).computeIfAbsent(packageId, k -> new ArrayList<>())
                        .add(item);
            }
        }

        // 为每个供应商创建通知对象
        List<YihuOrderNotifyDTO> resultList = new ArrayList<>();

        // 遍历每个供应商及其包裹
        for (Map.Entry<Long, Map<Long, List<ShopOrderItem>>> supplierEntry : supplierPackageItemsMap.entrySet()) {
            Long supplierId = supplierEntry.getKey();
            Map<Long, List<ShopOrderItem>> packageItemsMap = supplierEntry.getValue();

            // 遍历该供应商的每个包裹
            for (Map.Entry<Long, List<ShopOrderItem>> packageEntry : packageItemsMap.entrySet()) {
                Long packageId = packageEntry.getKey();
                List<ShopOrderItem> shopOrderItems = packageEntry.getValue();
                // 重置处方标志
                isIncludePrescription.set(false);
                // 子订单转换
                List<YihuOrderDetailDTO> thirdOrderDetailList = new ArrayList<>();
                for (ShopOrderItem shopOrderItem : shopOrderItems) {
                    ItemExtra extra = shopOrderItem.getExtra();
                    if (null != extra.getIsPrescriptionMedicine() && extra.getIsPrescriptionMedicine()) {
                        isIncludePrescription.set(true);
                    }
                    SupplierMerchantDTO supplierMerchantBySupplierId = goodsRpcService.getSupplierMerchantBySupplierId(supplierId);

                    SupplierProductOrderKeyDTO supplierProductOrderKeyDTO = new SupplierProductOrderKeyDTO();
                    supplierProductOrderKeyDTO.setSupplierId(supplierId).setOrderNo(order.getNo()).setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS);
                    List<SupplierProductOrder> supplierProductOrder = orderRpcService.getSupplierProductOrder(supplierProductOrderKeyDTO);

                    ShopOrderPackage packageInfo = packageIdMap.get(shopOrderItem.getPackageId());
                    YihuOrderDetailDTO yihuOrderDetailDTO = new YihuOrderDetailDTO();
                    yihuOrderDetailDTO.setOrderId(order.getId())
                            .setQuantity(shopOrderItem.getNum())
                            .setDiscountAmt(BigDecimal.ZERO)
                            .setDrugCode(shopOrderItem.getProductNo())
                            .setDrugName(shopOrderItem.getProductName())
                            .setPrice(AmountCalculateHelper.toYuan(shopOrderItem.getSalePrice()))
                            .setRealAmt(AmountCalculateHelper.toYuan(shopOrderItem.getDealPrice()))
                            .setTotalAmt(AmountCalculateHelper.toYuan(shopOrderItem.getSalePrice() * shopOrderItem.getNum()))
                            .setTotalDiscountAmt(BigDecimal.ZERO)
                            .setItemId(shopOrderItem.getId())
                            .setAfsNo(shopOrderItem.getAfsNo())
                            .setStatus(shopOrderItem.getAfsStatus().getValue().toString());
                    if (packageInfo != null) {
                        yihuOrderDetailDTO
                                .setCourierNumber(packageInfo.getExpressNo())
                                .setCourierCompany(packageInfo.getExpressCompanyName())
                                .setCourierCompanyCode(packageInfo.getExpressCompanyCode())
                                .setPackageStatus(packageInfo.getStatus().getValue().toString()).setPackageId(shopOrderItem.getPackageId());
                    }
                    if (!supplierProductOrder.isEmpty()) {
                        yihuOrderDetailDTO.setSupplierOrderNo(supplierProductOrder.get(0).getSupplierOrderNo());
                    }
                    if (supplierMerchantBySupplierId != null) {
                        yihuOrderDetailDTO.setSupplierType(supplierMerchantBySupplierId.getMchId());
                    }
                    OrderDiscountItem orderDiscountItem = discountItemMap.get(shopOrderItem.getId());
                    if (null != orderDiscountItem) {
                        yihuOrderDetailDTO.setDiscountAmt(AmountCalculateHelper.toYuan(orderDiscountItem.getDiscountAmount() / shopOrderItem.getNum()))
                                .setTotalDiscountAmt(AmountCalculateHelper.toYuan(orderDiscountItem.getDiscountAmount()));
                    }

                    thirdOrderDetailList.add(yihuOrderDetailDTO);
                }

                // Order 转为 YihuOrderNotifyDTO
                YihuOrderNotifyDTO yihuOrderNotifyDTO = new YihuOrderNotifyDTO();
                yihuOrderNotifyDTO.setCardId(null)
                        .setUserId(order.getBuyerId())
                        .setPolicyNo(order.getBuyerIdCode())
                        .setBizCode(order.getNo())
                        .setApplyTime(order.getOrderPayment().getPayTime())
                        .setCancelTime(null)
                        .setCouponAmt(AmountCalculateHelper.toYuan(order.getOrderPayment().getDiscountAmount()))
                        .setCourierNumber("")
                        .setCourierCompanyCode("")
                        .setDiscountPostageFee("0")
                        .setHasPrescription(isIncludePrescription.get() ? "Y" : "N")
                        .setPostageFee(AmountCalculateHelper.toYuan(order.getOrderPayment().getFreightAmount()))
                        .setPrescriptionPdf("")
                        .setRealAmt(AmountCalculateHelper.toYuan(order.getOrderPayment().getPayAmount()))
                        .setRefundTime(null)
                        .setSubmitTime(order.getCreateTime())
                        .setTotalAmt(AmountCalculateHelper.toYuan(
                                order.getOrderPayment().getTotalAmount() + order.getOrderPayment().getFreightAmount())
                        )
                        .setStatus("")
                        .setThirdOrderDetailList(thirdOrderDetailList)
                        .setThirdUserAddressInfo(thirdUserAddressInfo);
                // 店铺优惠券码,一单最多一张
                if (null != order.getOrderDiscounts() && !order.getOrderDiscounts().isEmpty()) {
                    List<OrderDiscount> shopCoupon = order.getOrderDiscounts().stream()
                            .filter(orderDiscount -> DiscountSourceType.SHOP_COUPON.getValue().equals(orderDiscount.getSourceType().getValue()))
                            .toList();
                    if (!shopCoupon.isEmpty()) {
                        yihuOrderNotifyDTO.setShopCouponId(shopCoupon.get(0).getSourceId().toString());
                    }
                }

                convertOrderType(notifyOrderType, order, yihuOrderNotifyDTO);
                resultList.add(yihuOrderNotifyDTO);
            }
        }
        return resultList;
    }


    /**
     * 合并相同policyNo的订单，合并thirdOrderDetailList
     */
    private List<YihuOrderNotifyDTO> mergeOrders(List<YihuOrderNotifyDTO> originalList,Long packageId) {
        // 按policyNo分组
        Map<String, List<YihuOrderNotifyDTO>> groupedOrders = originalList.stream()
                .collect(Collectors.groupingBy(YihuOrderNotifyDTO::getPolicyNo));

        List<YihuOrderNotifyDTO> mergedList = new ArrayList<>();

        for (Map.Entry<String, List<YihuOrderNotifyDTO>> entry : groupedOrders.entrySet()) {
            List<YihuOrderNotifyDTO> group = entry.getValue();
            if (group.isEmpty()) continue;

            // 使用第一个订单作为基础
            YihuOrderNotifyDTO mergedOrder = group.get(0);

            // 合并所有thirdOrderDetailList
            List<YihuOrderDetailDTO> mergedDetails = new ArrayList<>();
            for (YihuOrderNotifyDTO order : group) {
                mergedDetails.addAll(order.getThirdOrderDetailList());
            }
            mergedOrder.setThirdOrderDetailList(mergedDetails);
            // 设置特定字段为null
            List<YihuOrderNotifyDTO> splitOrderList = splitOrders(originalList);
            //如果同一个供应商不拆分
            if(!checkSameSupplierType(splitOrderList)){
                mergedOrder.setCourierCompany(null);
                mergedOrder.setCourierNumber(null);
                mergedOrder.setSupplierOrderNo(null);
                mergedOrder.setSupplierType(null);
            }else{
                mergedOrder.setSupplierOrderNo(mergedOrder.getThirdOrderDetailList().get(0).getSupplierOrderNo());
                mergedOrder.setSupplierType(mergedOrder.getThirdOrderDetailList().get(0).getSupplierType());
                mergedOrder.setCourierCompany(mergedOrder.getThirdOrderDetailList().get(0).getCourierCompany());
                mergedOrder.setCourierNumber(mergedOrder.getThirdOrderDetailList().get(0).getCourierNumber());
            }
            mergedList.add(mergedOrder);
        }

        return mergedList;
    }

    /**
     * 转换订单状态为医护状态
     * 根据订单状态设置不同的值
     * 医护订单状态【1-待付款, 2-待补方, 3-待收货, 4-已完成, 5-已退款, 6-已失效,9-待发货，10-退款中】
     *
     * @return
     */
    private void convertOrderType(NotifyOrderType notifyOrderType, Order order, YihuOrderNotifyDTO yihuOrderNotifyDTO) {

        switch (notifyOrderType) {
            case CREATED:
                yihuOrderNotifyDTO.setStatus("1");
                break;
            case CLOSED:
                yihuOrderNotifyDTO.setStatus("6");
                yihuOrderNotifyDTO.setCancelTime(order.getUpdateTime());
                break;
            case DELIVER:
                yihuOrderNotifyDTO.setStatus("3");
                break;
            case PAID:
                yihuOrderNotifyDTO.setStatus("9");
                break;
            case CONFIRM:
                yihuOrderNotifyDTO.setStatus("4");
                break;
            case RETURN_REFUND_AGREE:
                yihuOrderNotifyDTO.setStatus("10");
                break;
            case RETURNED_REFUNDED:
                yihuOrderNotifyDTO.setStatus("5");
                break;
            case ACCOMPLISH:
                break;
        }
    }

    private Map<Long, OrderDiscountItem> convertOrderDiscountToMap(List<OrderDiscount> orderDiscounts) {
        if (orderDiscounts.isEmpty()) {
            return Maps.newHashMap();
        }
        //所有折扣项
        List<OrderDiscountItem> discountItems = CollUtil.emptyIfNull(orderDiscounts)
                .stream()
                .flatMap(
                        discount -> {
                            List<OrderDiscountItem> items = discount.getDiscountItems();
                            //for gc
                            discount.setDiscountItems(null);
                            return CollUtil.emptyIfNull(items).stream()
                                    .peek(item -> item.setSourceType(discount.getSourceType()));
                        }
                ).toList();
        //按店铺商品项分
        return discountItems.stream().collect(Collectors.toMap(OrderDiscountItem::getItemId, v -> v));
    }

}
