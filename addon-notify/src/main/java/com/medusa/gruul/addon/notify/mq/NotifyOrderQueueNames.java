package com.medusa.gruul.addon.notify.mq;

public interface NotifyOrderQueueNames {
    /**
     * 订单发货回调队列
     */
    String NOTIFY_ORDER_DELIVER_CALLBACK_QUEUE = "notify:order:deliver:callback";
    /**
     * 订单创建
     */
    String NOTIFY_ORDER_CREATED_CALLBACK_QUEUE = "notify:order:created:callback";
    /**
     * 订单支付成功
     */
    String NOTIFY_ORDER_PAID_CALLBACK_QUEUE = "notify:order:paid:callback";
    /**
     * 订单确认收货
     */
    String NOTIFY_ORDER_CONFIRM_CALLBACK_QUEUE = "notify:order:confirm:callback";

    /**
     * 订单关闭
     */
    String NOTIFY_ORDER_CLOSE_CALLBACK_QUEUE = "notify:order:close:callback";
    /**
     * 订单完成
     */
//    String NOTIFY_ORDER_ACCOMPLISH_CALLBACK_QUEUE = "notify:order:accomplish:callback";

    /**
     * 订单已同意售后
     */
    String NOTIFY_AFS_AGREE_CALLBACK_QUEUE = "notify:afs:agree:callback";

    /**
     * 订单已拒绝售后
     */
//    String NOTIFY_AFS_REJECT_CALLBACK_QUEUE = "notify:afs:reject:callback";

    /**
     * 退款成功回调
     */
    String NOTIFY_AFS_REFUND_CALLBACK_QUEUE = "notify:afs:refund:callback";

    //===昱极
    /**
     * 订单支付成功
     */
    String NOTIFY_YUJI_ORDER_PAID_CALLBACK_QUEUE = "notify:yuji:order:paid:callback";
}
