package com.medusa.gruul.addon.notify.client;


import com.medusa.gruul.addon.notify.model.YihuOrderNotifyDTO;
import com.medusa.gruul.addon.notify.mp.entity.Notify;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.model.dto.SupplierOrderPayDTO;

/**
 * 供应商医护之家接口
 */
public interface INotifyYihuSupplierClient {
    /**
     * 通知订单
     */
    Result<Object> notifyOrder(Notify notify, YihuOrderNotifyDTO yihuOrderNotifyDTO);

    default String convertRequestUrl(String baseUrl, String apiUrl) {
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        if (!apiUrl.startsWith("/")) {
            baseUrl += "/";
        }
        return baseUrl + apiUrl;
    }
}
