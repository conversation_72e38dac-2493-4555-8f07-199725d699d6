package com.medusa.gruul.addon.notify.client.impl;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.addon.notify.client.INotifyYihuSupplierClient;
import com.medusa.gruul.addon.notify.model.YihuOrderNotifyDTO;
import com.medusa.gruul.addon.notify.model.enums.NotifyStatus;
import com.medusa.gruul.addon.notify.mp.entity.Notify;
import com.medusa.gruul.addon.notify.mp.entity.NotifyRecord;
import com.medusa.gruul.addon.notify.mp.mapper.NotifyMapper;
import com.medusa.gruul.addon.notify.mp.mapper.NotifyRecordMapper;
import com.medusa.gruul.addon.notify.properties.YihuProperties;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.api.model.param.NotifyYHParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 医护供应商接口
 */
@Slf4j
@Component
@AllArgsConstructor
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class NotifyYihuSupplierClientImpl implements INotifyYihuSupplierClient {

    private final NotifyRecordMapper notifyRecordMapper;

    private final NotifyMapper notifyMapper;

    private final YihuProperties yihuProperties;

    //通知订单
    private final String ORDER = "/api/innovate/drug/mall/orderNotify";
    //获取token
    private final String THIRD_TOKEN = "/api/user/login/third/getToken";


    @Override
    public Result<Object> notifyOrder(Notify notify, YihuOrderNotifyDTO yihuOrderNotifyDTO) {
        Result<Object> result = Result.ok();
        String url = convertRequestUrl(yihuProperties.getDomain(), ORDER);

        NotifyRecord record = new NotifyRecord();
        record.setRetryId(0L)
                .setRequestUrl(url)
                .setSendParam(JSONUtil.toJsonStr(yihuOrderNotifyDTO))
                .setStatus(NotifyStatus.INIT);
        try {
            log.debug("调用医护订单通知接口，请求参数：{}", yihuOrderNotifyDTO);
            String str = HttpUtil.post(url,
                    JSONUtil.toJsonStr(yihuOrderNotifyDTO));
            record.setResponse(str);
            record.setStatus(NotifyStatus.SUCCESS);
            log.debug("调用医护订单通知接口成功，返回结果：{}", str);
            result = JSONUtil.toBean(str, Result.class);
        } catch (Exception e) {
            if (null != e.getMessage()) {
                record.setResponse(JSONUtil.toJsonStr(e.getMessage()));
            }
            record.setStatus(NotifyStatus.FAIL);
            result.setCode(500);
            result.setMsg("请求失败");
            log.error("调用医护订单通知接口失败，e: {}", e.getMessage());
        } finally {
            notifyRecord(notify, record);
        }
        return result;
    }


    private void notifyRecord(Notify notify, NotifyRecord record) {
        notify.setStatus(record.getStatus());
        if (null == notify.getId()) {
            notifyMapper.insert(notify);
        } else {
            notifyMapper.updateById(notify);
        }
        record.setRetryId(notify.getId());
        notifyRecordMapper.insert(record);
    }

    private void sign(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&businessNo=" + params.get("businessNo");
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        params.put("sign", sign);
    }

    private void signToken(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&userId=" + params.get("userId");
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        params.put("sign", sign);
    }

    /**
     * 通知医护签名
     *
     * @param supplierMerchant 供应商配置
     * @param notifyYHParam
     */
    private void notifySign(SupplierMerchant supplierMerchant, NotifyYHParam notifyYHParam) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&thirdOrderId=" + notifyYHParam.getThirdOrderId() + "&timestamp=" + notifyYHParam.getTimestamp();
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        notifyYHParam.setSign(sign);
    }

    /**
     * 续购校验签名
     *
     * @param supplierMerchant 供应商配置
     * @param
     * @return
     */
    private void checkSign(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&userRightId=" + params.get("userRightId") + "&timestamp=" + params.get("timestamp");
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        params.put("sign", sign);
    }

}
