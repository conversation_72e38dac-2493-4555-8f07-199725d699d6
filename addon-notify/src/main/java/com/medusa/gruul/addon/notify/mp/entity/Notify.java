package com.medusa.gruul.addon.notify.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.addon.notify.model.enums.NotifyStatus;
import com.medusa.gruul.common.mp.model.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 通知供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Getter
@Setter
@TableName("t_notify")
@Accessors(chain = true)
public class Notify extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 请求次数
     */
    private Integer requestNum;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 状态 0--失败 1--成功
     */
    private NotifyStatus status;

    /**
     * 订单号
     */
    private String orderNo;
}
