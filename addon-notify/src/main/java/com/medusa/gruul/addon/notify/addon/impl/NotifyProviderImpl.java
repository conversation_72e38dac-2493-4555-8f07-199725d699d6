package com.medusa.gruul.addon.notify.addon.impl;

import com.medusa.gruul.addon.notify.addon.NotifyProvider;
import com.medusa.gruul.addon.notify.model.enums.NotifyOrderType;
import com.medusa.gruul.addon.notify.model.enums.NotifyStatus;
import com.medusa.gruul.addon.notify.mp.entity.Notify;
import com.medusa.gruul.addon.notify.mp.entity.NotifyRecord;
import com.medusa.gruul.addon.notify.mp.service.INotifyRecordService;
import com.medusa.gruul.addon.notify.mp.service.INotifyService;
import com.medusa.gruul.addon.notify.service.SendNotifyService;
import com.medusa.gruul.common.addon.provider.AddonProvider;
import com.medusa.gruul.common.addon.provider.AddonProviders;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.global.model.constant.Services;
import com.medusa.gruul.order.api.addon.OrderAddonConstant;
import com.medusa.gruul.order.api.entity.Order;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
@DubboService
@AddonProviders
@RequiredArgsConstructor
public class NotifyProviderImpl implements NotifyProvider {

    private final INotifyService notifyService;

    private final INotifyRecordService notifyRecordService;
    private final SendNotifyService sendNotifyService;

    @Override
    @Log("获取通知的请求参数")
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getSendParamByOrderNo")
    public String getSendParamByOrderNo(String orderNo) {
        Notify notify = notifyService.lambdaQuery()
                .eq(Notify::getOrderNo, orderNo)
                .eq(Notify::getStatus, NotifyStatus.SUCCESS.getValue())
                .orderByDesc(Notify::getUpdateTime)
                .last("limit 1")
                .one();
        if (null == notify) {
            throw new ServiceException("不存在通知记录");
        }
        NotifyRecord record = notifyRecordService.lambdaQuery()
                .eq(NotifyRecord::getRetryId, notify.getId())
                .orderByDesc(NotifyRecord::getUpdateTime)
                .last("limit 1")
                .one();

        return record.getSendParam();
    }

    @Override
    @Log("获取通知的请求参数")
    @AddonProvider(service = Services.GRUUL_MALL_ORDER, supporterId = OrderAddonConstant.ORDER_NOTIFY_SUPPORT_ID, methodName = "sendSync")
    public void sendSync(Order order) {
        sendNotifyService.sendSync(NotifyOrderType.CREATED, order);
    }
}
