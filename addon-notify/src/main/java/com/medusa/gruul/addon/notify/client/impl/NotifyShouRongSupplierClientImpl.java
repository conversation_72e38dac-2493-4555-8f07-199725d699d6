package com.medusa.gruul.addon.notify.client.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.hash.Hashing;
import com.medusa.gruul.addon.notify.client.INotifyShouRongSupplierClient;
import com.medusa.gruul.addon.notify.client.INotifyYujiSupplierClient;
import com.medusa.gruul.addon.notify.model.enums.NotifyStatus;
import com.medusa.gruul.addon.notify.mp.entity.Notify;
import com.medusa.gruul.addon.notify.mp.entity.NotifyRecord;
import com.medusa.gruul.addon.notify.mp.mapper.NotifyMapper;
import com.medusa.gruul.addon.notify.mp.mapper.NotifyRecordMapper;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.api.model.dto.SupplierOrderPayDTO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.Charset;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * sr供应商接口
 */
@Slf4j
@Component
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class NotifyShouRongSupplierClientImpl implements INotifyShouRongSupplierClient {
    @Autowired
    private NotifyRecordMapper notifyRecordMapper;
    @Autowired
    private NotifyMapper notifyMapper;
    @Autowired
    private GoodsRpcService goodsRpcService;
    private SupplierMerchant supplierMerchant;
    private final String MerchantId = "YH_SR";

    private final String POST_PAY_ORDER = StrUtil.format("pay");

    private static final ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true);
    }
    /**
     * 获取供应商账户配置
     *
     * @return
     */
    private SupplierMerchant getYujiMerchant() {
        if (supplierMerchant != null) {
            return supplierMerchant;
        }
        supplierMerchant = goodsRpcService.getSupplierMerchantByAppId(MerchantId);
        return supplierMerchant;
    }

    @Override
    public Result<Boolean> notifyOrder(Notify notify, SupplierOrderPayDTO supplierOrderPayDTO) {
        SupplierMerchant yujiMerchant = getYujiMerchant();
        if (yujiMerchant == null) {
            throw new GlobalException("调用首荣账户配置不存在");
        }
        String random = RandomUtil.randomString(28);
        String timeMis = String.valueOf(System.currentTimeMillis());

        supplierOrderPayDTO.setAppKey(yujiMerchant.getKeyPublic());
        Result<Boolean> result = Result.ok();
        Map<String, Object> signParams = RedisUtil.toBean(supplierOrderPayDTO, TreeMap.class);
        signParams.put("timestamp", timeMis);
        signParams.put("randomStr", random);
        signParams.put("appSecret", yujiMerchant.getKeyPrivate());
        signParams.put("appKey", yujiMerchant.getKeyPublic());
        //签名
        String signature = getSignature(getYujiMerchant(), signParams);

        String fullUrl = convertRequestUrl(yujiMerchant.getRequestDomainUrl(), POST_PAY_ORDER);
        //请求记录
        NotifyRecord record = new NotifyRecord();
        record.setRetryId(0L)
                .setRequestUrl(fullUrl)
                .setSendParam(JSONUtil.toJsonStr(supplierOrderPayDTO))
                .setStatus(NotifyStatus.INIT);
        log.debug("调用首荣通知订单支付成功接口，请求参数：{}", supplierOrderPayDTO);
        try {
            String str = HttpUtil.createPost(fullUrl)
                    .header("timestamp", timeMis)
                    .header("randomStr", random)
                    .header("signature", signature)
                    .body(toJsonStr(supplierOrderPayDTO))
                    .execute().body();
            log.debug("调用首荣通知订单支付成功接口成功，结果：{}", str);
            record.setResponse(str);
            record.setStatus(NotifyStatus.SUCCESS);
            JSONObject resultData = JSON.parseObject(str);
            if (!resultData.get("code").toString().equals("200")) {
                result.setCode(500);
                result.setMsg(resultData.get("msg").toString());
                return result;
            }
            result.setData(Boolean.TRUE);
        } catch (Exception e) {
            record.setStatus(NotifyStatus.FAIL);
            result.setCode(500);
            result.setMsg("请求失败");
            log.error("调用首荣通知订单支付成功接口失败，e：{},requestRecordEntity:{}", e.getMessage(), toJsonStr(supplierOrderPayDTO));
        } finally {
            notifyRecord(notify, record);
        }

        return result;
    }


    private void notifyRecord(Notify notify, NotifyRecord record) {
        notify.setStatus(record.getStatus());
        if (null == notify.getId()) {
            notifyMapper.insert(notify);
        } else {
            notifyMapper.updateById(notify);
        }
        record.setRetryId(notify.getId());
        notifyRecordMapper.insert(record);
    }

    /**
     * 获取签名
     *
     * @param supplierMerchant 供应商账户
     * @param params           参数
     * @return
     */
    public static String getSignature(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        Map<String, Object> signatureMap = JSON.parseObject(JSON.toJSONString(params), TreeMap.class);
        List<String> paramList = signatureMap.entrySet().stream().map(entry ->
                String.format("%s=%s", entry.getKey(), entry.getValue())).collect(Collectors.toList());
        log.debug(String.join("&", paramList));
        return Hashing.md5().hashBytes(String.join("&",
                paramList).getBytes(Charset.defaultCharset())).toString();
    }
    private String toJsonStr(Object object) {
        JSONConfig config = new JSONConfig();
        config.setIgnoreNullValue(false);
        return JSONUtil.toJsonStr(object, config);
    }
    public static String sortJson(String json) {

        try {
            JsonNode rootNode = objectMapper.readTree(json);

            if (rootNode.isObject()) {
                // 处理单个对象
                ObjectNode sortedObjectNode = sortObjectNode((ObjectNode) rootNode);
                return objectMapper.writeValueAsString(sortedObjectNode);
            } else if (rootNode.isArray()) {
                // 处理列表对象
                ArrayNode sortedArrayNode = objectMapper.createArrayNode();
                for (JsonNode node : rootNode) {
                    if (node.isObject()) {
                        ObjectNode sortedObjectNode = sortObjectNode((ObjectNode) node);
                        sortedArrayNode.add(sortedObjectNode);
                    } else {
                        // 如果不是对象，则直接添加到新数组中（假设我们不需要排序非对象节点）
                        sortedArrayNode.add(node);
                    }
                }
                return objectMapper.writeValueAsString(sortedArrayNode);
            } else {
                // 不处理其他类型
                return json;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new GlobalException("签名排序异常");
        }
    }

    private static ObjectNode sortObjectNode(ObjectNode objectNode) {
        // 创建一个 TreeMap 用于排序键
        Map<String, JsonNode> sortedMap = new TreeMap<>();
        Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            sortedMap.put(field.getKey(), field.getValue());
        }

        // 创建一个新的 ObjectNode
        ObjectNode sortedObjectNode = objectMapper.createObjectNode();
        sortedMap.forEach(sortedObjectNode::set);
        return sortedObjectNode;
    }

}
