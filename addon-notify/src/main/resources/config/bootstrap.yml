server:
  port: 8491
spring:
  main:
    allow-circular-references: true
  application:
    name: addon-hot
  profiles:
    active: prod
  cloud:
    nacos:
      server-addr: **************:8884
      discovery:
        namespace: ${spring.profiles.active}
        ip: *************
      config:
        namespace: ${spring.profiles.active}
        file-extension: yml
        shared-configs:
          - dataId: application-common.${spring.cloud.nacos.config.file-extension}
          - dataId: addon.${spring.cloud.nacos.config.file-extension}