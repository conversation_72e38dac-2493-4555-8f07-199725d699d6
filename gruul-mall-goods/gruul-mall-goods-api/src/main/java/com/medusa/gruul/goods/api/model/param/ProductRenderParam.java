package com.medusa.gruul.goods.api.model.param;
import com.medusa.gruul.goods.api.model.vo.ProductRenderVO;
import lombok.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
@Builder
@Data
@NoArgsConstructor
public class ProductRenderParam extends Page<ProductRenderVO> {
    /**
     * 店铺id
     */
    private Long shopId;
    private long pCurrent;

    private long pSize;
    public ProductRenderParam(Long shopId,long pCurrent,long pSize) {
        super(pCurrent,pSize);
        this.pCurrent = pCurrent;
        this.pSize = pSize;
        this.shopId = shopId;
    }
}
