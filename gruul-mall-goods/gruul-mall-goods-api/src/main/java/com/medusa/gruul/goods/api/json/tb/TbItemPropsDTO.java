package com.medusa.gruul.goods.api.json.tb;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/7
 * @describe 淘宝规格组
 */
@Data
@Accessors(chain = true)
public class TbItemPropsDTO {

    private List<TbItemPropsValues> values;
    /**
     * 规格名称
     */
    private String name;
    private String pid;


    /**
     * 淘宝规格组
     */
    @Data
    @Accessors(chain = true)
    public static class TbItemPropsValues {
        private String vid;
        /**
         * 规格值
         */
        private String name;

    }
}
