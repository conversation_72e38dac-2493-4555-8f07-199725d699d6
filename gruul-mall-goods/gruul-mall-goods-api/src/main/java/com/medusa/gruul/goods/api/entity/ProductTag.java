package com.medusa.gruul.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Getter
@Setter
@TableName("t_product_tag")
public class ProductTag extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 标签名
     */
    private String tagName;

    /**
     * 排序
     */
    private Integer sort;
}
