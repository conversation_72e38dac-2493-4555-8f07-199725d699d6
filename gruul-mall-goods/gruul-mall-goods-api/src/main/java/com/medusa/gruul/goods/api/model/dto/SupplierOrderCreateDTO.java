package com.medusa.gruul.goods.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SupplierOrderCreateDTO {
    private String appKey;
    /**
     * 系统订单号
     */
    private String orderId;
    /**
     * 商品结算价总和（单位为分）
     */
    private Integer totalPrice;
    /**
     * 运费结算⾦额（单位为分）
     */
    private Integer deliverSettlePrice;
    /**
     * 药品列表数组 ⼀次最多购买5种药
     */
    private List<SupplierOrderItemCreateDTO> itemList;
    /**
     * 药对应的处⽅笺url 地址，因为可能存在多张的可能性
     */
    private String[] prescriptionUrl;
    /**
     * 收件⼈实体类
     */
    private SupplierReceiveDTO receive;
    /**
     * 需要记录的额外信息
     */
    private String extra;
    /**
     * 总重量单位为g
     */
    private Integer heavy;
}
