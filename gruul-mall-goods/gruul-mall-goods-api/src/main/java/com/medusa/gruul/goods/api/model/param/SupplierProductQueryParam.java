package com.medusa.gruul.goods.api.model.param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.goods.api.entity.Product;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SupplierProductQueryParam extends Page<Product> {

    /**
     * 是否需要报备
     */
    private Boolean isNeedFiling;
    /**
     * 报备结果
     */
    private Boolean filingResult;

    private Long supplierId;


}
