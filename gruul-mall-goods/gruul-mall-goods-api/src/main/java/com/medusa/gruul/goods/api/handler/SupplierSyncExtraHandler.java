package com.medusa.gruul.goods.api.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.fastjson2.FastJson2;
import com.medusa.gruul.common.mp.IFastJson2TypeHandler;
import com.medusa.gruul.goods.api.model.dto.SupplierMerchantSyncExtraDTO;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;


/**
 * <AUTHOR>
 */

@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class SupplierSyncExtraHandler extends IFastJson2TypeHandler {

	@Override
	public TypeReference<?> getTypeReference() {
		return new TypeReference<List<SupplierMerchantSyncExtraDTO>>() {
		};
	}

	@Override
	protected Object parse(String json) {
		return JSON.parseObject(json, getTypeReference(), FastJson2.readFeature());
	}
}