package com.medusa.gruul.goods.api.model.dto;

import com.medusa.gruul.goods.api.enums.FrequencyEnum;
import com.medusa.gruul.goods.api.enums.UsageEnum;
import com.medusa.gruul.goods.api.enums.UsageUnitEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 药额外数据DTO
 *
 * <AUTHOR>
 * @Description ProductDrugExtraDTO.java
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductDrugExtraDTO implements Serializable {

    /**
     * 药品名称
     */
    private String name;

    /**
     * 药品包装
     */
    private String packaging;

    /**
     * 药品69码
     */
    private String barcode;

    /**
     * 药品通用名称
     */
    private String commonName;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 剂型
     */
    private String dosageForm;
    /**
     * 规格
     */
    private String specs;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 用法用量
     */
    private String usageDosage;
    /**
     * 注意事项
     */
    private String note;
    /**
     * 主要成分
     */
    private String composition;
    /**
     * 贮藏方式
     */
    private String storageMode;

    /**
     * 是否为药物
     */
    private Boolean isDrug;

    /**
     * 是否为处方药
     */
    private Boolean isPrescriptionDrug;
    /**
     * 用法,枚举值
     */
    private UsageEnum usage;

    /**
     * 用量,实际数值
     */
    private String dosage;

    /**
     * 用量单位,枚举值
     */
    private UsageUnitEnum dosageUnit;

    /**
     * 用药频率,枚举值
     */
    private FrequencyEnum frequency;
    /**
     * 对应icd疾病清单
     */
    private List<Integer> icdList;

    /**
     * 对应疾病标准icd10编码
     */
    private List<String> diseasesIcdList;

    /**
     * 药品编码
     */
    private String no;


    /**
     * 药品skuId
     */
    private String skuId;


}
