package com.medusa.gruul.goods.api.model.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Accessors(chain = true)
public class CancelCardRightParam {
    /**
     * 用户权益id
     */
    private String userRightId;
    /**
     * 权益id
     */
    private String rightId;
    /**
     * 使用人
     */
    private String contactId;
    /**
     * 本系统appid
     */
    private String appCode;
    /**
     * 商户订单号
     */
    private String outTradeNo;
    /**
     * 支付订单号
     */
    private String transactionId;
    /**
     * 第三方订单号
     */
    private String thirdOrderId;
}
