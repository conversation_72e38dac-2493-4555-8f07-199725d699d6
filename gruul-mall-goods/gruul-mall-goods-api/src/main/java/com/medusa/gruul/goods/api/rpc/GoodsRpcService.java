package com.medusa.gruul.goods.api.rpc;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.custom.aggregation.classify.dto.CategoryRankDTO;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.entity.*;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import com.medusa.gruul.goods.api.model.dto.*;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.param.*;
import com.medusa.gruul.goods.api.model.vo.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> xiaoq
 * @description : GoodsRpcService.java
 * @date : 2022/7/17 20:44
 */
public interface GoodsRpcService {

    /**
     * 平台 获取商品信息
     *
     * @param platformProductParam 查询条件
     * @return 符合条件得商品信息
     */
    Page<PlatformProductVO> queryProductInfoByParam(PlatformProductParam platformProductParam);

    /**
     * 获取当前店铺上架商品数量
     *
     * @return 当前店铺上架商品数量
     */
    Result<Long> getShopProductByPutaway();


    /**
     * 查询运费模版id是否被商品使用
     *
     * @param templateId
     * @return Boolean
     */
    Boolean checkProductByTemplateId(Long templateId);


    /**
     * 根据shopId productId 获取商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return Product
     */
    Product getProductInfo(@NotNull Long shopId, @NotNull Long productId);


    /**
     * 批量获取商品 信息
     *
     * @param shopProductKeys shopId,productId
     * @return map<{ shopId, productId }, product>
     */
    Map<ShopProductKey, Product> getProductBatch(Set<ShopProductKey> shopProductKeys);


    /**
     * 根据 平台三级类目id 获取商品信息
     *
     * @param levelCategoryList     list<三级类目id>
     * @param platformCategoryParam 查询数据
     * @return <<Page<ApiPlatformProductVO>>
     */
    Result<Page<ApiPlatformProductVO>> getProductInfoByPlatformCategoryId(List<Long> levelCategoryList, PlatformCategoryParam platformCategoryParam);


    /**
     * 获取平台三级类目下商品数量
     *
     * @param thirdIds 平台类目三级ids
     * @return map<平台类目ids, 商品数量>
     */
    Map<Long, Integer> getProductNumByPlatformThirdCategoryId(@NotNull Set<Long> thirdIds);

    /**
     * 获取随机商品
     *
     * @param productRandomParam 商品随机参数
     * @return 随机商品
     */
    Page<Product> randomGoods(ProductRandomParam productRandomParam);

    /**
     * 根据平台三级类目ids 获取  ApiProductVO
     *
     * @param categoryRank 类目等级dto
     * @return ApiPlatformProductVO
     */
    Page<ApiProductVO> getApiProductInfoByPlatformCategoryId(CategoryRankDTO categoryRank);


    /**
     * 获取条件商品信息 包含以删除商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    Product getConditionProductInfo(Long shopId, Long productId);

    /**
     * 用户收藏店铺数量
     *
     * @param userId 用户userid
     * @return 收藏店铺数量
     */
    Long shopFollow(Long userId);


    /**
     * 获取当前签约类目下是否有商品
     *
     * @param signingCategorySecondIds 签约类目二级ids
     * @param shopId                   店铺id
     * @return 是否可以删除
     */
    boolean getSigningCategoryProduct(Set<Long> signingCategorySecondIds, Long shopId);

    /**
     * 根据{@code supplierId}和{@code productId}获取商品信息
     *
     * @param supplierId 供应商ID
     * @param productId  商品id
     * @return #{@link Product}
     */
    Product getProductBySupplierIdAndProductId(@NotNull Long supplierId, @NotNull Long productId);

    /**
     * 查询单个供应商sku信息
     *
     * @param skuId 商品skuid
     * @return 商品sku 价格信息
     */
    SupplierGoodsSkuVO getSupperSkuBySkuId(Long skuId, Long shopId);
    /**
     * 查询产品所有sku价格信息
     *
     * @param productId  产品id
     * @return 商品sku 价格信息
     */
    List<SupplierGoodsSkuVO> getSupperSkuByProductId(Long productId, Long shopId);
    /**
     * 创建/更新供应商获得金额
     *
     * @param supplierGoodsSpecSkuDTO
     * @return
     */
    void saveOrUpdateSupperSku(SupplierGoodsSpecSkuDTO supplierGoodsSpecSkuDTO);

    /**
     * 供应商列表
     *
     * @return List
     */
    List<SupplierBankVO> getSupplierList();

    /**
     * 根据shopId查询商品列表首页专用
     *
     * @param productRenderParam
     * @return
     */
    IPage<ProductRenderVO> queryProductRenderVO(ProductRenderParam productRenderParam);
    /**
     * 查询供应商关联产品
     * @param productId 产品id
     * @return
     */
    List<SupplierProductSkuVO> getSupplierSkuByProductId(Long productId);
    /**
     * 查询sku与供应商关联产品
     * @param skuId 产品skuid
     * @return
     */
    SupplierProductSkuVO getSupplierSkuBySkuId(Long skuId);

    /**
     * 供应商api发货
     * @param supplierProductDeliverDTO
     */
    SupplierProductCardVO supplierProductDeliver(SupplierProductDeliverDTO supplierProductDeliverDTO);

    /**
     * 供应商api销卡
     * @param orderItemId 订单项id
     */
    void supplierProductOrderRefund(Long orderItemId);

    /**
     * 根据Id获取供应商
     * @param supplierId
     * @return
     */
    Supplier getSupplierById(Long supplierId);

    /**
     * 根据Id获取供应商商户
     * 有缓存
     * @param supplierId
     * @return
     */
    SupplierMerchantDTO getSupplierMerchantBySupplierId(Long supplierId);

    /**
     * 根据id获取商户
     * @param id
     * @return
     */
    SupplierMerchant getSupplierMerchantById(Long id);

    /**
     * 根据appId获取商户
     * @param appId
     * @return
     */
    SupplierMerchant getSupplierMerchantByAppId(String appId);
    /**
     * 根据mchId获取商户
     * @param mchId
     * @return
     */
    SupplierMerchant getSupplierMerchantByMchId(String mchId);

    /**
     * 根据供应商id获取供应商系统token
     * @param supplierId 供应商id
     * @return
     */
    String getTokenBySupplierId(Long supplierId,Long userId,Long supplierUserId);


    /**
     * 根据供应商id获取供应商系统token
     * @return
     */
    String getTokenBySupplierId(Long supplierId,Long userId);

    /**
     * 续购校验
     * @param checkParams
     */
    void checkRepurchase(Map<String, Object> checkParams);

    /**
     * 通知医护续购成功
     * @return
     */
    Result<Object> notifyYHRepurchaseSuccess(NotifyYHParam notifyYHParam);

    /**
     * 退用户供应商卡续购权益
     * @return
     */
    void cancelCardRightRefund(String orderNo);

    /**
     * 查询供应商产品
     * @param id
     */
    SupplierProduct getSupplierProductById(Long id);

    /**
     * 使用供应商图文问诊权益
     * @param userId 本系统userId
     * @param userRightId 供应商权益id
     * @param appCode 供应商appCode
     * @param extra 自定义额外参数，限制字节数128
     * @return 问诊链接
     */
    String useSupplierDiagnoseRight(Long userId, String userRightId, String msgId, String appCode, String extra);

    /**
     * 预推送问诊
     * @param param 预推送参数
     * @return 预推送返回的msgId
     */
    String preSupplierDiagnoseRight(String appCode, SupplierPreDiagnoseParam param);

    /**
     * 供应商业务开卡
     * @param param
     * @return
     */
    SupplierUserCardDTO supplierBusinessCard(SupplierBusinessCardParam param);

    /**
     * 新增/修改供应商与店铺产品
     * @param supplierProductDTO
     */
    void supplierProductSave(SupplierProductDTO supplierProductDTO);
    /**
     * 批量处理
     * 新增/修改供应商与店铺产品
     * @param supplierProductList
     * @param shopId 店铺id
     * @param supplierId
     */
    void supplierProductSaveBatch(Long supplierId, Long shopId, List<SupplierProductDTO> supplierProductList);

    /**
     * 只更新供应商产品
     * @param supplierProduct
     */
    void supplierProductUpdate(SupplierProduct supplierProduct);



    /**
     * 只更新供应商产品
     * @param supplierProducts
     */
    void supplierProductUpdateBatch(List<SupplierProduct> supplierProducts);

    /**
     * 查询
     * @param queryParam
     * @return
     */
    List<SupplierProduct> getSupplierProduct(SupplierProductQueryParam queryParam);


    /**
     * 产品上下架
     *
     * @param productStatusChange 商品状态更改信息
     * @param status              产品上下架状态
     */
    void updateProductStatus(ProductStatusChangeDTO productStatusChange, ProductStatus status);

    /**
     * 获取店铺商品list
     *
     * @param shopId
     * @return List<Product>
     */
    List<Product> getProductListByShopId(Long shopId);

    /**
     * 查询昱极快递费用
     * @param yujIDeliveryChargeDTO
     * @return
     */
    Result getYujIDeliveryCharges(YujIDeliveryChargeDTO yujIDeliveryChargeDTO);

    /**
     * 查询订单状态
     * @param supplierOrderStatusDTO
     * @return
     */
    YujiOrderCompareVo getOrderStatus(SupplierOrderStatusDTO supplierOrderStatusDTO);


    /**
     * 对比订单状态
     * @param mchId  供应商id
     * @param beforeMinutes 查询多多少分钟前的数据
     * @param status t_order订单表状态
     * @return
     */
    void compareYujIOrderStatus(String mchId,Long beforeMinutes,Long status);


    /**
     * 创建订单
     * @param createDTO
     * @return
     */
    String supplierCreateOrder(String mchId, Long userId, SupplierOrderCreateDTO createDTO);

    /**
     * 支付成功
     * @param mchId
     * @param payDTO
     * @return
     */
    void supplierPayOrder(String mchId, SupplierOrderPayDTO payDTO);

    /**
     * 根据分类名称和上级分类id获取分类id，没有就创建
     * @param productCategoryDTO
     * @return
     */
    CategoryLevel getProductCategoryByDto(ProductCategoryDTO productCategoryDTO);

    /**
     * 商品信息修改
     *
     * @param productDto 商品信息
     */
    void updateProduct(ProductDTO productDto);

    /**
     * 获取商户
     */
    List<SupplierMerchant> getSupplierMerchantList();
}
