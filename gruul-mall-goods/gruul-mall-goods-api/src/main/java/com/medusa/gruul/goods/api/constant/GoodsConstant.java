package com.medusa.gruul.goods.api.constant;
/**
 * 商品常量
 *
 * <AUTHOR>
 */
public interface GoodsConstant {
    
    /**
     * 商品详情
     * <p>
     * gruul:mall:goods:product:{shopId}:{productId}
     */
    String GOODS_DETAIL_CACHE_KEY = "gruul:mall:goods:product";

    /**
     * 一键复制商品详情
     * <a href="https://www.99api.com/test?testid=35&commid=25"/>
     */
    String COPY_GOODS_URL="https://api09.99api.com/{}/detail?apikey={}&itemid={}";

    /**
     * 一键复制成功返回code
     */
    String SUCCESS_CODE="0000";
    /**
     * JSON返回对象key
     */
    String RET_CODE = "retcode";
    String DATA ="data";
    String ITEM ="item";

    /**
     * 京东sku图片前缀
     */
    String JD_SKU_IMAGES_URL="https://img14.360buyimg.com/n0/{}";
    String IMAGE_PATH="imagePath";
    String ORIGINAL_PRICE="originalPrice";
    String PRICE="price";


    /**
     * 淘宝
     */
    String ID="id";
    String URL="url";
    String SEMICOLON=";";

    /**
     * 阿里
     */
    String AL_URL_SUF=".html";
    String DISCOUNT_PRICE="discountPrice";
    String CAN_BOOK_COUNT ="canBookCount";
    String SALE_COUNT ="saleCount";
    /**
     * 默认图片
     */
    String DEFAULT_PIC ="https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com/gruul/20230401/ee9f7e779f424cd88b2e6da2eb334eca.jpg";


    /**
     * 供应商商品常量
     */
    String ADDON_SUPPLIER_NEW_COUNT_PRODUCT_KEY = "newCreatedProduct";
    String ADDON_SUPPLIER_IRREGULARITY_PRODUCT_KEY = "irregularityProduct";

}
