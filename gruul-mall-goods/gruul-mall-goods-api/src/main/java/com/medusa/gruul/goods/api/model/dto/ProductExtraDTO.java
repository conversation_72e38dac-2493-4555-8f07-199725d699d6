package com.medusa.gruul.goods.api.model.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 产品额外数据DTO
 *
 * <AUTHOR>
 * @Description ProductExtraDTO.java
 * @date 2023-06-15 15:26
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductExtraDTO implements Serializable {

    /**
     * 店铺类目信息
     */
    private CategoryLevel shopCategory;


    /**
     * 平台类目信息
     */
    private CategoryLevel platformCategory;


    /**
     * 自定义折扣百分比
     */
    private Long customDeductionRatio;


    /**
     * 违规信息
     */
    private ProductViolationDTO productViolation;

    /**
     * 产品属性
     */
    private List<ProductFeaturesValueDTO> productAttributes;

    /**
     * 产品参数
     */
    private List<ProductFeaturesValueDTO> productParameters;

    /**
     * 价格设置
     */
    private ConsignmentPriceSettingDTO consignmentPriceSetting;


    /**
     * 供应商自定义折扣百分比
     */
    private Long supplierCustomDeductionRatio;
    /**
     * 供应商产品id
     */
    private Long supplierProductId;

    /**
     * 校验产品属性快照是否正常
     *
     * @param userSelectedForm 用户选择的属性表单
     * @return 产品关联快照
     */
    public Set<ProductFeaturesValueDTO> checkProductAttributes(Map<Long, Set<Long>> userSelectedForm) {
        List<ProductFeaturesValueDTO> productAttributes = getProductAttributes();
        if (CollUtil.isEmpty(productAttributes)) {
            return Set.of();
        }
        Map<Long, Set<Long>> finalUserSelectedForm = MapUtil.emptyIfNull(userSelectedForm);
        return productAttributes.stream()
                .map(attribute -> {
                    Set<FeatureValueDTO> featureValues;
                    if (attribute == null || CollUtil.isEmpty(featureValues = attribute.validAndGetValues(finalUserSelectedForm.get(attribute.getId())))) {
                        return null;
                    }
                    return attribute.setFeatureValues(featureValues);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }


}
