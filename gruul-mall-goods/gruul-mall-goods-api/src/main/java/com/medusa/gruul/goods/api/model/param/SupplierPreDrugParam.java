package com.medusa.gruul.goods.api.model.param;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 预推送问诊药品信息
 * @Author: weijian
 */
@Data
@Accessors(chain = true)
public class SupplierPreDrugParam {

    /**
     * 药品名称
     */
    private String drugName;

    /**
     * 药品编码
     */
    private String drugCode;
    /**
     * 69码， 目前先传药品编码
     */
    private String sourceCode;

    /**
     * 别名
     */
    private String drugCommonName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 药品包装， 例如：盒、瓶、袋等
     */
    private String drugPackage;

    /**
     * 价格
     */
    private String price;

    /**
     * 用药几次
     */
    private String frequency;

    /**
     * 用量
     */
    private String usage;

    /**
     * 用药单位
     */
    private String doseUnit;

    /**
     * 每次剂量
     */
    private String doseEachTime;


}
