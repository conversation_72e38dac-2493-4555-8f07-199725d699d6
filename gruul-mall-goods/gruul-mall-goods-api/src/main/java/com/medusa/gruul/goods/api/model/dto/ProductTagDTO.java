package com.medusa.gruul.goods.api.model.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.goods.api.entity.ProductTag;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProductTagDTO extends Page<ProductTag> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 排序值
     */
    private Integer sort;

    private Long id;
}
