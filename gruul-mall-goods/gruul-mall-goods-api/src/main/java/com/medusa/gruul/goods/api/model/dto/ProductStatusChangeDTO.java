package com.medusa.gruul.goods.api.model.dto;

import com.medusa.gruul.common.model.base.ShopProductKey;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 商品状态DTO
 *
 * <AUTHOR>
 * @Description ProductStatusDTO.java
 * @date 2023-06-19 14:40
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductStatusChangeDTO {

    /**
     * 商品id
     */
    @NotNull
    @Size(min = 1)
    private Set<ShopProductKey> keys;

    /**
     * 违规信息
     */
    private ProductViolationDTO productViolation;


}
