package com.medusa.gruul.goods.api.model.dto;

import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.global.model.o.BaseDTO;
import com.medusa.gruul.goods.api.model.enums.PricingType;
import com.medusa.gruul.storage.api.entity.StorageSku;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/8/8
 * @describe 代销价格设置DTO
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ConsignmentPriceSettingDTO implements BaseDTO {
    /**
     * 设价方式
     */
    private PricingType type;
    /**
     * 销售价比值
     */
    private Long sale;
    /**
     * 划线价比值
     */
    private Long scribe;

    @Override
    public void validParam() {
        if (type != null) {
            if (sale == null || scribe == null) {
                throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "代销商品比值不能为空");
            }
            if (type == PricingType.RATE) {
                if (!(sale >= CommonPool.NUMBER_ZERO && sale <= CommonPool.UNIT_CONVERSION_TEN_THOUSAND * CommonPool.UNIT_CONVERSION_HUNDRED)) {
                    throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "销售价数值超出限制");
                }
                if (!(scribe >= CommonPool.NUMBER_ZERO && scribe <= CommonPool.UNIT_CONVERSION_TEN_THOUSAND * CommonPool.UNIT_CONVERSION_HUNDRED)) {
                    throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "划线价数值超出限制");
                }
            }

            if (type == PricingType.REGULAR) {
                if (!(sale >= CommonPool.NUMBER_ZERO && sale <= 9000 * CommonPool.UNIT_CONVERSION_TEN_THOUSAND)) {
                    throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "销售价数值超出限制");
                }
                if (!(scribe >= CommonPool.NUMBER_ZERO && scribe <= 9000 * CommonPool.UNIT_CONVERSION_TEN_THOUSAND)) {
                    throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "划线价数值超出限制");
                }
            }

        }
    }


    /**
     * 代销金额计算
     *
     * @param consignmentPriceSetting 商品代销配置
     * @param storageSkus             商品下SKU信息
     */
    public void consignmentCalculate(ConsignmentPriceSettingDTO consignmentPriceSetting, List<StorageSku> storageSkus) {
        boolean isRegular = consignmentPriceSetting.getType() == PricingType.REGULAR;
        storageSkus.forEach(sku -> {
            long newSalePrice = isRegular
                    ? consignmentPriceSetting.getSale()
                    : (sku.getSalePrice() * consignmentPriceSetting.getSale() / 1000000);
            sku.setSalePrice(sku.getSalePrice() + newSalePrice);
            long newPrice = isRegular
                    ? consignmentPriceSetting.getScribe()
                    : (sku.getPrice() * consignmentPriceSetting.getScribe() / 1000000);
            sku.setPrice(sku.getPrice() + newPrice);
        });
    }
}
