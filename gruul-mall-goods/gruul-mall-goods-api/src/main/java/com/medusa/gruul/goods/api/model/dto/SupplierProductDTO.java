package com.medusa.gruul.goods.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* 供应商产品
*
* <AUTHOR> 
*/
@Data
@Accessors(chain = true)
public class SupplierProductDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 产品编码
     */
    private String productNo;
    /**
     * skuid
     */
    private String skuId;
    /**
     * 产品信息
     */
    private String productInfo;

    /**
     * 店铺产品
     */
    private ProductDTO productDTO;
    /**
     *
     */
    private Long shopId;
    /**
     * 是否为处方药
     */
    private Boolean isPrescriptionMedicine;

    /**
     * 是否需要报备
     */
    private Boolean isNeedFiling;
    /**
     * 报备结果，true成功
     */
    private Boolean filingResult;

    private String remark;

    /**
     * 上架状态 * 0 下架 1 上架
     */
    private Integer status;
    /**
     * 库存数量
     */
    private  Long stockNum;
    /**
     * 额外药品数据
     */
    private ProductDrugExtraDTO  drugExtra;
}