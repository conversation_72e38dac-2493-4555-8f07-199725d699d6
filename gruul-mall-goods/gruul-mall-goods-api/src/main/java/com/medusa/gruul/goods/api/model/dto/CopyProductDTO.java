package com.medusa.gruul.goods.api.model.dto;

import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.storage.api.dto.SpecGroupDTO;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@Data
@Accessors(chain = true)
public class CopyProductDTO {
    /**
     * id
     */
    private Long id;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 展示图片
     */
    private String pic;
    /**
     * 宽屏展示图片
     */
    private String widePic;
    /**
     * 画册图片，连产品图片限制为6张，以逗号分割
     */
    @NotNull
    private String albumPics;
    /**
     * 视频url
     */
    private String videoUrl;
    /**
     * 销量
     */
    private Integer sale;
    /**
     * 商品详情
     */
    private String detail;
    /**
     * 卖点描述
     */
    private String saleDescribe;
    /**
     * 评分
     */
    private BigDecimal score;


    /**
     * 规格组
     */
    @Valid
    private List<SpecGroupDTO> specGroups;

    /**
     * sku列表
     */
    private List<CopySkuDto> skus;

    @Data
    @Accessors(chain = true)
    public static class CopySkuDto {
        /**
         * id
         */
        private Long id;
        /**
         * 规格值
         */
        private List<String> specs;
        /**
         * 初始库存 仅新增sku时 可以使用
         */
        private Integer initStock;
        /**
         * 库存类型
         */
        private StockType stockType;

        /**
         * 初始销量
         */
        @NotNull
        @Min(0)
        private Integer initSalesVolume;
        /**
         * 限购类型
         */
        @NotNull
        private LimitType limitType;
        /**
         * 限购数量
         */
        @NotNull
        @Min(0)
        private Integer limitNum;
        /**
         * sku图片
         */
        @NotBlank
        @Size(max = 1024)
        private String image;

        /**
         * 原价 单位豪
         */
        @NotNull
        @Min(0)
        private Double price;

        /**
         * 真实销售价 单位豪 1豪 = 0.01分
         */
        @NotNull
        @Min(0)
        private Double salePrice;

        /**
         * 规格重量 单位/千克
         */
        private BigDecimal weight;


        /**
         * 最低购买数量
         */
        private Long minimumPurchase = 0L;
    }
}
