package com.medusa.gruul.goods.api.enums;

import lombok.Getter;

/**
 * 用法
 * <AUTHOR>
 */
@Getter
public enum UsageEnum {
    INIT(0, "厂商未提供"),
    ORAL(2, "口服"),
    ORAL_BREAKFAST(3, "早餐时口服"),
    ORAL_AFTER_DINNER(4, "晚餐后口服"),
    ORAL_BEFORE_MEAL(5, "餐前口服"),
    ORAL_AFTER_MEAL(6, "餐后口服"),
    ORAL_WITH_MEAL(7, "随餐口服"),
    CHEWABLE(8, "嚼服"),
    CHEW(9, "咀嚼"),
    LOZENGE(10, "含服"),
    DISSOLVE(12, "溶解后服用"),
    SUBLINGUAL(13, "舌下用药"),
    EYE_DROP(14, "滴眼"),
    NASAL_DROP(15, "滴鼻"),
    THROAT_SPRAY(16, "喷喉"),
    NASAL_INHALATION(17, "鼻腔吸入"),
    INHALATION(18, "吸入用药"),
    TRACHEAL_MEDICATION(19, "气管内用药"),
    EXTERNAL_USE(20, "外用"),
    WOUND_DRESSING(21, "敷伤口"),
    SKIN_APPLICATION(22, "擦皮肤"),
    LOCAL_USE(23, "局部用药"),
    JOINT_CAVITY(24, "关节腔内用药"),
    PERITONEAL_USE(25, "腹腔用药"),
    VAGINAL_INSERTION(26, "阴塞"),
    VAGINAL_CLEANSING(27, "阴道清洗"),
    VAGINAL_USE(28, "阴道用药"),
    RECTAL_INSERTION(29, "肛塞"),
    RECTAL_USE(30, "直肠用药"),
    OTHER(31, "其他"),
    NASAL_SPRAY(1, "鼻腔喷入"),
    INJURY_SPRAY(85, "喷患处"),
    MOUTH_RINSE(87, "漱口"),
    VAGINAL_ADMINISTRATION(88, "阴道给药"),
    OTHER_LOCAL_USE(89, "其他局部用药途径"),
    OTHER_MEDICATION_ROUTE(90, "其他用药途径"),
    RINSING(123, "冲洗"),
    RECTAL_MEDICATION(125, "肛门用药"),
    ORAL_INHALATION(126, "口腔吸入"),
    SUBLINGUAL_LOZENGE(128, "舌下含服"),
    SWALLOW(129, "吞服"),
    EXTERNAL_RINSING(120, "外用冲洗"),
    NASAL_SPRAY_EXT(127, "喷鼻"),
    INJURY_APPLICATION(86, "涂患处"),
    EAR_DROP(91, "滴耳"),
    EMPTY_STOMACH(104, "空腹服用");

    private final int code;
    private final String description;

    UsageEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UsageEnum of(Integer code) {
        if (null == code) {
            return null;
        }
        for (UsageEnum usageEnum : UsageEnum.values()) {
            if (usageEnum.getCode() == code) {
                return usageEnum;
            }
        }
        return null;
    }

    public static UsageEnum ofOrNull(Integer code) {
        for (UsageEnum usageEnum : UsageEnum.values()) {
            if (usageEnum.getCode() == code && code != UsageEnum.INIT.getCode()) {
                return usageEnum;
            }
        }
        return null;
    }

}
