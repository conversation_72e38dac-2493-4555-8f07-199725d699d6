package com.medusa.gruul.goods.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SupplierReceiveDTO {
    private String appKey;
    /**
     * 收件地址-省编码
     */
    private Integer provinceCode;
    /**
     * 收件地址-市 编码
     */
    private Integer cityCode;
    /**
     * 收件地址-区县 编码
     */
    private Integer areaCode;
    /**
     * 收件⼈⼿机号
     */
    private String mobile;
    /**
     * 收件⼈名
     */
    private String name;
    /**
     * 收件⼈详细地址
     */
    private String address;
    /**
     * 快递公司,暂定固定传 shentong
     */
    private String expressCompany;
}
