package com.medusa.gruul.goods.api.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.mp.FastJson2TypeHandler;
import com.medusa.gruul.common.mp.handler.type.LongListTypeHandler;
import com.medusa.gruul.common.mp.handler.type.ServiceBarrierTypeHandler;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.global.model.enums.ServiceBarrier;
import com.medusa.gruul.goods.api.enums.StorageType;
import com.medusa.gruul.goods.api.handler.DistributionModeTypeHandler;
import com.medusa.gruul.goods.api.handler.ProductTagTypeHandler;
import com.medusa.gruul.goods.api.model.dto.ProductDrugExtraDTO;
import com.medusa.gruul.goods.api.model.dto.ProductExtraDTO;
import com.medusa.gruul.goods.api.model.dto.ProductTagDTO;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.enums.ProductType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息表
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@Data
@EqualsAndHashCode(of = {"shopId"}, callSuper = true)
@Accessors(chain = true)
@TableName(value = "t_product", autoResultMap = true)
public class Product extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 商品编号
     */
    private String no;
    /**
     * 供应商id
     */
    @TableField("provider_id")
    private Long providerId;

    /**
     * 供应商id(S2B2C)
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 运费模板id
     */
    @TableField("freight_template_id")
    private Long freightTemplateId;

    /**
     * 分类id
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 平台类目id
     */
    @TableField("platform_category_id")
    private Long platformCategoryId;

    /**
     * 品牌id
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 商品名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 展示图片
     */
    @TableField("pic")
    private String pic;

    /**
     * 宽屏展示图片
     */
    @TableField("wide_pic")
    private String widePic;

    /**
     * 画册图片，连产品图片限制为6张，以逗号分割
     */
    @TableField("album_pics")
    private String albumPics;

    /**
     * 视频url
     */
    @TableField("video_url")
    private String videoUrl;

    /**
     * 状态(默认上架，ProductStatus)
     */
    @TableField("`status`")
    private ProductStatus status;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;


    /**
     * 商品价格
     */
    private Long salePrice;

    /**
     * 商品类型
     */
    private ProductType productType;

    /**
     * 服务保障["ServiceBarrier","ServiceBarrier"]
     */
    @TableField(value = "service_ids", typeHandler = ServiceBarrierTypeHandler.class)
    private List<ServiceBarrier> serviceIds;

    /**
     * 商品详情
     */
    @TableField("detail")
    private String detail;

    /**
     * 规格是否展开
     */
    @TableField("is_open_specs")
    private Integer openSpecs;


    /**
     * 卖点描述
     */
    @TableField("sale_describe")
    private String saleDescribe;

    /**
     * 评分
     */
    @TableField("score")
    private BigDecimal score;


    /**
     * 销售类型
     */
    @TableField("sell_type")
    private SellType sellType;


    /**
     * 配送方式(0--商家配送，1--快递配送，2--同城配送，3--门店)
     */
    @TableField(value = "distribution_mode", typeHandler = DistributionModeTypeHandler.class)
    private List<DistributionMode> distributionMode;


    @TableField(value = "sale_prices", typeHandler = LongListTypeHandler.class)
    private List<Long> salePrices;

    /**
     * 是否为药物
     */
    private Boolean isMedicine;

    /**
     * 是否为处方药
     */
    private Boolean isPrescriptionMedicine;

    /**
     * 附加数据
     */
    @TableField(typeHandler = FastJson2TypeHandler.class)
    private ProductExtraDTO extra;
    /**
     * 药品附加数据
     */
    @TableField(typeHandler = FastJson2TypeHandler.class)
    private ProductDrugExtraDTO drugExtra;

    /**
     * 商品标签
     */
    @TableField(value = "product_tags", typeHandler = ProductTagTypeHandler.class)
    private List<ProductTagDTO> productTags;

    /**
     * 仓储类型
     */
    private StorageType storageType;
    /**
     * 是否是支持的配送方式
     *
     * @param distributionMode 配送方式
     * @return boolean 是否支持 true 支持 false 不支持
     */
    public boolean isSupportDistributionMode(DistributionMode distributionMode) {
        return CollUtil.contains(getDistributionMode(), distributionMode);
    }
}
