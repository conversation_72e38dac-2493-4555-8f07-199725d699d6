package com.medusa.gruul.goods.api.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 商品类型
 *
 * <AUTHOR>
 * @Description ProductType.java
 * @date 2023-06-17 11:06
 */
@Getter
@RequiredArgsConstructor
public enum SupplierProductType {


    /**
     * 权益卡
     */
    CARD_PRODUCT(1),

    /**
     * 权益卡权益
     */
    RIGHT_PRODUCT(2),

    /**
     * 药品
     */
    DRUG_PRODUCT(3),

    ;


    @EnumValue
    private final Integer value;
}
