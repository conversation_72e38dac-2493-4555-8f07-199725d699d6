package com.medusa.gruul.goods.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.custom.aggregation.classify.enums.CategoryLevel;
import com.medusa.gruul.goods.api.entity.ProductCategory;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
@Data
@Accessors(chain = true)
public class ProductCategoryDTO {
    private Long id;
    /**
     * 上级分类的编号：0表示一级分类
     */
    private Long parentId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类级别
     */
    private CategoryLevel level;

    /**
     * 排序
     */
    private Integer sort;
    /**
     * 图片  只有level为三级时 不为空
     */
    private String categoryImg;

    private Long shopId;
}
