package com.medusa.gruul.goods.api.model.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description: 预推送问诊信息
 * @Author: weijian
 */
@Data
@Accessors(chain = true)
public class SupplierPreDiagnoseParam {

    /**
     * 外部系统用户权益id
     */
    private String userRightId;

    /**
     * 外部系统用户id
     */
    private String userId;

    /**
     * 本系统的订单ID
     */
    private String orderNo;

    /**
     * 药品信息
     */
    private List<SupplierPreDrugParam> drugList;

}
