package com.medusa.gruul.goods.api.json.al;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/3
 *
 */
@Data
@Accessors(chain = true)
public class AlSkuPropsJsonDTO {
    private String fid;
    /**
     *  规格名称
     */
    private String prop;
    /**
     * 规格值
     */
    private List<AlPropDto> value;

    @Data
    public static class AlPropDto {
        private String imageUrl;
        private String name;
    }
}
