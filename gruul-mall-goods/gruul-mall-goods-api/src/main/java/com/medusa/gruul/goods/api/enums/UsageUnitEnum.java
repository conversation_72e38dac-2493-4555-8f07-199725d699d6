package com.medusa.gruul.goods.api.enums;

import lombok.Getter;

/**
 * 用量单位
 *
 * <AUTHOR>
 */
@Getter
public enum UsageUnitEnum {
    INIT(0, "厂商未提供"),
    DOSE(36, "剂"),
    TABLET(37, "片"),
    PILL(38, "粒"),
    PILL_FORM(39, "丸"),
    DROPS(40, "滴"),
    SPRAY(41, "喷"),
    PATCH(42, "贴"),
    UNIT(43, "枚"),
    PRESS(44, "揿"),
    PACK(45, "包"),
    TEASPOON(46, "汤匙"),
    PROPER_AMOUNT(47, "适量"),
    GRAMS(48, "g"),
    MILLIGRAMS(49, "mg"),
    MICROGRAMS(50, "ug"),
    NANOGRAMS(51, "ng"),
    LITERS(52, "L"),
    MILLILITERS(53, "ml"),
    INTERNATIONAL_UNITS(54, "IU"),
    UNITS(55, "U"),
    <PERSON><PERSON>LE(121, "吸"),
    BOX(32, "盒"),
    <PERSON><PERSON><PERSON><PERSON>(33, "瓶"),
    BAG(34, "袋"),
    TU<PERSON>(35, "支");

    private final int code;
    private final String description;

    UsageUnitEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UsageUnitEnum of(Integer code) {
        if (null == code) {
            return null;
        }
        for (UsageUnitEnum usageUnitEnum : UsageUnitEnum.values()) {
            if (usageUnitEnum.getCode() == code) {
                return usageUnitEnum;
            }
        }
        return null;
    }

    public static UsageUnitEnum ofOrNull(Integer code) {
        for (UsageUnitEnum usageUnitEnum : UsageUnitEnum.values()) {
            if (usageUnitEnum.getCode() == code && code != UsageUnitEnum.INIT.getCode()) {
                return usageUnitEnum;
            }
        }
        return null;
    }

}
