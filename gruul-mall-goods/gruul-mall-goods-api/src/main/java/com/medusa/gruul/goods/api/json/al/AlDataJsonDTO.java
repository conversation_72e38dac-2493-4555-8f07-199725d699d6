package com.medusa.gruul.goods.api.json.al;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/3
 * @describe 阿里data JSOn对象
 */
@Data
public class AlDataJsonDTO {
    private List<String> images;
    private Map<String, JSONObject> skuMap;
    private String title;
    private String videoUrl;
    private List<AlSkuPropsJsonDTO> skuProps;
    private String desc;
    private List<ShowPriceJsonDto> showPriceRanges;

    @Data
    public static class ShowPriceJsonDto{
        /**
         * 起始数量
         */
        private String startAmount;
        /**
         * 价钱
         */
        private String price;
        private String beginAmount;
    }
}
