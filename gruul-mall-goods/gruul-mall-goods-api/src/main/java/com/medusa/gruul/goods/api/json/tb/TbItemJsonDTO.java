package com.medusa.gruul.goods.api.json.tb;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1
 */
@Data
public class TbItemJsonDTO {
    /**
     * 名称
     */
    private String title;
    /**
     * 销售价格范围
     */
    private String priceRange;
    /**
     * 市场价范围
     * 10-30
     */
    private String marketPriceRange;
    /**
     * 图片
     */
    private List<String> images;

    /**
     * 视频
     */
    private List<String> videos;
    /**
     * sku
     */
    private List<TbSkuJsonDTO> sku;
    /**
     * 描述
     */
    private String desc;
    /**
     * 规格组
     */
    private List<TbItemPropsDTO> props;

}
