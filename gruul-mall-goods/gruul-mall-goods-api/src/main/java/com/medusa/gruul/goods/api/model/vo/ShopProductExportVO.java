package com.medusa.gruul.goods.api.model.vo;


import com.medusa.gruul.goods.api.model.dto.ProductDrugExtraDTO;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;

import com.medusa.gruul.storage.api.enums.StockType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Description: 商品信息Vo
 * @Author: wyy
 * @Date : 2025-07-23 10:25
 */

@Data
@Accessors(chain = true)
public class ShopProductExportVO {

    /**
     * 产品ID
     */
    private Long id;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 分类信息
     */
    private String productCategory;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 状态(默认上架，0--下架，1--上架)
     */
    private ProductStatus status;

    /**
     * 供应商是否上架
     */
    private String supplierStatus;

    /**
     * 库存数量
     */
    private Long stock;

    /**
     * 库存类型
     */
    private StockType stockType;

    /**
     * 销售价格
     */
    private BigDecimal salePrices;

    /**
     * 供应商结算价
     */
    private BigDecimal prices;

    /**
     * 供应商名称
     */
    private String providerName;

    /**
     * 药品附加数据
     */
    private ProductDrugExtraDTO drugExtra;

}

