package com.medusa.gruul.goods.api.model.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Accessors(chain = true)
public class NotifyYHParam {
    private  String userRightId;

    private String rightId;


    private String contactId;


    private String appCode;


    private String sign;


    private Long timestamp;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 支付订单号
     */
    private String transactionId;

    /**
     * 支付时间
     */
    private Date payAt;
    /**
     * 单位 元
     */
    private BigDecimal payAmount;

    /**
     * 下单时间
     */
    private LocalDateTime createDate;

    /**
     * 第三方订单号
     */
    private String thirdOrderId;


    private String remark;

    /**
     * 单位 元
     */
    private BigDecimal totalAmt;
}
