package com.medusa.gruul.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 供应商请求记录
 *
 * <AUTHOR> 
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
@TableName(value = "t_supplier_request_record", excludeProperty = {"deleted"})
public class SupplierRequestRecord extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 供应商id
	*/
	private Long supplierId;
	/**
	 * 系统订单号
	 */
	private String orderNo;
	/**
	* 请求编号
	*/
	private String requestNo;
	/**
	* 请求地址
	*/
	private String requestUrl;
	/**
	* 请求参数
	*/
	private String requestParams;
	/**
	* 最终发送数据
	*/
	private String sendParam;
	/**
	* 返回或回调数据
	*/
	private String notifyParam;

}