package com.medusa.gruul.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * 供应商商品sku
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_supplier_goods_sku")
public class SupplierGoodsSku extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * sku_id
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * product_id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * price
     */
    @TableField("price")
    private Long price;

    /**
     * supplier_id
     */
    @TableField("supplier_id")
    private Long supplierId;

}
