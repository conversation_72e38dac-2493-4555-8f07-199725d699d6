package com.medusa.gruul.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 供应商产品sku关联
 *
 * <AUTHOR> 
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
@TableName(value = "t_supplier_product_sku")
public class SupplierProductSku extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 供应商id
	*/
	private Long supplierId;
	/**
	* 供应商产品id
	*/
	private Long supplierProductId;
	/**
	* skuid
	*/
	private Long storageSkuId;
	/**
	 * 店铺id
	 */
	private Long shopId;
	/**
	* 产品id
	*/
	private Long productId;
	/**
	* 产品信息
	*/
	@TableField(exist = false)
	private String productInfo;


}