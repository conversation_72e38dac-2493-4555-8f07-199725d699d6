package com.medusa.gruul.goods.api.model.param;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 供应商创建卡
 * @Author: weijian
 */
@Data
@Accessors(chain = true)
public class SupplierBusinessCardParam {

    /**
     * 由健康服务系统分配给第三方应用的标识
     */
    private String appCode;

    /**
     * 用户的出生日期
     */
    private String birthDate;

    /**
     * 用户的证件号
     */
    private String certificateNo;

    /**
     * 用户的证件类型
     */
    private String certificateType;

    /**
     * 用户的性别 0 男 1 女
     */
    private String gender;

    /**
     * 用户的手机号码
     */
    private String mobile;

    /**
     * 用户的姓名
     */
    private String name;

    /**
     * 用户是否需要自助登录健康服务系统
     */
    private String selfLogin;

    /**
     * 第三方应用户ID(和医护之家用户ID关联)
     */
    private String thirdUserId;

    /**
     * 第三方业务单号(和医护之家卡号关联)
     */
    private String businessNo;

    /**
     * 产品编码(由医护之家发行)
     */
    private String productCode;
}
