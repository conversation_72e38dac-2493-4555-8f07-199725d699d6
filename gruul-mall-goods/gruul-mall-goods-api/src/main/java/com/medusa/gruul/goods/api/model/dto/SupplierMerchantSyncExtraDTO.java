package com.medusa.gruul.goods.api.model.dto;

import com.medusa.gruul.goods.api.model.CategoryLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商户同步药品信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class SupplierMerchantSyncExtraDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 目标店铺
     */
    private Long shopId;

    /**
     * 平台分类
     */
    private CategoryLevel platformCategory;
    /**
     * 店铺分类
     */
    private CategoryLevel shopCategory;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;


}

