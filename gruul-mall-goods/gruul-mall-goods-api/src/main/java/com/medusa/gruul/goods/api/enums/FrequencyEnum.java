package com.medusa.gruul.goods.api.enums;

import lombok.Getter;

/**
 * 用药频率
 *
 * <AUTHOR>
 */
@Getter
public enum FrequencyEnum {

    INIT(0, "厂商未提供"),
    FIVE_TIMES_DAILY(96, "每日5次"),
    EVERY_SIX_HOURS(97, "每6小时1次"),
    SIX_TIMES_DAILY(98, "每日6次"),
    TEN_TIMES_DAILY(99, "每日10次"),
    FOUR_TIMES_DAILY(61, "每天4次"),
    ONCE_IN_THE_MORNING(101, "每早1次"),
    IMMEDIATELY(56, "立即"),
    AS_NEEDED(57, "必要时"),
    ONCE_DAILY(58, "每天1次"),
    TWICE_DAILY(59, "每天2次"),
    THREE_TIMES_DAILY(60, "每天3次"),
    EVERY_THREE_HOURS(100, "每3小时1次"),
    FIVE_TIMES_DAILY_ALT(62, "每天5次"),
    TEN_TIMES_DAILY_ALT(63, "每天10次"),
    EVERY_HOUR(64, "每小时1次"),
    EVERY_TWO_HOURS(65, "每两小时1次"),
    EVERY_THREE_HOURS_ALT(66, "每三小时1次"),
    EVERY_FOUR_HOURS(67, "每四小时1次"),
    EVERY_SIX_HOURS_ALT(68, "每六小时1次"),
    EVERY_TWELVE_HOURS(69, "每12小时1次"),
    SIX_TIMES_A_DAY(70, "一日六次"),
    EVERY_OTHER_DAY(71, "隔日1次"),
    EVERY_MORNING(72, "每天早上"),
    EVERY_NIGHT(73, "每晚1次"),
    BEFORE_BED(74, "睡前1次"),
    ONCE_A_WEEK(75, "每周1次"),
    TWICE_A_WEEK(76, "每周2次"),
    MAX_THREE_TABLETS_PER_DAY(77, "一日总量不超过3片，连用不得超过1周"),
    SINGLE_DOSE(78, "1次顿服"),
    EVERY_OTHER_DAY_ALT(102, "隔天1次"),
    EVERY_NIGHT_ALT(103, "每天睡前"),
    THREE_TIMES_DAILY_ALT(92, "每日3次"),
    ONCE_DAILY_ALT(93, "每日1次"),
    TWICE_DAILY_ALT(94, "每日2次"),
    FOUR_TIMES_DAILY_ALT(95, "每日4次"),
    INHALE(131, "吸");

    private final int code;
    private final String description;

    FrequencyEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static FrequencyEnum of(Integer code) {
        if (null == code) {
            return null;
        }
        for (FrequencyEnum frequency : FrequencyEnum.values()) {
            if (frequency.getCode() == code) {
                return frequency;
            }
        }
        return null;
    }

    public static FrequencyEnum ofOrNull(Integer code) {
        for (FrequencyEnum frequency : FrequencyEnum.values()) {
            if (frequency.getCode() == code && code != FrequencyEnum.INIT.getCode()) {
                return frequency;
            }
        }
        return null;
    }

}
