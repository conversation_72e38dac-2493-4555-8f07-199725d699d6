package com.medusa.gruul.goods.api.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.fastjson2.FastJson2;
import com.medusa.gruul.common.mp.IFastJson2TypeHandler;
import com.medusa.gruul.goods.api.entity.ProductTag;
import com.medusa.gruul.goods.api.model.dto.ProductTagDTO;
import io.vavr.collection.List;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes({List.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class ProductTagTypeHandler extends IFastJson2TypeHandler {


    @Override
    protected TypeReference<?> getTypeReference() {
        return new TypeReference<List<ProductTagDTO>>() {
        };
    }

    @Override
    protected Object parse(String json) {
        return JSON.parseArray(json, FastJson2.readFeature());
    }
}
