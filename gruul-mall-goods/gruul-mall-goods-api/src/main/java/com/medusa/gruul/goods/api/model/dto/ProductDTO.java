package com.medusa.gruul.goods.api.model.dto;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.global.model.enums.ServiceBarrier;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.enums.StorageType;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import com.medusa.gruul.goods.api.model.enums.GoodsError;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.enums.ProductType;
import com.medusa.gruul.storage.api.dto.SkuDTO;
import com.medusa.gruul.storage.api.dto.SpecGroupDTO;
import io.vavr.control.Option;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 新增或修改产品信息DTO
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 商品编号
     */
    private String no;
    /**
     * 供应商id
     */
    private Long providerId;

    /**
     * 供应商id(S2B2C)
     */
    private Long supplierId;

    /**
     * 供应商产品id(S2B2C)
     */
    private Long supplierProductId;


    /**
     * 配送方式(0--商家配送，1--快递配送，2--同城配送,3--门店)
     */
    @NotNull
    @Size(min = 1)
    private List<DistributionMode> distributionMode;
    /**
     * 运费模板ID
     */
    @NotNull
    private Long freightTemplateId;


    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 店铺类目
     */
    private CategoryLevel shopCategory;

    /**
     * 平台类目
     */
    @NotNull
    @Valid
    private CategoryLevel platformCategory;
    /**
     * 商品名称
     */
    @Size(max = 32, message = "商品名称过长")
    @NotBlank(message = "商品名称不可为空")
    private String name;
    /**
     * 展示图片
     */
    private String pic;
    /**
     * 宽屏展示图片
     */
    private String widePic;
    /**
     * 画册图片，连产品图片限制为6张，以逗号分割
     */
    @NotNull
    private String albumPics;
    /**
     * 视频url
     */
    private String videoUrl;
    /**
     * 状态(默认上架)
     */
    private ProductStatus status;

    /**
     * 服务保障枚举ids
     */
    private List<ServiceBarrier> serviceIds;
    /**
     * 商品详情
     */
    private String detail;
    /**
     * 规格是否展开
     */
    private Boolean openSpecs;

    /**
     * 卖点描述
     */
    private String saleDescribe;
    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 商品类型
     */
    @NotNull
    private ProductType productType;

    /**
     * 销售类型
     */
    @NotNull
    private SellType sellType;

    /**
     * 是否为药物
     */
    private Boolean isMedicine;

    /**
     * 是否为处方药
     */
    private Boolean isPrescriptionMedicine;

    /**
     * 规格组
     */
    @Valid
    private List<SpecGroupDTO> specGroups;

    /**
     * sku列表
     */
    @Valid
    @NotNull
    @Size(min = 1)
    private List<SkuDTO> skus;


    /**
     * 产品属性
     */
    @Valid
    private List<ProductFeaturesValueDTO> productAttributes;

    /**
     * 产品参数
     */
    @Valid
    private List<ProductFeaturesValueDTO> productParameters;
    /**
     * 产品标签
     */
    private List<ProductTagDTO> productTags;

    /**
     * 价格设置
     */
    private ConsignmentPriceSettingDTO consignmentPriceSetting;


    /**
     * 供应商自定以折扣百分比
     */
    private Long supplierCustomDeductionRatio;

    private Boolean deleted;

    /**
     * 药品附加数据
     */
    private ProductDrugExtraDTO drugExtra;

    /**
     * 库存类型
     */
    private StorageType storageType;

    public Product coverProduct() {
        Product product = new Product();
        if (this.getProductType() == ProductType.VIRTUAL_PRODUCT || this.getProductType() == ProductType.REPURCHASE_PRODUCT) {
            GoodsError.VIRTUAL_DISTRIBUTION_CHECK_ERROR.falseThrow(distributionMode.equals(Collections.singletonList(DistributionMode.VIRTUAL)));
        } else {
            GoodsError.REAL_DISTRIBUTION_CHECK_ERROR.trueThrow(distributionMode.contains(DistributionMode.VIRTUAL));
        }
        if (shopCategory == null || shopCategory.areFieldsNonNull()) {
            throw new ServiceException("店铺类目信息为必传,请检查相关参数");
        }
        BeanUtil.copyProperties(this, product);
        product.setDrugExtra(drugExtra);
        product.setCategoryId(Option.of(getShopCategory()).map(CategoryLevel::getThree).getOrNull())
                .setPlatformCategoryId(Option.of(getPlatformCategory()).map(CategoryLevel::getThree).getOrNull());
        return product;
    }

    public void checkDistributionMode(Mode mode) {
        if (mode == Mode.O2O) {
            if (getDistributionMode().contains(DistributionMode.EXPRESS)) {
                throw new ServiceException("o2o模式不可包含快递配送");
            }
        }
    }


}

