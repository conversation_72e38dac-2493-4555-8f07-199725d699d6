package com.medusa.gruul.goods.api.model.dto;

import com.medusa.gruul.goods.api.enums.CopyGoodsType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/1/30
 * @describe 一键复制dto
 */
@Data
public class CopyGoodsDTO {
    /**
     * TaoBao 淘宝
     * JD 京东
     * AliBaBa 阿里
     */
    @NotNull(message = "平台类型不能为空!")
    private CopyGoodsType copyGoodsType;

    /**
     * 复制的原URL链接
     */
    @NotNull(message = "商品链接地址不能为空!")
    private String goodsUrl;

    /**
     * 冗余字段
     * 方便参数传递
     */
    private String apikey;
}
