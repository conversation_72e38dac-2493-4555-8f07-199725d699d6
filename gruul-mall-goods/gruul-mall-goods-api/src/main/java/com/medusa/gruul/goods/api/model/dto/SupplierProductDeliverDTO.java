package com.medusa.gruul.goods.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Description: 供应商发货
 */
@Data
public class SupplierProductDeliverDTO {

	/**
	 * 供应商id
	 */
	@NotNull
	private Long supplierId;
	/**
	 * 订单id
	 */
	@NotNull
	private Long orderId;

	/**
	 * 订单号
	 */
	@NotNull
	private String orderNo;
	/**
	 * 产品编码
	 */
	@NotEmpty
	private String productNo;


}
