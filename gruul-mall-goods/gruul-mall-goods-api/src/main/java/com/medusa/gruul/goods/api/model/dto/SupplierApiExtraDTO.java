package com.medusa.gruul.goods.api.model.dto;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Description: 供应商api相关配置
 */
@Data
@ToString
@Accessors(chain = true)
public class SupplierApiExtraDTO {

	/**
	 * 全局控制
	 * 是否开启api调用
	 */
	private boolean enableApi;

	/**
	 * 是否开启订单同步
	 */
	private boolean orderSync = true;

	/**
	 * 是否开启供应商运费查询
	 */
	private boolean freightQuery;
	/**
	 * 空不指定
	 * 指定快递公司
	 * 如:shentong
	 */
	private String deliveryCompany;




}
