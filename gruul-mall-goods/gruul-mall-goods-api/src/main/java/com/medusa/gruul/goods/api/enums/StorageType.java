package com.medusa.gruul.goods.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 仓储类型
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum StorageType {

    /**
     * 默认(自发)
     */
    DEFAULT(0),

    /**
     * 聚水潭
     */
    JST(1),

    /**
     * 优易
     */
    YY(2);

    @EnumValue
    private final Integer value;
}
