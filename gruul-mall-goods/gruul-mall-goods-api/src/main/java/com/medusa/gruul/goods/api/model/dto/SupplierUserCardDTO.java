package com.medusa.gruul.goods.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SupplierUserCardDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商用户id
     */
    private Long supplierUserId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 供应商卡id
     */
    private Long cardId;
    /**
     * 激活码
     */
    private String activateCode;
    /**
     * 卡有效期开始时间
     */
    private Date startTime;
    /**
     * 卡有效期截止时间
     */
    private Date endTime;
    /**
     * 补充字段 卡的权益信息
     */
    private String extra;
}
