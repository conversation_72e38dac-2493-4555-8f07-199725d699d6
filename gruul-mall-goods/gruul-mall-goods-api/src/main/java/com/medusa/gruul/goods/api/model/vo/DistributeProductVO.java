package com.medusa.gruul.goods.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.mp.handler.type.LongListTypeHandler;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import lombok.Data;

import java.util.List;

@Data

public class DistributeProductVO {
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品no
     */
    private String productNo;

    /**
     * 销量
     */
    private Long sales;

    /**
     * 商品名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 商品主图
     */
    private String pic;

    /**
     * 商品价格
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> salePrices;

    /**
     * 商品状态
     */
    private ProductStatus status;

    /**
     * 分销状态 IN_DISTRIBUTION--分销中 CANCEL_DISTRIBUTION--取消分销
     */

    private String distributionStatus;

    /**
     * 佣金类型 佣金类型 1.统一设置 2.固定金额 3.百分比
     */
    private Integer shareType;

    /**
     * 一级分佣
     */
    private Long one;

    /**
     * 二级分佣
     */
    private Long two;

    /**
     * 三级分佣
     */
    private Long three;

}
