package com.medusa.gruul.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.FastJson2TypeHandler;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.goods.api.model.dto.ProductDrugExtraDTO;
import com.medusa.gruul.goods.api.model.enums.SupplierProductType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 供应商产品
 *
 * <AUTHOR> 
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
@TableName(value = "t_supplier_product",autoResultMap = true)
public class SupplierProduct extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 供应商id
	*/
	private Long supplierId;
	/**
	* 产品名称
	*/
	private String name;
	/**
	* 产品编码
	*/
	private String productNo;
	/**
	 * skuid
	 */
	private String skuId;
	/**
	* 产品信息
	*/
	private String productInfo;
	/**
	 * 类型
	 */
	private SupplierProductType type;
	/**
	 * 是否为处方药
	 */
	private Boolean isPrescriptionMedicine;
	/**
	 * 是否需要报备
	 */
	private Boolean isNeedFiling;
	/**
	 * 报备结果，true成功
	 */
	private Boolean filingResult;

	private String remark;
	/**
	 * 上架状态 * 0 下架 1 上架
	 */
	private Integer status;
	/**
	 * 库存数量
	 */
	private  Long stockNum;
	/**
	 * 额外药品数据
	 */
	@TableField(typeHandler = FastJson2TypeHandler.class)
	private ProductDrugExtraDTO  drugExtra;
}