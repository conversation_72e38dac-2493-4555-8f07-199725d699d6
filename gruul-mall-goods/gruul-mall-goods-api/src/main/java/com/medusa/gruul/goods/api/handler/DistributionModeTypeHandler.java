package com.medusa.gruul.goods.api.handler;

import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.mp.IFastJson2TypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 配送模式TypeHandler
 * @date 2023-03-13 16:00
 */

@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class DistributionModeTypeHandler extends IFastJson2TypeHandler {

	@Override
	public TypeReference<?> getTypeReference() {
		return new TypeReference<List<DistributionMode>>() {
		};
	}
}