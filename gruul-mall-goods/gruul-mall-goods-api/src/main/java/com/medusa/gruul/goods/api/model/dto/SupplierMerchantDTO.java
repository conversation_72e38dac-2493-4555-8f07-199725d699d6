package com.medusa.gruul.goods.api.model.dto;

import com.medusa.gruul.goods.api.model.enums.SupplierStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 供应商商户
 *
 * <AUTHOR> 
 */
@Data
@Accessors(chain = true)
public class SupplierMerchantDTO {
	private static final long serialVersionUID = 1L;

	private Long id;

	/**
	* 供应商id
	*/
	private Long supplierId;
	/**
	* 应用id
	*/
	private String appId;
	/**
	* 商户id，商户号，合作伙伴id等等
	*/
	private String mchId;
	/**
	* 当私钥公钥为证书类型的时候，这里必填，可选值:PATH,STR,INPUT_STREAM,CLASS_PATH,URL
	*/
	private String certStoreType;
	/**
	* 私钥或私钥证书
	*/
	private String keyPrivate;
	/**
	* 公钥或公钥证书
	*/
	private String keyPublic;
	/**
	* key证书,附加证书使用，如SSL证书，或者银联根级证书方面
	*/
	private String keyCert;
	/**
	* 私钥证书或key证书的密码
	*/
	private String keyCertPwd;
	/**
	* 异步回调
	*/
	private String notifyUrl;
	/**
	* 同步回调地址，大部分用于付款成功后页面转跳
	*/
	private String returnUrl;
	/**
	* 签名方式,MD5,RSA等等
	*/
	private String signType;
	/**
	* 编码类型，如utf-8
	*/
	private String inputCharset;
	/**
	* 主体名称
	*/
	private String subjectName;
	/**
	* 是否为测试环境: 0 否，1 测试环境
	*/
	private Integer isTest;
	/**
	 * 请求api根域名
	 */
	private String requestDomainUrl;
	/**
	 * 同步信息
	 */
	private List<SupplierMerchantSyncExtraDTO> syncExtra;

	/**
	 * 供应商识别号
	 */
	private String supplierSn;

	/**
	 * 供应商名称
	 */
	private String name;

	/**
	 * 供应商api相关配置
	 */
	private SupplierApiExtraDTO apiExtra;

	/**
	 * 状态(默认待审核，1--已审核，3--禁用中)
	 */
	private SupplierStatus status;
}