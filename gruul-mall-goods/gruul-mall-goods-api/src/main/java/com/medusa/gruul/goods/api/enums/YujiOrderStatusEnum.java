package com.medusa.gruul.goods.api.enums;

import com.medusa.gruul.global.model.exception.GlobalException;
import lombok.Getter;

import java.util.List;

@Getter
public enum YujiOrderStatusEnum {

    /**
     * 1 未支付,对应昱极订单状态
     */
    UNPAID(1L, List.of(100),"未支付状态订单对比"),

    /**
     * 2 已支付,对应昱极订单状态
     */
    PAID(2L, List.of(200, 250, 350, 400, 500, 600),"已支付状态订单对比"),

    /**
     * 3 待发货
     */
    WAITING_FOR_DELIVER(3L, List.of(350),"待发货状态订单对比"),

    /**
     * 4 待收货
     */
    WAITING_FOR_RECEIVE(4L, List.of(400),"待收货订单状态订单对比"),

    /**
     * 999 订单收货状态或发货状态状态比对异常
     */
    COMPARE_EXCEPTION(999L, List.of(999),"");

    private final Long status;
    private final List<Integer> yujiStatus;
    private final String desc;


    YujiOrderStatusEnum(Long status, List<Integer> yujiStatus,String desc) {
        this.status = status;
        this.yujiStatus = yujiStatus;
        this.desc = desc;
    }


    /**
     * 根据订单状态返回昱极订单状态
     * @param status 订单状态
     * @return 对应的订单状态code
     */
    public static List<Integer> getYujiStatusBystatus(Long status) {
        for (YujiOrderStatusEnum w : YujiOrderStatusEnum.values()) {
            if (w.getStatus().equals(status)) {
                return w.getYujiStatus();
            }
        }
        throw new GlobalException("未匹配订单状态");
    }


    /**
     * 根据订单状态返回状态描述
     * @param status 订单状态
     * @return 对应的订单描述desc
     */
    public static String getDescBystatus(Long status) {
        for (YujiOrderStatusEnum w : YujiOrderStatusEnum.values()) {
            if (w.getStatus().equals(status)) {
                return w.getDesc();
            }
        }
        throw new GlobalException("未匹配订单描述");
    }
}
