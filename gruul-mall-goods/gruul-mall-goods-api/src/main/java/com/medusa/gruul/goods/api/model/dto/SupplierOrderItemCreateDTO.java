package com.medusa.gruul.goods.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SupplierOrderItemCreateDTO {
    /**
     * 药品skuId,对应系统的skuNo或productNo
     */
    private String skuId;
    /**
     * 购买数量
     */
    private Integer buyCount;
    /**
     * 结算价 单位为分
     */
    private Integer settlePrice;
    /**
     * 1 处⽅ 2 otc 3其他
     */
    private Integer prescription;
}
