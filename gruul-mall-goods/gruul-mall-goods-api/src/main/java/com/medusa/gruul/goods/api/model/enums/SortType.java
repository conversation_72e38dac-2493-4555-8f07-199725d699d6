package com.medusa.gruul.goods.api.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


/**
 *
 * @Description: 店铺商品排序type
 * @Author: xiaoq
 * @Date : 2022-04-27 17:11
 *
 * @deprecated 暂标记过期 接口暂未调用
 */
@Getter
@Deprecated
@RequiredArgsConstructor
public enum SortType {

    /**
     * 修改时间正序
     */
    UPDATE_TIME("update_time"),

    /**
     * 修改时间倒叙
     */
    UPDATE_TIME_DESC("update_time desc"),

    /**
     * 销量正序
     */
    SALE("sale"),

    /**
     * 按价格正序
     */
    PRICE("price"),

    /**
     * 按价格倒叙
     */
    PRICE_DESC("price desc");

    @EnumValue
    private final String value;
}
