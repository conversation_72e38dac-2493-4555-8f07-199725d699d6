<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.medusa.gruul</groupId>
        <artifactId>gruul-mall-goods</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>gruul-mall-goods-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-mybatis-plus</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <artifactId>gruul-common-module-api</artifactId>
            <groupId>com.medusa.gruul</groupId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-storage-api</artifactId>
            <version>1.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-custom-aggregation</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>14</source>
                    <target>14</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>