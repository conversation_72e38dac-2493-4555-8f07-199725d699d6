

package com.medusa.gruul.addon.full.reduction;

import static org.junit.Assert.assertEquals;

import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionQueryDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionShopMapDTO;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO;
import com.medusa.gruul.addon.full.reduction.service.FullReductionManageService;
import com.medusa.gruul.common.model.resp.Result;

import java.time.LocalDateTime;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FullReductionControllerTest {

    @Autowired
    private FullReductionManageService fullReductionManageService;

    @Test
    public void editFullReductionTest() {
        FullReductionDTO fullReductionDTO = new FullReductionDTO();
        fullReductionDTO.setShopId(1L);
        fullReductionDTO.setFullReductionId(1L);
        fullReductionDTO.setFullReductionName("�����");
        fullReductionDTO.setFullReductionStatus(FullReductionStatus.NOT_STARTED);
        fullReductionDTO.setFullReductionStartTime(LocalDateTime.now());
        fullReductionDTO.setFullReductionEndTime(LocalDateTime.now());
        fullReductionManageService.editFullReduction(fullReductionDTO);
//        assertEquals(result.getCode(), 200);
    }

//    @Test
//    public void getFullReductionByIdTest() {
//        Long shopId = 1L;
//        Long fullReductionId = 1L;
//        Result<FullReductionVO> result = fullReductionManageService.getFullReductionById(shopId, fullReductionId);
//        assertEquals(result.getCode(), 200);
//    }
//
//    @Test
//    public void fullReductionPageTest() {
//        FullReductionQueryDTO fullReductionQuery = new FullReductionQueryDTO();
//        fullReductionQuery.setShopId(1L);
//        fullReductionQuery.setFullReductionName("�����");
//        fullReductionQuery.setFullReductionType(1);
//        fullReductionQuery.setFullReductionStatus(1);
//        fullReductionQuery.setFullReductionStartTime("2020-02-07");
//        fullReductionQuery.setFullReductionEndTime("2020-02-08");
//        fullReductionQuery.setFullReductionCondition(100);
//        fullReductionQuery.setFullReductionDiscount(10);
//        fullReductionQuery.setFullReductionRemark("�������ע");
//        Result<FullReductionVO> result = fullReductionManageService.fullReductionPage(fullReductionQuery);
//        assertEquals(result.getCode(), 200);
//    }
//
//    @Test
//    public void deleteFullReductionByIdTest() {
//        Long fullReductionId = 1L;
//        Result<Void> result = fullReductionManageService.deleteFullReductionById(1L, fullReductionId);
//        assertEquals(result.getCode(), 200);
//    }
//
//    @Test
//    public void deleteFullReductionBatchTest() {
//        FullReductionShopMapDTO fullReductionShop

}