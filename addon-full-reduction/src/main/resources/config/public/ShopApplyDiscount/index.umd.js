(function(e,_){typeof exports=="object"&&typeof module<"u"?module.exports=_(require("vue"),require("@vueuse/core"),require("@/apis/http"),require("vue-router"),require("@/composables/useConvert"),require("@/components/PageManage.vue"),require("@/store/modules/shopInfo"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@/apis/http","vue-router","@/composables/useConvert","@/components/PageManage.vue","@/store/modules/shopInfo","element-plus"],_):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopApplyDiscount=_(e.ShopApplyDiscountContext.Vue,e.ShopApplyDiscountContext.VueUse,e.ShopApplyDiscountContext.Request,e.ShopApplyDiscountContext.VueRouter,e.ShopApplyDiscountContext.UseConvert,e.ShopApplyDiscountContext.PageManageTwo,e.ShopApplyDiscountContext.ShopInfoStore,e.ShopApplyDiscountContext.ElementPlus))})(this,function(e,_,b,C,D,k,E,h){"use strict";var V=document.createElement("style");V.textContent=`.fullcolumn[data-v-ebb271cb]{width:966px;height:144px;background:#f9f9f9;margin-bottom:10px;padding:10px;display:flex;justify-content:center;align-items:center}.fullcolumn__left[data-v-ebb271cb]{display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-around;align-items:flex-start;width:50%;height:100%;font-size:12px;color:#333}.fullcolumn__left--title[data-v-ebb271cb]{font-size:14px}.fullcolumn__left--statistical[data-v-ebb271cb]{width:100%;color:#a9a9a9;display:flex;justify-content:center;align-items:center;padding-right:40px;justify-content:space-between}.fullcolumn__center[data-v-ebb271cb]{width:20%;height:100%}.fullcolumn__center--title[data-v-ebb271cb]{font-size:14px}.fullcolumn__right[data-v-ebb271cb]{width:30%;height:100%;display:flex;justify-content:center;align-items:center}.nots[data-v-ebb271cb]{color:#2e99f3}.ongoing[data-v-ebb271cb]{color:#f57373}.hasEnded[data-v-ebb271cb],.off[data-v-ebb271cb],.suspended[data-v-ebb271cb]{color:#a9a9a9}.container[data-v-a93b1229]{overflow-y:scroll}
`,document.head.appendChild(V);const N=e.defineComponent({__name:"selectType",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Array,default:()=>[{label:"1",value:"2"}]}},emits:["update:modelValue","change"],setup(t,{emit:r}){const a=t,c=r,n=_.useVModel(a,"modelValue",c);return(s,f)=>{const d=e.resolveComponent("el-option"),o=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(o,{modelValue:e.unref(n),"onUpdate:modelValue":f[0]||(f[0]=l=>e.isRef(n)?n.value=l:null),placeholder:a.placeholder,style:{width:"150px"},onChange:f[1]||(f[1]=l=>c("change",l))},{default:e.withCtx(()=>[e.renderSlot(s.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.list,(l,i)=>(e.openBlock(),e.createBlock(d,{key:i,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),w=e.defineComponent({__name:"head-search",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0},leftBtnText:{type:String,default:"leftBtnText"}},emits:["update:modelValue","leftBtnClick","search"],setup(t,{emit:r}){const a=t,c=[{value:"NOT_STARTED",label:"未开始"},{value:"PROCESSING",label:"进行中"},{value:"OVER",label:"已结束"},{value:"ILLEGAL_SELL_OFF",label:"违规下架"}],n=r,s=_.useVModel(a,"modelValue",n),f=e.computed(()=>d=>{var o;return(o=window==null?void 0:window.permissionList)==null?void 0:o.includes(d)});return(d,o)=>{const l=e.resolveComponent("el-button"),i=e.resolveComponent("el-option"),u=e.resolveComponent("el-col"),p=e.resolveComponent("el-date-picker"),y=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(y,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px",width:"100%"}},{default:e.withCtx(()=>[e.createVNode(u,{span:14},{default:e.withCtx(()=>[f.value("marketingApp:applyDiscount:add")?(e.openBlock(),e.createBlock(l,{key:0,round:"",type:"primary",onClick:o[0]||(o[0]=m=>n("leftBtnClick"))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(a.leftBtnText),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(N,{modelValue:e.unref(s).fullReductionStatus,"onUpdate:modelValue":o[1]||(o[1]=m=>e.unref(s).fullReductionStatus=m),style:{"margin-left":"15px"},list:c,onChange:o[2]||(o[2]=m=>n("search"))},{default:e.withCtx(()=>[e.createVNode(i,{label:"全部状态",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(u,{span:8},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(s).date,"onUpdate:modelValue":o[3]||(o[3]=m=>e.unref(s).date=m),style:{width:"300px"},format:"YYYY/MM/DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:o[4]||(o[4]=m=>n("search",m))},null,8,["modelValue"])]),_:1})]),_:1})}}}),B=t=>b.get({url:"addon-full-reduction/fullReduction/",params:t}),R=t=>b.del({url:`addon-full-reduction/fullReduction/${t}`}),x={NOT_STARTED:{title:"未开始",class:"nots"},PROCESSING:{title:"进行中",class:"ongoing"},OVER:{title:"已结束",class:"hasEnded"},ILLEGAL_SELL_OFF:{title:"违规下架",class:"off"}},g=e.computed(()=>t=>{var r,a;return console.log((r=window==null?void 0:window.permissionList)==null?void 0:r.includes(t),"是否包含"),(a=window==null?void 0:window.permissionList)==null?void 0:a.includes(t)}),T={class:"fullcolumn"},A={class:"fullcolumn__left"},L={class:"fullcolumn__left--title"},M={class:"fullcolumn__left--statistical"},O={class:"fullcolumn__center"},z={class:"fullcolumn__right"},I=e.defineComponent({__name:"column",props:{item:{type:Object,required:!0}},emits:["del"],setup(t,{emit:r}){const{divTenThousand:a}=D(),c=r,n=C.useRouter(),s=d=>{n.push({name:"applyDiscountBaseinfo",query:{id:d}})},f=e.computed(()=>d=>Number(d)===0?"全部":d+"件");return(d,o)=>{const l=e.resolveComponent("el-button"),i=e.resolveComponent("el-button-group");return e.openBlock(),e.createElementBlock("div",T,[e.createElementVNode("div",A,[e.createElementVNode("h1",L,e.toDisplayString(t.item.fullReductionName),1),e.createElementVNode("time",null,"活动时间："+e.toDisplayString(t.item.fullReductionStartTime)+"至"+e.toDisplayString(t.item.fullReductionEndTime),1),e.createElementVNode("div",null,"活动商品："+e.toDisplayString(f.value(t.item.productNum)),1),e.createElementVNode("div",M,[e.createElementVNode("span",null,"参加人数："+e.toDisplayString(t.item.peopleNum||0),1),e.createElementVNode("span",null,"支付单数："+e.toDisplayString(t.item.payOrder||0),1),e.createElementVNode("span",null,"应收金额："+e.toDisplayString(t.item.amountReceivable&&e.unref(a)(t.item.amountReceivable)||0),1)])]),e.createElementVNode("div",O,[e.createElementVNode("h1",{class:e.normalizeClass(["fullcolumn__center--title",e.unref(x)[t.item.fullReductionStatus].class])},e.toDisplayString(e.unref(x)[t.item.fullReductionStatus].title),3)]),e.createElementVNode("div",z,[e.createVNode(i,null,{default:e.withCtx(()=>[(t.item.fullReductionStatus!=="NOT_STARTED"?e.unref(g)("marketingApp:applyDiscount:detail"):e.unref(g)("marketingApp:applyDiscount:edit"))?(e.openBlock(),e.createBlock(l,{key:0,round:"",onClick:o[0]||(o[0]=u=>s(t.item.id))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(t.item.fullReductionStatus!=="NOT_STARTED"?"查看":"编辑")+"活动 ",1)]),_:1})):e.createCommentVNode("",!0),e.unref(g)("marketingApp:applyDiscount:delete")?(e.openBlock(),e.createBlock(l,{key:1,round:"",onClick:o[1]||(o[1]=u=>c("del",t.item.id))},{default:e.withCtx(()=>o[2]||(o[2]=[e.createTextVNode("删除活动")])),_:1})):e.createCommentVNode("",!0)]),_:1})])])}}}),Y="",S=(t,r)=>{const a=t.__vccOpts||t;for(const[c,n]of r)a[c]=n;return a},j=S(I,[["__scopeId","data-v-ebb271cb"]]),q={style:{height:"calc(100vh - 220px)"},class:"container"},$=e.defineComponent({__name:"ShopApplyDiscount",setup(t){const r=C.useRouter(),a=e.ref([]),c=e.reactive({keyword:"",date:"",fullReductionStatus:""}),n=e.reactive({size:10,current:1,total:0});e.onBeforeMount(()=>{s()});async function s(){const{date:l,fullReductionStatus:i}=c,u={fullReductionStartTime:"",fullReductionEndTime:""};Array.isArray(l)&&l.length===2&&(u.fullReductionStartTime=l[0],u.fullReductionEndTime=l[1]);const p={...n,...u,shopId:E.useShopInfoStore().shopInfo.id,fullReductionStatus:i},{code:y,data:m}=await B(p);if(y!==200)return h.ElMessage.error("获取活动列表失败");a.value=m.records,n.current=m.current,n.size=m.size,n.total=m.total}const f=async l=>{try{if(!await h.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:u,data:p}=await R(l);if(u!==200){h.ElMessage.error("删除失败");return}h.ElMessage.success("删除成功"),a.value=a.value.filter(y=>y.id!==l),n.total--}catch(i){console.log("isValidate",i)}},d=l=>{n.size=l,s()},o=l=>{n.current=l,s()};return(l,i)=>{const u=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(w,{modelValue:c,"onUpdate:modelValue":i[0]||(i[0]=p=>c=p),"left-btn-text":"新增满减活动",onSearch:s,onLeftBtnClick:i[1]||(i[1]=p=>e.unref(r).push({name:"applyDiscountBaseinfo"}))},null,8,["modelValue"]),e.createElementVNode("div",q,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.value,p=>(e.openBlock(),e.createBlock(j,{key:p.id,item:p,onDel:f},null,8,["item"]))),128))]),e.createVNode(u,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:n,"onUpdate:modelValue":i[2]||(i[2]=p=>n=p),"load-init":!0,"page-size":n.size,total:n.total,onReload:s,onHandleSizeChange:d,onHandleCurrentChange:o},null,8,["modelValue","page-size","total"])]),_:1})])}}}),U="";return S($,[["__scopeId","data-v-a93b1229"]])});
