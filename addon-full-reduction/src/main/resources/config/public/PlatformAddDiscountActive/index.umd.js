(function(e,p){typeof exports=="object"&&typeof module<"u"?module.exports=p(require("vue"),require("vue-router"),require("@/apis/http"),require("@/apis/good"),require("@/utils/date"),require("decimal.js"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("element-plus"),require("@/composables/useConvert"),require("@/components/q-choose-goods-popup")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/apis/http","@/apis/good","@/utils/date","decimal.js","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","element-plus","@/composables/useConvert","@/components/q-choose-goods-popup"],p):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAddDiscountActive=p(e.PlatformAddDiscountActiveContext.Vue,e.PlatformAddDiscountActiveContext.VueRouter,e.PlatformAddDiscountActiveContext.Request,e.PlatformAddDiscountActiveContext.GoodAPI,e.PlatformAddDiscountActiveContext.DateUtil,e.PlatformAddDiscountActiveContext.Decimal,e.PlatformAddDiscountActiveContext.QChooseGoodsPopup,e.PlatformAddDiscountActiveContext.ElementPlus,e.PlatformAddDiscountActiveContext.UseConvert,e.PlatformAddDiscountActiveContext.QChooseGoodsPopupFun))})(this,function(e,p,k,U,I,A,P,N,L,q){"use strict";var T=document.createElement("style");T.textContent=`@charset "UTF-8";.add[data-v-f0d49b82]{margin:-20px -15px;height:calc(100vh - 85px);padding-bottom:60px;overflow-y:auto;scrollbar-width:none;-ms-overflow-style:none}.add[data-v-f0d49b82]::-webkit-scrollbar{display:none}.title[data-v-f0d49b82]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-f0d49b82]{font-size:12px;margin-left:12px;color:#c4c4c4}.rules[data-v-f0d49b82]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-f0d49b82]{width:300px;display:flex}.text[data-v-f0d49b82]{font-size:14px;color:#333}.goodsData[data-v-f0d49b82]{border:1px solid #ccc}.goods-list[data-v-f0d49b82]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-f0d49b82]{display:flex}.goods-list__goods-list__info-name[data-v-f0d49b82]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-f0d49b82]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-f0d49b82]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-f0d49b82]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-f0d49b82]{font-size:16px}.ruleform-date[data-v-f0d49b82]{width:100%;display:flex;align-items:center}.flex[data-v-f0d49b82]{margin-top:10px;height:50px}.flex-item[data-v-f0d49b82]{width:40%}.coupon-rules[data-v-f0d49b82]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-f0d49b82]{width:1010px;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin:0 auto 0 -15px}
`,document.head.appendChild(T);const S=[{label:"满X元减",value:"FULL_REDUCTION"},{label:"满X元折",value:"FULL_DISCOUNT"}],B=r=>k.get({url:`addon-full-reduction/fullReduction/${r.shopId}/${r.fullReductionId}`}),u=r=>(e.pushScopeId("data-v-f0d49b82"),r=r(),e.popScopeId(),r),M={style:{padding:"40px"},class:"add"},O=u(()=>e.createElementVNode("h1",{class:"title"},"基本信息",-1)),F=u(()=>e.createElementVNode("span",{class:"msg"},"活动名称不超过5个字",-1)),G={class:"ruleform-date"},Y={key:0,class:"flex",style:{width:"100%"}},z=u(()=>e.createElementVNode("span",null,"满",-1)),$=u(()=>e.createElementVNode("span",null,"元,打",-1)),j=u(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"折",-1)),H={key:1,class:"flex",style:{width:"100%"}},Q=u(()=>e.createElementVNode("span",null,"满",-1)),X=u(()=>e.createElementVNode("span",null,"元,减",-1)),W=u(()=>e.createElementVNode("span",{style:{"margin-left":"5px"}},"元",-1)),J={class:"goods-list__info"},K={class:"goods-list__goods-list__info-name"},Z={class:"goods-list__goods-list__info-name--name"},v={class:"goods-list__goods-list__info-name--price"},ee={key:0,class:"text"},te={class:"nav-button"},oe=e.defineComponent({__name:"PlatformAddDiscountActive",setup(r){var D;const x=p.useRouter(),m=p.useRoute(),g=new I,b=((D=m.query.shopId)==null?void 0:D.toString())||"",le=e.reactive({isEditDisable:!1,fullReductionTime:[],rules:{fullReductionName:[{required:!0,message:"请输入活动名称",trigger:"blur"}],type:[{required:!0,message:"请选择优惠券类型",trigger:["blur","change"]}],productType:[{required:!0,message:"请输入满减规则",trigger:["blur","change"]}],fullReductionStartTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]}],fullReductionEndTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]}]},chooseGoodsPopup:!1,chooseGoodsList:[]}),{isEditDisable:a,rules:ne,chooseGoodsPopup:V,chooseGoodsList:c}=e.toRefs(le),de=e.ref(),{mulTenThousand:Ae,divTenThousand:ae}=L(),s=e.ref({fullReductionId:"",fullReductionName:"",fullReductionStatus:"ILLEGAL_SELL_OFF",fullReductionStartTime:"",fullReductionEndTime:"",fullReductionRules:[{fullReductionRule:"",conditionAmount:1,discountAmount:1,discountRatio:.1}],shopId:b,shopName:"",productType:"ALL_PRODUCT",productIds:[],isUpdate:!1});ie();async function ie(){if(!m.query.id)return;a.value=!0;const{code:l,data:t,msg:i}=await B({shopId:b,fullReductionId:m.query.id.toString()});if(l!==200){N.ElMessage.error(i||"获取活动信息失败");return}re(t.fullReductionStatus),t.fullReductionRules=ce(t.fullReductionRules,ue);for(const n in t){const f=t[n];s[n]=f}se(t)}async function se(l){if(l.productIds&&l.productIds.length){const{code:t,data:i}=await U.doGetRetrieveProduct({productId:l.productIds});if(t!==200){N.ElMessage.error("获取商品信息失败");return}c.value=i.list.map(n=>({...n,isCheck:!0})),console.log("chooseGoodsList.value",c.value)}}function re(l){l&&l!=="NOT_STARTED"&&(a.value=!0)}function ce(l,t){return l.map(i=>{const{fullReductionRule:n,conditionAmount:f,discountAmount:_,discountRatio:R}=i;return i.fullReductionRule==="FULL_DISCOUNT"?{fullReductionRule:n,conditionAmount:t(f),discountRatio:R}:i.fullReductionRule==="FULL_REDUCTION"?{fullReductionRule:n,conditionAmount:t(f),discountAmount:t(_)}:i})}function ue(l){return l?ae(l).toNumber():0}const pe=l=>{c.value=[...l.tempGoods]},me=async l=>{await N.ElMessageBox.confirm("确定移除该商品?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&(c.value=c.value.filter(i=>i.productId!==l))},fe=l=>{s.value.fullReductionRules.splice(l,1)},_e=()=>{s.value.fullReductionRules.push({fullReductionRule:"",conditionAmount:0,discountAmount:0,discountRatio:0})},he=l=>{const t=g.getYMDs(l),i=g.getYMDs(new Date);return new A(new Date(t).getTime()).lessThan(new Date(i).getTime())||new A(new Date(l).getTime()).greaterThanOrEqualTo(new Date(xe(6)).getTime())};function xe(l){let t=new Date;return t.setMonth(t.getMonth()+Number(l)),t.toLocaleString().replace(/\//g,"-")}return(l,t)=>{const i=e.resolveComponent("el-input"),n=e.resolveComponent("el-form-item"),f=e.resolveComponent("el-date-picker"),_=e.resolveComponent("el-link"),R=e.resolveComponent("el-option"),ge=e.resolveComponent("el-select"),h=e.resolveComponent("el-table-column"),C=e.resolveComponent("el-input-number"),E=e.resolveComponent("el-table"),y=e.resolveComponent("el-radio"),be=e.resolveComponent("el-radio-group"),Ve=e.resolveComponent("el-image"),w=e.resolveComponent("el-button"),Ce=e.resolveComponent("el-row"),Ne=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",M,[O,e.createVNode(Ne,{ref_key:"ruleFormRef",ref:de,model:s.value,rules:e.unref(ne),"label-width":"auto","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(n,{label:"活动名称",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:s.value.fullReductionName,"onUpdate:modelValue":t[0]||(t[0]=o=>s.value.fullReductionName=o),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"5",placeholder:"请输入活动名称",disabled:e.unref(a)},null,8,["modelValue","disabled"]),F]),_:1}),e.createVNode(n,{label:"活动时间",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",G,[e.createVNode(n,{prop:"fullReductionEndTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(f,{modelValue:s.value.fullReductionEndTime,"onUpdate:modelValue":t[1]||(t[1]=o=>s.value.fullReductionEndTime=o),disabled:e.unref(a),type:"datetime",placeholder:"请选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":he},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(n,{"label-width":"80px",style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(_,{underline:!1,type:"primary",disabled:e.unref(a),onClick:_e},{default:e.withCtx(()=>[e.createTextVNode("添加规则")]),_:1},8,["disabled"])]),_:1}),e.createVNode(n,{label:"活动规则"},{default:e.withCtx(()=>[e.createVNode(E,{data:s.value.fullReductionRules,style:{width:"80%"},border:"","header-cell-style":{textAlign:"center",fontSize:"14px",color:"#606266"},"cell-style":{height:"60px"}},{default:e.withCtx(()=>[e.createVNode(h,{label:"满减条件",width:"170"},{default:e.withCtx(({row:o})=>[e.createVNode(n,{prop:"fullReductionRule"},{default:e.withCtx(()=>[e.createVNode(ge,{modelValue:o.fullReductionRule,"onUpdate:modelValue":d=>o.fullReductionRule=d,disabled:e.unref(a),placeholder:"全部类型"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(S),d=>(e.openBlock(),e.createBlock(R,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1024)]),_:1}),e.createVNode(h,{label:"满减规则",width:"300"},{default:e.withCtx(({row:o})=>[o.fullReductionRule==="FULL_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",Y,[e.createVNode(n,{prop:"conditionAmount","label-width":"0%"},{default:e.withCtx(()=>[z,e.createVNode(C,{modelValue:o.conditionAmount,"onUpdate:modelValue":d=>o.conditionAmount=d,modelModifiers:{number:!0},style:{width:"60%",margin:"0 5px"},disabled:e.unref(a),controls:!1,max:99999,min:1},null,8,["modelValue","onUpdate:modelValue","disabled"]),$]),_:2},1024),e.createVNode(n,{prop:"discountRatio","label-width":"0%"},{default:e.withCtx(()=>[e.createVNode(C,{modelValue:o.discountRatio,"onUpdate:modelValue":d=>o.discountRatio=d,modelModifiers:{number:!0},disabled:e.unref(a),style:{width:"80%"},controls:!1,max:9.9,precision:1,min:.1},null,8,["modelValue","onUpdate:modelValue","disabled"]),j]),_:2},1024)])):e.createCommentVNode("",!0),o.fullReductionRule==="FULL_REDUCTION"?(e.openBlock(),e.createElementBlock("div",H,[e.createVNode(n,{prop:"requiredAmount","label-width":0},{default:e.withCtx(()=>[Q,e.createVNode(C,{modelValue:o.conditionAmount,"onUpdate:modelValue":d=>o.conditionAmount=d,modelModifiers:{number:!0},disabled:e.unref(a),controls:!1,style:{width:"90px",margin:"0 5px"},max:999999,min:1},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1024),e.createVNode(n,{prop:"discountAmount","label-width":0},{default:e.withCtx(()=>[X,e.createVNode(C,{modelValue:o.discountAmount,"onUpdate:modelValue":d=>o.discountAmount=d,modelModifiers:{number:!0},disabled:e.unref(a),style:{width:"90px",margin:"0 5px"},controls:!1,max:o.conditionAmount,min:1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]),W]),_:2},1024)])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(h,{label:"操作",align:"center"},{default:e.withCtx(({$index:o})=>[e.createVNode(_,{underline:!1,type:"danger",disabled:e.unref(a),onClick:d=>fe(o)},{default:e.withCtx(()=>[e.createTextVNode("删除")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e.createVNode(n,{label:"商品选择",prop:"productType"},{default:e.withCtx(()=>[e.createVNode(be,{modelValue:s.value.productType,"onUpdate:modelValue":t[2]||(t[2]=o=>s.value.productType=o),disabled:e.unref(a)},{default:e.withCtx(()=>[e.createVNode(y,{label:"ALL_PRODUCT"},{default:e.withCtx(()=>[e.createTextVNode("全部商品参与")]),_:1}),e.createVNode(y,{label:"SPECIFIED_PRODUCT_PARTICIPATE"},{default:e.withCtx(()=>[e.createTextVNode("指定商品参与")]),_:1}),e.createVNode(y,{label:"SPECIFIED_PRODUCT_NOT_PARTICIPATE"},{default:e.withCtx(()=>[e.createTextVNode("指定商品不参与")]),_:1})]),_:1},8,["modelValue","disabled"]),s.value.productType!=="ALL_PRODUCT"&&e.unref(c).length?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass(["goods-list",e.unref(c).length&&"goodsData"])},[e.createVNode(E,{style:{width:"100%"},data:e.unref(c),height:"260px","header-cell-style":{fontSize:"14px",color:"#606266",background:"#f2f2f2",height:"54px",fontWeight:"normal"}},{default:e.withCtx(()=>[e.createVNode(h,{label:"商品信息"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",J,[e.createVNode(Ve,{style:{width:"60px",height:"60px"},"preview-teleported":!0,src:o.pic,fit:"","preview-src-list":[o.pic]},null,8,["src","preview-src-list"]),e.createElementVNode("div",K,[e.createElementVNode("div",Z,e.toDisplayString(o.productName),1),e.createElementVNode("div",v,e.toDisplayString(e.unref(q.formatGoodsPrice)(o)),1)])])]),_:1}),e.createVNode(h,{label:"操作",width:"80px"},{default:e.withCtx(({row:o})=>[e.createVNode(_,{underline:!1,type:"primary",disabled:e.unref(a),onClick:d=>me(o.productId)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])],2)):e.createCommentVNode("",!0),s.value.productType!=="ALL_PRODUCT"?(e.openBlock(),e.createBlock(Ce,{key:1,justify:"space-between",style:{width:"90%","margin-top":"10px"}},{default:e.withCtx(()=>[e.createVNode(w,{type:"primary",round:"",plain:"",disabled:e.unref(a),onClick:t[3]||(t[3]=o=>V.value=!0)},{default:e.withCtx(()=>[e.createTextVNode(" 选择商品 ")]),_:1},8,["disabled"]),e.unref(c).length?(e.openBlock(),e.createElementBlock("span",ee,"已选择"+e.toDisplayString(e.unref(c).length)+"款商品",1)):e.createCommentVNode("",!0)]),_:1})):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["model","rules"])]),e.createElementVNode("div",te,[e.createVNode(w,{round:"",plain:"",onClick:t[4]||(t[4]=o=>e.unref(x).back())},{default:e.withCtx(()=>[e.createTextVNode("返回")]),_:1}),e.createVNode(w,{type:"primary",round:"",onClick:t[5]||(t[5]=o=>e.unref(x).back())},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})]),e.createVNode(P,{modelValue:e.unref(V),"onUpdate:modelValue":t[6]||(t[6]=o=>e.isRef(V)?V.value=o:null),onOnConfirm:pe},null,8,["modelValue"])])}}}),Re="";return((r,x)=>{const m=r.__vccOpts||r;for(const[g,b]of x)m[g]=b;return m})(oe,[["__scopeId","data-v-f0d49b82"]])});
