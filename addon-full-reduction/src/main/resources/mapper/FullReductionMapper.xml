<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.addon.full.reduction.mp.mapper.FullReductionMapper">

    <resultMap id="fullReductionMap" type="com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO">
        <id column="id" property="id"/>
        <result column="full_reduction_name" property="fullReductionName"/>
        <result column="full_reduction_status" property="fullReductionStatus"/>
        <result column="full_reduction_start_time" property="fullReductionStartTime"/>
        <result column="full_reduction_end_time" property="fullReductionEndTime"/>
        <result column="shop_id" property="shopId"/>
        <result column="product_type" property="productType"/>
        <result column="full_reduction_rules" typeHandler="com.medusa.gruul.addon.full.reduction.handler.FullReductionRuleHandler" property="fullReductionRules"/>
        <collection property="productIds"
                    select="com.medusa.gruul.addon.full.reduction.mp.mapper.FullReductionProductMapper.getFullReductionProductIds"
                    column="id"/>
    </resultMap>

    <resultMap id="shopFullReductionMap" type="com.medusa.gruul.addon.full.reduction.model.vo.ShopFullReductionVO">
        <id column="id" property="fullReductionId"/>
        <result column="shop_id" property="shopId"/>
        <result column="product_type" property="productType"/>
        <result column="full_reduction_rules" typeHandler="com.medusa.gruul.addon.full.reduction.handler.FullReductionRuleHandler" property="fullReductionRules"/>
        <result column="product_ids" typeHandler="com.medusa.gruul.common.mp.handler.type.LongSetTypeHandler" javaType="java.util.Set" property="productIds"/>
    </resultMap>

    <select id="getFullReductionById" resultMap="fullReductionMap">
        SELECT
            fullReduction.id,
            fullReduction.full_reduction_name,
            fullReduction.full_reduction_start_time,
            fullReduction.full_reduction_end_time,
            fullReduction.shop_id,
            fullReduction.product_type,
            fullReduction.full_reduction_rules,
        CASE
            WHEN fullReduction.full_reduction_status = ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF.value}
                 THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF.value}
            WHEN NOW() >= fullReduction.full_reduction_start_time AND NOW()  <![CDATA[<=]]>  fullReduction.full_reduction_end_time
                 THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @PROCESSING.value}
            WHEN fullReduction.full_reduction_start_time > NOW()
                THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @NOT_STARTED.value}
            WHEN NOW() > fullReduction.full_reduction_end_time
                THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @OVER.value}
            END
                full_reduction_status
        FROM
             t_full_reduction AS fullReduction
    <trim prefix="where" prefixOverrides="and">
        <if test="fullReductionId != null">
             fullReduction.id = #{fullReductionId}
        </if>
        <if test="shopId != null">
          AND fullReduction.shop_id = #{shopId}
        </if>
        AND deleted = 0
    </trim>
</select>
    <sql id="statisticsPeopleNum">
        (SELECT COUNT(DISTINCT user_id) FROM t_full_reduction_payment_info
        WHERE activity_id = fullReduction.id AND fullReduction.deleted = 0) AS peopleNum,
    </sql>
    <sql id="statisticsPayOrder">
        (SELECT COUNT(DISTINCT order_no) FROM t_full_reduction_payment_info paymentInfo
         WHERE paymentInfo.activity_id = fullReduction.id
           AND paymentInfo.amount_receivable > 0
           AND fullReduction.deleted = 0) AS payOrder,
    </sql>
    <sql id="statisticsAmountReceivable">
        (SELECT SUM(amount_receivable) FROM t_full_reduction_payment_info paymentInfo
        WHERE paymentInfo.activity_id = fullReduction.id
          AND fullReduction.deleted = 0) AS amountReceivable,
    </sql>
    <select id="fullReductionPage" resultType="com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO">
        SELECT
            fullReduction.id,
            fullReduction.full_reduction_name,
            fullReduction.full_reduction_start_time,
            fullReduction.full_reduction_end_time,
            fullReduction.shop_id,
            fullReduction.shop_name,
            fullReduction.product_type,
            fullReduction.product_num,
            <include refid="statisticsPeopleNum"/>
            <include refid="statisticsPayOrder"/>
            <include refid="statisticsAmountReceivable"/>
        CASE
            WHEN fullReduction.full_reduction_status = ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF.value}
                THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF.value}
            WHEN NOW() >= fullReduction.full_reduction_start_time AND NOW()  <![CDATA[<=]]>  fullReduction.full_reduction_end_time
                THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @PROCESSING.value}
            WHEN fullReduction.full_reduction_start_time > NOW()
                THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @NOT_STARTED.value}
            WHEN NOW() > fullReduction.full_reduction_end_time
                THEN ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @OVER.value}
            END
                 full_reduction_status
        FROM
            t_full_reduction AS fullReduction
        WHERE
            fullReduction.deleted = 0
        <if test="fullReductionQuery != null">
            <if test="fullReductionQuery.shopId != null">
                AND fullReduction.shop_id = #{fullReductionQuery.shopId}
            </if>
            <if test="fullReductionQuery.fullReductionStartTime != null and fullReductionQuery.fullReductionStartTime != ''">
               AND fullReduction.full_reduction_start_time >=#{fullReductionQuery.fullReductionStartTime}
            </if>
            <if test="fullReductionQuery.fullReductionEndTime != null and fullReductionQuery.fullReductionEndTime != ''">
                AND fullReduction.full_reduction_end_time  <![CDATA[<=]]> #{fullReductionQuery.fullReductionEndTime}
            </if>
             <if test="fullReductionQuery.keyword != null and fullReductionQuery.keyword != ''">
                AND (fullReduction.full_reduction_name LIKE CONCAT('%',#{fullReductionQuery.keyword},'%') OR fullReduction.shop_name LIKE CONCAT('%',#{fullReductionQuery.keyword},'%'))
             </if>
            <if test="fullReductionQuery.fullReductionStatus != null">
                <if test="fullReductionQuery.fullReductionStatus == @com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @PROCESSING">
                   AND NOW() >= fullReduction.full_reduction_start_time AND NOW()  <![CDATA[<=]]>  fullReduction.full_reduction_end_time
                </if>
                <if test="fullReductionQuery.fullReductionStatus == @com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @NOT_STARTED">
                    AND NOW() <![CDATA[<]]>  fullReduction.full_reduction_start_time
                </if>
                <if test="fullReductionQuery.fullReductionStatus == @com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @OVER">
                    AND NOW() > fullReduction.full_reduction_end_time
                </if>
                <if test="fullReductionQuery.fullReductionStatus == @com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF">
                    AND fullReduction.full_reduction_status = #{fullReductionQuery.fullReductionStatus}
                </if>
                <if test="fullReductionQuery.fullReductionStatus != @com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF">
                    AND fullReduction.full_reduction_status != ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF.value}
                </if>

            </if>
        </if>
        ORDER BY full_reduction_start_time DESC
    </select>
    <select id="productDetailFullReduction" resultType="com.medusa.gruul.addon.full.reduction.model.vo.FullReductionProductVO">
        SELECT
            fullReduction.deduction_type,
            fullReduction.apply_types,
            ROUND(IF(#{productDetailFullReduction.amount} >= condition_amount,
                if(full_reduction_rule = ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionRuleType @FULL_REDUCTION.value},
                    discount_amount, #{productDetailFullReduction.amount} * (10 - discount_ratio) / 10),0),2)  AS productDiscountAmount
        FROM t_full_reduction AS fullReduction
        WHERE IF(full_reduction_status, full_reduction_status, 0) != ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF.value}
        AND NOW() >= full_reduction_start_time
        AND NOW() <![CDATA[<=]]>  full_reduction_end_time
        AND fullReduction.shop_id = #{productDetailFullReduction.shopId}
        AND fullReduction.deleted = 0
        AND (
               CASE fullReduction.product_type
                WHEN ${@com.medusa.gruul.addon.full.reduction.model.enums.ProductType @SPECIFIED_PRODUCT_PARTICIPATE.value}
                THEN EXISTS(
                    SELECT product.product_id
                    FROM t_full_reduction_product AS product
                    WHERE product.full_reduction_id = fullReduction.id
                    AND product.shop_id = fullReduction.shop_id
                    AND product.deleted = 0
                    AND product.product_id = #{productDetailFullReduction.productId}
                )
                WHEN ${@com.medusa.gruul.addon.full.reduction.model.enums.ProductType @SPECIFIED_PRODUCT_NOT_PARTICIPATE.value}
                THEN NOT EXISTS(
                    SELECT product.product_id
                    FROM t_full_reduction_product AS product
                    WHERE product.full_reduction_id = fullReduction.id
                    AND product.shop_id = fullReduction.shop_id
                    AND product.deleted = 0
                    AND product.product_id = #{productDetailFullReduction.productId}
                )
        ELSE TRUE
        END)
    </select>
    <select id="getFullReductions" resultMap="shopFullReductionMap">
        SELECT
            fullReduction.id,
            fullReduction.full_reduction_name,
            fullReduction.full_reduction_start_time,
            fullReduction.full_reduction_end_time,
            fullReduction.shop_id,
            fullReduction.product_type,
            fullReduction.product_ids,
            fullReduction.full_reduction_rules
        FROM
            t_full_reduction AS fullReduction
        WHERE
            fullReduction.full_reduction_start_time  <![CDATA[<=]]>  NOW()
        AND
            fullReduction.full_reduction_end_time >= NOW()
        AND
            fullReduction.shop_id IN
            <foreach collection="shopIds" item="shopId" open="(" close=")" separator=",">
                #{shopId}
            </foreach>
        AND
            fullReduction.full_reduction_status != ${@com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus @ILLEGAL_SELL_OFF.value}
        AND
            fullReduction.deleted = 0
    </select>

</mapper>
