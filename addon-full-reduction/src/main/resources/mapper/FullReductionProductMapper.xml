<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.addon.full.reduction.mp.mapper.FullReductionProductMapper">

    <select id="getFullReductionProductIds" resultType="java.lang.Long">
        SELECT
            product_id
        FROM
            t_full_reduction_product
        WHERE
            full_reduction_id = #{fullReductionId}
          AND
            deleted = 0
    </select>
</mapper>
