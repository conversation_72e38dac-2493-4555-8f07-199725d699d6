
package com.medusa.gruul.addon.full.reduction.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 检索商品DTO
 */
@Getter
@Setter
@Accessors(chain = true)
public class SearchShopProductIdDTO {


    /**
     * 店铺id
     */
    @NotNull
    private Long shopId;


    /**
     * 商品id
     */
    @NotNull
    private Long productId;


}
