package com.medusa.gruul.addon.full.reduction.model.dto;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class FullReductionQueryDTO extends Page<FullReductionVO> {

    /**
     * 开始日期
     */
    private String fullReductionStartTime;

    /**
     * 结束日期
     */
    private String fullReductionEndTime;

    /**
     * 店铺id
     */
    private Long shopId;


    /**
     * 活动状态
     */
    private FullReductionStatus fullReductionStatus;

    /**
     * 关键词
     */
    private String keyword;
}
