package com.medusa.gruul.addon.full.reduction.addon.impl;

import com.medusa.gruul.addon.full.reduction.addon.AddonFullReductionProvider;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionRuleType;
import com.medusa.gruul.addon.full.reduction.model.vo.ShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import com.medusa.gruul.addon.full.reduction.service.ConsumerFullReductionService;
import com.medusa.gruul.common.addon.provider.AddonProvider;
import com.medusa.gruul.common.addon.provider.AddonProviders;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.global.model.constant.Services;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.global.model.helper.CompletableTask;
import com.medusa.gruul.order.api.addon.OrderAddonConstant;
import com.medusa.gruul.order.api.addon.fullreduction.FullReductionResponse;
import com.medusa.gruul.order.api.addon.fullreduction.OrderFullReductionParam;
import com.medusa.gruul.order.api.entity.OrderDiscount;
import com.medusa.gruul.order.api.enums.DiscountSourceStatus;
import com.medusa.gruul.order.api.enums.DiscountSourceType;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
@DubboService
@AddonProviders
@RequiredArgsConstructor
public class AddonFullReductionProviderImpl implements AddonFullReductionProvider {


    private final ConsumerFullReductionService consumerFullReductionService;
    private final Executor fullReductionExecutor;

    /**
     * 获取满减可用的优惠列表
     *
     * @param orderFullReductionParam 订单满减参数
     * @return 满减活动对应的优惠列表
     */
    @Log("订单使用满减活动")
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_ORDER, supporterId = OrderAddonConstant.ORDER_DISCOUNT_SUPPORT_ID, methodName = "fullReduction")
    public FullReductionResponse getFullReductionsOrderDiscount(OrderFullReductionParam orderFullReductionParam) {
        // 获取缓存中的满减活动
        consumerFullReductionService.getExistsFullReduction();
        Map<Long, Map<Long, Long>> shopFullReductionMap = orderFullReductionParam.getShopFullReductionMap();
        Set<Long> shopIds = shopFullReductionMap.keySet();
        List<ShopFullReductionVO> fullReductions = consumerFullReductionService.getOrderFullReduction(shopIds);
        // 满减活动Map
        Map<Long, ShopFullReductionVO> fullReductionMap = fullReductions.stream()
                .collect(Collectors.toMap(ShopFullReductionVO::getShopId, v -> v));
        /**
         * 店铺优惠信息
         * key 店铺id value 优惠金额与信息
         */
        Map<Long, OrderDiscount> orderDiscountsMap = new HashMap<>(fullReductions.size());

        try {
            CompletableTask.allOf(fullReductionExecutor, getFullReductionOrderDiscount(orderFullReductionParam, fullReductionMap, orderDiscountsMap)).get();
        } catch (InterruptedException | ExecutionException exception) {
            Throwable cause = exception.getCause();
            if (cause instanceof RuntimeException runtimeEx) {
                throw runtimeEx;
            }
            throw new RuntimeException(exception);
        }
        return new FullReductionResponse()
                .setOrderDiscounts(orderDiscountsMap);
    }
    private Runnable[] getFullReductionOrderDiscount(OrderFullReductionParam orderFullReductionParam, Map<Long, ShopFullReductionVO> fullReductionMap, Map<Long, OrderDiscount> orderDiscountsMap) {
        // 店铺商品总金额map shopId  Map<productId,productTotalAmount>
        return orderFullReductionParam.getShopProductAmountMap().entrySet()
                .stream()
                .map(entry -> (Runnable) () -> {
                    Long shopId = entry.getKey();
                    Map<Long, Long> productAmountMap = entry.getValue();
                    ShopFullReductionVO fullReduction = fullReductionMap.get(shopId);
                    if (fullReduction == null) {
                        return;
                    }
                    // 可用的满减优惠商品总额
                    long totalAmount = 0;
                    // 优惠金额
                    Long sourceAmount;
                    Set<Long> productIds = fullReduction.getProductIds();
                    Set<Long> discountProductIds = new HashSet<>();
                    switch (fullReduction.getProductType()) {
                        // 全部商品可参与
                        case ALL_PRODUCT -> {
                            discountProductIds.addAll(productAmountMap.keySet());
                            totalAmount = productAmountMap.values().stream().mapToLong(productTotalAmount -> productTotalAmount).sum();
                        }
                        // 指定商品参与
                        case SPECIFIED_PRODUCT_PARTICIPATE -> totalAmount = productAmountMap.entrySet()
                                .stream()
                                .filter(products -> productIds.contains(products.getKey()))
                                .mapToLong(productEntry -> {
                                    discountProductIds.add(productEntry.getKey());
                                    return productEntry.getValue();
                                }).sum();

                        // 指定商品不参与
                        case SPECIFIED_PRODUCT_NOT_PARTICIPATE -> totalAmount = productAmountMap.entrySet()
                                .stream()
                                .filter(products -> !productIds.contains(products.getKey()))
                                .mapToLong(productEntry -> {
                                    discountProductIds.add(productEntry.getKey());
                                    return productEntry.getValue();
                                }).sum();
                    }
                    Map<Long, Map<Long, Long>> shopFullReductionMap = orderFullReductionParam.getShopFullReductionMap();
                    Long fullReductionId = fullReduction.getFullReductionId();
                    Long fullReductionRuleId = shopFullReductionMap.get(shopId).get(fullReductionId);
                    if (fullReductionRuleId == null) {
                        throw new ServiceException("满减规则不存在");
                    }
                    Tuple2<Long, String> tuple = this.getMostDiscountAmount(fullReduction, fullReductionRuleId, totalAmount);
                    if (tuple == null || (sourceAmount = tuple._1) <= 0) {
                        throw new ServiceException("满减规则不存在");
                    }
                    orderDiscountsMap.put(shopId, new OrderDiscount()
                            .setSourceId(fullReductionId)
                            .setSourceStatus(DiscountSourceStatus.OK)
                            .setSourceType(DiscountSourceType.FULL_REDUCTION)
                            .setSourceDesc(tuple._2)
                            .setTotalAmount(totalAmount)
                            .setSourceAmount(sourceAmount)
                            .setProductIds(discountProductIds));
                }).toArray(Runnable[]::new);
    }

    private Tuple2<Long, String> getMostDiscountAmount(ShopFullReductionVO fullReduction, Long fullReductionRuleId, long totalAmount) {
        Long discountAmount;
        for (FullReductionRule fullReductionRule : fullReduction.getFullReductionRules()) {
            if (Objects.equals(fullReductionRule.getId(), fullReductionRuleId)) {
                Long conditionAmount = fullReductionRule.getConditionAmount();
                if (totalAmount < conditionAmount) {
                    throw new ServiceException("满减规则不满足条件");
                }
                String ruleName;
                if (fullReductionRule.getFullReductionRule() == FullReductionRuleType.FULL_REDUCTION) {
                    discountAmount = fullReductionRule.getDiscountAmount();
                    ruleName = fullReductionRule.concatFullReductionName();
                } else {
                    discountAmount = AmountCalculateHelper.getDiscountAmountByDiscount(totalAmount, fullReductionRule.getDiscountRatio());
                    ruleName = fullReductionRule.concatFullDiscountName();
                }
                return Tuple.of(discountAmount, ruleName);
            }

        }
        return null;
    }


    public static void main(String[] args) {

//        List<OrderDiscount> orderDiscounts = new ArrayList<>(10);
//        OrderDiscount orderDiscount;
//        for (int i = 0; i < 10; i++) {
//            orderDiscount = new OrderDiscount();
//            orderDiscount.setSourceId((long) i);
//            orderDiscounts.add(orderDiscount);
//        }
//        System.out.println(orderDiscounts);


    }
}
