package com.medusa.gruul.addon.full.reduction.model;

public interface FullReductionErrorCode {

    /**
     * 满减活动已存在
     */
    Integer FULL_REDUCTION_EXIST = 78001;


    /**
     * 满减活动进行中
     */
    Integer FULL_REDUCTION_PROCESSING = 78002;

    /**
     * 满减活动编辑失败
     */
    Integer FULL_REDUCTION_EDIT = 78003;

    /**
     * 满减活动商品保存失败
     */
    Integer FULL_REDUCTION_PRODUCT_EDIT = 78004;

    /**
     * 满减活动商品删除失败
     */
    Integer FULL_REDUCTION_PRODUCT_DELETE = 78005;

    /**
     * 满减活动不存在
     */
    Integer FULL_REDUCTION_NOT_EXIST = 78006;

}
