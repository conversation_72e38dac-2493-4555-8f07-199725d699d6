package com.medusa.gruul.addon.full.reduction.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 
 * 满减活动商品
 * 
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_full_reduction_product")
public class FullReductionProduct extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 满减活动id
     */
    private Long fullReductionId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 满减活动商品id
     */
    private Long productId;
}
