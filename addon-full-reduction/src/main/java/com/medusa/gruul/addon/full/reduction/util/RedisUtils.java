package com.medusa.gruul.addon.full.reduction.util;

import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.addon.full.reduction.constant.FullReductionConstant;
import com.medusa.gruul.common.redis.util.RedisUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.util.function.Supplier;

@SuppressWarnings(value = {"unchecked", "rawtypes"})
public class RedisUtils {

    private static final RedisTemplate redisTemplate = RedisUtil.getRedisTemplate();
    private static final RedissonClient redissonClient = RedisUtil.getRedissonClient();


    public static <T> T getCacheValue(
            Class<T> type,
            Supplier<T> supplier,
            Duration expireTime,
            String key
    ) {
        return RedisUtils.getCacheValue(
                () -> RedisUtils.getCacheValue(key, type),
                supplier,
                expireTime,
                key
        );
    }

    public static <T> T getCacheValue(String key, Class<T> type) {
        Object value = redisTemplate.opsForValue().get(key);
        if (value == null) {
            return null;
        }

        return JSON.to(type, value);
    }

    public static <T> T getCacheValue(String key,
                                      Supplier<T> cacheFactory,
                                      Supplier<T> supplier,
                                      Duration expireTime,
                                      Duration subTriggerTime
    ) {
        T cacheValue = cacheFactory.get();
        if (cacheValue != null) {
            Long expire = redisTemplate.getExpire(key);
            if (subTriggerTime.compareTo(Duration.ofSeconds(expire == null ? 0L : expire)) >= 0) {
                redisTemplate.expire(key, expireTime);
            }
            return cacheValue;
        }
        RLock lock = redissonClient.getLock(key + FullReductionConstant.FULL_REDUCTION_REDIS_LOCK_KEY);
        lock.lock();
        try {
            cacheValue = cacheFactory.get();
            if (cacheValue != null) {
                return cacheValue;
            }
            cacheValue = supplier.get();
            redisTemplate.opsForValue().set(key, cacheValue, expireTime);
            return cacheValue;
        } finally {
            lock.unlock();
        }

    }


    public static <T> T getCacheValue(Supplier<T> cacheFactory,
                                      Supplier<T> supplier,
                                      Duration expireTime,
                                      String key) {

        T cacheValue = cacheFactory.get();
        if (cacheValue != null) {
            return cacheValue;
        }
        RLock lock = redissonClient.getLock(key + FullReductionConstant.FULL_REDUCTION_REDIS_LOCK_KEY);
        lock.lock();
        try {
            if (cacheFactory.get() != null) {
                return cacheValue;
            }
            cacheValue = supplier.get();
            Long value = (Long) cacheValue;
            if (value > 0) {
                expireTime = Duration.ofSeconds(value);
            }
            redisTemplate.opsForValue().set(key, value, expireTime);
            return cacheValue;
        } finally {
            lock.unlock();
        }


    }

    public static <T> T getCacheFullReduction(Supplier<T> cacheFactory, Supplier<T> supplier, String key) {
        RLock lock = redissonClient.getLock(RedisUtil.key(FullReductionConstant.FULL_REDUCTION_REDIS_LOCK_KEY, key));
        lock.lock();
        try {
            T t = cacheFactory.get();
            if (t != null) {
                return t;
            }
            return supplier.get();
        } finally {
            lock.unlock();
        }
    }

    public static <T> T getMultiFullReduction(Supplier<T> cacheFactory, Supplier<T> supplier, RLock... rlock) {
        RLock multiLock = redissonClient.getMultiLock(rlock);
        multiLock.lock();
        try {
            T t = cacheFactory.get();
            if (t != null) {
                return t;
            }
            return supplier.get();
        } finally {
            multiLock.unlock();
        }

    }
}
