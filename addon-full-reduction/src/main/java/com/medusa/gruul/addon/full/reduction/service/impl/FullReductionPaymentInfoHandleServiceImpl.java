package com.medusa.gruul.addon.full.reduction.service.impl;

import com.medusa.gruul.addon.full.reduction.mp.entity.FullReduction;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionPaymentInfo;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionPaymentInfoService;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionService;
import com.medusa.gruul.addon.full.reduction.service.FullReductionPaymentInfoHandleService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.order.api.entity.ShopOrderItem;
import com.medusa.gruul.order.api.model.OrderPaidBroadcastDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FullReductionPaymentInfoHandleServiceImpl implements FullReductionPaymentInfoHandleService {

    private final IFullReductionPaymentInfoService fullReductionPaymentInfoService;
    private final IFullReductionService fullReductionService;

    @Override
    public void addFullReductionPaymentInfo(OrderPaidBroadcastDTO orderPaidBroadcast) {
        Map<Long, List<ShopOrderItem>> fullReduceMap = orderPaidBroadcast.getFullReduceMap().entrySet()
                .stream()
                .filter(entry -> fullReductionService.lambdaQuery()
                        .eq(FullReduction::getId, entry.getKey())
                        .eq(FullReduction::getShopId, entry.getValue().get(CommonPool.NUMBER_ZERO).getShopId())
                        .exists()
                ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (CollectionUtils.isEmpty(fullReduceMap)) {
            return;
        }
        String orderNo = orderPaidBroadcast.getOrderNo();
        Long payerId = orderPaidBroadcast.getPayerId();
        List<FullReductionPaymentInfo> fullReductionPaymentInfos = fullReduceMap.entrySet()
                .stream()
                .map(entry -> entry.getValue()
                        .stream()
                        .map(shopOrderItem ->
                                new FullReductionPaymentInfo()
                                        .setActivityId(entry.getKey())
                                        .setShopId(shopOrderItem.getShopId())
                                        .setOrderNo(orderNo)
                                        .setUserId(payerId)
                                        .setShopOrderItemId(shopOrderItem.getId())
                                        .setAmountReceivable(shopOrderItem.getDealPrice() + shopOrderItem.getFixPrice())
                        ).collect(Collectors.toList())
                ).flatMap(List::stream)
                .toList();
        fullReductionPaymentInfoService.saveBatch(fullReductionPaymentInfos);
    }

    /**
     * 满减活动订单退款
     *
     * @param orderInfo 满减活动退款信息
     */
    @Override
    public void fullReductionPaymentRefund(OrderInfo orderInfo) {
        Long activityId = orderInfo.getActivityId();
        Long shopId = orderInfo.getShopId();
        boolean exists = fullReductionService.lambdaQuery()
                .eq(FullReduction::getId, activityId)
                .eq(FullReduction::getShopId, shopId)
                .exists();
        if (!exists) {
            return;
        }
        Long refundAmount = orderInfo.getAfs().getRefundAmount();
        Long shopOrderItemId = orderInfo.getAfs().getShopOrderItemId();
        String orderNo = orderInfo.getOrderNo();
        FullReductionPaymentInfo fullReductionPaymentInfo = fullReductionPaymentInfoService.lambdaQuery()
                .select(FullReductionPaymentInfo::getId, FullReductionPaymentInfo::getAmountReceivable)
                .eq(FullReductionPaymentInfo::getOrderNo, orderNo)
                .eq(FullReductionPaymentInfo::getShopOrderItemId, shopOrderItemId)
                .eq(FullReductionPaymentInfo::getActivityId, activityId)
                .eq(FullReductionPaymentInfo::getShopId, shopId)
                .one();
        if (fullReductionPaymentInfo == null) {
            return;
        }
        Long amountReceivable = fullReductionPaymentInfo.getAmountReceivable();
        if (amountReceivable.equals(refundAmount)) {
            fullReductionPaymentInfoService.lambdaUpdate()
                    .eq(FullReductionPaymentInfo::getId, fullReductionPaymentInfo.getId())
                    .eq(FullReductionPaymentInfo::getOrderNo, orderNo)
                    .remove();
            return;
        }
        fullReductionPaymentInfo.setAmountReceivable(amountReceivable - refundAmount);
        fullReductionPaymentInfoService.updateById(fullReductionPaymentInfo);

    }

}
