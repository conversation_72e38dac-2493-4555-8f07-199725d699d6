package com.medusa.gruul.addon.full.reduction.model.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.addon.full.reduction.handler.FullReductionRuleHandler;
import com.medusa.gruul.addon.full.reduction.model.enums.ApplyType;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionRuleType;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus;
import com.medusa.gruul.addon.full.reduction.model.enums.ProductType;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class FullReductionVO {


    /**
     * 满减活动id
     */
    private Long id;
    /**
     * 满减活动名称
     */
    private String fullReductionName;


    /**
     * 活动状态 [0未开始 1进行中 2已结束 3违规下架]
     */
    private FullReductionStatus fullReductionStatus;

    /**
     * 开始时间
     */
    private LocalDateTime fullReductionStartTime;

    /**
     * 结束时间
     */
    private LocalDateTime fullReductionEndTime;

    /**
     * 0:满减 1:满折
     */
    private FullReductionRuleType fullReductionRule;

    /**
     * 店铺id
     */
    private Long shopId;


    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 0:全部商品 1:制定商品 2:指定商品不参与
     */
    private ProductType productType;

    /**
     * 满减金额/满折金额
     */
    private Long conditionAmount;

    /**
     * 优惠金额
     */
    private Long discountAmount;

    /**
     * 折扣比 0.1-9.9
     */
    private BigDecimal discountRatio;
    /**
     * 可用抵扣： 0-积分
     */
    private Boolean deductionType;

    /**
     * 适用优惠：0-会员价  1-优惠劵
     */
    private List<ApplyType> applyTypes;

    /**
     * 活动商品数
     */
    private Integer productNum;

    /**
     * 参与人数
     */
    private Integer peopleNum;

    /**
     * 支付订单数
     */
    private Integer payOrder;

    /**
     * 应收金额
     */
    private Long amountReceivable;

    /**
     * 商品ids
     */
    private List<Long> productIds;

    /**
     * 满减规则
     */
    @TableField(value = "full_reduction_rules", typeHandler = FullReductionRuleHandler.class)
    private List<FullReductionRule> fullReductionRules;
}
