package com.medusa.gruul.addon.full.reduction.mp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionQueryDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.ProductDetailFullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionProductVO;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO;
import com.medusa.gruul.addon.full.reduction.model.vo.ShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReduction;
import com.medusa.gruul.addon.full.reduction.mp.mapper.FullReductionMapper;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 满减活动表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service
public class FullReductionServiceImpl extends ServiceImpl<FullReductionMapper, FullReduction> implements IFullReductionService {

    /**
     * 查询单个满减活动
     *
     * @param shopId          店铺id
     * @param fullReductionId 满减活动id
     * @return 满减活动
     */
    @Override
    public FullReductionVO getFullReductionById(Long shopId, Long fullReductionId) {
        return baseMapper.getFullReductionById(shopId, fullReductionId);

    }

    /**
     * 分页查询满减活动
     *
     * @param fullReductionQuery 查询参数
     * @return 满减活动分页结果
     */
    @Override
    public IPage<FullReductionVO> fullReductionPage(FullReductionQueryDTO fullReductionQuery) {
        return baseMapper.fullReductionPage(fullReductionQuery);
    }

    /**
     * 商品详情页满减优惠
     *
     * @param productDetailFullReduction 商品详情满减信息
     * @return 满减优惠信息
     */
    @Override
    public FullReductionProductVO productDetailFullReduction(ProductDetailFullReductionDTO productDetailFullReduction) {
        return baseMapper.productDetailFullReduction(productDetailFullReduction);
    }

    /**
     * 根据店铺id查询满减信息
     *
     * @param shopIds 店铺id
     * @return 店铺满减信息
     */
    @Override
    public List<ShopFullReductionVO> getFullReductions(Set<Long> shopIds) {
        return baseMapper.getFullReductions(shopIds);
    }

}
