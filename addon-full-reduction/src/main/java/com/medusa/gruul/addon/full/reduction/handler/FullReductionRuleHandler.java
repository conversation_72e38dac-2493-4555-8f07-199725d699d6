package com.medusa.gruul.addon.full.reduction.handler;

import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import com.medusa.gruul.common.mp.IFastJson2TypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class FullReductionRuleHandler extends IFastJson2TypeHandler {
    @Override
    protected TypeReference<?> getTypeReference() {
        return new TypeReference<List<FullReductionRule>>() {
        };
    }
}
