package com.medusa.gruul.addon.full.reduction.controller;


import com.medusa.gruul.addon.full.reduction.model.dto.CartShopProductIdDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.OrderShopProductDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.ProductDetailFullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.SearchShopProductIdDTO;
import com.medusa.gruul.addon.full.reduction.model.vo.CartShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.model.vo.SearchProductVO;
import com.medusa.gruul.addon.full.reduction.model.vo.ShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import com.medusa.gruul.addon.full.reduction.service.ConsumerFullReductionService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * 用户端满减
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/fullReduction/consumer")
public class ConsumerFullReductionController {

    private final ConsumerFullReductionService consumerFullReductionService;

    /**
     * 商品详情页满减优惠
     *
     * @param productDetailFullReduction 商品详情满减信息
     * @return 满减优惠信息
     */
    @Log("商品详情页满减优惠")
    @PreAuthorize("permitAll()")
    @GetMapping("/productDetail")
    public Result<List<FullReductionRule>> productDetailFullReduction(@Valid ProductDetailFullReductionDTO productDetailFullReduction) {
        return Result.ok(
                consumerFullReductionService.productDetailFullReduction(productDetailFullReduction)
        );
    }

    /**
     * 购物车最佳优惠满减信息
     *
     * @param shopProductIds 店铺商品id集合
     * @return 满减信息
     */
    @Log("购物车满减信息")
    @GetMapping("/cart")
    @PreAuthorize("@S.user")
    public Result<List<CartShopFullReductionVO>> fullReductionsCart(@RequestBody @Valid @Size(min = 1) List<CartShopProductIdDTO> shopProductIds) {
        return Result.ok(
                consumerFullReductionService.fullReductionsCart(shopProductIds)
        );
    }


    /**
     * 查询当前满减活动是否存在
     *
     * @return true or false
     */
    @Log("查询当前满减活动")
    @GetMapping("/exist")
    @PreAuthorize("permitAll()")
    public Result<Boolean> getFullReduction() {
        return Result.ok(
                consumerFullReductionService.getNowFullReduction()
        );
    }

    /**
     * 结算页满减信息
     *
     * @param orderShopProducts 订单店铺商品
     * @return 店铺订单商品满减信息
     */
    @Log("结算页满减信息")
    @PostMapping("/confirm/order")
    @PreAuthorize("@S.user")
    public Result<List<ShopFullReductionVO>> confirmOrderFullReduction(@RequestBody @Valid @Size(min = 1)
                                                                       List<OrderShopProductDTO> orderShopProducts) {
        return Result.ok(
                consumerFullReductionService.confirmOrderFullReduction(orderShopProducts)
        );
    }

    /**
     * 根据店铺id查询购物车满减活动
     *
     * @param shopIds 店铺id
     * @return 购物车满减信息
     */
    @Log("根据店铺id查询购物车满减活动")
    @PreAuthorize("@S.user")
    @PostMapping("/shopCart")
    public Result<List<ShopFullReductionVO>> cartShopFullReduction(@RequestBody @Size(min = 1) Set<Long> shopIds) {
        return Result.ok(
                consumerFullReductionService.cartShopFullReduction(shopIds)
        );
    }

    /**
     * 检索商品满减信息
     *
     * @param searchShopProductIds 检索商品店铺id、商品id
     * @return 检索商品满减信息
     */
    @Log("检索满减信息")
    @GetMapping("/searchProduct")
    @PreAuthorize("permitAll()")
    public Result<List<SearchProductVO>> fullReductionSearchProduct(@RequestBody @Validated @Size(min = 1) List<SearchShopProductIdDTO> searchShopProductIds) {
        return Result.ok(
                consumerFullReductionService.fullReductionSearchProduct(searchShopProductIds)
        );

    }

}
