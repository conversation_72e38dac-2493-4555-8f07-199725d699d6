package com.medusa.gruul.addon.full.reduction.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.addon.full.reduction.constant.FullReductionConstant;
import com.medusa.gruul.addon.full.reduction.model.FullReductionErrorCode;
import com.medusa.gruul.addon.full.reduction.model.dto.*;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionRuleType;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus;
import com.medusa.gruul.addon.full.reduction.model.enums.ProductType;
import com.medusa.gruul.addon.full.reduction.model.vo.CartShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.model.vo.SearchProductVO;
import com.medusa.gruul.addon.full.reduction.model.vo.ShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReduction;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionService;
import com.medusa.gruul.addon.full.reduction.service.ConsumerFullReductionService;
import com.medusa.gruul.addon.full.reduction.util.RedisUtils;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.global.model.helper.CompletableTask;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class ConsumerFullReductionServiceImpl implements ConsumerFullReductionService {

    private final IFullReductionService fullReductionService;
    private final Executor fullReductionExecutor;

    /**
     * 商品详情页满减优惠
     *
     * @param productDetailFullReduction 商品详情满减信息
     * @return 满减优惠信息
     */
    @Override
    public List<FullReductionRule> productDetailFullReduction(ProductDetailFullReductionDTO productDetailFullReduction) {
        Long productId = productDetailFullReduction.getProductId();
        Long shopId = productDetailFullReduction.getShopId();
        Set<Long> shopIds = CollUtil.newHashSet(shopId);
        Long productAmount = productDetailFullReduction.getAmount();
        // 获取缓存中的满减活动
        List<ShopFullReductionVO> cacheFullReduction = getCacheFullReduction(shopIds, ShopFullReductionVO.class);
        // 缓存不存在
        if (cacheFullReduction == null) {
            cacheFullReduction = RedisUtils.getCacheFullReduction(
                    () -> fullReductionService.getFullReductions(shopIds),
                    () -> getCacheFullReduction(shopIds, ShopFullReductionVO.class),
                    shopId.toString()
            );
            if (CollectionUtils.isEmpty(cacheFullReduction)) {
                setCacheNullValue(shopIds);
                return Collections.emptyList();
            }
        }
        // 缓存为空值
        if (CollectionUtils.isEmpty(cacheFullReduction)) {
            return Collections.emptyList();
        }
        // 缓存不为空
        return productDetailFullReduction(productId, productAmount, cacheFullReduction);
    }

    private List<FullReductionRule> productDetailFullReduction(Long productId, Long productAmount, List<ShopFullReductionVO> cacheFullReduction) {
        ShopFullReductionVO shopFullReduction = cacheFullReduction.get(CommonPool.NUMBER_ZERO);
        ProductType productType = shopFullReduction.getProductType();
        boolean canUse = true;
        Set<Long> productIds = shopFullReduction.getProductIds();
        switch (productType) {
            case SPECIFIED_PRODUCT_PARTICIPATE -> canUse = productIds.contains(productId);
            case SPECIFIED_PRODUCT_NOT_PARTICIPATE -> canUse = !productIds.contains(productId);
        }
        if (canUse) {
            List<FullReductionRule> fullReductionRules = getCanUseFullReductions(shopFullReduction, productAmount);
            if (!CollectionUtils.isEmpty(fullReductionRules)) {
                return fullReductionRules;
            }
        }
        return Collections.emptyList();
    }

    /**
     * 确认订单参与的满减活动
     *
     * @param orderShopProducts 订单店铺商品
     * @return 每个店铺参与的满减活动信息
     */
    @Override
    public List<ShopFullReductionVO> confirmOrderFullReduction(List<OrderShopProductDTO> orderShopProducts) {
        this.getExistsFullReduction();
        Set<Long> shopIds = orderShopProducts.stream().map(OrderShopProductDTO::getShopId).collect(Collectors.toSet());
        List<ShopFullReductionVO> fullReductions = getOrderFullReduction(shopIds);
        List<ShopFullReductionVO> canUseFullReductions = new ArrayList<>(fullReductions.size());
        // shopId  productId,productAmount
        Map<Long, Map<Long, Long>> orderShopProductsMap = orderShopProducts.stream()
                .collect(Collectors.toMap(
                        OrderShopProductDTO::getShopId,
                        v -> v.getProductAmounts()
                                .stream()
                                .collect(Collectors.toMap(ProductAmountDTO::getProductId,
                                        ProductAmountDTO::getAmount, Long::sum))
                ));

        int size = fullReductions.size();
        if (size >= 2) {
            // 多个满减活动
            getMultithreadingFullReduction(canUseFullReductions, fullReductions, orderShopProductsMap, size);
            return canUseFullReductions;
        }
        // 单个满减活动
        ShopFullReductionVO fullReduction = fullReductions.get(CommonPool.NUMBER_ZERO);
        Long shopId = fullReduction.getShopId();
        // productId,productAmount
        Map<Long, Long> productAmountsMap = orderShopProductsMap.get(shopId);
        if (CollectionUtils.isEmpty(productAmountsMap)) {
            return Collections.emptyList();
        }
        getFullReductions(canUseFullReductions, fullReduction, productAmountsMap);
        return canUseFullReductions;
    }

    // 多线程满减
    private void getMultithreadingFullReduction(List<ShopFullReductionVO> canUseFullReductions, List<ShopFullReductionVO> fullReductions, Map<Long, Map<Long, Long>> orderShopProductsMap, int size) {
        CompletableFuture<Void>[] completableFutures = new CompletableFuture[size];
        for (int i = 0; i < fullReductions.size(); i++) {
            ShopFullReductionVO fullReduction = fullReductions.get(i);
            Long shopId = fullReduction.getShopId();
            // productId,productAmount
            Map<Long, Long> productAmountsMap = orderShopProductsMap.get(shopId);
            if (CollectionUtils.isEmpty(productAmountsMap)) {
                continue;
            }
            completableFutures[i] = CompletableFuture.runAsync(() -> getFullReductions(canUseFullReductions, fullReduction, productAmountsMap), fullReductionExecutor);
        }
        CompletableTask.getOrThrowException(
                CompletableFuture.allOf(completableFutures)
        );
    }

    /**
     * 获取指定店铺的满减优惠信息
     *
     * @param canUseFullReductions 满减优惠信息列表
     * @param fullReduction        满减优惠信息
     * @param productAmountsMap    商金额Map
     */
    private void getFullReductions(List<ShopFullReductionVO> canUseFullReductions, ShopFullReductionVO fullReduction, Map<Long, Long> productAmountsMap) {
        Set<Long> fullReductionProductIds = fullReduction.getProductIds();
        long canUseProductAmount = 0;
        switch (fullReduction.getProductType()) {
            // 所有商品参与
            case ALL_PRODUCT ->
                    canUseProductAmount = productAmountsMap.values().stream().mapToLong(productAmount -> productAmount).sum();
            // 指定商品参与
            case SPECIFIED_PRODUCT_PARTICIPATE -> canUseProductAmount = productAmountsMap.entrySet()
                    .stream()
                    .filter(entry -> fullReductionProductIds.contains(entry.getKey()))
                    .mapToLong(Map.Entry::getValue)
                    .sum();
            // 指定商品不参与
            case SPECIFIED_PRODUCT_NOT_PARTICIPATE -> canUseProductAmount = productAmountsMap.entrySet()
                    .stream()
                    .filter(entry -> !fullReductionProductIds.contains(entry.getKey()))
                    .mapToLong(Map.Entry::getValue)
                    .sum();
        }
        // 如果可用金额为0,不计算优惠规则
        if (canUseProductAmount == 0) {
            return;
        }
        List<FullReductionRule> fullReductionRules = getCanUseFullReductions(fullReduction, canUseProductAmount);
        if (CollectionUtils.isEmpty(fullReductionRules)) {
            return;
        }
        // 获取可用的优惠规则
        fullReduction.setFullReductionRules(fullReductionRules);
        // 添加满减优惠信息到列表
        canUseFullReductions.add(fullReduction);
    }

    private List<FullReductionRule> getCanUseFullReductions(ShopFullReductionVO fullReduction, long canUseProductAmount) {
        return fullReduction.getFullReductionRules().stream()
                .filter(fullReductionRule -> canUseProductAmount >= fullReductionRule.getConditionAmount())
                .peek(fullReductionRule -> {
                    FullReductionRuleType rule = fullReductionRule.getFullReductionRule();
                    long orderDiscount;
                    if (rule == FullReductionRuleType.FULL_REDUCTION) {
                        orderDiscount = fullReductionRule.getDiscountAmount();
                    } else {
                        orderDiscount = AmountCalculateHelper.getDiscountAmountByDiscount(canUseProductAmount, fullReductionRule.getDiscountRatio());
                    }
                    fullReductionRule.setOrderDiscount(orderDiscount);
                }).sorted(Comparator.comparing(FullReductionRule::getOrderDiscount).reversed()).toList();
    }

    /**
     * 获取最优惠的满减规则
     *
     * @param fullReduction      满减信息
     * @param productTotalAmount 商品总额
     */
    private void getMostOptimalRules(CartShopFullReductionVO cartShopFullReduction, FullReduction fullReduction, Long productTotalAmount) {
        long discount = 0;
        for (FullReductionRule fullReductionRule : fullReduction.getFullReductionRules()) {
            Long conditionAmount = fullReductionRule.getConditionAmount();
            if (productTotalAmount < conditionAmount) {
                continue;
            }
            FullReductionRuleType rule = fullReductionRule.getFullReductionRule();
            Long fullReductionDiscountAmount;
            if (rule == FullReductionRuleType.FULL_REDUCTION) {
                fullReductionDiscountAmount = fullReductionRule.getDiscountAmount();
            } else {
                fullReductionDiscountAmount = AmountCalculateHelper.getDiscountAmountByDiscount(productTotalAmount, fullReductionRule.getDiscountRatio());
            }
            if (fullReductionDiscountAmount > discount) {
                discount = fullReductionDiscountAmount;
            }
            cartShopFullReduction.setDiscountAmount(discount)
                    .setFullReductionRules(fullReductionRule);
        }
    }

    /**
     * 购物车满减信息
     *
     * @param shopProductIds 店铺商品id集合
     * @return 满减信息
     */
    @Override
    public List<CartShopFullReductionVO> fullReductionsCart(List<CartShopProductIdDTO> shopProductIds) {
        // 获取当前是否存在满减活动
        this.getExistsFullReduction();
        LocalDateTime now = LocalDateTime.now();
        Set<Long> shopIds = shopProductIds.stream().map(CartShopProductIdDTO::getShopId).collect(Collectors.toSet());
        Map<Long, FullReduction> fullReductionMap = fullReductionService.lambdaQuery()
                .in(FullReduction::getShopId, shopIds)
                .le(FullReduction::getFullReductionStartTime, now)
                .ge(FullReduction::getFullReductionEndTime, now)
                .ne(FullReduction::getFullReductionStatus, FullReductionStatus.ILLEGAL_SELL_OFF)
                .list()
                .stream()
                .collect(Collectors.toMap(FullReduction::getShopId, v -> v));
        if (CollectionUtils.isEmpty(fullReductionMap)) {
            return null;
        }
        List<CartShopFullReductionVO> cartShopFullReductions = new ArrayList<>(shopProductIds.size());
        try {
            // 获取购物车的满减活动最佳优惠规则
            CompletableTask.allOf(fullReductionExecutor, this.getCartFullReduction(shopProductIds, cartShopFullReductions, fullReductionMap)).get();
        } catch (InterruptedException | ExecutionException exception) {
            Throwable cause = exception.getCause();
            if (cause instanceof RuntimeException runtimeEx) {
                throw runtimeEx;
            }
            throw new RuntimeException(exception);
        }
        return cartShopFullReductions;
    }

    private Runnable[] getCartFullReduction(List<CartShopProductIdDTO> shopProductIds, List<CartShopFullReductionVO> cartShopFullReductions, Map<Long, FullReduction> fullReductionMap) {
        return shopProductIds.stream()
                .map(shopProductId -> (Runnable) () -> {
                    Long shopId = shopProductId.getShopId();
                    FullReduction fullReduction = fullReductionMap.get(shopId);
                    if (fullReduction == null) {
                        return;
                    }
                    List<ProductAmountDTO> productAmounts = shopProductId.getProductAmounts();
                    Set<Long> productIds = fullReduction.getProductIds();
                    CartShopFullReductionVO cartShopFullReduction = new CartShopFullReductionVO();
                    List<Long> fullReductionProductIds = new ArrayList<>();
                    long canUseProductAmount = 0;
                    switch (fullReduction.getProductType()) {
                        case ALL_PRODUCT ->
                                canUseProductAmount = productAmounts.stream().map(ProductAmountDTO::getAmount).reduce(0L, Long::sum);
                        case SPECIFIED_PRODUCT_PARTICIPATE -> canUseProductAmount = productAmounts.stream()
                                .filter(productAmountDTO -> productIds.contains(productAmountDTO.getProductId()))
                                .filter(productAmountDTO -> fullReductionProductIds.add(productAmountDTO.getProductId()))
                                .map(ProductAmountDTO::getAmount)
                                .reduce(0L, Long::sum);
                        case SPECIFIED_PRODUCT_NOT_PARTICIPATE -> canUseProductAmount = productAmounts.stream()
                                .filter(productAmount -> !productIds.contains(productAmount.getProductId()))
                                .filter(productAmountDTO -> fullReductionProductIds.add(productAmountDTO.getProductId()))
                                .map(ProductAmountDTO::getAmount)
                                .reduce(0L, Long::sum);
                    }
                    if (canUseProductAmount == 0) {
                        return;
                    }
                    // 获取最优惠的满减规则
                    this.getMostOptimalRules(cartShopFullReduction, fullReduction, canUseProductAmount);
                    cartShopFullReductions.add(cartShopFullReduction.setShopId(shopId)
                            .setProductIds(fullReductionProductIds));
                }).toArray(Runnable[]::new);
    }

    /**
     * 查询当前满减活动
     *
     * @return 满减id集合
     */
    @Override
    public Boolean getNowFullReduction() {
        LocalDateTime now = LocalDateTime.now();
        return RedisUtils.getCacheValue(
                Long.class,
                () -> {
                    FullReduction fullReduction = fullReductionService.query()
                            .select("MIN(full_reduction_end_time) AS full_reduction_end_time")
                            .le("full_reduction_start_time", now)
                            .ge("full_reduction_end_time", now)
                            .ne("full_reduction_status", FullReductionStatus.ILLEGAL_SELL_OFF)
                            .one();
                    if (fullReduction == null) {
                        fullReduction = fullReductionService.query()
                                .select("MIN(full_reduction_start_time) AS full_reduction_start_time")
                                .gt("full_reduction_start_time", now)
                                .ne("full_reduction_status", FullReductionStatus.ILLEGAL_SELL_OFF)
                                .one();
                        if (fullReduction != null) {
                            return Duration.between(now, fullReduction.getFullReductionStartTime()).getSeconds();
                        }
                        return 0L;
                    }
                    return Duration.between(now, fullReduction.getFullReductionEndTime()).getSeconds();

                },
                Duration.ofHours(CommonPool.NUMBER_ONE),
                FullReductionConstant.FULL_REDUCTION_PARTICIPATE

        ).compareTo(0L) > 0;
    }

    public void getExistsFullReduction() {
        Boolean exist = this.getNowFullReduction();
        if (!exist) {
            throw new ServiceException("满减活动不存在", FullReductionErrorCode.FULL_REDUCTION_EXIST);
        }
    }

    /**
     * 检索商品满减信息
     *
     * @param searchShopProductIds 检索商品店铺id、商品id
     * @return 检索商品满减信息
     */
    @Override
    public List<SearchProductVO> fullReductionSearchProduct(List<SearchShopProductIdDTO> searchShopProductIds) {
        this.getExistsFullReduction();
        LocalDateTime now = LocalDateTime.now();
        Map<Long, FullReduction> fullReductionMap = fullReductionService.lambdaQuery()
                .le(FullReduction::getFullReductionStartTime, now)
                .ge(FullReduction::getFullReductionEndTime, now)
                .ne(FullReduction::getFullReductionStatus, FullReductionStatus.ILLEGAL_SELL_OFF)
                .list()
                .stream()
                .collect(Collectors.toMap(FullReduction::getShopId, v -> v));
        return searchShopProductIds.stream()
                .filter(searchShopProductId -> {
                    FullReduction fullReduction = fullReductionMap.get(searchShopProductId.getShopId());
                    if (fullReduction == null) {
                        return false;
                    }
                    boolean isParticipate = false;
                    Long productId = searchShopProductId.getProductId();
                    Set<Long> productIds = fullReduction.getProductIds();
                    ProductType productType = fullReduction.getProductType();
                    switch (productType) {
                        case ALL_PRODUCT -> isParticipate = true;
                        case SPECIFIED_PRODUCT_PARTICIPATE -> {
                            if (productIds.contains(productId)) {
                                isParticipate = true;
                            }
                        }
                        case SPECIFIED_PRODUCT_NOT_PARTICIPATE -> {
                            if (!productIds.contains(productId)) {
                                isParticipate = true;
                            }
                        }
                    }
                    return isParticipate;
                })
                .map(searchShopProductIdDTO -> new SearchProductVO().setShopId(searchShopProductIdDTO.getShopId()).setProductId(searchShopProductIdDTO.getProductId()))
                .toList();
    }


    @Override
    public List<ShopFullReductionVO> cartShopFullReduction(Set<Long> shopIds) {
        try {
            getExistsFullReduction();
        } catch (GlobalException globalException) {
            return List.of();
        }
        List<ShopFullReductionVO> cacheFullReduction;
        // 获取缓存满减信息
        cacheFullReduction = getCacheFullReduction(shopIds, ShopFullReductionVO.class);
        if (cacheFullReduction != null) {
            return cacheFullReduction;
        }
        RLock lock = RedisUtil.getRedissonClient().getLock(FullReductionConstant.FULL_REDUCTION_CART_REDIS_LOCK_KEY);
        lock.lock();
        try {
            // 获取缓存满减信息
            cacheFullReduction = getCacheFullReduction(shopIds, ShopFullReductionVO.class);
            if (cacheFullReduction != null) {
                return cacheFullReduction;
            }
            cacheFullReduction = fullReductionService.getFullReductions(shopIds);
            if (CollUtil.isNotEmpty(cacheFullReduction)) {
                setCacheFullReduction(cacheFullReduction);
                return cacheFullReduction;
            }
            setCacheNullValue(shopIds);
            return cacheFullReduction;
        } finally {
            lock.unlock();
        }
    }

    private void setCacheFullReduction(List<ShopFullReductionVO> fullReductions) {
        RedisUtil.executePipelined(
                (redisOperations) -> {
                    ValueOperations valueOperations = redisOperations.opsForValue();
                    for (ShopFullReductionVO fullReduction : fullReductions) {
                        valueOperations.set(RedisUtil.key(FullReductionConstant.SHOP_FULL_REDUCTION_KEY, fullReduction.getShopId()), JSON.toJSONString(fullReduction), Duration.between(LocalDateTime.now(), fullReduction.getFullReductionEndTime()));
                    }
                }
        );
    }

    private void setCacheNullValue(Set<Long> shopIds) {
        RedisUtil.executePipelined(
                (redisOperations) -> {
                    ValueOperations valueOperations = redisOperations.opsForValue();
                    for (Long shopId : shopIds) {
                        valueOperations.set(RedisUtil.key(FullReductionConstant.SHOP_FULL_REDUCTION_KEY, shopId), "n", Duration.ofHours(CommonPool.NUMBER_ONE));
                    }
                }
        );
    }

    /**
     * 从Redis中获取指定店铺ID集合的满减优惠信息，返回对应的实体类集合。
     * 如果Redis中不存在某个店铺的满减信息，将该店铺ID放入notExist集合中，以便后续统计。
     * 如果所有店铺的满减信息都不存在，返回空集合。
     *
     * @param shopIds 店铺ID集合
     * @param type    返回的实体类类型
     * @param <T>     返回的实体类类型
     * @return 对应的实体类集合，或空集合，或null
     */
    public <T> List<T> getCacheFullReduction(Set<Long> shopIds, Class<T> type) {
        int size = shopIds.size();
        List<T> shopFullReductions = new ArrayList<>(size);
        List<String> notExist = new ArrayList<>(size);
        for (Long shopId : shopIds) {
            String str = (String) RedisUtil.getRedisTemplate().opsForValue().get(RedisUtil.key(FullReductionConstant.SHOP_FULL_REDUCTION_KEY, shopId));
            if (str != null) {
                if ("n".equals(str)) {
                    notExist.add("n");
                } else {
                    shopFullReductions.add(JSON.parseObject(str, type));
                }
            }
        }
        // 如果所有店铺的满减信息不存在，返回空
        if (shopFullReductions.isEmpty()) {
            return null;
        }
        // 缓存为空"n"
        if (notExist.size() == size) {
            return Collections.emptyList();
        }
        return shopFullReductions;
    }

    public List<ShopFullReductionVO> getOrderFullReduction(Set<Long> shopIds) {
        List<ShopFullReductionVO> fullReductions = getCacheFullReduction(shopIds, ShopFullReductionVO.class);
        // 缓存为空值
        if (fullReductions != null && fullReductions.size() == 0) {
            throw new ServiceException("满减活动不存在", FullReductionErrorCode.FULL_REDUCTION_EXIST);
        }
        if (CollectionUtils.isEmpty(fullReductions)) {
            fullReductions = RedisUtils.getMultiFullReduction(
                    () -> getCacheFullReduction(shopIds, ShopFullReductionVO.class),
                    () -> fullReductionService.getFullReductions(shopIds),
                    shopIds.stream()
                            .map(id -> RedisUtil.key(FullReductionConstant.FULL_REDUCTION_ORDER_REDIS_LOCK_KEY, id))
                            .map(RedisUtil.getRedissonClient()::getLock)
                            .toArray(RLock[]::new)
            );
            if (CollectionUtils.isEmpty(fullReductions)) {
                setCacheNullValue(shopIds);
                throw new ServiceException("满减活动不存在", FullReductionErrorCode.FULL_REDUCTION_EXIST);
            }
        }
        return fullReductions;
    }
}
