package com.medusa.gruul.addon.full.reduction.service;

import com.medusa.gruul.addon.full.reduction.model.dto.OrderShopProductDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.ProductDetailFullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.CartShopProductIdDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.SearchShopProductIdDTO;
import com.medusa.gruul.addon.full.reduction.model.vo.CartShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.model.vo.SearchProductVO;
import com.medusa.gruul.addon.full.reduction.model.vo.ShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;

import java.util.List;
import java.util.Set;

public interface ConsumerFullReductionService {

    /**
     * 商品详情页满减优惠
     *
     * @param productDetailFullReduction 商品详情满减信息
     * @return 满减优惠信息
     */
    List<FullReductionRule> productDetailFullReduction(ProductDetailFullReductionDTO productDetailFullReduction);

    List<ShopFullReductionVO> confirmOrderFullReduction(List<OrderShopProductDTO> orderShopProducts);

    /**
     * 购物车满减信息
     *
     * @param shopProductIds 店铺商品id集合
     * @return 满减信息
     */
    List<CartShopFullReductionVO> fullReductionsCart(List<CartShopProductIdDTO> shopProductIds);

    /**
     * 查询当前满减活动
     *
     * @return 满减id集合
     */
    Boolean getNowFullReduction();


    /**
     * 获取满减活动是否存在
     */
    void getExistsFullReduction();

    /**
     * 检索商品满减信息
     *
     * @param searchShopProductIds 检索商品店铺id、商品id
     * @return 检索商品满减信息
     */
    List<SearchProductVO> fullReductionSearchProduct(List<SearchShopProductIdDTO> searchShopProductIds);

    /**
     * 查询购物车满减信息
     *
     * @param shopIds 店铺id
     * @return 购物车满减信息
     */
    List<ShopFullReductionVO> cartShopFullReduction(Set<Long> shopIds);

    /**
     * 获取缓存中的满减活动
     *
     * @param shopIds 店铺id
     * @param type    类型
     * @return 满减活动
     */
    <T> List<T> getCacheFullReduction(Set<Long> shopIds, Class<T> type);

    /**
     * 订单获取满减活动是否存在
     * @param shopIds 店铺id
     * @return 满减信息
     */
    List<ShopFullReductionVO> getOrderFullReduction(Set<Long> shopIds);

}