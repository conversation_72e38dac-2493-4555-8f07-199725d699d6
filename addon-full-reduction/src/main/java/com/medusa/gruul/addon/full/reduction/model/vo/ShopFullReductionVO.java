package com.medusa.gruul.addon.full.reduction.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.addon.full.reduction.handler.FullReductionRuleHandler;
import com.medusa.gruul.addon.full.reduction.model.enums.ProductType;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import com.medusa.gruul.common.mp.handler.type.LongSetTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * 店铺满减信息
 */
@Getter
@Setter
@Accessors(chain = true)
public class ShopFullReductionVO {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 满减id
     */
    private Long fullReductionId;

    /**
     * 满减金额
     */
    private Long discount;


    /**
     * 结束时间
     */
    private LocalDateTime fullReductionEndTime;

    /**
     * 参加满减活动的商品
     */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private Set<Long> productIds;


    private ProductType productType;

    /**
     * 满减规则
     */
    @TableField(typeHandler = FullReductionRuleHandler.class)
    private List<FullReductionRule> fullReductionRules;

}
