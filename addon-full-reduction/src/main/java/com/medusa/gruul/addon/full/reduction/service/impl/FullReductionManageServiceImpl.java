package com.medusa.gruul.addon.full.reduction.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.full.reduction.constant.FullReductionConstant;
import com.medusa.gruul.addon.full.reduction.model.FullReductionErrorCode;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionQueryDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionShopMapDTO;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus;
import com.medusa.gruul.addon.full.reduction.model.enums.ProductType;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReduction;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionProduct;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionProductService;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionService;
import com.medusa.gruul.addon.full.reduction.service.FullReductionManageService;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.redis.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class FullReductionManageServiceImpl implements FullReductionManageService {


    private final IFullReductionService fullReductionService;
    private final IFullReductionProductService fullReductionProductService;
    private final RedisTemplate<String, Object> redisTemplate = RedisUtil.getRedisTemplate();

    /**
     * 编辑满减活动
     *
     * @param fullReductionActivity 满减活动
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editFullReduction(FullReductionDTO fullReductionActivity) {
        Long shopId = fullReductionActivity.getShopId();
        FullReduction fullReduction = fullReductionActivity.newFullReduction();
        boolean status = fullReductionService.saveOrUpdate(fullReduction);
        if (!status) {
            throw new ServiceException("满减活动保存失败", FullReductionErrorCode.FULL_REDUCTION_EDIT);
        }
        Long fullReductionId = fullReduction.getId();
        // 更新
        if (fullReductionActivity.getIsUpdate()) {
            fullReductionProductService.lambdaUpdate()
                    .eq(FullReductionProduct::getFullReductionId, fullReductionId)
                    .eq(FullReductionProduct::getShopId, shopId)
                    .remove();
        }
        if (fullReductionActivity.getProductType() != ProductType.ALL_PRODUCT) {
            List<FullReductionProduct> fullReductionProducts = fullReductionActivity.newFullReductionProduct(fullReductionId);
            boolean batch = fullReductionProductService.saveBatch(fullReductionProducts);
            if (!batch) {
                throw new ServiceException("满减活动商品保存失败", FullReductionErrorCode.FULL_REDUCTION_PRODUCT_EDIT);
            }
        }
        Collection<String> keys = RedisUtil.keys(RedisUtil.key(FullReductionConstant.SHOP_FULL_REDUCTION_KEY, "*"));
        keys.add(FullReductionConstant.FULL_REDUCTION_PARTICIPATE);
        RedisUtil.doubleDeletion(
                () -> redisTemplate.delete(keys),
                keys
        );
    }


    /**
     * 查询单个满减活动
     *
     * @param shopId          店铺id
     * @param fullReductionId 满减活动id
     * @return 满减活动
     */
    @Override
    public FullReductionVO getFullReductionById(Long shopId, Long fullReductionId) {
        return fullReductionService.getFullReductionById(shopId, fullReductionId);
    }

    /**
     * 分页查询满减活动
     *
     * @param fullReductionQuery 查询参数
     * @return 满减活动分页结果
     */
    @Override
    public IPage<FullReductionVO> fullReductionPage(FullReductionQueryDTO fullReductionQuery) {
        return fullReductionService.fullReductionPage(fullReductionQuery);
    }

    /**
     * 商家端删除满减活动
     *
     * @param shopId          店铺id
     * @param fullReductionId 满减活动id
     */
    @Override
    public void deleteFullReductionById(Long shopId, Long fullReductionId) {
        Set<String> fullReductionKey = getFullReductionKey(CollectionUtil.newHashSet(shopId));
        RedisUtil.doubleDeletion(
                () -> {
                    fullReductionService.lambdaUpdate()
                            .eq(FullReduction::getId, fullReductionId)
                            .eq(FullReduction::getShopId, shopId)
                            .remove();
                    fullReductionProductService.lambdaUpdate()
                            .eq(FullReductionProduct::getFullReductionId, fullReductionId)
                            .remove();
                },
                () -> redisTemplate.delete(fullReductionKey)
        );

    }

    private Set<String> getFullReductionKey(Set<Long> shopIds) {
        Set<String> set = shopIds.stream()
                .map(shopId -> RedisUtil.key(FullReductionConstant.SHOP_FULL_REDUCTION_KEY, shopId))
                .collect(Collectors.toSet());
        set.add(FullReductionConstant.FULL_REDUCTION_PARTICIPATE);
        return set;
    }

    /**
     * 平台批量删除满减活动
     *
     * @param fullReductionShopIds 店铺和满减id
     */
    @Override
    public void deleteFullReductionBatch(List<FullReductionShopMapDTO> fullReductionShopIds) {
        Map<Long, Set<Long>> map = new HashMap<>(fullReductionShopIds.size());
        Set<Long> shopIds = combineShopFullReductionId(fullReductionShopIds, map);
        Set<String> fullReductionKey = getFullReductionKey(shopIds);
        RedisUtil.doubleDeletion(
                () -> map.forEach((shopId, fullReductionIds) -> {
                    fullReductionService.lambdaUpdate()
                            .in(FullReduction::getId, fullReductionIds)
                            .eq(FullReduction::getShopId, shopId)
                            .remove();
                    fullReductionProductService.lambdaUpdate()
                            .in(FullReductionProduct::getFullReductionId, fullReductionIds)
                            .eq(FullReductionProduct::getShopId, shopId)
                            .remove();
                }),
                () -> redisTemplate.delete(fullReductionKey)
        );


    }

    private Set<Long> combineShopFullReductionId(List<FullReductionShopMapDTO> fullReductionShopIds, Map<Long, Set<Long>> map) {
        Set<Long> shopIds = new HashSet<>();
        for (FullReductionShopMapDTO fullReductionShopId : fullReductionShopIds) {
            Long shopId = fullReductionShopId.getShopId();
            Set<Long> set = map.computeIfAbsent(shopId, (fullReductionId) -> new HashSet<>());
            set.add(fullReductionShopId.getFullReductionId());
            shopIds.add(shopId);
        }
        return shopIds;
    }


    /**
     * 平台批量下架满减活动
     *
     * @param fullReductionShopIds 店铺和满减id
     */
    @Override
    public void sellOfFullReduction(List<FullReductionShopMapDTO> fullReductionShopIds) {
        Map<Long, Set<Long>> map = new HashMap<>(fullReductionShopIds.size());
        Set<Long> shopIds = combineShopFullReductionId(fullReductionShopIds, map);
        Set<String> fullReductionKey = getFullReductionKey(shopIds);
        RedisUtil.doubleDeletion(
                () -> map.forEach((shopId, fullReductionIds) ->
                        fullReductionService.lambdaUpdate()
                                .set(FullReduction::getFullReductionStatus, FullReductionStatus.ILLEGAL_SELL_OFF)
                                .in(FullReduction::getId, fullReductionIds)
                                .eq(FullReduction::getShopId, shopId)
                                .update()
                ),
                () -> redisTemplate.delete(fullReductionKey)
        );
    }


}

