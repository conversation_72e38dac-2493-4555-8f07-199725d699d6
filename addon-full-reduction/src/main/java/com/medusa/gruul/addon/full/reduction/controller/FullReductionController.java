package com.medusa.gruul.addon.full.reduction.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionQueryDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionShopMapDTO;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionService;
import com.medusa.gruul.addon.full.reduction.service.FullReductionManageService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.global.model.o.Final;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Size;
import java.util.List;


/**
 * 商家平台端满减活动 前端控制器
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@RestController
@Validated
@RequiredArgsConstructor
@RequestMapping("/fullReduction")
public class FullReductionController {


	private final FullReductionManageService fullReductionManageService;

	private final IFullReductionService fullReductionService;

	/**
	 * 编辑满减活动
	 *
	 * @param fullReductionDTO 满减活动DTO
	 */
	@Log("编辑满减活动")
	@PostMapping
	@PreAuthorize("@S.shopPerm('marketingApp:applyDiscount')")
	public Result<Void> editFullReduction(@RequestBody @Validated FullReductionDTO fullReductionDTO) {
		fullReductionDTO.validParams(ISecurity.userMust().getShopId(), fullReductionService);
		fullReductionManageService.editFullReduction(fullReductionDTO);
		return Result.ok();
	}


	/**
	 * 查询单个满减活动
	 *
	 * @param shopId          店铺id
	 * @param fullReductionId 满减活动id
	 * @return 满减活动
	 */
	@Log("查询单个满减活动")
	@GetMapping("/{shopId}/{fullReductionId}")
	@PreAuthorize("@S.anyPerm('applyDiscount','marketingApp:applyDiscount')")
	public Result<FullReductionVO> getFullReductionById(@PathVariable Long shopId,
	                                                    @PathVariable Long fullReductionId) {
		Final<Long> box = new Final<>(shopId);
		ISecurity.match()
				.ifAnyShopAdmin(secureUser -> box.set(secureUser.getShopId()));
		return Result.ok(
				fullReductionManageService.getFullReductionById(box.get(), fullReductionId)
		);
	}

	/**
	 * 分页查询满减活动
	 *
	 * @param fullReductionQuery 查询参数
	 * @return 满减活动分页结果
	 */
	@Log("分页查询满减活动")
	@GetMapping
	@PreAuthorize("@S.anyPerm('applyDiscount','marketingApp:applyDiscount')")
	public Result<IPage<FullReductionVO>> fullReductionPage(FullReductionQueryDTO fullReductionQuery) {
		ISecurity.match()
				.ifAnyShopAdmin(secureUser -> fullReductionQuery.setShopId(secureUser.getShopId()));
		return Result.ok(
				fullReductionManageService.fullReductionPage(fullReductionQuery)
		);
	}

	/**
	 * 商家端删除满减活动
	 *
	 * @param fullReductionId 满减活动id
	 */
	@Log("商家端删除满减活动")
	@PreAuthorize("@S.shopPerm('marketingApp:applyDiscount')")
	@DeleteMapping("/{fullReductionId}")
	public Result<Void> deleteFullReductionById(@PathVariable Long fullReductionId) {
		fullReductionManageService.deleteFullReductionById(ISecurity.userMust().getShopId(), fullReductionId);
		return Result.ok();
	}

	/**
	 * 平台批量删除满减活动
	 *
	 * @param fullReductionShopIds 店铺和满减id
	 */
	@Log("平台批量删除满减活动")
	@DeleteMapping("/batch")
	@PreAuthorize("@S.platformPerm('applyDiscount')")
	public Result<Void> deleteFullReductionBatch(@RequestBody @Validated @Size(min = 1) List<FullReductionShopMapDTO> fullReductionShopIds) {
		fullReductionManageService.deleteFullReductionBatch(fullReductionShopIds);
		return Result.ok();
	}

	/**
	 * 平台批量下架满减活动
	 *
	 * @param fullReductionShopIds 店铺和满减id
	 */
	@Log("平台批量下架满减活动")
	@PutMapping("/sellOf")
	@PreAuthorize("@S.platformPerm('applyDiscount')")
	public Result<Void> sellOfFullReduction(@RequestBody @Validated @Size(min = 1) List<FullReductionShopMapDTO> fullReductionShopIds) {
		fullReductionManageService.sellOfFullReduction(fullReductionShopIds);
		return Result.ok();
	}


}
