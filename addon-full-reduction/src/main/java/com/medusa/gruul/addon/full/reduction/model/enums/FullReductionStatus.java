package com.medusa.gruul.addon.full.reduction.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


@RequiredArgsConstructor
@Getter
public enum FullReductionStatus {

    /**
     * 未开始
     */
    NOT_STARTED(0, "未开始"),


    /**
     * 进行中
     */
    PROCESSING(1, "进行中"),

    /**
     * 已结束
     */
    OVER(2, "已结束"),

    /**
     * 违规下架
     */
    ILLEGAL_SELL_OFF(3, "违规下架");


    @EnumValue
    private final Integer value;

    private final String name;

}
