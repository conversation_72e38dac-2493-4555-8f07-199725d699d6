package com.medusa.gruul.addon.full.reduction.constant;

public interface FullReductionConstant {


    /**
     * 参加满减key
     */
    String FULL_REDUCTION_PARTICIPATE = "addon:fullReduction:participate";


    String SHOP_FULL_REDUCTION_KEY = "addon:fullReduction:shop:key";

    /**
     * 查询满减活动redis lock
     */
    String FULL_REDUCTION_REDIS_LOCK_KEY = "iFRld0sYw";

    /**
     * 购物车查询满减活动redis lock
     */
    String FULL_REDUCTION_CART_REDIS_LOCK_KEY = "LHry5MtUHZ";

    /**
     * 订单查询key
     */
    String FULL_REDUCTION_ORDER_REDIS_LOCK_KEY = "i7kht346jgi";

    /**
     * 支付单数 sql模板
     */
    String PAY_ORDER_INCREMENT_SQL_TEMPLATE = "pay_order = pay_order - {}";

    /**
     * 应付金额
     */
    String AMOUNT_RECEIVABLE_SQL_TEMPLATE = "amount_receivable = amount_receivable - {}";


    /**
     * 参加人数
     */
    String PEOPLE_NUM_SQL_TEMPLATE = "people_num = people_num - {}";
}
