package com.medusa.gruul.addon.full.reduction.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import com.medusa.gruul.common.mp.handler.type.LongSetTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class CartShopFullReductionVO {

    /**
     *  店铺id
     */
    private Long shopId;

    /**
     * 满减id
     */
    private Long fullReductionId;


    /**
     * 参加满减活动的商品
     */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private List<Long> productIds;


    /**
     * 满减优惠金额
     */
    private Long discountAmount;


    /**
     * 满减规则
     */
    private FullReductionRule fullReductionRules;


}
