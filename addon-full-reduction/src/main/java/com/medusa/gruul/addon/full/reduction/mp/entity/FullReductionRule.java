package com.medusa.gruul.addon.full.reduction.mp.entity;

import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionRuleType;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
public class FullReductionRule {

    /**
     * id
     */
    private Long id;

    /**
     * 0:满减 1:满折
     */
    @NotNull
    private FullReductionRuleType fullReductionRule;

    /**
     * 满减金额/满折金额
     */
    @NotNull
    private Long conditionAmount;

    /**
     * 优惠金额
     */
    private Long discountAmount;

    /**
     * 折扣比 0.1-9.9
     */
    private BigDecimal discountRatio;

    /**
     * 满减规则名称
     */
    private String fullReductionRuleName;

    /**
     * 订单优惠金额
     */
    private Long orderDiscount;

    public String concatFullReductionName() {
        return StrUtil.format("满{}元减{}", AmountCalculateHelper.toYuan(conditionAmount).toPlainString(), AmountCalculateHelper.toYuan(discountAmount).toPlainString());
    }

    public String concatFullDiscountName() {
        return StrUtil.format("满{}元{}折", AmountCalculateHelper.toYuan(conditionAmount).toPlainString(), discountRatio.toPlainString());
    }
}
