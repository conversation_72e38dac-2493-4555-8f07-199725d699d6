package com.medusa.gruul.addon.full.reduction.model.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class FullReductionProductVO {

    /**
     * 满减id
     */
    private Long fullReductionId;


    /**
     * 满减活动名称
     */
    private String fullReductionName;


    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 满减金额
     */
    private Long fullReductionAmount;




}
