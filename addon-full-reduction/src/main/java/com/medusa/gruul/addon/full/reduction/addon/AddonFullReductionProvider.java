package com.medusa.gruul.addon.full.reduction.addon;

import com.medusa.gruul.order.api.addon.fullreduction.FullReductionResponse;
import com.medusa.gruul.order.api.addon.fullreduction.OrderFullReductionParam;

public interface AddonFullReductionProvider {

    /**
     * 获取满减可用的优惠列表
     *
     * @param orderFullReductionParam 订单满减参数
     * @return 满减活动对应的优惠列表
     */
    FullReductionResponse getFullReductionsOrderDiscount(OrderFullReductionParam orderFullReductionParam);

}
