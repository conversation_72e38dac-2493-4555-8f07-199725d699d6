package com.medusa.gruul.addon.full.reduction.model.dto;


import com.alibaba.nacos.common.utils.DateFormatUtils;
import com.medusa.gruul.addon.full.reduction.model.FullReductionErrorCode;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionRuleType;
import com.medusa.gruul.addon.full.reduction.model.enums.FullReductionStatus;
import com.medusa.gruul.addon.full.reduction.model.enums.ProductType;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReduction;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionProduct;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReductionRule;
import com.medusa.gruul.addon.full.reduction.mp.service.IFullReductionService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.sql.Wrapper;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@Accessors(chain = true)
public class FullReductionDTO {


    /**
     * 满减id
     */
    private Long fullReductionId;


    /**
     * 满减活动名称
     */
    @NotBlank(message = "满减活动不能为空")
    private String fullReductionName;


    /**
     * 活动状态 [0未开始 1进行中 2已结束 3违规下架]
     */
    private FullReductionStatus fullReductionStatus;

    /**
     * 开始时间
     */
    @Future
    private LocalDateTime fullReductionStartTime;

    /**
     * 结束时间
     */
    @Future
    private LocalDateTime fullReductionEndTime;

    /**
     * 满减规则
     */
    @NotNull
    @Valid
    @Size(min = 1)
    private List<FullReductionRule> fullReductionRules;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    @NotBlank(message = "店铺名称不能为空")
    private String shopName;

    /**
     * 0:全部商品 1:指定商品 2:指定商品不参与
     */
    @NotNull
    private ProductType productType;

    /**
     * 商品ids
     */
    private Set<Long> productIds;

    /**
     * 活动商品数
     */
    private Integer productNum;
    /**
     * 是否更新
     */
    @NotNull
    private Boolean isUpdate;

    public void validParams(Long shopId, IFullReductionService fullReductionService) {
        if (fullReductionStartTime.isAfter(fullReductionEndTime)) {
            throw new ServiceException("开始时间不能小于结束时间", SystemCode.PARAM_VALID_ERROR_CODE);
        }
        if (productType != ProductType.ALL_PRODUCT && CollectionUtils.isEmpty(productIds)) {
            throw new ServiceException("关联商品不能为空", SystemCode.PARAM_VALID_ERROR_CODE);
        }
        this.setShopId(shopId);
        if (isUpdate) {
            if (fullReductionId == null) {
                throw new ServiceException("id不能为空", SystemCode.PARAM_VALID_ERROR_CODE);
            }
            FullReduction fullReduction = fullReductionService.lambdaQuery()
                    .eq(FullReduction::getId, fullReductionId)
                    .eq(FullReduction::getShopId, this.shopId)
                    .one();
            if (fullReduction == null) {
                throw new ServiceException("满减活动不存在!", FullReductionErrorCode.FULL_REDUCTION_NOT_EXIST);

            }
            if (LocalDateTime.now().isAfter(fullReduction.getFullReductionStartTime()) || fullReduction.getFullReductionStatus() == FullReductionStatus.ILLEGAL_SELL_OFF) {
                throw new ServiceException("满减活动只可在未开始编辑或已下架!", FullReductionErrorCode.FULL_REDUCTION_PROCESSING);

            }
        } else {
            if (fullReductionId != null) {
                throw new ServiceException("新增id为空", SystemCode.PARAM_VALID_ERROR_CODE);
            }
        }

        // 3:00 4:00
        /**
         * 3 - 5
         *  3,30  5
         *  1 - 3,30
         *  3.30 3.40
         *  1 - 5
         *
         */
        boolean exists = fullReductionService.lambdaQuery()
                .ne(isUpdate, FullReduction::getId, fullReductionId)
                .eq(FullReduction::getShopId, shopId)
                .ne(FullReduction::getFullReductionStatus, FullReductionStatus.ILLEGAL_SELL_OFF)
                .not(wrapper -> wrapper.gt(FullReduction::getFullReductionStartTime, fullReductionEndTime)
                        .or()
                        .lt(FullReduction::getFullReductionEndTime, fullReductionStartTime)
                ).exists();
        if (exists) {
            throw new ServiceException("满减活动已存在", FullReductionErrorCode.FULL_REDUCTION_EXIST);
        }


    }

    public FullReduction newFullReduction() {
        for (FullReductionRule fullReductionRule : fullReductionRules) {
            fullReductionRule.setId(MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(fullReductionRule).longValue());
        }
        FullReduction fullReduction = new FullReduction();
        fullReduction.setFullReductionName(fullReductionName)
                .setFullReductionStatus(fullReductionStatus)
                .setShopId(shopId)
                .setShopName(shopName)
                .setFullReductionStartTime(fullReductionStartTime)
                .setFullReductionEndTime(fullReductionEndTime)
                .setFullReductionRules(fullReductionRules)
                .setFullReductionStatus(FullReductionStatus.NOT_STARTED)
                .setProductType(productType)
                .setPeopleNum(CommonPool.NUMBER_ZERO)
                .setPayOrder(CommonPool.NUMBER_ZERO)
                .setAmountReceivable(0L)
                .setProductNum(productNum)
                .setProductIds(productIds)
                .setId(fullReductionId);
        return fullReduction;

    }


    public List<FullReductionProduct> newFullReductionProduct(Long fullReductionId) {
        return productIds.stream().map(productId ->
                new FullReductionProduct()
                        .setFullReductionId(fullReductionId)
                        .setProductId(productId)
                        .setShopId(shopId)
        ).collect(Collectors.toList());


    }
}
