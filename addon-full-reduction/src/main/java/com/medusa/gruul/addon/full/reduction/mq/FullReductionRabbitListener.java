package com.medusa.gruul.addon.full.reduction.mq;

import cn.hutool.core.collection.CollUtil;
import com.medusa.gruul.addon.full.reduction.service.FullReductionPaymentInfoHandleService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.order.api.model.OrderPaidBroadcastDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class FullReductionRabbitListener {


    private final FullReductionPaymentInfoHandleService fullReductionPaymentInfoHandleService;

    /**
     * 添加满减活动支付信息
     */
    @Log("添加满减活动支付信息")
    @RabbitListener(queues = FullReductionRabbitQueueNames.FULL_REDUCTION_PAYMENT_INFO_QUEUE)
    public void fullReductionPaymentInfo(OrderPaidBroadcastDTO orderPaidBroadcast, Channel channel, Message message) throws IOException {
        log.info("添加满减活动支付信息: {}", orderPaidBroadcast);
        if (CollUtil.isNotEmpty(orderPaidBroadcast.getFullReduceMap())) {
            fullReductionPaymentInfoHandleService.addFullReductionPaymentInfo(orderPaidBroadcast);
        }
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    /**
     * 满减活动订单退款
     */
    @Log("满减活动订单退款")
    @RabbitListener(queues = FullReductionRabbitQueueNames.FULL_REDUCTION_PAYMENT_REFUND_QUEUE)
    public void fullReductionPaymentRefund(OrderInfo orderInfo, Channel channel, Message message) throws IOException {
        log.info("满减活动订单退款: {}", orderInfo);
        fullReductionPaymentInfoHandleService.fullReductionPaymentRefund(orderInfo);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }


}
