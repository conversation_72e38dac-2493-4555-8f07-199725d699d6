package com.medusa.gruul.addon.full.reduction;


import com.medusa.gruul.addon.full.reduction.properties.FullReductionConfigurationProperties;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableConfigurationProperties(FullReductionConfigurationProperties.class)
@EnableDubbo(scanBasePackages = "com.medusa.gruul.addon.full.reduction.addon.impl")
public class AddonFullReductionApplication {

	public static void main(String[] args) {
		SpringApplication.run(AddonFullReductionApplication.class, args);
	}


}