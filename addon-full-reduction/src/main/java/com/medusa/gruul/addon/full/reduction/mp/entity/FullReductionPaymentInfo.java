package com.medusa.gruul.addon.full.reduction.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-02-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_full_reduction_payment_info")
public class FullReductionPaymentInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 满减活动id
     */
    private Long activityId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单商品项id
     */
    private Long shopOrderItemId;

    /**
     * 参与人数
     */
    private Integer peopleNum;

    /**
     * 支付订单数
     */
    private Integer payOrder;

    /**
     * 应收金额
     */
    private Long amountReceivable;
}
