package com.medusa.gruul.addon.full.reduction.model.dto;

import lombok.Getter;
import lombok.Setter;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class OrderShopProductDTO {

    /**
     * 店铺id
     */
    @NotNull
    private Long shopId;


    /**
     * 商品金额列表
     */
    @NotNull
    @Size(min = 1)
    private List<ProductAmountDTO> productAmounts;
}
