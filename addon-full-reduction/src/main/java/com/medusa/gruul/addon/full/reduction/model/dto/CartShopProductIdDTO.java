package com.medusa.gruul.addon.full.reduction.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 购物车满减信息
 */
@Getter
@Setter
@Accessors(chain = true)
public class CartShopProductIdDTO {


    /**
     * 店铺id
     */
    private Long shopId;


    /**
     * 商品金额列表
     */
    @NotNull
    @Size(min = 1)
    private List<ProductAmountDTO> productAmounts;

    /**
     * 店铺总额
     */
    private Long shopAmount;

}
