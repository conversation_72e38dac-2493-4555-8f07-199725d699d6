package com.medusa.gruul.addon.full.reduction.service;

import com.medusa.gruul.order.api.model.OrderPaidBroadcastDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;

public interface FullReductionPaymentInfoHandleService {

    /**
     * 新增满减活动支付信息
     *
     * @param orderPaidBroadcast 满减活动支付信息
     */
    void addFullReductionPaymentInfo(OrderPaidBroadcastDTO orderPaidBroadcast);

    /**
     * 满减活动订单退款
     *
     * @param orderInfo 满减活动退款信息
     */
    void fullReductionPaymentRefund(OrderInfo orderInfo);
}
