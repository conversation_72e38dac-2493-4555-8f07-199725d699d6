package com.medusa.gruul.addon.full.reduction.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionQueryDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.ProductDetailFullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionProductVO;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO;
import com.medusa.gruul.addon.full.reduction.model.vo.ShopFullReductionVO;
import com.medusa.gruul.addon.full.reduction.mp.entity.FullReduction;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 
 * 满减活动表 Mapper 接口
 * 
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface FullReductionMapper extends BaseMapper<FullReduction> {

    /**
     * 查询单个满减活动
     * @param shopId          店铺id
     * @param fullReductionId 满减活动id
     * @return 满减活动
     */
    FullReductionVO getFullReductionById(@Param("shopId") Long shopId,
                                         @Param("fullReductionId") Long fullReductionId);
    /**
     * 分页查询满减活动
     * @param fullReductionQuery 查询参数
     * @return 满减活动分页结果
     */
    IPage<FullReductionVO> fullReductionPage(@Param("fullReductionQuery") FullReductionQueryDTO fullReductionQuery);

    /**
     * 商品详情页满减优惠
     * @param productDetailFullReduction 商品详情满减信息
     * @return 满减优惠信息
     */
    FullReductionProductVO productDetailFullReduction(@Param("productDetailFullReduction") ProductDetailFullReductionDTO productDetailFullReduction);
    /**
     * 根据店铺id查询满减信息
     * @param shopIds 店铺id
     * @return 店铺满减信息
     */
    List<ShopFullReductionVO> getFullReductions(@Param("shopIds") Set<Long> shopIds);
}
