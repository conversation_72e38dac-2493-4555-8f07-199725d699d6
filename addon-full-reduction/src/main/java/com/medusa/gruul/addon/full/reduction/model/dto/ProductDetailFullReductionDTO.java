package com.medusa.gruul.addon.full.reduction.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 商品详情满减信息
 */
@Getter
@Setter
@Accessors(chain = true)
public class ProductDetailFullReductionDTO {

    /**
     * 店铺id
     */
    @NotNull(message = "店铺id不能为空")
    private Long shopId;

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空")
    private Long productId;

    /**
     *商品金额
     */
    @Min(1)
    @NotNull(message = "商品金额不能为空")
    private Long amount;
}
