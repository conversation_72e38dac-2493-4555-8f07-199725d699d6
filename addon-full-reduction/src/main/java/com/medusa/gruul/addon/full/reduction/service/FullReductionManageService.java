package com.medusa.gruul.addon.full.reduction.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionQueryDTO;
import com.medusa.gruul.addon.full.reduction.model.dto.FullReductionShopMapDTO;
import com.medusa.gruul.addon.full.reduction.model.vo.FullReductionVO;

import java.util.List;

public interface FullReductionManageService  {
    /**
     * 编辑满减活动
     * @param fullReductionDTO  满减活动DTO
     */
    void editFullReduction(FullReductionDTO fullReductionDTO);
    /**
     * 查询单个满减活动
     * @param shopId          店铺id
     * @param fullReductionId 满减活动id
     * @return 满减活动
     */
    FullReductionVO getFullReductionById(Long shopId, Long fullReductionId);
    /**
     * 分页查询满减活动
     * @param fullReductionQuery 查询参数
     * @return 满减活动分页结果
     */
    IPage<FullReductionVO> fullReductionPage(FullReductionQueryDTO fullReductionQuery);

    /**
     * 商家端删除满减活动
     * @param shopId          店铺id
     * @param fullReductionId 满减活动id
     */
    void deleteFullReductionById(Long shopId, Long fullReductionId);

    /**
     * 平台批量删除满减活动
     * @param fullReductionShopIds 店铺和满减id
     */
    void deleteFullReductionBatch(List<FullReductionShopMapDTO> fullReductionShopIds);

    /**
     * 平台批量下架满减活动
     * @param fullReductionShopIds 店铺和满减id
     */
    void sellOfFullReduction(List<FullReductionShopMapDTO> fullReductionShopIds);
}
