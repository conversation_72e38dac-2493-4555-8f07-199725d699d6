package com.medusa.gruul.addon.coupon.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 优惠券状态
 *
 * <AUTHOR>
 * date 2022/11/3
 */
@Getter
@RequiredArgsConstructor
public enum CouponStatus {

    /**
     * 正常
     */
    OK(1),

    /**
     * 已下架
     */
    BANED(2);

    @EnumValue
    private final Integer value;
}
