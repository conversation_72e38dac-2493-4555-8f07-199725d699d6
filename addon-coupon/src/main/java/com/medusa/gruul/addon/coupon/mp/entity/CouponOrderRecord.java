package com.medusa.gruul.addon.coupon.mp.entity;

import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.handler.type.MapTypeHandler;
import com.medusa.gruul.global.model.o.KeyValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "t_coupon_order_record", autoResultMap = true)
public class CouponOrderRecord {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 买家id
     */
    private Long buyerId;

    /**
     * 店铺id 与优惠券id对应的map
     */
    @TableField(typeHandler = ShopIdCouponIdMapHandler.class)
    private Map<Long, Long> coupons;


    @MappedTypes({Map.class})
    @MappedJdbcTypes({JdbcType.VARCHAR})
    public static class ShopIdCouponIdMapHandler extends MapTypeHandler<Long, Long> {

        @Override
        protected TypeReference<Set<KeyValue<Long, Long>>> keyValueReference() {
            return new TypeReference<>() {
            };
        }
    }
}
