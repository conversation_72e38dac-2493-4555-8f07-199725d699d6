package com.medusa.gruul.addon.coupon.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.coupon.model.CouponConstant;
import com.medusa.gruul.addon.coupon.model.CouponErrorCode;
import com.medusa.gruul.addon.coupon.model.dto.*;
import com.medusa.gruul.addon.coupon.model.enums.CouponStatus;
import com.medusa.gruul.addon.coupon.model.enums.EffectType;
import com.medusa.gruul.addon.coupon.model.enums.ProductType;
import com.medusa.gruul.addon.coupon.mp.entity.Coupon;
import com.medusa.gruul.addon.coupon.mp.entity.CouponProduct;
import com.medusa.gruul.addon.coupon.mp.entity.CouponUser;
import com.medusa.gruul.addon.coupon.mp.service.ICouponProductService;
import com.medusa.gruul.addon.coupon.mp.service.ICouponService;
import com.medusa.gruul.addon.coupon.mp.service.ICouponUserService;
import com.medusa.gruul.addon.coupon.service.AdminCouponService;
import com.medusa.gruul.addon.coupon.service.CouponPlusService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * <AUTHOR>
 * date 2022/11/2
 */
@Service
@RequiredArgsConstructor
public class AdminCouponServiceImpl implements AdminCouponService {

    private final ShopRpcService shopRpcService;
    private final ICouponService couponService;
    private final ICouponUserService couponUserService;
    private final ICouponProductService couponProductService;
    private final CouponPlusService couponPlusService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void newCoupon(Long shopId, CouponDTO coupon) {
        coupon.validParam(Boolean.FALSE);
        Coupon couponEntity = coupon.toCouponEntity(shopId);
        boolean success = couponService.save(couponEntity);
        if (!success) {
            throw new ServiceException(SystemCode.DATA_ADD_FAILED);
        }
        Set<Long> productIds = coupon.getProductIds();
        if (CollUtil.isEmpty(productIds)) {
            return;
        }
        Long couponEntityId = couponEntity.getId();
        couponProductService.saveBatch(
                productIds.stream()
                        .map(productId -> new CouponProduct().setShopId(shopId).setCouponId(couponEntityId).setProductId(productId))
                        .collect(Collectors.toList())
        );
    }

    @Override
    public IPage<Coupon> couponPage(Option<Long> shopIdOpt, CouponQueryDTO queryParam) {
        IPage<Coupon> page = couponService.couponPage(shopIdOpt.getOrNull(), queryParam);
        List<Coupon> coupons = page.getRecords();
        if (CollUtil.isEmpty(coupons)) {
            return page;
        }
        shopIdOpt.onEmpty(
                () -> {
                    Map<Long, String> shopNameMap = shopRpcService.getShopInfoByShopIdList(
                                    coupons.stream().map(Coupon::getShopId).collect(Collectors.toSet()
                                    )
                            ).stream()
                            .collect(Collectors.toMap(ShopInfoVO::getId, ShopInfoVO::getName));
                    coupons.forEach(
                            coupon -> coupon.setShopName(shopNameMap.get(coupon.getShopId()))
                    );
                }
        );
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = CouponConstant.COUPON_EDIT_LOCK_KEY, key = "#shopId+':'+#couponId")
    public void editCoupon(Long shopId, Long couponId, CouponDTO coupon) {
        coupon.validParam(Boolean.FALSE);
        Coupon couponEntity = this.getAndCheckCoupon(shopId, couponId, Boolean.FALSE);
        ProductType currentProductType = coupon.getProductType();
        ProductType preProductType = couponEntity.getProductType();
        couponEntity.setEffectType(coupon.getEffectType());
        couponEntity.setDays(coupon.getDays())
                .setNum(coupon.getNum())
                .setStock(coupon.getNum())
                .setLimit(coupon.getLimit())
                .setShopId(shopId)
                .setType(coupon.getType())
                .setName(StrUtil.trim(coupon.getName()))
                .setRequiredAmount(coupon.getRequiredAmount())
                .setAmount(coupon.getAmount())
                .setDiscount(coupon.getDiscount())
                .setProductType(currentProductType)
                .setStartDate(coupon.getStartDate())
                .setEndDate(coupon.getEndDate());
        //缓存双删
        RedisUtil.doubleDeletion(
                () -> {
                    boolean update = couponService.updateById(couponEntity);
                    if (!update) {
                        throw new ServiceException(SystemCode.DATA_UPDATE_FAILED);
                    }
                },
                CouponConstant.COUPON_CACHE_KEY, shopId, couponId
        );
        //保存 优惠券商品关联表
        this.updateCouponProducts(shopId, couponId, preProductType, currentProductType, coupon.getProductIds());

    }

    private void updateCouponProducts(Long shopId, Long couponId, ProductType preProductType, ProductType currentProductType, Set<Long> currentProductIds) {
        Boolean preIsAssigned = preProductType.getIsAssigned();
        Boolean currentIsAssigned = currentProductType.getIsAssigned();


        // 之前非指定 当前也是非指定 跳过
        if (!preIsAssigned && !currentIsAssigned) {
            return;
        }
        //之前指定 当前非指定 删除之前指定的数据
        if (preIsAssigned && !currentIsAssigned) {
            couponProductService.lambdaUpdate().eq(CouponProduct::getShopId, shopId).eq(CouponProduct::getCouponId, couponId).remove();
            return;
        }
        //剩下的都是当前指定过的数据
        //之前非指定 当前指定 则新增商品指定关系
        if (!preIsAssigned) {
            List<CouponProduct> couponProducts = currentProductIds.stream().map(
                    productId -> new CouponProduct().setCouponId(couponId).setShopId(shopId).setProductId(productId)
            ).collect(Collectors.toList());
            couponProductService.saveBatch(couponProducts);
            return;
        }
        //之前指定 当前指定 查询之前的商品
        Set<Long> preProductIds = couponProductService.lambdaQuery().select(CouponProduct::getProductId).eq(CouponProduct::getShopId, shopId)
                .eq(CouponProduct::getCouponId, couponId)
                .list().stream().map(CouponProduct::getProductId).collect(Collectors.toSet());

        //取之前的商品id列表 与 当前商品id列表 交集
        Collection<Long> interProductIds = CollUtil.intersection(preProductIds, currentProductIds);
        preProductIds.removeAll(interProductIds);
        if (CollUtil.isNotEmpty(preProductIds)) {
            couponProductService.lambdaUpdate().eq(CouponProduct::getShopId, shopId).eq(CouponProduct::getCouponId, couponId).in(CouponProduct::getProductId, preProductIds).remove();
        }
        currentProductIds.removeAll(interProductIds);
        List<CouponProduct> couponProducts = currentProductIds.stream().map(
                productId -> new CouponProduct().setCouponId(couponId).setShopId(shopId).setProductId(productId)
        ).collect(Collectors.toList());
        couponProductService.saveBatch(couponProducts);
    }

    /**
     * 检查并获取优惠券entity
     *
     * @param shopId      店铺id
     * @param couponId    优惠券id
     * @param needWorking 是否需要正在进行中的优惠券
     * @return 优惠券entity
     */
    private Coupon getAndCheckCoupon(Long shopId, Long couponId, boolean needWorking) {
        Coupon couponEntity = couponPlusService.getCoupon(shopId, couponId)
                .getOrElseThrow(() -> new ServiceException(SystemCode.DATA_NOT_EXIST));
        //检查是否处于生效期
        LocalDate now = LocalDate.now();
        Boolean isPeriod = couponEntity.getEffectType().getIsPeriod();

        boolean worked = isPeriod && !now.isBefore(couponEntity.getStartDate()) && !now.isAfter(couponEntity.getEndDate());
        //是否正在进行中
        boolean working = worked || !isPeriod;

        //需要优惠券生效
        if (needWorking) {
            if (working) {
                return couponEntity;
            }
            throw new ServiceException("编辑方式有误", CouponErrorCode.COUPON_NEED_INVALID_EDIT);
        }
        //不需优惠券生效 但当前优惠券已生效
        if (working) {
            throw new ServiceException("编辑方式有误", CouponErrorCode.COUPON_NEED_VALID_EDIT);
        }
        return couponEntity;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = CouponConstant.COUPON_EDIT_LOCK_KEY, key = "#shopId+':'+#couponId")
    public void editValidCoupon(Long shopId, Long couponId, CouponWorkingEditDTO coupon) {
        coupon.validParam(Boolean.FALSE);
        Coupon couponEntity = this.getAndCheckCoupon(shopId, couponId, Boolean.TRUE);

        EffectType effectType = couponEntity.getEffectType();
        //立即生效 生效天数校验
        if (EffectType.IMMEDIATELY == effectType) {
            Integer days = coupon.getDays();
            if (days == null) {
                throw new ServiceException(SystemCode.PARAM_VALID_ERROR);
            }
            couponEntity.setDays(days);
        }
        //固定时间生效 生效结束时间校验
        if (EffectType.PERIOD == effectType) {
            LocalDate endDate = coupon.getEndDate();
            if (endDate.isBefore(couponEntity.getStartDate())) {
                throw new ServiceException(SystemCode.PARAM_VALID_ERROR);
            }
            couponEntity.setEndDate(endDate);
        }
        ProductType preProductType = couponEntity.getProductType();
        ProductType currentProductType = coupon.getProductType();
        couponEntity.setName(StrUtil.trim(coupon.getName()))
                .setProductType(currentProductType);

        RedisUtil.doubleDeletion(
                () -> {
                    boolean update = couponService.updateById(couponEntity);
                    if (!update) {
                        throw new ServiceException(SystemCode.DATA_UPDATE_FAILED);
                    }
                },
                CouponConstant.COUPON_CACHE_KEY, shopId, couponId
        );
        this.updateCouponProducts(shopId, couponId, preProductType, currentProductType, coupon.getProductIds());
    }

    @Override
    public void deleteShopCouponBatch(Long shopId, Set<Long> couponIds) {
        Set<String> couponCacheKeys = couponIds.stream()
                .map(couponId -> RedisUtil.key(CouponConstant.COUPON_CACHE_KEY, shopId, couponId))
                .collect(Collectors.toSet());
        RedisUtil.doubleDeletion(
                () -> {
                    boolean remove = couponService.lambdaUpdate()
                            .eq(Coupon::getShopId, shopId)
                            .in(Coupon::getId, couponIds)
                            .remove();
                    if (!remove) {
                        throw new ServiceException(SystemCode.DATA_DELETE_FAILED);
                    }
                },
                () -> RedisUtil.delete(couponCacheKeys)
        );

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCouponBatch(List<ShopCouponMapDTO> shopCoupons) {
        this.platformCouponEdit(
                shopCoupons,
                (shopId, couponIds) -> {
                    boolean remove = couponService.lambdaUpdate()
                            .eq(Coupon::getShopId, shopId)
                            .in(Coupon::getId, couponIds)
                            .remove();
                    if (!remove) {
                        throw new ServiceException(SystemCode.DATA_DELETE_FAILED);
                    }
                }
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void banCouponBatch(List<ShopCouponMapDTO> shopCoupons) {
        this.platformCouponEdit(
                shopCoupons,
                (shopId, couponIds) -> {
                    boolean update = couponService.lambdaUpdate()
                            .set(Coupon::getStatus, CouponStatus.BANED)
                            .in(Coupon::getId, couponIds)
                            .update();
                    if (!update) {
                        throw new ServiceException(SystemCode.DATA_UPDATE_FAILED);
                    }
                }
        );

    }

    @Override
    public Coupon coupon(Long shopId, Long couponId) {
        return couponPlusService.getCoupon(shopId, couponId)
                .peek(
                        coupon -> {
                            if (!coupon.getProductType().getIsAssigned()) {
                                return;
                            }
                            coupon.setProductIds(couponPlusService.getProductIds(shopId, couponId));
                        }
                ).getOrNull();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void giftsToUser(boolean isPlatform, Long shopId, GiftsToUserDTO giftsToUser) {
        CouponDTO coupon = giftsToUser.getCoupon();
        coupon.validParam(isPlatform);
        Coupon couponEntity = coupon.toCouponEntity(shopId);
        boolean success = couponService.save(couponEntity);
        if (!success) {
            throw new ServiceException(SystemCode.DATA_ADD_FAILED);
        }
        saveCouponUser(giftsToUser, coupon, couponEntity);
    }

    private void saveCouponUser(GiftsToUserDTO giftsToUser, CouponDTO coupon, Coupon couponEntity) {
        List<CouponUser> couponUsers = giftsToUser.getUserIds()
                .stream()
                .flatMap(
                        userId -> LongStream.range(0, coupon.getNum())
                                .boxed()
                                .map(index -> couponEntity.newCouponUser(userId, couponEntity.calcStartAnEndDate(), null))
                ).collect(Collectors.toList());
        boolean success = couponUserService.saveBatch(couponUsers);
        if (!success) {
            throw new ServiceException(SystemCode.DATA_ADD_FAILED);
        }
    }


    private void platformCouponEdit(List<ShopCouponMapDTO> shopCoupons, BiConsumer<Long, Set<Long>> shopIdCouponIdsConsumer) {
        Map<Long, Set<Long>> shopIdCouponIdsMap = new HashMap<>(CommonPool.NUMBER_FIFTEEN);
        Set<String> couponCacheKeys = new HashSet<>(CommonPool.NUMBER_THIRTY);
        shopCoupons.forEach(
                shopCoupon -> {
                    Long shopId = shopCoupon.getShopId();
                    Long couponId = shopCoupon.getCouponId();
                    Set<Long> couponIds = shopIdCouponIdsMap.computeIfAbsent(shopId, (key) -> new HashSet<>());
                    couponIds.add(couponId);
                    couponCacheKeys.add(RedisUtil.key(CouponConstant.COUPON_CACHE_KEY, shopId, couponId));
                }
        );
        RedisUtil.doubleDeletion(
                () -> shopIdCouponIdsMap.forEach(shopIdCouponIdsConsumer),
                () -> RedisUtil.delete(couponCacheKeys)
        );
    }

}
