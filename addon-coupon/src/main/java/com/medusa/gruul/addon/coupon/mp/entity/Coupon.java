package com.medusa.gruul.addon.coupon.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.addon.coupon.model.BaseCouponModel;
import com.medusa.gruul.addon.coupon.model.CouponErrorCode;
import com.medusa.gruul.addon.coupon.model.enums.CouponStatus;
import com.medusa.gruul.addon.coupon.model.enums.ProductType;
import com.medusa.gruul.common.model.exception.ServiceException;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Set;
import java.util.function.BiFunction;

/**
 * <p>
 * 优惠券
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_coupon")
public class Coupon extends BaseCouponModel {

    /**
     * 优惠券状态
     */
    private CouponStatus status;

    /**
     * 立即生效天数
     */
    private Integer days;

    /**
     * 发行数量
     */
    private Long num;

    /**
     * 剩余库存
     */
    private Long stock;

    /**
     * 每人限领
     */
    @TableField("`limit`")
    private Integer limit;


    /**
     * 已使用的数量
     */
    @TableField(exist = false)
    private Long usedCount;

    /**
     * 店铺名称
     */
    @TableField(exist = false)
    private String shopName;

    /**
     * 商品id列表
     */
    @TableField(exist = false)
    private Set<Long> productIds;

    public Tuple2<LocalDate, LocalDate> calcStartAnEndDate() {
        LocalDate now = LocalDate.now();
        LocalDate endDate = getEndDate();
        LocalDate startDate = getStartDate();
        switch (getEffectType()) {
            case IMMEDIATELY:
                return Tuple.of(now, now.plusDays(getDays() - 1));
            case PERIOD:
                if (endDate.isBefore(now)) {
                    throw new ServiceException("优惠券已失效", CouponErrorCode.COUPON_INVALID);
                }
                return Tuple.of(startDate.isAfter(now) ? startDate : now, endDate);
            default:
                throw new RuntimeException("暂不支持此种类型");
        }
    }

    public CouponUser newCouponUser(Long userId, BiFunction<Long, Long, Set<Long>> productIdsFunction) {
        ProductType productType = getProductType();
        Long shopId = getShopId();
        Long couponId = getId();
        CouponUser couponUser = new CouponUser()
                .setUserId(userId)
                .setCouponId(couponId)
                .setUsed(Boolean.FALSE)
                .setProductIds(productType.getIsAssigned() ? productIdsFunction.apply(shopId, couponId) : null);
        couponUser.setShopId(shopId)
                .setName(getName())
                .setType(getType())
                .setEffectType(getEffectType())
                .setRequiredAmount(getRequiredAmount())
                .setAmount(getAmount())
                .setDiscount(getDiscount())
                .setProductType(productType);
        Tuple2<LocalDate, LocalDate> startAnEndDate = calcStartAnEndDate();
        couponUser.setStartDate(startAnEndDate._1())
                .setEndDate(startAnEndDate._2());
        return couponUser;
    }


    public CouponUser newCouponUser(Long userId, Tuple2<LocalDate, LocalDate> startAnEndDate, Set<Long> productIds) {
        ProductType productType = getProductType();
        Long shopId = getShopId();
        Long couponId = getId();
        CouponUser couponUser = new CouponUser()
                .setUserId(userId)
                .setCouponId(couponId)
                .setUsed(Boolean.FALSE)
                .setProductIds(productType.getIsAssigned() ? productIds : null);
        couponUser.setShopId(shopId)
                .setName(getName())
                .setType(getType())
                .setEffectType(getEffectType())
                .setRequiredAmount(getRequiredAmount())
                .setAmount(getAmount())
                .setDiscount(getDiscount())
                .setProductType(productType);
        couponUser.setStartDate(startAnEndDate._1())
                .setEndDate(startAnEndDate._2());
        return couponUser;
    }


}
