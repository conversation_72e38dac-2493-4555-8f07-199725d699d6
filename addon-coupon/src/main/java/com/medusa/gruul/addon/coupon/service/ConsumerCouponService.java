package com.medusa.gruul.addon.coupon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.coupon.model.dto.ConsumerCouponQueryDTO;
import com.medusa.gruul.addon.coupon.model.dto.OrderCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.dto.ProductCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.vo.CouponVO;
import com.medusa.gruul.addon.coupon.mp.entity.CouponUser;
import io.vavr.control.Option;

import java.util.List;

/**
 * <AUTHOR>
 * date 2022/11/3
 */
public interface ConsumerCouponService {

    /**
     * 消费者端分页查询优惠券
     *
     * @param userIdOpt 可能为空的用户id 为空则表示匿名登陆获取可领取的所有优惠券
     * @param query     查询条件 仅当查询状态为待使用时才会查询 适用的商品id列表
     * @return 分页查询结果
     */
    IPage<CouponVO> consumerCouponPage(Option<Long> userIdOpt, ConsumerCouponQueryDTO query);

    /**
     * 领取优惠券
     *
     * @param userId   用户id
     * @param shopId   店铺id
     * @param couponId 优惠券id
     */
    void collectCoupon(Long userId, Long shopId, Long couponId);


    /**
     * 获取用户平台可用优惠券列表
     *
     * @param userId 用户id
     * @return 平台优惠券列表
     */
    List<CouponVO> platformCouponAvailable(Long userId);

    /**
     * 结算选择优惠券
     *
     * @param userId          用户id
     * @param orderCouponPage 分页查询条件  查询平台时 店铺id为0
     * @return 分页结果
     */
    IPage<CouponVO> orderShopCouponPage(Long userId, OrderCouponPageDTO orderCouponPage);

    /**
     * 商品详情优惠券优惠
     *
     * @param query 查询参数
     * @return 分页查询结果
     */
    IPage<CouponVO> productShopCouponPage(ProductCouponPageDTO query);

    /**
     * 根据优惠券id获取优惠券
     * @param couponId
     * @param userId
     * @return
     */
    List<CouponUser> getCouponById(Long couponId, Long userId);

    /**
     * 结算选择优惠券（医护）
     *
     * @param userId          用户id
     * @param orderCouponPage 分页查询条件  查询平台时 店铺id为0
     * @return 分页结果
     */
    IPage<CouponVO> orderShopCouponYihuPage(Long userId, OrderCouponPageDTO orderCouponPage);
}
