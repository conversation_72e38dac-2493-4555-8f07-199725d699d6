package com.medusa.gruul.addon.coupon.addon;

import com.medusa.gruul.order.api.addon.coupon.CouponResponse;
import com.medusa.gruul.order.api.addon.coupon.OrderCouponParam;

/**
 * <AUTHOR>
 * date 2022/11/4
 */
public interface AddonCouponProvider {

    /**
     * 使用优惠券
     *
     * @param orderCoupon 订单优惠券参数
     * @return 计算优惠后的结果哦
     */
    CouponResponse useCoupon(OrderCouponParam orderCoupon);

    /**
     * 使用优惠券（医护）
     *
     * @param orderCoupon 订单优惠券参数
     * @return 计算优惠后的结果哦
     */
    CouponResponse useCouponYihu(OrderCouponParam orderCoupon);
}
