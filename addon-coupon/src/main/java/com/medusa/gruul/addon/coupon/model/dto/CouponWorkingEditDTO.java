package com.medusa.gruul.addon.coupon.model.dto;

import cn.hutool.core.collection.CollUtil;
import com.medusa.gruul.addon.coupon.model.enums.ProductType;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2022/11/2
 */
@Getter
@Setter
@ToString
public class CouponWorkingEditDTO {

    /**
     * 优惠券名称
     */
    @NotBlank
    @Size(max = 5)
    private String name;

    /**
     * 领券立即生效 持续时间
     */
    @Min(1)
    private Integer days;

    /**
     * 固定日期段 结束日期
     */
    private LocalDate endDate;

    /**
     * 作用的商品类型 平台全部商品 店铺全部商品 点不指定商品生效 店铺指定商品不生效
     */
    @NotNull
    private ProductType productType;

    /**
     * 指定商品 生效/不生效的 id 列表
     */
    @Size(min = 1)
    private Set<Long> productIds;


    /**
     * 检查优惠券类型及其 额外参数
     */
    public void validParam(boolean isPlatform) {
        Boolean isPlatformType = getProductType().getIsPlatform();
        if (isPlatform && isPlatformType) {
            return;
        }
        if (isPlatformType) {
            throw new ServiceException(SystemCode.PARAM_VALID_ERROR);
        }
        
        ProductType productType = this.getProductType();
        if (!productType.getIsAssigned()) {
            this.setProductIds(null);
            return;
        }
        if (CollUtil.isEmpty(this.getProductIds())) {
            throw new ServiceException(SystemCode.PARAM_VALID_ERROR);
        }

    }
}
