package com.medusa.gruul.addon.coupon.model;

import com.medusa.gruul.addon.coupon.model.enums.CouponType;
import com.medusa.gruul.addon.coupon.model.enums.EffectType;
import com.medusa.gruul.addon.coupon.model.enums.ProductType;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * date 2022/11/2
 */
@Getter
@Setter
@Accessors(chain = true)
public class BaseCouponModel extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺id 平台优惠券为0
     */
    private Long shopId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型
     */
    private CouponType type;

    /**
     * 优惠券生效类型
     */
    private EffectType effectType;
    
    /**
     * 满减/满折 需要满足的金额
     */
    private Long requiredAmount;

    /**
     * 优惠金额
     */
    private Long amount;

    /**
     * 折扣比 1
     */
    private BigDecimal discount;

    /**
     * 作用的商品类型
     */
    private ProductType productType;

    /**
     * 固定时间生效开始时间
     */
    private LocalDate startDate;

    /**
     * 固定时间生效结束时间
     */
    private LocalDate endDate;
}
