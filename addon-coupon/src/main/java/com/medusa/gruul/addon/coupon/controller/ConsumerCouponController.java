package com.medusa.gruul.addon.coupon.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.coupon.model.dto.ConsumerCouponQueryDTO;
import com.medusa.gruul.addon.coupon.model.dto.OrderCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.dto.ProductCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.vo.CouponVO;
import com.medusa.gruul.addon.coupon.mp.entity.CouponUser;
import com.medusa.gruul.addon.coupon.service.ConsumerCouponService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 消费者优惠券控制器
 *
 * <AUTHOR>
 * date 2022/11/3
 */
@RestController
@Valid
@RequestMapping("/coupon/consumer")
@PreAuthorize("@S.user")
@RequiredArgsConstructor
public class ConsumerCouponController {

	private final ConsumerCouponService consumerCouponService;

	/**
	 * 领券中心 优惠券分页查询
	 *
	 * @param query 查询条件
	 * @return 分页查询结果
	 */
	@GetMapping
	@PreAuthorize("permitAll()")
	@Log("领券中心/我的优惠券/ 可以领取的店铺优惠券查询 分页查询")
	public Result<IPage<CouponVO>> consumerCouponPage(@Valid ConsumerCouponQueryDTO query) {
		return Result.ok(
				consumerCouponService.consumerCouponPage(ISecurity.userOpt().map(SecureUser::getId), query)
		);
	}
	@GetMapping("/coupon/{couponId}")
	@PreAuthorize("permitAll()")
	public Result<List<CouponUser>> getCouponById(@PathVariable @NotNull Long couponId){
		SecureUser secureUser = ISecurity.userMust();
		return Result.ok(
				consumerCouponService.getCouponById(couponId,secureUser.getId())
		);
	}



	/**
	 * 结算页分页查询优惠券
	 *
	 * @param orderCouponPage 订单优惠券分页参数
	 * @return 分页查询结果
	 */
	@PostMapping("/order")
	@Log("结算页选择优惠券")
	public Result<IPage<CouponVO>> orderShopCouponPage(@RequestBody @Valid OrderCouponPageDTO orderCouponPage) {
		return Result.ok(
				consumerCouponService.orderShopCouponPage(ISecurity.userMust().getId(), orderCouponPage)
		);
	}

	/**
	 * 分页查询指定商品详情优惠券
	 *
	 * @param productCouponPage 分页查询参数
	 * @return 分页查询结果
	 */
	@PostMapping("/product")
	@Log("商品详情优惠券优惠")
	@PreAuthorize("permitAll()")
	public Result<IPage<CouponVO>> productShopCouponPage(@Valid @RequestBody ProductCouponPageDTO productCouponPage) {
		return Result.ok(
				consumerCouponService.productShopCouponPage(productCouponPage)
		);
	}

	/**
	 * 领取优惠券
	 *
	 * @param shopId   店铺id
	 * @param couponId 优惠券id
	 * @return 领取结果
	 */
	@Log("领取优惠券")
	@PostMapping("/collect/shop/{shopId}/{couponId}")
	public Result<Void> collectCoupon(@PathVariable Long shopId, @PathVariable Long couponId) {
		consumerCouponService.collectCoupon(ISecurity.userMust().getId(), shopId, couponId);
		return Result.ok();
	}

	/**
	 * 结算页分页查询优惠券（医护）
	 *
	 * @param orderCouponPage 订单优惠券分页参数
	 * @return 分页查询结果
	 */
	@PostMapping("/orderYihu")
	@Log("结算页选择优惠券")
	public Result<IPage<CouponVO>> orderShopCouponYihuPage(@RequestBody @Valid OrderCouponPageDTO orderCouponPage) {
		return Result.ok(
				consumerCouponService.orderShopCouponYihuPage(ISecurity.userMust().getId(), orderCouponPage)
		);
	}
}
