package com.medusa.gruul.addon.coupon.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.addon.coupon.model.BaseCouponModel;
import com.medusa.gruul.common.mp.handler.type.LongSetTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * 用户优惠券关联表 领取时的优惠券快照
 *
 * <AUTHOR>
 * @since 2022-11-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "t_coupon_user", autoResultMap = true)
public class CouponUser extends BaseCouponModel {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 优惠券id 只用于优惠券统计
     */
    private Long couponId;

    /**
     * 是否已使用
     */
    private Boolean used;

    /**
     * 可以使用的商品id 列表
     */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private Set<Long> productIds;
}
