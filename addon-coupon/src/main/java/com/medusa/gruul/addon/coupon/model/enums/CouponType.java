package com.medusa.gruul.addon.coupon.model.enums;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;

/**
 * 优惠券类型
 *
 * <AUTHOR>
 * date 2022/11/2
 */
@Getter
@RequiredArgsConstructor
public enum CouponType {

    /**
     * 无门槛现金券
     */
    PRICE_REDUCE(1, Boolean.FALSE, Boolean.FALSE),

    /**
     * 无门槛折扣券
     */
    PRICE_DISCOUNT(2, Boolean.FALSE, Boolean.TRUE),
    /**
     * 满减券
     */
    REQUIRED_PRICE_REDUCE(3, Boolean.TRUE, Boolean.FALSE),

    /**
     * 满折券
     */
    REQUIRED_PRICE_DISCOUNT(4, Boolean.TRUE, Boolean.TRUE),

    /**
     * 医护优惠 无门槛现金券
     */
    YIHU_PRICE_REDUCE(5, Boolean.FALSE, Boolean.FALSE),

    /**
     * 医护优惠 无门槛折扣券
     */
    YIHU_PRICE_DISCOUNT(6, Boolean.FALSE, Boolean.TRUE);

    @EnumValue
    private final Integer value;

    /**
     * 是否有金额限制 （满足 多少 金额 优惠券才生效）
     */
    private final Boolean requiredAmount;

    /**
     * 是否是折扣优惠 true 是  false 现金优惠
     */
    private final Boolean hasDiscount;

    /**
     * 获取优惠的价格
     *
     * @param totalAmount 目前的商品总价
     * @param amount      优惠金额
     * @param discount    折扣
     * @return 优惠价
     */
    public Long getDiscountAmount(Long totalAmount, Long amount, BigDecimal discount) {
        if (getHasDiscount()) {
            return AmountCalculateHelper.getDiscountAmountByDiscount(totalAmount, discount);
        }
        return AmountCalculateHelper.getDiscountAmountByAmount(totalAmount, amount);
    }

    private final static String REQUIRED_AMOUNT_TEMPLATE = "满{}元";
    private final static String NOT_REQUIRED_TEMPLATE = "无门槛";
    private final static String DISCOUNT_TEMPLATE = "{}折";
    private final static String NOT_DISCOUNT_TEMPLATE = "减{}元";

    public String getDesc(Long requiredAmount, Long amount, BigDecimal discount) {
        return (getRequiredAmount() ? StrUtil.format(REQUIRED_AMOUNT_TEMPLATE, requiredAmount) : NOT_REQUIRED_TEMPLATE) +
                (getHasDiscount() ? StrUtil.format(DISCOUNT_TEMPLATE, discount.toPlainString()) : StrUtil.format(NOT_DISCOUNT_TEMPLATE, amount));
    }


}
