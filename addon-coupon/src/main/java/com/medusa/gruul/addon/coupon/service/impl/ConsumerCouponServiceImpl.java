package com.medusa.gruul.addon.coupon.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.addon.coupon.model.CouponConstant;
import com.medusa.gruul.addon.coupon.model.CouponErrorCode;
import com.medusa.gruul.addon.coupon.model.dto.ConsumerCouponQueryDTO;
import com.medusa.gruul.addon.coupon.model.dto.OrderCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.dto.ProductAmountDTO;
import com.medusa.gruul.addon.coupon.model.dto.ProductCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.enums.CouponStatus;
import com.medusa.gruul.addon.coupon.model.enums.CouponType;
import com.medusa.gruul.addon.coupon.model.enums.EffectType;
import com.medusa.gruul.addon.coupon.model.enums.ProductType;
import com.medusa.gruul.addon.coupon.model.vo.CouponVO;
import com.medusa.gruul.addon.coupon.mp.entity.Coupon;
import com.medusa.gruul.addon.coupon.mp.entity.CouponCalculate;
import com.medusa.gruul.addon.coupon.mp.entity.CouponUser;
import com.medusa.gruul.addon.coupon.mp.service.ICouponCalculateService;
import com.medusa.gruul.addon.coupon.mp.service.ICouponService;
import com.medusa.gruul.addon.coupon.mp.service.ICouponUserService;
import com.medusa.gruul.addon.coupon.properties.YihuProperties;
import com.medusa.gruul.addon.coupon.service.ConsumerCouponService;
import com.medusa.gruul.addon.coupon.service.CouponPlusService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/11/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConsumerCouponServiceImpl implements ConsumerCouponService {

    private final ShopRpcService shopRpcService;
    private final ICouponService couponService;
    private final ICouponUserService couponUserService;
    private final CouponPlusService couponPlusService;
    private final ICouponCalculateService couponCalculateService;
    private final YihuProperties yihuProperties;
    private final String DISCOUNT = "/api/innovate/drug/mall/getDiscountV2";


    @Override
    public IPage<CouponVO> consumerCouponPage(Option<Long> userIdOpt, ConsumerCouponQueryDTO query) {
        query.validParam();
        IPage<CouponVO> page = couponService.consumerCouponPage(userIdOpt.getOrNull(), query);
        List<CouponVO> coupons = page.getRecords();
        //当查询平台券 或 指定了店铺id 都不用去查询店铺名称
        if (query.getIsPlatform() || query.getShopId() != null || CollUtil.isEmpty(coupons)) {
            return page;
        }
        Map<Long, String> shopNameMap = shopRpcService.getShopInfoByShopIdList(
                        coupons.stream().map(CouponVO::getShopId).collect(Collectors.toSet())
                ).stream()
                .collect(Collectors.toMap(ShopInfoVO::getId, ShopInfoVO::getName));
        coupons.forEach(coupon -> coupon.setShopName(shopNameMap.get(coupon.getShopId())));
        return page;
    }

    @Override
    @Redisson(value = CouponConstant.COUPON_USER_COLLECT_LOCK, key = "#userId+':'+#shopId+':'+#couponId")
    public void collectCoupon(Long userId, Long shopId, Long couponId) {
        //查询优惠券详情
        Coupon coupon = couponPlusService.getCoupon(shopId, couponId).getOrElseThrow(() -> new ServiceException(SystemCode.DATA_NOT_EXIST));
        //校验是否不可用
        if (coupon.getStatus() == CouponStatus.BANED) {
            throw new ServiceException("当前优惠券不可用", CouponErrorCode.COUPON_INVALID);
        }
        //检查是否库存不足
        Long stock = coupon.getStock();
        Integer num = CommonPool.NUMBER_ONE;
        if (stock < num) {
            throw new ServiceException("优惠券库存不足", CouponErrorCode.COUPON_OUT_STOCK);
        }
        //校验已领取的优惠券数量是否超额
        Long count = couponUserService.lambdaQuery()
                .eq(CouponUser::getUserId, userId)
                .eq(CouponUser::getShopId, shopId)
                .eq(CouponUser::getCouponId, couponId)
                .count();
        if (count >= coupon.getLimit()) {
            throw new ServiceException("超过了每人限领数量", CouponErrorCode.COUPON_OUT_LIMIT);
        }
        // redis 减库存 -> 异步db减库存
        coupon = couponPlusService.couponStockReduce(shopId, couponId, num);
        boolean save = couponUserService.save(coupon.newCouponUser(userId, couponPlusService::getProductIds));
        if (!save) {
            throw new ServiceException(SystemCode.DATA_ADD_FAILED);
        }
    }

    @Override
    public List<CouponVO> platformCouponAvailable(Long userId) {
        //满折
        //满减
        //无门槛 折扣/优惠
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IPage<CouponVO> orderShopCouponPage(Long userId, OrderCouponPageDTO orderCouponPage) {
        Long shopId = orderCouponPage.getShopId();
        return couponCalculateService.todo(
                orderCouponPage.getProductAmounts()
                        .stream()
                        .map(productAmount -> new CouponCalculate().setShopId(shopId).setProductId(productAmount.getProductId()).setAmount(productAmount.getAmount()))
                        .collect(Collectors.toList()),
                bid -> couponService.orderShopCouponPage(bid, userId, orderCouponPage)
        );

    }

    @Override
    public IPage<CouponVO> productShopCouponPage(ProductCouponPageDTO query) {
        return couponService.productShopCouponPage(query, ISecurity.userOpt().map(SecureUser::getId).getOrNull());
    }

    @Override
    public List<CouponUser> getCouponById(Long couponId, Long userId) {
        return couponUserService.lambdaQuery().eq(CouponUser::getCouponId, couponId).eq(CouponUser::getUserId,userId).list();
    }

    @Override
    public IPage<CouponVO> orderShopCouponYihuPage(Long userId, OrderCouponPageDTO orderCouponPage) {
        Long shopId = orderCouponPage.getShopId();
        // 获取医护的优惠
        return couponCalculateService.todo(
                orderCouponPage.getProductAmounts()
                        .stream()
                        .map(productAmount -> new CouponCalculate().setShopId(shopId).setProductId(productAmount.getProductId()).setAmount(productAmount.getAmount()))
                        .collect(Collectors.toList()),
                bid -> getDiscount(userId,orderCouponPage)
        );
    }

    private String convertRequestUrl(String baseUrl, String apiUrl) {
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        if (!apiUrl.startsWith("/")) {
            baseUrl += "/";
        }
        return baseUrl + apiUrl;
    }

    private IPage<CouponVO> getDiscount(Long userId, OrderCouponPageDTO orderCouponPage) {
        List<ProductAmountDTO> productAmounts = orderCouponPage.getProductAmounts();
        Long shopId = orderCouponPage.getShopId();
        String url = convertRequestUrl(yihuProperties.getDomain(), DISCOUNT);
        // 总金额
        long totalAmt = productAmounts.stream().map(ProductAmountDTO::getAmount).reduce(0L, Long::sum);

        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("policyNo", ISecurity.userMust().getIdCode());
        params.put("totalAmt", totalAmt);

        IPage<CouponVO> page = new Page<>();
        try {
            log.debug("调用医护优惠券获取接口，请求参数：{}", params);
            String str = HttpUtil.post(url, JSONUtil.toJsonStr(params));
            Result<Object> result = JSONUtil.toBean(str, Result.class);
            if (result.getCode() != 0) {
                throw new ServiceException(result.getMsg());
            }
            if (result.getData() == null || "null".equals(result.getData())) {
                return page;
            }
            JSONObject data = JSONUtil.parseObj(result.getData());
            if (data != null && data.get("discountType") != null) {
                Long discountAmount = data.getLong("discountAmount");
                if (discountAmount == null || discountAmount == 0L) {
                    return page;
                }

                CouponVO couponVO = new CouponVO().setCouponUserId(userId);
                couponVO.setAmount(data.getLong("couponAmt"));
                couponVO.setDiscountAmount(discountAmount);
                // 折扣 1即1折
                couponVO.setDiscount(StrUtil.isEmpty(data.getStr("discount")) ? null : new BigDecimal(data.getStr("discount")));
                // 优惠券名称
                couponVO.setName(StrUtil.isEmpty(data.getStr("couponName")) ? "现金券" : data.getStr("couponName"));
                couponVO.setEffectType(EffectType.IMMEDIATELY);
                couponVO.setShopId(shopId);
                couponVO.setProductType(ProductType.SHOP_ALL);

                String discountType = data.getStr("discountType");
                if ("COUPON".equals(discountType)) {
                    couponVO.setType(CouponType.YIHU_PRICE_REDUCE);
                } else if ("DISCOUNT".equals(discountType)) {
                    couponVO.setType(CouponType.YIHU_PRICE_DISCOUNT);
                }
                page.setRecords(Collections.singletonList(couponVO));
                page.setTotal(1);
            }
            log.debug("调用医护优惠券获取接口成功，返回结果：{}", str);
        } catch (Exception e) {
            log.error("调用医护优惠券获取接口失败，e: {}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
        return page;
    }
}
