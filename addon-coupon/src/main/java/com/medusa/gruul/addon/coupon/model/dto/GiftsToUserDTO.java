package com.medusa.gruul.addon.coupon.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2022/11/5
 */
@Getter
@Setter
@ToString
public class GiftsToUserDTO {

    /**
     * 优惠券详情
     */
    @NotNull
    @Valid
    private CouponDTO coupon;

    /**
     * 用户id列表
     */
    @NotNull
    @Size(min = 1)
    private Set<Long> userIds;
}
