package com.medusa.gruul.addon.coupon.mq;

import com.medusa.gruul.addon.coupon.service.ConsumerCouponService;
import com.medusa.gruul.addon.coupon.service.CouponOnOrderCloseService;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.service.uaa.api.dto.GiftUserCouponDTO;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * date 2022/11/11
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CouponRabbitListener {

    private final CouponOnOrderCloseService couponOnOrderCloseService;
    private final ConsumerCouponService consumerCouponService;


    @RabbitListener(queues = CouponRabbitQueueName.COUPON_ORDER_CLOSED)
    public void orderCreateFailQueue(OrderInfo orderInfo, Channel channel, Message message) throws IOException {
        couponOnOrderCloseService.orderClose(orderInfo);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    /**
     * 新人券
     * @param giftCouponDTO
     * @param channel
     * @param message
     * @throws IOException
     */
    @RabbitListener(queues = CouponRabbitQueueName.COUPON_NEW_USER)
    public void orderCreateFailQueue(GiftUserCouponDTO giftCouponDTO, Channel channel, Message message) throws IOException {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        try {
            consumerCouponService.collectCoupon(giftCouponDTO.getUserId(), giftCouponDTO.getShopId(), giftCouponDTO.getCouponId());
        } catch (Exception e) {
            log.error("赠送新人券失败, userId:{}, shopId:{}, couponId:{}", giftCouponDTO.getUserId(), giftCouponDTO.getShopId(), giftCouponDTO.getCouponId(), e);
            throw new RuntimeException(e);
        }
    }

}
