package com.medusa.gruul.addon.coupon;

import com.medusa.gruul.addon.coupon.properties.CouponConfigurationProperties;
import com.medusa.gruul.addon.coupon.properties.YihuProperties;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR>
 * date 2022/11/2
 */
@SpringBootApplication
@EnableConfigurationProperties({CouponConfigurationProperties.class, YihuProperties.class})
@EnableDubbo(scanBasePackages = "com.medusa.gruul.addon.coupon.addon.impl")
public class AddonCouponApplication {
    public static void main(String[] args) {
        SpringApplication.run(AddonCouponApplication.class, args);
    }
}
