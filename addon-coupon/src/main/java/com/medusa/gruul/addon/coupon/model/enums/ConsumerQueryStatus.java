package com.medusa.gruul.addon.coupon.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * date 2022/11/2
 */
@Getter
@RequiredArgsConstructor
public enum ConsumerQueryStatus {
    
    /**
     * 待使用
     */
    UNUSED(false),

    /**
     * 待领取
     */
    UNCLAIMED(true),

    /**
     * 已使用
     */
    USED(false),

    /**
     * 已过期
     */
    EXPIRED(false);

    /**
     * 是否必须只能店铺查询
     */
    private final boolean shopMust;

}
