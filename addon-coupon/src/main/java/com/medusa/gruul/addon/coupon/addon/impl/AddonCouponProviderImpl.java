package com.medusa.gruul.addon.coupon.addon.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.addon.coupon.addon.AddonCouponProvider;
import com.medusa.gruul.addon.coupon.model.CouponErrorCode;
import com.medusa.gruul.addon.coupon.model.enums.CouponType;
import com.medusa.gruul.addon.coupon.model.enums.EffectType;
import com.medusa.gruul.addon.coupon.model.enums.ProductType;
import com.medusa.gruul.addon.coupon.mp.entity.CouponOrderRecord;
import com.medusa.gruul.addon.coupon.mp.entity.CouponUser;
import com.medusa.gruul.addon.coupon.mp.service.ICouponOrderRecordService;
import com.medusa.gruul.addon.coupon.properties.YihuProperties;
import com.medusa.gruul.addon.coupon.service.CouponPlusService;
import com.medusa.gruul.common.addon.provider.AddonProvider;
import com.medusa.gruul.common.addon.provider.AddonProviders;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.base.ShopProductSkuKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.global.model.constant.SecurityConst;
import com.medusa.gruul.global.model.constant.Services;
import com.medusa.gruul.order.api.addon.OrderAddonConstant;
import com.medusa.gruul.order.api.addon.coupon.CouponResponse;
import com.medusa.gruul.order.api.addon.coupon.OrderCouponParam;
import com.medusa.gruul.order.api.entity.OrderDiscount;
import com.medusa.gruul.order.api.enums.DiscountSourceStatus;
import com.medusa.gruul.order.api.enums.DiscountSourceType;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * date 2022/11/4
 */
@Slf4j
@Service
@DubboService
@AddonProviders
@RequiredArgsConstructor
public class AddonCouponProviderImpl implements AddonCouponProvider {


    private final CouponPlusService couponPlusService;
    private final ICouponOrderRecordService couponOrderRecordService;
    private final YihuProperties yihuProperties;
    private final String DISCOUNT = "/api/innovate/drug/mall/getDiscountV2";

    @Override
    @Log("订单使用优惠券")
    @AddonProvider(service = Services.GRUUL_MALL_ORDER, supporterId = OrderAddonConstant.ORDER_DISCOUNT_SUPPORT_ID, methodName = "coupon")
    public CouponResponse useCoupon(OrderCouponParam orderCoupon) {

        Long buyerId = orderCoupon.getBuyerId();
        String orderNo = orderCoupon.getOrderNo();
        CouponResponse couponResponse = new CouponResponse();
        couponResponse.setBuyerId(buyerId)
                .setOrderNo(orderNo);
        Map<Long, Long> shopCoupons = orderCoupon.getShopCouponMap();
        if (CollUtil.isEmpty(shopCoupons)) {
            return couponResponse.setOrderDiscounts(Collections.emptyMap());
        }
        return couponPlusService.lockUserCouponsBatch(
                buyerId,
                shopCoupons,
                () -> {
                    Map<Long, Map<Long, Long>> shopProductAmountMap = orderCoupon.getShopProductAmountMap();
                    Map<Long, AtomicLong> allStatisticsMap = this.toShopStatisticsMap(shopProductAmountMap);
                    //添加平台的数据
                    couponResponse.setOrderDiscounts(
                            shopCoupons.entrySet()
                                    .stream()
                                    .collect(Collectors.toMap(
                                                    Map.Entry::getKey,
                                                    entry -> {
                                                        Long shopId = entry.getKey();
                                                        return couponPlusService.getCouponUserForUse(buyerId, shopId, entry.getValue())
                                                                .map(
                                                                        couponUser -> this.validCouponUser(couponUser, shopProductAmountMap.get(shopId), allStatisticsMap)
                                                                )
                                                                .getOrElseThrow(() -> new ServiceException("优惠券不存在", CouponErrorCode.COUPON_NOT_EXISTS, new ShopProductSkuKey().setShopId(shopId)));
                                                    }
                                            )
                                    )
                    );
                    if (!couponOrderRecordService.save(
                            new CouponOrderRecord()
                                    .setOrderNo(orderNo)
                                    .setBuyerId(buyerId)
                                    .setCoupons(shopCoupons)
                    )) {
                        throw new ServiceException(SystemCode.DATA_ADD_FAILED);
                    }
                    return couponResponse;
                }
        );
    }

    @Override
    @Log("订单使用优惠券（医护）")
    @AddonProvider(service = Services.GRUUL_MALL_ORDER, supporterId = OrderAddonConstant.ORDER_DISCOUNT_SUPPORT_ID, methodName = "couponYihu")
    public CouponResponse useCouponYihu(OrderCouponParam orderCoupon) {
        Long buyerId = orderCoupon.getBuyerId();
        String orderNo = orderCoupon.getOrderNo();
        CouponResponse couponResponse = new CouponResponse();
        couponResponse.setBuyerId(buyerId)
                .setOrderNo(orderNo);
        Map<Long, Long> shopCoupons = orderCoupon.getShopCouponMap();
        if (CollUtil.isEmpty(shopCoupons)) {
            return couponResponse.setOrderDiscounts(Collections.emptyMap());
        }

        Map<Long, Map<Long, Long>> shopProductAmountMap = orderCoupon.getShopProductAmountMap();
        Map<Long, AtomicLong> allStatisticsMap = this.toShopStatisticsMap(shopProductAmountMap);
        //添加平台的数据
        couponResponse.setOrderDiscounts(
                shopCoupons.entrySet()
                        .stream()
                        .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        entry -> {
                                            Long shopId = entry.getKey();
                                            return this.getDiscount(buyerId, orderCoupon.getBuyerIdCode(), shopId, allStatisticsMap)
                                                    .map(
                                                            couponUser ->  this.validCouponUserYihu(couponUser, shopProductAmountMap.get(shopId), allStatisticsMap)
                                                    )
                                                    .getOrElseThrow(() -> new ServiceException("优惠券不存在", CouponErrorCode.COUPON_NOT_EXISTS, new ShopProductSkuKey().setShopId(shopId)));
                                        }
                                )
                        )
        );
        if (!couponOrderRecordService.save(
                new CouponOrderRecord()
                        .setOrderNo(orderNo)
                        .setBuyerId(buyerId)
                        .setCoupons(shopCoupons)
        )) {
            throw new ServiceException(SystemCode.DATA_ADD_FAILED);
        }
        return couponResponse;
    }

    private OrderDiscount validCouponUser(CouponUser couponUser, Map<Long, Long> productAmountMap, Map<Long, AtomicLong> allStatisticsMap) {
        Long shopId = couponUser.getShopId();
        LocalDate now = LocalDate.now();
        //检查是否已使用
        if (couponUser.getUsed()) {
            throw new ServiceException("优惠券已被使用", CouponErrorCode.COUPON_USED, new ShopProductSkuKey().setShopId(shopId));
        }
        //检查是否在有效期内
        if (now.isBefore(couponUser.getStartDate())) {
            throw new ServiceException("改优惠券暂不能使用", CouponErrorCode.COUPON_NOT_WORKING, new ShopProductSkuKey().setShopId(shopId));
        }
        if (now.isAfter(couponUser.getEndDate())) {
            throw new ServiceException("该优惠券已失效", CouponErrorCode.COUPON_INVALID, new ShopProductSkuKey().setShopId(shopId));
        }

        // 检查优惠券 商品类型
        ProductType productType = couponUser.getProductType();
        AtomicLong shopAmount = allStatisticsMap.get(shopId);
        Set<Long> targetProductIds = null;
        //指定商品生效
        if (productType.getIsAssigned()) {
            Tuple2<Set<Long>, AtomicLong> workingProductIdsAndTotalAmount = this.getWorkingProductIdsAndTotalAmount(couponUser.getProductType(), couponUser.getProductIds(), productAmountMap);
            targetProductIds = workingProductIdsAndTotalAmount._1();
            if (CollUtil.isEmpty(targetProductIds)) {
                throw new ServiceException("不满足优惠券使用条件", CouponErrorCode.COUPON_WRONG_USE_CONDITION, new ShopProductSkuKey().setShopId(shopId));
            }
            shopAmount = workingProductIdsAndTotalAmount._2;
        }
        //优惠券使用的实际总额
        long realTotalAmount = shopAmount.get();
        //检查是否满足门槛额度
        CouponType type = couponUser.getType();
        Long requiredAmount = couponUser.getRequiredAmount();
        if (type.getRequiredAmount() && realTotalAmount < requiredAmount) {
            throw new ServiceException("不满足优惠券使用条件", CouponErrorCode.COUPON_WRONG_USE_CONDITION, new ShopProductSkuKey().setShopId(shopId));
        }
        BigDecimal discount = couponUser.getDiscount();
        Long amount = couponUser.getAmount();
        Long couponId = couponUser.getCouponId();
        return new OrderDiscount()
                .setSourceType(shopId != SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID ? DiscountSourceType.SHOP_COUPON : DiscountSourceType.PLATFORM_COUPON)
                .setSourceStatus(DiscountSourceStatus.OK)
                .setSourceId(couponId)
                .setSourceAmount(type.getDiscountAmount(realTotalAmount, amount, discount))
                .setTotalAmount(realTotalAmount)
                .setSourceDesc(type.getDesc(requiredAmount, amount, discount))
                .setProductIds(targetProductIds);
    }


    private Tuple2<Set<Long>, AtomicLong> getWorkingProductIdsAndTotalAmount(ProductType productType, Set<Long> couponUserProductIds, Map<Long, Long> productAmountMap) {
        Tuple2<Set<Long>, AtomicLong> result = Tuple.of(new HashSet<>(), new AtomicLong());
        productAmountMap.forEach(
                (productId, amount) -> {
                    //属于优惠券目标商品 指定生效则必须包含，指定不生效 则必须不包含
                    if ((ProductType.ASSIGNED == productType) == couponUserProductIds.contains(productId)) {
                        result._1().add(productId);
                        result._2().addAndGet(amount);
                    }
                }
        );
        return result;
    }

    /**
     * 遍历同及店铺信息 店铺总额与 店铺所有商品id
     *
     * @param shopProductAmount 店铺商品总价map
     * @return key：店铺id，平台我为0； value： 1。属于这个店铺的商品id集合 平台为空集合，2。属于这个店铺的商品总额，平台为所有店铺的总额
     */
    private Map<Long, AtomicLong> toShopStatisticsMap(Map<Long, Map<Long, Long>> shopProductAmount) {
        Map<Long, AtomicLong> amountMap = new HashMap<>(CommonPool.NUMBER_THIRTY);
        shopProductAmount.forEach(
                (shopId, productAmountMap) -> productAmountMap.forEach(
                        (productId, amount) -> {
                            AtomicLong shopAmount = amountMap.computeIfAbsent(shopId, (key) -> new AtomicLong());
                            shopAmount.addAndGet(amount);
                            AtomicLong platformAmount = amountMap.computeIfAbsent(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID, (key) -> new AtomicLong());
                            platformAmount.addAndGet(amount);
                        }
                )
        );
        return amountMap;
    }

    private String convertRequestUrl(String baseUrl, String apiUrl) {
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        if (!apiUrl.startsWith("/")) {
            baseUrl += "/";
        }
        return baseUrl + apiUrl;
    }

    private OrderDiscount validCouponUserYihu(CouponUser couponUser, Map<Long, Long> productAmountMap, Map<Long, AtomicLong> allStatisticsMap) {
        Long shopId = couponUser.getShopId();

        // 检查优惠券 商品类型
        ProductType productType = couponUser.getProductType();
        AtomicLong shopAmount = allStatisticsMap.get(shopId);
        Set<Long> targetProductIds = null;
        //指定商品生效
        if (productType.getIsAssigned()) {
            Tuple2<Set<Long>, AtomicLong> workingProductIdsAndTotalAmount = this.getWorkingProductIdsAndTotalAmount(couponUser.getProductType(), couponUser.getProductIds(), productAmountMap);
            targetProductIds = workingProductIdsAndTotalAmount._1();
            if (CollUtil.isEmpty(targetProductIds)) {
                throw new ServiceException("不满足优惠券使用条件", CouponErrorCode.COUPON_WRONG_USE_CONDITION, new ShopProductSkuKey().setShopId(shopId));
            }
            shopAmount = workingProductIdsAndTotalAmount._2;
        }
        //优惠券使用的实际总额
        long realTotalAmount = shopAmount.get();
        //检查是否满足门槛额度
        CouponType type = couponUser.getType();
        Long requiredAmount = couponUser.getRequiredAmount();
        if (type.getRequiredAmount() && realTotalAmount < requiredAmount) {
            throw new ServiceException("不满足优惠券使用条件", CouponErrorCode.COUPON_WRONG_USE_CONDITION, new ShopProductSkuKey().setShopId(shopId));
        }
        BigDecimal discount = couponUser.getDiscount();
        Long amount = couponUser.getAmount();
        return new OrderDiscount()
                .setSourceType(shopId != SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID ? DiscountSourceType.SHOP_COUPON : DiscountSourceType.PLATFORM_COUPON)
                .setSourceStatus(DiscountSourceStatus.OK)
                .setSourceId(couponUser.getUserId())
                .setSourceAmount(type.getDiscountAmount(realTotalAmount, amount, discount))
                .setTotalAmount(realTotalAmount)
                .setSourceDesc(type.getDesc(requiredAmount, amount, discount) + "（医护）")
                .setProductIds(targetProductIds);
    }

    private Option<CouponUser> getDiscount(Long buyerId, String buyerIdCode, Long shopId, Map<Long, AtomicLong> allStatisticsMap) {
        // 金额
        AtomicLong shopAmount = allStatisticsMap.get(shopId);
        long totalAmt = shopAmount.get();

        String url = convertRequestUrl(yihuProperties.getDomain(), DISCOUNT);

        Map<String, Object> params = new HashMap<>();
        params.put("userId", buyerId);
        params.put("policyNo", buyerIdCode);
        params.put("totalAmt", totalAmt);
        try {
            log.debug("调用医护优惠券获取接口，请求参数：{}", params);
            String str = HttpUtil.post(url, JSONUtil.toJsonStr(params));
            Result<Object> result = JSONUtil.toBean(str, Result.class);
            if (result.getCode() != 0) {
                throw new ServiceException(result.getMsg());
            }
            if (result.getData() == null || "null".equals(result.getData())) {
                return Option.none();
            }
            JSONObject data = JSONUtil.parseObj(result.getData());
            if (data != null && data.get("discountType") != null) {
                Long amount = data.getLong("discountAmount");
                if (amount == null || amount == 0L) {
                    return Option.none();
                }
                CouponUser couponUser = new CouponUser().setUserId(buyerId).setUsed(Boolean.FALSE);
                couponUser.setShopId(shopId)
                        .setAmount(amount)
                        .setDiscount(StrUtil.isEmpty(data.getStr("discount")) ? null : new BigDecimal(data.getStr("discount")))
                        .setName(StrUtil.isEmpty(data.getStr("couponName")) ? "现金券" : data.getStr("couponName"))
                        .setEffectType(EffectType.IMMEDIATELY)
                        .setProductType(ProductType.SHOP_ALL)
                        .setDeleted(Boolean.FALSE);

                String discountType = data.getStr("discountType");
                if ("COUPON".equals(discountType)) {
                    couponUser.setType(CouponType.YIHU_PRICE_REDUCE);
                } else if ("DISCOUNT".equals(discountType)) {
                    couponUser.setType(CouponType.YIHU_PRICE_DISCOUNT);
                }
                log.debug("调用医护优惠券获取接口成功，返回结果：{}", str);
                return Option.of(couponUser);
            }
        } catch (Exception e) {
            log.error("调用医护优惠券获取接口失败，e: {}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
        return Option.none();
    }

}
