(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("vue-router"),require("@/utils/date"),require("@/composables/useConvert"),require("@vueuse/core"),require("@/components/pageManage/PageManage.vue"),require("@/apis/http"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/utils/date","@/composables/useConvert","@vueuse/core","@/components/pageManage/PageManage.vue","@/apis/http","element-plus"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopCouponList=V(e.ShopCouponListContext.Vue,e.ShopCouponListContext.VueRouter,e.ShopCouponListContext.DateUtil,e.ShopCouponListContext.UseConvert,e.ShopCouponListContext.VueUse,e.ShopCouponListContext.PageManage,e.ShopCouponListContext.Request,e.ShopCouponListContext.ElementPlus))})(this,function(e,V,P,I,S,O,b,y){"use strict";var B=document.createElement("style");B.textContent=`.name[data-v-ec1f3151]{width:120px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}
`,document.head.appendChild(B),I();const q={OPEN:"进行中",NOT_OPEN:"未开始",CLOSED:"已结束",BANED:"违规下架"},U={PRICE_DISCOUNT:"无门槛折扣券",PRICE_REDUCE:"无门槛现金券",REQUIRED_PRICE_DISCOUNT:"满折券",REQUIRED_PRICE_REDUCE:"满减券"};var N=(l=>(l.PRICE_REDUCE="PRICE_REDUCE",l.PRICE_DISCOUNT="PRICE_DISCOUNT",l.REQUIRED_PRICE_REDUCE="REQUIRED_PRICE_REDUCE",l.REQUIRED_PRICE_DISCOUNT="REQUIRED_PRICE_DISCOUNT",l))(N||{});e.computed(()=>l=>{var r;return(r=window==null?void 0:window.permissionList)==null?void 0:r.includes(l)});const L=l=>{const r=new P;return l.status!=="OK"?"违规下架":l.effectType==="PERIOD"&&l.endDate>=r.getYMDs()?l.stock==="0"?"已结束":l.startDate>r.getYMDs()?"未开始":"进行中":l.effectType==="PERIOD"&&l.endDate<r.getYMDs()?"已结束":l.effectType==="IMMEDIATELY"?l.stock!=="0"?"进行中":"已结束":""},T=e.defineComponent({__name:"select-couppon-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(l,{emit:r}){const s=l,p=r,a=S.useVModel(s,"modelValue",p);return(C,c)=>{const n=e.resolveComponent("el-option"),m=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(m,{modelValue:e.unref(a),"onUpdate:modelValue":c[0]||(c[0]=d=>e.isRef(a)?a.value=d:null),placeholder:s.placeholder,style:{width:"150px"},onChange:c[1]||(c[1]=d=>p("change",d))},{default:e.withCtx(()=>[e.renderSlot(C.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Object.values(s.list).length?s.list:e.unref(U),(d,_)=>(e.openBlock(),e.createBlock(n,{key:_,label:d,value:_},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),z=e.defineComponent({__name:"head-operation",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0}},emits:["update:modelValue","addCoupon","delCoupon"],setup(l,{emit:r}){const s=l,p=r,a=S.useVModel(s,"modelValue",p),C=e.computed(()=>c=>{var n;return(n=window==null?void 0:window.permissionList)==null?void 0:n.includes(c)});return(c,n)=>{const m=e.resolveComponent("el-button"),d=e.resolveComponent("el-space"),_=e.resolveComponent("el-option"),h=e.resolveComponent("el-col"),k=e.resolveComponent("el-date-picker"),g=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(g,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px"}},{default:e.withCtx(()=>[e.createVNode(h,{span:14},{default:e.withCtx(()=>[e.createVNode(d,null,{default:e.withCtx(()=>[C.value("marketing:coupon:add")?(e.openBlock(),e.createBlock(m,{key:0,round:"",type:"primary",onClick:n[0]||(n[0]=f=>p("addCoupon"))},{default:e.withCtx(()=>n[5]||(n[5]=[e.createTextVNode("新增优惠券")])),_:1})):e.createCommentVNode("",!0)]),_:1}),e.createVNode(d,null,{default:e.withCtx(()=>[C.value("marketing:coupon:delete")?(e.openBlock(),e.createBlock(m,{key:0,disabled:c.$props.batchDisabled,round:"",onClick:n[1]||(n[1]=f=>p("delCoupon"))},{default:e.withCtx(()=>n[6]||(n[6]=[e.createTextVNode("批量删除")])),_:1},8,["disabled"])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(d,null,{default:e.withCtx(()=>[e.createVNode(T,{modelValue:e.unref(a).status,"onUpdate:modelValue":n[2]||(n[2]=f=>e.unref(a).status=f),list:e.unref(q)},{default:e.withCtx(()=>[e.createVNode(_,{value:"",label:"全部状态"})]),_:1},8,["modelValue","list"])]),_:1}),e.createVNode(T,{modelValue:e.unref(a).type,"onUpdate:modelValue":n[3]||(n[3]=f=>e.unref(a).type=f)},{default:e.withCtx(()=>[e.createVNode(_,{value:"",label:"全部类型"})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(h,{span:10},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:e.unref(a).date,"onUpdate:modelValue":n[4]||(n[4]=f=>e.unref(a).date=f),"default-value":[new Date,new Date],"end-placeholder":"结束日期",format:"YYYY/MM/DD","start-placeholder":"开始日期",type:"daterange","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-value"])]),_:1})]),_:1})}}}),Y=l=>b.get({url:"addon-coupon/coupon",params:l}),w=l=>b.del({url:"addon-coupon/coupon/shop/batch",data:l}),$={class:"name"},A={key:0},j={key:1},Q={key:2},H={key:3},F=e.defineComponent({__name:"coupon-list",props:{search:{type:Object,default:()=>({})}},setup(l,{expose:r}){const s=V.useRouter(),p=l,{divTenThousand:a}=I(),C=e.ref([]),c=e.ref(),n=e.reactive({size:10,current:1,total:0}),m=e.ref([]);e.watch(()=>p.search,i=>{d()},{deep:!0});async function d(){const{status:i,type:o,date:u}=p.search;let E={status:i,type:o,endDate:"",startDate:""};Array.isArray(p.search.date)&&(E.startDate=p.search.date[0],E.endDate=p.search.date[1]);const D={...n,...E},{code:R,data:t}=await Y(D);if(R!==200)return y.ElMessage.error("获取优惠券列表失败");C.value=t.records,n.current=t.current,n.size=t.size,n.total=t.total}const _=i=>{s.push({path:"/coupons/baseInfo",query:{id:i.id,shopId:i.shopId}})},h=async i=>{if(!await y.ElMessageBox.confirm("确定删除该优惠券?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:u,data:E}=await w([i]);if(u!==200){y.ElMessage.error("删除失败");return}y.ElMessage.success("删除成功"),C.value=C.value.filter(D=>D.id!==i)},k=async i=>{const{code:o,data:u}=await w(i);if(o!==200){y.ElMessage.error("删除失败");return}y.ElMessage.success("删除成功"),d()},g=i=>{n.size=i,d()},f=i=>{n.current=i,d()};r({chooseList:m,handleDelBatch:k});const x=e.computed(()=>i=>{var o;return(o=window==null?void 0:window.permissionList)==null?void 0:o.includes(i)});return(i,o)=>{const u=e.resolveComponent("el-table-column"),E=e.resolveComponent("el-button"),D=e.resolveComponent("el-table"),R=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(D,{ref_key:"multipleTableRef",ref:c,"cell-style":{fontSize:"12px",color:"#333333"},data:C.value,"header-cell-style":{background:"#f6f8fa"},"header-row-style":{fontSize:"12px",color:"#909399"},stripe:"",onSelectionChange:o[0]||(o[0]=t=>m.value=t)},{default:e.withCtx(()=>[e.createVNode(u,{type:"selection",width:"55"}),e.createVNode(u,{label:"活动名称"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",$,e.toDisplayString(t.name),1)]),_:1}),e.createVNode(u,{label:"类型"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(U)[t.type]),1)]),_:1}),e.createVNode(u,{label:"规则"},{default:e.withCtx(({row:t})=>[t.type===e.unref(N).PRICE_REDUCE?(e.openBlock(),e.createElementBlock("div",A,[e.createElementVNode("span",null,e.toDisplayString(t.amount&&e.unref(a)(t.amount)),1),o[2]||(o[2]=e.createElementVNode("span",null,"元，无门槛使用",-1))])):e.createCommentVNode("",!0),t.type===e.unref(N).PRICE_DISCOUNT?(e.openBlock(),e.createElementBlock("div",j,[e.createElementVNode("span",null,e.toDisplayString(t.discount),1),o[3]||(o[3]=e.createElementVNode("span",null,"折，无门槛使用",-1))])):e.createCommentVNode("",!0),t.type===e.unref(N).REQUIRED_PRICE_DISCOUNT?(e.openBlock(),e.createElementBlock("div",Q,[o[4]||(o[4]=e.createElementVNode("span",null,"满",-1)),e.createElementVNode("span",null,e.toDisplayString(t.requiredAmount&&e.unref(a)(t.requiredAmount)),1),o[5]||(o[5]=e.createElementVNode("span",null,"元，打",-1)),e.createElementVNode("span",null,e.toDisplayString(t.discount),1),o[6]||(o[6]=e.createElementVNode("span",null,"折",-1))])):e.createCommentVNode("",!0),t.type===e.unref(N).REQUIRED_PRICE_REDUCE?(e.openBlock(),e.createElementBlock("div",H,[o[7]||(o[7]=e.createElementVNode("span",null,"满",-1)),e.createElementVNode("span",null,e.toDisplayString(t.requiredAmount&&e.unref(a)(t.requiredAmount)),1),o[8]||(o[8]=e.createElementVNode("span",null,"元，减",-1)),e.createElementVNode("span",null,e.toDisplayString(t.amount&&e.unref(a)(t.amount)),1),o[9]||(o[9]=e.createElementVNode("span",null,"元",-1))])):e.createCommentVNode("",!0),e.createElementVNode("span",null,e.toDisplayString(t.rules),1)]),_:1}),e.createVNode(u,{label:"已领取/剩余",prop:"date"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.num-t.stock)+"/"+e.toDisplayString(t.stock),1)]),_:1}),e.createVNode(u,{label:"已使用",prop:"usedCount"}),e.createVNode(u,{label:"状态",prop:"address"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(L)(t)),1)]),_:1}),e.createVNode(u,{fixed:"right",label:"操作",prop:"address",width:"120"},{default:e.withCtx(({row:t})=>[["进行中","未开始"].includes(e.unref(L)(t))&&x.value("marketing:coupon:edit")?(e.openBlock(),e.createBlock(E,{key:0,link:"",size:"small",type:"primary",onClick:M=>_(t)},{default:e.withCtx(()=>o[10]||(o[10]=[e.createTextVNode("编辑 ")])),_:2},1032,["onClick"])):x.value("marketing:coupon:detail")?(e.openBlock(),e.createBlock(E,{key:1,link:"",size:"small",type:"primary",onClick:M=>_(t)},{default:e.withCtx(()=>o[11]||(o[11]=[e.createTextVNode("查看")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),x.value("marketing:coupon:delete")?(e.openBlock(),e.createBlock(E,{key:2,link:"",size:"small",type:"primary",onClick:M=>h(t.id)},{default:e.withCtx(()=>o[12]||(o[12]=[e.createTextVNode("删除")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(R,{align:"middle",justify:"end"},{default:e.withCtx(()=>[e.createVNode(O,{modelValue:n,"onUpdate:modelValue":o[1]||(o[1]=t=>n=t),"load-init":!0,"page-size":n.size,total:n.total,onReload:d,onHandleSizeChange:g,onHandleCurrentChange:f},null,8,["modelValue","page-size","total"])]),_:1})])}}}),K="",G=((l,r)=>{const s=l.__vccOpts||l;for(const[p,a]of r)s[p]=a;return s})(F,[["__scopeId","data-v-ec1f3151"]]);return e.defineComponent({__name:"ShopCouponList",setup(l){const r=e.reactive({status:"",type:"",date:""}),s=e.ref(),p=V.useRouter(),a=e.computed(()=>s.value?!s.value.chooseList.length:!0),C=()=>{const n=s.value.chooseList.map(m=>m.id);s.value.handleDelBatch(n)},c=()=>p.push("/coupons/baseInfo");return(n,m)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode(z,{modelValue:r,"onUpdate:modelValue":m[0]||(m[0]=d=>r=d),"batch-disabled":a.value,onAddCoupon:c,onDelCoupon:C},null,8,["modelValue","batch-disabled"]),e.createVNode(G,{ref_key:"couponListRef",ref:s,search:r},null,8,["search"])]))}})});
