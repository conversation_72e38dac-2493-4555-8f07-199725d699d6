(function(e,V){typeof exports=="object"&&typeof module<"u"?module.exports=V(require("vue"),require("@vueuse/core"),require("@/utils/date"),require("@/composables/useConvert"),require("@element-plus/icons-vue"),require("vue-router"),require("@/components/pageManage/PageManage.vue"),require("@/apis/http"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@/utils/date","@/composables/useConvert","@element-plus/icons-vue","vue-router","@/components/pageManage/PageManage.vue","@/apis/http","element-plus"],V):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformCouponList=V(e.PlatformCouponListContext.Vue,e.PlatformCouponListContext.VueUse,e.PlatformCouponListContext.DateUtil,e.PlatformCouponListContext.UseConvert,e.PlatformCouponListContext.ElementPlusIconsVue,e.PlatformCouponListContext.VueRouter,e.PlatformCouponListContext.PageManage,e.PlatformCouponListContext.Request,e.PlatformCouponListContext.ElementPlus))})(this,function(e,V,B,I,S,U,P,g,m){"use strict";var k=document.createElement("style");k.textContent=`.name[data-v-68e43125]{width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
`,document.head.appendChild(k),I();const T={PRICE_DISCOUNT:"无门槛折扣券",PRICE_REDUCE:"无门槛现金券",REQUIRED_PRICE_DISCOUNT:"满折券",REQUIRED_PRICE_REDUCE:"满减券"};var y=(t=>(t.PRICE_REDUCE="PRICE_REDUCE",t.PRICE_DISCOUNT="PRICE_DISCOUNT",t.REQUIRED_PRICE_REDUCE="REQUIRED_PRICE_REDUCE",t.REQUIRED_PRICE_DISCOUNT="REQUIRED_PRICE_DISCOUNT",t))(y||{});const w=t=>{const r=new B;return t.status!=="OK"?"违规下架":t.effectType==="PERIOD"&&t.endDate>=r.getYMDs()?t.stock==="0"?"已结束":t.startDate>r.getYMDs()?"未开始":"进行中":t.effectType==="PERIOD"&&t.endDate<r.getYMDs()?"已结束":t.effectType==="IMMEDIATELY"?t.stock!=="0"?"进行中":"已结束":""},L=e.defineComponent({__name:"select-couppon-type",props:{modelValue:{type:String,default:""},placeholder:{type:String,default:""},list:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(t,{emit:r}){const a=t,d=V.useVModel(a,"modelValue",r);return(p,n)=>{const f=e.resolveComponent("el-option"),l=e.resolveComponent("el-select");return e.openBlock(),e.createBlock(l,{modelValue:e.unref(d),"onUpdate:modelValue":n[0]||(n[0]=i=>e.isRef(d)?d.value=i:null),placeholder:a.placeholder,style:{width:"150px"},onChange:n[1]||(n[1]=i=>r("change",i))},{default:e.withCtx(()=>[e.renderSlot(p.$slots,"default"),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(Object.values(a.list).length?a.list:e.unref(T),(i,u)=>(e.openBlock(),e.createBlock(f,{key:u,label:i,value:u},null,8,["label","value"]))),128))]),_:3},8,["modelValue","placeholder"])}}}),M=e.defineComponent({__name:"head-operation2",props:{modelValue:{type:Object,default(){return{}}},batchDisabled:{type:Boolean,default:!0}},emits:["update:modelValue","delCoupon","search"],setup(t,{emit:r}){const a=t,d=V.useVModel(a,"modelValue",r);return(p,n)=>{const f=e.resolveComponent("el-button"),l=e.resolveComponent("el-space"),i=e.resolveComponent("el-col"),u=e.resolveComponent("el-option"),N=e.resolveComponent("el-input"),D=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(D,{gutter:24,justify:"space-between",style:{"margin-bottom":"15px"}},{default:e.withCtx(()=>[e.createVNode(i,{span:14},{default:e.withCtx(()=>[e.createVNode(l,null,{default:e.withCtx(()=>[e.createVNode(f,{disabled:p.$props.batchDisabled,round:"",onClick:n[0]||(n[0]=h=>r("delCoupon"))},{default:e.withCtx(()=>[e.createTextVNode("批量移除")]),_:1},8,["disabled"])]),_:1})]),_:1}),e.createVNode(i,{span:9},{default:e.withCtx(()=>[e.createVNode(l,null,{default:e.withCtx(()=>[e.createVNode(L,{modelValue:e.unref(d).status,"onUpdate:modelValue":n[1]||(n[1]=h=>e.unref(d).status=h)},{default:e.withCtx(()=>[e.createVNode(u,{value:"",label:"全部"})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(N,{modelValue:e.unref(d).keywords,"onUpdate:modelValue":n[3]||(n[3]=h=>e.unref(d).keywords=h),placeholder:"输入关键词",style:{width:"55%"}},{append:e.withCtx(()=>[e.createVNode(f,{icon:e.unref(S.Search),onClick:n[2]||(n[2]=h=>r("search"))},null,8,["icon"])]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),O=t=>g.get({url:"addon-coupon/coupon",params:t}),R=t=>g.del({url:"addon-coupon/coupon/batch",data:t}),q=t=>g.put({url:"addon-coupon/coupon/ban/batch",data:t}),_=t=>(e.pushScopeId("data-v-68e43125"),t=t(),e.popScopeId(),t),z={class:"name"},$={class:"name"},Q={key:0},j=_(()=>e.createElementVNode("span",null,"元，无门槛使用",-1)),A={key:1},Y=_(()=>e.createElementVNode("span",null,"折，无门槛使用",-1)),H={key:2},F=_(()=>e.createElementVNode("span",null,"满",-1)),G=_(()=>e.createElementVNode("span",null,"元，打",-1)),K=_(()=>e.createElementVNode("span",null,"折",-1)),J={key:3},W=_(()=>e.createElementVNode("span",null,"满",-1)),X=_(()=>e.createElementVNode("span",null,"元，减",-1)),Z=_(()=>e.createElementVNode("span",null,"元",-1)),v=_(()=>e.createElementVNode("div",{style:{bottom:"20px",background:"#fff",width:"980px",height:"70px"}},null,-1)),ee={style:{position:"fixed",bottom:"20px",background:"#fff",width:"980px",height:"70px","z-index":"1000"}},te=e.defineComponent({__name:"coupon-list2",props:{search:{type:Object,default:()=>({})}},setup(t,{expose:r}){const a=t,d=U.useRouter(),{divTenThousand:p}=I(),n=e.ref([]),f=e.ref(),l=e.reactive({size:10,current:1,total:0}),i=e.ref([]);e.watch(()=>a.search.status,()=>{u()},{deep:!0}),u();async function u(){const s={...l,...a.search},{code:C,data:c}=await O(s);if(C!==200)return m.ElMessage.error("获取优惠券列表失败");n.value=c.records,l.current=c.current,l.size=c.size,l.total=c.total}const N=async s=>{if(!await m.ElMessageBox.confirm("确定下架该优惠券?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:c}=await q([{shopId:s.shopId,couponId:s.id}]);if(c!==200){m.ElMessage.error("下架失败");return}m.ElMessage.success("下架成功");const E=n.value.find(x=>x.id===s.id);E&&(E.status="BANED")},D=async s=>{if(!await m.ElMessageBox.confirm("确定删除该优惠券?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:c,data:E}=await R([{shopId:s.shopId,couponId:s.id}]);if(c!==200){m.ElMessage.error("删除失败");return}m.ElMessage.success("删除成功"),n.value=n.value.filter(x=>x.id!==s.id)},h=s=>{d.push({name:"couponBaseInfo",query:{id:s.id,shopId:s.shopId}})},ne=async s=>{const{code:C}=await R(s);if(C!==200){m.ElMessage.error("删除失败");return}m.ElMessage.success("删除成功"),u()},le=s=>{l.current=1,l.size=s,u()},ae=s=>{l.current=s,u()};return r({chooseList:i,handleDelBatch:ne,initCouponList:u}),(s,C)=>{const c=e.resolveComponent("el-table-column"),E=e.resolveComponent("el-link"),x=e.resolveComponent("el-row"),se=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(se,{ref_key:"multipleTableRef",ref:f,"cell-style":{fontSize:"12px",color:"#333333"},data:n.value,"header-cell-style":{background:"#f6f8fa"},"header-row-style":{fontSize:"12px",color:"#000"},height:"calc(100vh - 250px)",stripe:"",onSelectionChange:C[0]||(C[0]=o=>i.value=o)},{default:e.withCtx(()=>[e.createVNode(c,{type:"selection",width:"55"}),e.createVNode(c,{label:"店铺名称",width:"140"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",z,e.toDisplayString(o.shopName),1)]),_:1}),e.createVNode(c,{align:"center",label:"活动名称",width:"140"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",$,e.toDisplayString(o.name),1)]),_:1}),e.createVNode(c,{align:"center",label:"规则",width:"180"},{default:e.withCtx(({row:o})=>[o.type===e.unref(y).PRICE_REDUCE?(e.openBlock(),e.createElementBlock("div",Q,[e.createElementVNode("span",null,e.toDisplayString(o.amount&&e.unref(p)(o.amount)),1),j])):e.createCommentVNode("",!0),o.type===e.unref(y).PRICE_DISCOUNT?(e.openBlock(),e.createElementBlock("div",A,[e.createElementVNode("span",null,e.toDisplayString(o.discount),1),Y])):e.createCommentVNode("",!0),o.type===e.unref(y).REQUIRED_PRICE_DISCOUNT?(e.openBlock(),e.createElementBlock("div",H,[F,e.createElementVNode("span",null,e.toDisplayString(o.requiredAmount&&e.unref(p)(o.requiredAmount)),1),G,e.createElementVNode("span",null,e.toDisplayString(o.discount),1),K])):e.createCommentVNode("",!0),o.type===e.unref(y).REQUIRED_PRICE_REDUCE?(e.openBlock(),e.createElementBlock("div",J,[W,e.createElementVNode("span",null,e.toDisplayString(o.requiredAmount&&e.unref(p)(o.requiredAmount)),1),X,e.createElementVNode("span",null,e.toDisplayString(o.amount&&e.unref(p)(o.amount)),1),Z])):e.createCommentVNode("",!0),e.createElementVNode("span",null,e.toDisplayString(o.rules),1)]),_:1}),e.createVNode(c,{align:"center",label:"已领取/剩余",prop:"date"},{default:e.withCtx(({row:o})=>[e.createElementVNode("span",null,e.toDisplayString(o.num-o.stock)+"/"+e.toDisplayString(o.stock),1)]),_:1}),e.createVNode(c,{align:"center",label:"已使用",prop:"usedCount"}),e.createVNode(c,{label:"状态",prop:"address"},{default:e.withCtx(({row:o})=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(w)(o)),1)]),_:1}),e.createVNode(c,{align:"center",fixed:"right",label:"操作",prop:"address",width:"150"},{default:e.withCtx(({row:o})=>[e.createVNode(x,{justify:"end",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(E,{underline:!1,size:"small",style:{padding:"0 5px"},type:"primary",onClick:b=>h(o)},{default:e.withCtx(()=>[e.createTextVNode(" 查看 ")]),_:2},1032,["onClick"]),o.status!=="BANED"?(e.openBlock(),e.createBlock(E,{key:0,underline:!1,size:"small",style:{padding:"0 5px"},type:"primary",onClick:b=>N(o)},{default:e.withCtx(()=>[e.createTextVNode("下架 ")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.createVNode(E,{underline:!1,size:"small",style:{padding:"0 5px"},type:"primary",onClick:b=>D(o)},{default:e.withCtx(()=>[e.createTextVNode(" 删除 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),v,e.createElementVNode("div",ee,[e.createVNode(P,{"page-size":l.size,"page-num":l.current,total:l.total,"load-init":!0,onHandleSizeChange:le,onHandleCurrentChange:ae},null,8,["page-size","page-num","total"])])])}}}),re="",oe=((t,r)=>{const a=t.__vccOpts||t;for(const[d,p]of r)a[d]=p;return a})(te,[["__scopeId","data-v-68e43125"]]);return e.defineComponent({__name:"PlatformCouponList",setup(t){const r=e.reactive({status:"",type:"",keywords:""}),a=e.ref(),d=e.computed(()=>a.value?!a.value.chooseList.length:!0),p=()=>{a.value.initCouponList()},n=()=>{const f=a.value.chooseList.map(l=>({couponId:l.id,shopId:l.shopId}));a.value.handleDelBatch(f)};return(f,l)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode(M,{modelValue:r,"onUpdate:modelValue":l[0]||(l[0]=i=>r=i),"batch-disabled":d.value,onSearch:p,onDelCoupon:n},null,8,["modelValue","batch-disabled"]),e.createVNode(oe,{ref_key:"couponListRef",ref:a,search:r},null,8,["search"])]))}})});
