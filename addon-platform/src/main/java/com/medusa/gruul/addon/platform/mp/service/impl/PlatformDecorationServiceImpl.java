package com.medusa.gruul.addon.platform.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.addon.platform.mp.entity.PlatformDecoration;
import com.medusa.gruul.addon.platform.mp.mapper.PlatformDecorationMapper;
import com.medusa.gruul.addon.platform.mp.service.IPlatformDecorationService;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.system.model.model.Platform;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Description
 * @date 2022-11-01 19:45
 */
@Service
public class PlatformDecorationServiceImpl extends ServiceImpl<PlatformDecorationMapper, PlatformDecoration> implements IPlatformDecorationService {

    @Override
    public List<AggregationDecorationVO>  getAggregationPlatformDecorate() {
        return  this.baseMapper.queryAggregationPlatformDecorate();
    }
}
