package com.medusa.gruul.addon.platform.service;

import com.medusa.gruul.addon.platform.mp.entity.PlatformDecorationDetails;
import com.medusa.gruul.common.custom.aggregation.decoration.dto.FunctionDecorationDTO;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.FunctionType;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.PropertiesVO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Description
 * @date 2022-11-01 09:10
 */
public interface PlatformRenovationService {
    /**
     * 删除装修页面
     *
     * @param ids 装修页面ids
     * @param aggregationPlatform 聚合平台类型
     */
    void delDecorationPage(Long[] ids,AggregationPlatform aggregationPlatform);

    /**
     * 修改页面名称
     *
     * @param name 修改名称
     * @param id   修改页面id
     */
    void updatePageName(String name, Long id);

    /**
     * 获取装修数据
     *
     * @param id 装修详情id
     * @return PropertiesVO.java
     */
    PropertiesVO getDecorationInfoById(Long id);


    /**
     * 编辑装修功能
     *
     * @param functionDecoration FunctionDecorationDTO.java
     * @return   entityId
     */
    Long editFunctionDecoration(FunctionDecorationDTO functionDecoration);

    /**
     * 设置页面为首页
     *
     * @param aggregationPlatform 聚合平台类型
     * @param id                  页面id
     */
    void setHomePage(Long id, AggregationPlatform aggregationPlatform);


    /**
     * 获取页面基础信息 list
     *
     * @param aggregationPlatform 聚合APP平台类型
     * @return List<页面基础信息>
     */
    List<PlatformDecorationDetails> getPageBasicsInfoList(AggregationPlatform aggregationPlatform);

    /**
     * 获取非页面装修数据信息 by 枚举
     *
     * @param aggregationPlatform 聚合平台类型
     * @param functionType  装修功能类型
     * @return PropertiesVO.java
     */
    PropertiesVO getNotPageDecorationInfo(AggregationPlatform aggregationPlatform, FunctionType functionType);

    /**
     * 获取页面装修数据信息
     *
     * @param aggregationPlatform 聚合平台类型
     * @return PropertiesVO.java
     */
    PropertiesVO getDecorationInfo(AggregationPlatform aggregationPlatform);

    /**
     * 获取聚合装修类型及对应的首页名称
     *
     * @param aggregationPlatforms 聚合平台类型
     * @return List<AggregationDecorationVO>
     */
    List<AggregationDecorationVO> getAggregationPlatformDecorate(List<AggregationPlatform> aggregationPlatforms);

    /**
     * 获取非装修页面基础信息
     *
     * @param aggregationPlatform 聚合app类型
     * @param functionType        装修功能累心
     * @return PlatformDecorationDetails
     */
    PlatformDecorationDetails getNotPageBasicDecorationInfo(AggregationPlatform aggregationPlatform, FunctionType functionType);


    /**
     * 获取首页装修功能详情
     *
     * @return List<Object>
     */
    List<Object> getHomePage();


    /**
     * 获取装修类型详情
     *
     * @param functionType 装修功能类型
     * @return PropertiesVO.java
     */
    PropertiesVO getFunctionTypeInfo(FunctionType functionType);

    /**
     * 设置页面为同城页面
     *
     * @param id 页面id
     * @param aggregationPlatform 聚合平台类型
     */
    void setLocalPage(Long id, AggregationPlatform aggregationPlatform);

    /**
     *  获取同城页面装修信息
     *
     * @return PropertiesVO.java
     */
    PropertiesVO getLocalPage();


}
