package com.medusa.gruul.addon.platform.controller;

import com.medusa.gruul.addon.platform.mp.entity.PlatformDecorationDetails;
import com.medusa.gruul.addon.platform.service.PlatformRenovationService;
import com.medusa.gruul.common.custom.aggregation.decoration.dto.FunctionDecorationDTO;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.FunctionType;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.PropertiesVO;
import com.medusa.gruul.common.idem.annotation.Idem;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.wildfly.common.annotation.NotNull;

import java.util.List;

/**
 * 平台装修详情相关接口
 *
 * <AUTHOR>
 * @Description PlatformDecorationDetailsController.java
 * date 2022-10-31 20:57
 */
@RestController
@RequiredArgsConstructor
@PreAuthorize("@S.platformPerm('decoration')")
@RequestMapping("platform/decoration")
public class PlatformDecorationDetailsController {

    private final PlatformRenovationService platformRenovationService;

    /**
     * 删除装修页面
     *
     * @param ids 装修详情ids
     * @return Result.ok();
     */
    @Log("删除装修页面")
    @DeleteMapping("del/{ids}")
    public Result<Void> delDecorationPage(@PathVariable(name = "ids") Long[] ids, AggregationPlatform aggregationPlatform) {
        platformRenovationService.delDecorationPage(ids, aggregationPlatform);
        return Result.ok();
    }

    /**
     * 编辑装修功能
     *
     * @param functionDecoration FunctionDecorationDTO.class
     * @return Result.ok();
     */
    @Idem(2000)
    @Log("编辑装修功能详情")
    @PostMapping("/edit")
    public Result<Long> editFunctionDecoration(@RequestBody FunctionDecorationDTO functionDecoration) {
        return Result.ok(platformRenovationService.editFunctionDecoration(functionDecoration));
    }


    /**
     * 修改页面名称
     *
     * @param name 修改名称
     * @param id   修改页面id
     * @return Result.ok();
     */
    @Log("修改页面名称")
    @PutMapping("page/update/{name}")
    public Result<Void> updatePageName(@PathVariable(name = "name") String name, Long id) {
        platformRenovationService.updatePageName(name, id);
        return Result.ok();
    }

    /**
     * 设置页面为首页
     *
     * @param id                  页面id
     * @param aggregationPlatform 聚合 APP平台类型
     * @return Result<Void>
     */

    @Log("设置为首页")
    @PutMapping("set/home/<USER>/{id}")
    public Result<Void> setHomePage(@PathVariable(name = "id") Long id, @NotNull AggregationPlatform aggregationPlatform) {
        platformRenovationService.setHomePage(id, aggregationPlatform);
        return Result.ok();
    }


    /**
     * 设置页面为同城
     *
     * @param id                  页面id
     * @param aggregationPlatform 聚合 APP平台类型
     * @return Result<Void>
     */

    @Log("设置为同城")
    @PutMapping("set/local/page/{id}")
    public Result<Void> setLocalPage(@PathVariable(name = "id") Long id, @NotNull AggregationPlatform aggregationPlatform) {
        platformRenovationService.setLocalPage(id, aggregationPlatform);
        return Result.ok();
    }

    /**
     * 获取装修页面基础信息
     *
     * @param aggregationPlatform 聚合app类型
     * @return Result<List < PlatformDecorationDetails>>
     */
    @Log("获取装修页面基础信息")
    @PreAuthorize("permitAll()")
    @GetMapping("page/list")
    public Result<List<PlatformDecorationDetails>> getPageBasicsInfoList(@NotNull AggregationPlatform aggregationPlatform) {
        return Result.ok(platformRenovationService.getPageBasicsInfoList(aggregationPlatform));
    }

    /**
     * 获取非装修页面基础信息
     *
     * @param aggregationPlatform 聚合app类型
     * @param functionType        装修功能类型
     * @return PlatformDecorationDetails
     */
    @Log("获取非装修页面基础信息")
    @PreAuthorize("permitAll()")
    @GetMapping("not/page")
    public Result<PlatformDecorationDetails> getNotPageBasicDecorationInfo(@NotNull AggregationPlatform aggregationPlatform,
                                                                           @NotNull FunctionType functionType) {
        return Result.ok(platformRenovationService.getNotPageBasicDecorationInfo(aggregationPlatform, functionType));
    }


    /**
     * 获取非页面装修数据信息 by 枚举
     *
     * @param aggregationPlatform 聚合平台类型
     * @param functionType        查询装修功能类型
     */
    @Log("获取非装修页面数据信息")
    @PreAuthorize("permitAll()")
    @GetMapping("not/page/info")
    public Result<PropertiesVO> getNotPageDecorationInfo(@NotNull AggregationPlatform aggregationPlatform,
                                                         @NotNull FunctionType functionType) {
        return Result.ok(platformRenovationService.getNotPageDecorationInfo(aggregationPlatform, functionType));
    }

    /**
     * 获取首页装修功能详情
     *
     * @param aggregationPlatform 聚合平台类型
     * @return PropertiesVO.java
     */
    @Log("获取首页装修功能详情")
    @PreAuthorize("permitAll()")
    @GetMapping("page/info")
    public Result<PropertiesVO> getDecorationInfo(@NotNull AggregationPlatform aggregationPlatform) {
        PropertiesVO properties = platformRenovationService.getDecorationInfo(aggregationPlatform);
        return Result.ok(properties);
    }


    /**
     * 获取聚合装修类型及对应的首页名称
     *
     * @param aggregationPlatforms 聚合平台类型
     * @return List<AggregationDecorationVO>
     */
    @Log("获取聚合装修类型及对应的首页名称")
    @PreAuthorize("permitAll()")
    @GetMapping("aggregation/decorate")
    public Result<List<AggregationDecorationVO>> getAggregationPlatformDecorate(@RequestParam List<AggregationPlatform> aggregationPlatforms) {
        return Result.ok(platformRenovationService.getAggregationPlatformDecorate(aggregationPlatforms));
    }


//============================================C端获取装修信息===============================================================
//============================================C端获取装修信息===============================================================

    /**
     * 获取首页装修功能详情
     *
     * @return List<Object>
     */
    @Log("获取首页装修功能详情")
    @GetMapping("client/page/info")
    @PreAuthorize("permitAll()")
    public Result<List<Object>> getHomePage() {
        return Result.ok(platformRenovationService.getHomePage());
    }

    /**
     * 获取装修类型详情
     *
     * @param functionType 装修功能类型
     * @return PropertiesVO.java
     */
    @Log("获取装修类型详情")
    @GetMapping("client/type/info")
    @PreAuthorize("permitAll()")
    public Result<PropertiesVO> getFunctionTypeInfo(@NotNull FunctionType functionType) {
        return Result.ok(platformRenovationService.getFunctionTypeInfo(functionType));
    }


    /**
     * 获取装修 <功能信息(页面)> by id
     *
     * @param id 页面id
     * @return PropertiesVO.class
     */
    @Log("获取装修控件信息ById")
    @GetMapping("get")
    @PreAuthorize("permitAll()")
    public Result<PropertiesVO> getDecorationInfoById(@NotNull Long id) {
        PropertiesVO properties = platformRenovationService.getDecorationInfoById(id);
        return Result.ok(properties);
    }


    /**
     * 获取同城页面装修信息
     *
     * @return 同城页面装修信息
     */
    @Log("获取同城页面装修")
    @GetMapping("client/local/info")
    @PreAuthorize("permitAll()")
    public Result<PropertiesVO> getLocalPage() {
        return Result.ok(platformRenovationService.getLocalPage());
    }

}
