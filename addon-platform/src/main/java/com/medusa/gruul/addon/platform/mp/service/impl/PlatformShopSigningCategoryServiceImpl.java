package com.medusa.gruul.addon.platform.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.addon.platform.model.vo.CategoryVO;
import com.medusa.gruul.addon.platform.model.vo.SigningCategoryVO;
import com.medusa.gruul.addon.platform.mp.entity.PlatformShopSigningCategory;
import com.medusa.gruul.addon.platform.mp.mapper.PlatformShopSigningCategoryMapper;
import com.medusa.gruul.addon.platform.mp.service.IPlatformShopSigningCategoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 平台店铺签约类目持久层
 *
 * <AUTHOR>
 * @Description PlatformShopSigningCategoryServiceImpl.java
 * @date 2023-05-15 10:09
 */
@Service
public class PlatformShopSigningCategoryServiceImpl extends ServiceImpl<PlatformShopSigningCategoryMapper, PlatformShopSigningCategory> implements IPlatformShopSigningCategoryService {
    @Override
    public List<SigningCategoryVO> getSigningCategoryListByShopId(Long shopId,boolean match) {
        return this.baseMapper.querySigningCategoryListByShopId(shopId,match);
    }

    @Override
    public List<CategoryVO> getChoosableCategoryInfo(Long shopId) {
        return this.baseMapper.queryChoosableCategoryInfo(shopId);
    }


}
