package com.medusa.gruul.addon.platform.addon;


import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.vo.PlatformCategoryVo;
import com.medusa.gruul.goods.api.model.vo.SupplierGoodsSkuVO;
import com.medusa.gruul.shop.api.model.dto.SigningCategoryDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

public interface PlatformAddonProvider {

    /**
     *
     * com.medusa.gruul.goods.service.addon.GoodsAddonSupporter
     * @param status 商品状态
     *
     * @return 修改后商品状态
     */
    ProductStatus getProductStatus(ProductStatus status);

    /**
     * 根据三级类目id获取对应的一级、二级类目id
     * @param platformCategoryIdSet 三级类目id
     * @return PlatformCategoryVo.java
     */
    PlatformCategoryVo getPlatformCategoryVoByLevel3Id(Set<Long> platformCategoryIdSet);


    /**
     * 保存店铺平台签约类目
     *
     * @param  signingCategories 签约类目
     * @param  shopId 店铺id
     * @return 是否成功
     */
    Boolean  editPlatformShopSigningCategory(List<SigningCategoryDTO> signingCategories,Long shopId);


    /**
     *  根据平台二级类目获取之定义扣率
     *
     * @param platformTwoCategory 平台二级类目
     * @param shopId 店铺id
     * @return 自定义类目扣率
     */
    Long getCustomDeductionRatio(Long platformTwoCategory,Long shopId);


    /**
     * 获取店铺签约类目ids
     *
     * @param shopId 店铺id
     * @return 签约二级类目ids
     */
    Set<Long> getSigningCategoryIds(Long shopId);

    /**
     * 查询产品所有sku价格信息
     *
     * @param productId  产品id
     * @return 商品sku 价格信息
     */
    List<SupplierGoodsSkuVO> getSupperSkuByProductId(Long productId, Long shopId);
}
