package com.medusa.gruul.addon.platform;

import com.medusa.gruul.global.model.constant.AspectOrder;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 平台端服务插件
 *
 * <AUTHOR>
 * date 2022/4/14
 */
@SpringBootApplication
@EnableCaching(order = AspectOrder.CACHE_ASPECT)
@EnableDubbo(scanBasePackages = "com.medusa.gruul.addon.platform.addon.impl")
@EnableTransactionManagement(order = AspectOrder.TRANSACTIONAL_ASPECT)
public class AddonPlatformApplication {
    public static void main(String[] args) {
        SpringApplication.run(AddonPlatformApplication.class, args);
    }
}
