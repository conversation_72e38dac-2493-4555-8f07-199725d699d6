package com.medusa.gruul.addon.platform.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.addon.platform.mp.entity.PlatformDecoration;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.system.model.model.Platform;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Description
 * @date 2022-11-01 19:46
 */
public interface PlatformDecorationMapper extends BaseMapper<PlatformDecoration> {

    /**
     * 查询聚合类型页面名称
     * @return  List<AggregationDecorationVO>
     */
    List<AggregationDecorationVO>  queryAggregationPlatformDecorate();
}
