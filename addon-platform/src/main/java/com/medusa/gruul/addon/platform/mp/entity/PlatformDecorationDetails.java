package com.medusa.gruul.addon.platform.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.custom.aggregation.decoration.entity.DecorateDetailsEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 平台装修详细信息
 *
 * <AUTHOR>
 * @Description PlatformDecorationDetails.java
 * @date 2022-10-31 20:54
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_platform_decoration_details")
public class PlatformDecorationDetails extends DecorateDetailsEntity {

    @TableField("platform_decoration_id")
    private Long platformDecorationId;


    /**
     * 是否为同城
     */
    @TableField("is_local")
    private Boolean isLocal;
}
