package com.medusa.gruul.addon.platform.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.addon.platform.functions.PlatformCategoryQueryFunction;
import com.medusa.gruul.addon.platform.model.dto.CategoryDTO;
import com.medusa.gruul.addon.platform.model.dto.PlatformCategoryRankDTO;
import com.medusa.gruul.addon.platform.model.dto.PlatformCategorySortDTO;
import com.medusa.gruul.addon.platform.model.parpm.CategoryParam;
import com.medusa.gruul.addon.platform.model.vo.CategoryFirstIdWithNumVO;
import com.medusa.gruul.addon.platform.model.vo.CategoryThirdlyVO;
import com.medusa.gruul.addon.platform.model.vo.CategoryVO;
import com.medusa.gruul.addon.platform.mp.entity.PlatformCategory;
import com.medusa.gruul.addon.platform.mp.entity.PlatformShopSigningCategory;
import com.medusa.gruul.addon.platform.mp.mapper.CategoryMapper;
import com.medusa.gruul.addon.platform.mp.service.IPlatformCategoryService;
import com.medusa.gruul.addon.platform.mp.service.IPlatformShopSigningCategoryService;
import com.medusa.gruul.addon.platform.service.PlatformCategoryService;
import com.medusa.gruul.common.custom.aggregation.classify.dto.CategoryRankDTO;
import com.medusa.gruul.common.custom.aggregation.classify.enums.CategoryLevel;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.mp.page.PageBean;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.goods.api.model.vo.ApiProductVO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.search.api.enums.SearchRabbit;
import com.medusa.gruul.search.api.model.NestedCategory;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;


/**
 * @Description: 类目信息 ServiceImpl
 * @Author: xiaoq
 * @Date : 2022-04-18 15:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformCategoryServiceImpl implements PlatformCategoryService {


    private final CategoryMapper categoryMapper;
    private final IPlatformCategoryService platformCategoryService;
    private final GoodsRpcService goodsRpcService;
    private final PlatformCategoryQueryFunction categoryQueryFunction;
    private final RabbitTemplate rabbitTemplate;
    private final IPlatformShopSigningCategoryService platformShopSigningCategoryService;
    private final ShopRpcService shopRpcService;

    /**
     * 一级类目新增,先判断一级类目名称是否已存在
     *
     * @param categoryDto 类目dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCategory(CategoryDTO categoryDto) {
        categoryDto.paramCheck();
        categoryDto.save(platformCategoryService);
    }


    @Override
    public void updateCategory(CategoryDTO categoryDto) {
        categoryDto.paramCheck();
        categoryDto.saveOrUpdate(platformCategoryService, categoryDto.getId());
    }


    /**
     * 类目删除 有子类目删子类目
     *
     * @param id 类目id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(Long id) {
        PlatformCategory platformCategory = categoryMapper.selectById(id);
        if (platformCategory == null) {
            throw new ServiceException("当前分类不存在", SystemCode.DATA_EXISTED.getCode());
        }
        List<Long> ids;
        NestedCategory nestedCategory = new NestedCategory();
        // 判断删除类目是否为一级类目 如为一级类目同步删除该一级类目下的二级类目 及三级类目
        if (platformCategory.getLevel() == CategoryLevel.LEVEL_1) {
            nestedCategory.setOne(id);
            List<PlatformCategory> categories = categoryMapper.selectList(new QueryWrapper<PlatformCategory>().eq("parent_id", id));
            ids = categories.stream().map(PlatformCategory::getId).collect(Collectors.toList());
            // 判断是否有签约类目
            if (CollUtil.isNotEmpty(ids)) {
                isCategorySigned(ids);
                //删除三级类目
                categoryMapper.delete(new QueryWrapper<PlatformCategory>().in("parent_id", ids));
                //删除二级类目
                categoryMapper.delete(new QueryWrapper<PlatformCategory>().eq("parent_id", id));
            }
        }
        //判断删除类目是否为二级类目 删除二级类目下的三级类目
        if (platformCategory.getLevel() == CategoryLevel.LEVEL_2) {
            nestedCategory.setTwo(id);
            isCategorySigned(Stream.of(id).collect(Collectors.toList()));
            categoryMapper.delete(new QueryWrapper<PlatformCategory>().eq("parent_id", id));
        }
        if (platformCategory.getLevel() == CategoryLevel.LEVEL_3) {
            PlatformCategory thirdLevelCategory = categoryMapper.selectById(id);
            isCategorySigned(Stream.of(thirdLevelCategory.getParentId()).collect(Collectors.toList()));
            nestedCategory.setThree(id);
        }
        //删除自身分类
        categoryMapper.deleteById(id);
        rabbitTemplate.convertAndSend(SearchRabbit.EXCHANGE, SearchRabbit.CLASSIFY_REMOVE.routingKey(), nestedCategory);

    }


    /**
     * 获取类目信息
     *
     * @param page 分页对象
     * @return IPage<CategoryVO>
     */
    @Override
    public IPage<CategoryVO> getCategoryList(Page<?> page) {
        IPage<CategoryVO> categoryList = categoryMapper.getCategoryList(page);
        if (CollUtil.isEmpty(categoryList.getRecords())) {
            return categoryList;
        }
        // 获取当前类目下所有三级类目
        Set<CategoryThirdlyVO> categoryThirdlyVos = categoryList.getRecords()
                .stream()
                .filter(secondCategory -> CollUtil.isNotEmpty(secondCategory.getSecondCategoryVos()))
                .flatMap(secondCategory -> secondCategory.getSecondCategoryVos().stream())
                .filter(categoryThirdly -> CollUtil.isNotEmpty(categoryThirdly.getCategoryThirdlyVos()))
                .flatMap(categoryThirdly -> categoryThirdly.getCategoryThirdlyVos().stream())
                .collect(Collectors.toSet());
        // stream 得到当前类目的三级类目id
        Set<Long> thirdIds = categoryThirdlyVos.stream().map(CategoryThirdlyVO::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(thirdIds)) {
            return categoryList;
        }
        // 获取三级分类对应的商品数量
        Map<Long, Integer> productNumMap = goodsRpcService.getProductNumByPlatformThirdCategoryId(thirdIds);
        categoryThirdlyVos.forEach(categoryThirdlyVo -> {
            categoryThirdlyVo.setProductNumber(productNumMap.get(categoryThirdlyVo.getId()));
        });
        return categoryList;
    }


    /**
     * 修改平台类目排序  ORDER BY FIELD (id 按id 顺序排序
     *
     * @param platformCategorySortDto platformCategorySortDto
     */
    @Override
    public void updatePlatformCategorySort(PlatformCategorySortDTO platformCategorySortDto) {
        List<Long> sortedIds = platformCategorySortDto.getSortedIds();
        List<PlatformCategory> categories = platformCategoryService.lambdaQuery().in(PlatformCategory::getId, sortedIds)
                .eq(PlatformCategory::getParentId, platformCategorySortDto.getParentId())
                .last("ORDER BY FIELD (id," + sortedIds.stream().map(String::valueOf)
                        .collect(Collectors.joining(",")) + ")")
                .list();
        IntStream.range(0, categories.size()).forEach(
                index -> categories.get(index).setSort(index)
        );
        platformCategoryService.updateBatchById(categories);

    }

    /**
     * 获取指定level 等级类目
     *
     * @param categoryParam 查询参数
     * @return IPage<PlatformCategory>
     */
    @Override
    public IPage<PlatformCategory> getCategoryListByLevel(CategoryParam categoryParam) {
        return categoryMapper.selectPage(categoryParam, new QueryWrapper<PlatformCategory>()
                .eq("level", categoryParam.getLevel())
                .eq("parent_id", categoryParam.getParentId()));
    }

    @Override
    public List<Long> getLevelCategoryList(Long platformCategoryId) {
        return TenantShop.disable(() -> categoryMapper.getLevelCategoryList(platformCategoryId));
    }

    @Override
    public List<PlatformCategory> getCategoryInfoByIds(PlatformCategoryRankDTO categoryRank) {
        List<PlatformCategory> categories = platformCategoryService.lambdaQuery()
                .eq(PlatformCategory::getParentId, CommonPool.NUMBER_ZERO)
                .eq(PlatformCategory::getLevel, CategoryLevel.LEVEL_1)
                .last("ORDER BY FIELD (id," + categoryRank.getIds().stream().map(String::valueOf).collect(Collectors.joining(",")) + ")")
                .in(BaseEntity::getId, categoryRank.getIds())
                .list();
        //查询出的一级分类列表
        if (CollUtil.isEmpty(categories)) {
            return categories;
        }
        List<PlatformCategory> categoriesSecond = categoryQueryFunction.setChildren(categories, CategoryLevel.LEVEL_2);

        //三级分类列表
        if (CollUtil.isEmpty(categoriesSecond)) {
            return categories;
        }
        if (categoryRank.getCategoryLevel() == CategoryLevel.LEVEL_3) {
            categoryQueryFunction.setChildren(categoriesSecond, CategoryLevel.LEVEL_3);
        }
        return categories;
    }

    @Override
    public IPage<CategoryFirstIdWithNumVO> getCategoryFirstIdWithNumVO(Page<PlatformCategory> page) {
        Page<PlatformCategory> platformCategoryPage = platformCategoryService.lambdaQuery()
                .select(PlatformCategory::getId, PlatformCategory::getName)
                .eq(PlatformCategory::getLevel, CategoryLevel.LEVEL_1)
                .page(page);
        List<PlatformCategory> platformCategoryList = platformCategoryPage.getRecords();

        if (CollUtil.isEmpty(platformCategoryList)) {
            return null;
        }
        List<CategoryFirstIdWithNumVO> categoryFirstIdWithNums = platformCategoryList.stream().map(platformCategory -> {
            CategoryFirstIdWithNumVO categoryFirstIdWithNumVO = new CategoryFirstIdWithNumVO();
            categoryFirstIdWithNumVO.setPlatformCategoryFirstId(platformCategory.getId());
            categoryFirstIdWithNumVO.setPlatformCategoryFirstName(platformCategory.getName());
            List<Long> thirdIds = TenantShop.disable(() -> categoryMapper.getLevelCategoryList(platformCategory.getId()));
            int productNum = 0;
            if (CollUtil.isNotEmpty(thirdIds)) {
                Map<Long, Integer> productNumMap = goodsRpcService.getProductNumByPlatformThirdCategoryId(new HashSet<>(thirdIds));
                for (Long thirdId : thirdIds) {
                    Integer num = productNumMap.get(thirdId);
                    productNum += num != null ? num : 0;
                }
            }
            categoryFirstIdWithNumVO.setProductNum(productNum);
            return categoryFirstIdWithNumVO;
        }).collect(Collectors.toList());
        return PageBean.getPage(page, categoryFirstIdWithNums, platformCategoryPage.getTotal(), platformCategoryPage.getPages());


    }

    @Override
    public Page<ApiProductVO> getProductInfoByPlatformCategoryId(PlatformCategoryRankDTO categoryRank) {
        Set<Long> ids = null;
        switch (categoryRank.getCategoryLevel()) {
            case LEVEL_1:
                // 如果当前等级为一级类目，获取对应的三级平台类目ID
                ids = getThirdLevelPlatformCategoryIds(categoryRank.getIds());
                break;
            case LEVEL_2:
                // 如果当前等级为二级类目，获取二级以下所有的三级平台类目ID
                List<PlatformCategory> secondLevelCategories = platformCategoryService.lambdaQuery()
                        .in(PlatformCategory::getId, categoryRank.getIds())
                        .list();
                if (CollUtil.isEmpty(secondLevelCategories)) {
                    return null;
                }
                List<Long> secondLevelIds = secondLevelCategories.stream()
                        .map(BaseEntity::getId)
                        .collect(Collectors.toList());
                List<PlatformCategory> thirdLevelCategories = platformCategoryService.lambdaQuery()
                        .in(PlatformCategory::getParentId, secondLevelIds)
                        .list();
                if (CollUtil.isEmpty(thirdLevelCategories)) {
                    return null;
                }
                ids = thirdLevelCategories.stream()
                        .map(BaseEntity::getId)
                        .collect(Collectors.toSet());
                break;
            case LEVEL_3:
                ids = categoryRank.getIds();
                break;
            default:
                // 其他等级不支持，返回null
                return null;
        }

        if (CollUtil.isEmpty(ids)) {
            return null;
        }
        CategoryRankDTO productCategoryRank = new CategoryRankDTO();
        productCategoryRank.setIds(ids);
        productCategoryRank.setSize(categoryRank.getSize());
        productCategoryRank.setCurrent(categoryRank.getCurrent());

        return goodsRpcService.getApiProductInfoByPlatformCategoryId(productCategoryRank);
    }

    private Set<Long> getThirdLevelPlatformCategoryIds(Collection<Long> categoryIds) {
        // 获取一级类目
        List<PlatformCategory> firstLevelCategories = platformCategoryService.lambdaQuery()
                .in(BaseEntity::getId, categoryIds)
                .list();

        if (CollUtil.isEmpty(firstLevelCategories)) {
            return null;
        }
        List<Long> collect = firstLevelCategories.stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toList());
        // 获取二级类目ID
        List<Long> secondLevelIds = platformCategoryService.lambdaQuery()
                .in(PlatformCategory::getParentId, collect)
                .list()
                .stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(secondLevelIds)) {
            return null;
        }

        // 获取三级类目
        List<PlatformCategory> thirdLevelCategories = platformCategoryService.lambdaQuery()
                .in(PlatformCategory::getParentId, secondLevelIds)
                .list();
        if (CollUtil.isEmpty(thirdLevelCategories)) {
            return null;
        }
        return thirdLevelCategories.stream()
                .map(BaseEntity::getId)
                .collect(Collectors.toSet());
    }


    private void isCategorySigned(List<Long> ids) {
        // 判断是否有签约类目
        List<PlatformShopSigningCategory> list = platformShopSigningCategoryService.lambdaQuery()
                .in(PlatformShopSigningCategory::getCurrentCategoryId, ids)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            /*  获取相关shopId
             */
            Set<Long> shopIds = list.stream()
                    .map(PlatformShopSigningCategory::getShopId)
                    .collect(Collectors.toSet());
            log.warn("当前签约类目信息   :".concat(shopIds.toString()));
            List<ShopInfoVO> shopInfoByShopIdList = shopRpcService.getShopInfoByShopIdList(shopIds);
            String shopNames = shopInfoByShopIdList.stream()
                    .map(ShopInfoVO::getName)
                    .collect(Collectors.joining(", "));
            throw new GlobalException("当前类目已有店铺,或供应商签约，请先解除签约类目再删除：" + shopNames);
        }
    }
}
