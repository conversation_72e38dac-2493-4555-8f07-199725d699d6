package com.medusa.gruul.addon.platform.addon.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.medusa.gruul.addon.platform.addon.PlatformAddonProvider;
import com.medusa.gruul.addon.platform.addon.PlatformAddonSupporter;
import com.medusa.gruul.addon.platform.mp.entity.PlatformCategory;
import com.medusa.gruul.addon.platform.mp.entity.PlatformShopSigningCategory;
import com.medusa.gruul.addon.platform.mp.service.IPlatformCategoryService;
import com.medusa.gruul.addon.platform.mp.service.IPlatformShopSigningCategoryService;
import com.medusa.gruul.addon.platform.service.SigningCategoryService;
import com.medusa.gruul.common.addon.provider.AddonProvider;
import com.medusa.gruul.common.addon.provider.AddonProviders;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.global.model.constant.Services;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.vo.PlatformCategoryVo;
import com.medusa.gruul.goods.api.model.vo.SupplierGoodsSkuVO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.shop.api.model.dto.SigningCategoryDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@AddonProviders
@DubboService
@Service
@RequiredArgsConstructor
public class PlatformAddonProviderImpl implements PlatformAddonProvider {

    private final IPlatformCategoryService platformCategoryService;

    private final IPlatformShopSigningCategoryService platformShopSigningCategoryService;


    private final GoodsRpcService goodsRpcService;

    private final PlatformAddonSupporter platformAddonSupporter;

    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getProductStatus", order = 0)
    public ProductStatus getProductStatus(ProductStatus status) {
        log.debug("PlatformAddonProviderImpl invoking " + status.name());
        return ProductStatus.SELL_ON;
    }

    /**
     * 根据三级类目id获取对应的一级、二级类目id
     *
     * @param platformCategoryIdSet 三级类目id
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getPlatformCategoryVoByLevel3Id", order = 1)
    public PlatformCategoryVo getPlatformCategoryVoByLevel3Id(Set<Long> platformCategoryIdSet) {
        Map<Long, Long> platformCategories = getPlatformCategories(platformCategoryIdSet);
        if (platformCategories == null) {
            return null;
        }
        PlatformCategoryVo platformCategoryVo = new PlatformCategoryVo();
        platformCategoryVo.setThirdSecondMap(platformCategories);
        Map<Long, Long> secondFirstMap = getPlatformCategories(platformCategories.values());
        platformCategoryVo.setSecondFirstMap(secondFirstMap);
        return platformCategoryVo;

    }


    /**
     * 签约类目信息保存
     *
     * @param signingCategories 签约类目
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @AddonProvider(service = Services.GRUUL_MALL_SHOP, supporterId = "shopSigningCategory", methodName = "editSingingCategory", order = 1)
    public Boolean editPlatformShopSigningCategory(List<SigningCategoryDTO> signingCategories, Long shopId) {

        List<PlatformShopSigningCategory> existingSigningCategories = platformShopSigningCategoryService.lambdaQuery()
                .eq(PlatformShopSigningCategory::getShopId, shopId)
                .list();
        if (CollUtil.isNotEmpty(existingSigningCategories)) {
            // 删除逻辑
            List<PlatformShopSigningCategory> platformShopSigningCategories = existingSigningCategories.stream().filter(existingSigningCategory -> {
                return signingCategories.stream()
                        .noneMatch(bean -> bean.getId() != null && bean.getId().equals(existingSigningCategory.getId()));
            }).toList();

            if (CollUtil.isNotEmpty(platformShopSigningCategories)) {
                Set<Long> deleteSigningCategoryIds = platformShopSigningCategories.stream().map(PlatformShopSigningCategory::getCurrentCategoryId).collect(Collectors.toSet());
                boolean signingCategoryProduct = goodsRpcService.getSigningCategoryProduct(deleteSigningCategoryIds, shopId);
                if (signingCategoryProduct) {
                    throw new GlobalException("当前删除签约类目下存在商品请联系商家删除");
                }
                Boolean supplierSigningCategoryProduct = platformAddonSupporter.getSupplierSigningCategoryProduct(deleteSigningCategoryIds, shopId);
                if (supplierSigningCategoryProduct != null && supplierSigningCategoryProduct) {
                    throw new GlobalException("当前删除签约类目下存在商品请联系供应商删除");
                }

                platformShopSigningCategoryService.removeByIds(deleteSigningCategoryIds);
            }
        }

        //新增
        List<PlatformShopSigningCategory> addSigningCategory = signingCategories.stream()
                .filter(bean -> bean.getId() == null)
                .map(bean -> convertToPlatformShopSigningCategory(bean, shopId))
                .collect(Collectors.toList());
        platformShopSigningCategoryService.saveBatch(addSigningCategory);

        //修改
        List<PlatformShopSigningCategory> updateSigningCategory = signingCategories.stream()
                .filter(bean -> bean.getId() != null)
                .map(bean -> {
                    PlatformShopSigningCategory platformShopSigningCategory =
                            convertToPlatformShopSigningCategory(bean, shopId);
                    platformShopSigningCategory.setId(bean.getId());
                    return platformShopSigningCategory;
                })
                .collect(Collectors.toList());
        platformShopSigningCategoryService.updateBatchById(updateSigningCategory);
        if (CollUtil.isNotEmpty(updateSigningCategory)){
            // 修改签约类目 应通知商品修改类目信息
            log.error("修改签约类目");
        }


        return Boolean.TRUE;
    }

    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getCustomDeductionRatio", order = 0)
    public Long getCustomDeductionRatio(Long platformTwoCategory, Long shopId) {
        PlatformShopSigningCategory shopSigningCategory = platformShopSigningCategoryService.lambdaQuery()
                .select(PlatformShopSigningCategory::getCustomDeductionRatio, BaseEntity::getId)
                .eq(PlatformShopSigningCategory::getCurrentCategoryId, platformTwoCategory)
                .eq(PlatformShopSigningCategory::getShopId, shopId).one();
        if (shopSigningCategory == null) {
            throw new GlobalException("当前类目暂未无签约,请刷新后选择");
        }
        return shopSigningCategory.getCustomDeductionRatio();
    }

    /**
     * 获取店铺签约类目ids
     *
     * @param shopId 店铺id
     * @return 二级类目ids
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getSigningCategoryIds", order = 0)
    public Set<Long> getSigningCategoryIds(Long shopId) {
        List<PlatformShopSigningCategory> platformShopSigningCategories =
                platformShopSigningCategoryService.lambdaQuery()
                        .eq(PlatformShopSigningCategory::getShopId, shopId)
                        .list();
        if (CollUtil.isNotEmpty(platformShopSigningCategories)) {
            return platformShopSigningCategories.stream()
                    .map(PlatformShopSigningCategory::getCurrentCategoryId)
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getSupperSkuByProductId", order = 0)
    public List<SupplierGoodsSkuVO> getSupperSkuByProductId(Long productId, Long shopId) {
        return goodsRpcService.getSupperSkuByProductId(productId, shopId);
    }

    private PlatformShopSigningCategory convertToPlatformShopSigningCategory(SigningCategoryDTO bean, Long shopId) {
        return new PlatformShopSigningCategory()
                .setShopId(shopId)
                .setParentId(bean.getParentId())
                .setCurrentCategoryId(bean.getCurrentCategoryId())
                .setCustomDeductionRatio(bean.getCustomDeductionRatio());
    }

    private Map<Long, Long> getPlatformCategories(Collection<Long> platformCategoryIdSet) {
        return CollectionUtil.emptyIfNull(platformCategoryService.lambdaQuery()
                .select(PlatformCategory::getId, PlatformCategory::getParentId)
                .in(PlatformCategory::getId, platformCategoryIdSet).
                list()).stream().collect(Collectors.toMap(PlatformCategory::getId, PlatformCategory::getParentId));
    }
}
