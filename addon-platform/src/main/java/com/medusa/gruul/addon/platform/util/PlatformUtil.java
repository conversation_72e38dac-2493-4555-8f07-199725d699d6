package com.medusa.gruul.addon.platform.util;

import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.model.Platform;

/**
 * <AUTHOR>
 *
 * @Description 平台工具类
 * @date 2022-11-02 18:51
 */
public class PlatformUtil {

    private PlatformUtil() {

    }

    public static String platformCacheKey(AggregationPlatform platforms) {
        return RedisUtil.key("platform:decoration:page", platforms);

    }


}
