package com.medusa.gruul.addon.platform.model.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.addon.platform.mp.entity.PlatformCategory;
import com.medusa.gruul.addon.platform.mp.service.IPlatformCategoryService;
import com.medusa.gruul.common.custom.aggregation.classify.enums.CategoryLevel;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description: 类目DTO
 * @Author: xiaoq
 * @Date : 2022-04-18 14:29
 */
@Getter
@Setter
@ToString
public class CategoryDTO {

    private Long id;

    /**
     * 上机分类的编号：0表示一级分类
     */
    @NotNull
    private Long parentId;

    /**
     * 分类名称
     */
    @NotNull
    @Size(min = 1)
    private List<PlatformCategoryNameImgDTO> nameImgs;

    /**
     * 分类级别：0->1级；1->2级 ; 2>3级
     */
    @NotNull
    private CategoryLevel level;


    /**
     * 修改编辑分类
     */
    public void update(IPlatformCategoryService categoryService, Long categoryId) {
        PlatformCategoryNameImgDTO categoryNameImg = this.getNameImgs().iterator().next();
        String name = categoryNameImg.getName();
        boolean exists = categoryService.lambdaQuery()
                .eq(PlatformCategory::getName, name)
                .eq(PlatformCategory::getParentId, getParentId())
                .eq(PlatformCategory::getDeleted, 0)
                .ne(PlatformCategory::getId, categoryId).exists();
        if (exists) {
            throw new ServiceException("同级分类下,已存在同名分类", SystemCode.DATA_UPDATE_FAILED_CODE);
        }
        PlatformCategory category = categoryService.getById(categoryId);
        if (category == null) {
            throw new ServiceException("未查到对应数据", SystemCode.DATA_NOT_EXIST_CODE);
        }
        category.setParentId(this.getParentId())
                .setName(name)
                .setDeductionRatio(categoryNameImg.getDeductionRatio())
                .setCategoryImg(categoryNameImg.getCategoryImg());
        boolean success = categoryService.updateById(category);
        if (!success) {
            throw new ServiceException("更新数据失败", SystemCode.DATA_UPDATE_FAILED_CODE);
        }
    }


    /**
     * 新增分类
     */
    public void save(IPlatformCategoryService categoryService) {
        boolean exists = categoryService.lambdaQuery()
                .in(PlatformCategory::getName, getNameImgs().stream().map(PlatformCategoryNameImgDTO::getName).collect(Collectors.toList()))
                .eq(PlatformCategory::getParentId, getParentId())
                .exists();
        if (exists) {
            throw new ServiceException("同级分类下,已存在同名分类", SystemCode.DATA_ADD_FAILED_CODE);
        }

        Long parentId = this.getParentId();
        Long count = categoryService.lambdaQuery()
                .eq(PlatformCategory::getParentId, parentId)
                .count();
        final AtomicInteger sort = new AtomicInteger(count == null ? 0 : count.intValue() + 1);
        /*
         * 批量保存
         */
        boolean success = categoryService.saveBatch(
                this.getNameImgs().stream().map(
                        nameImg -> {
                            PlatformCategory category = new PlatformCategory()
                                    .setParentId(parentId)
                                    .setName(nameImg.getName())
                                    .setSort(sort.get())
                                    .setLevel(this.getLevel())
                                    .setDeductionRatio(CategoryLevel.LEVEL_2 == this.level ? nameImg.getDeductionRatio() : null)
                                    .setCategoryImg(CategoryLevel.LEVEL_1 != this.getLevel() ? nameImg.getCategoryImg() : null);
                            sort.incrementAndGet();
                            return category;
                        }
                ).collect(Collectors.toList())
        );
        if (!success) {
            throw new ServiceException("保存数据失败", SystemCode.DATA_ADD_FAILED_CODE);
        }
    }


    /**
     * 保存或更新
     */
    public void saveOrUpdate(IPlatformCategoryService categoryService, Long id) {
        boolean isEdit = id != null;
        if (isEdit) {
            this.update(categoryService, id);
        } else {
            this.save(categoryService);
        }


    }


    /**
     * 检查参数
     */
    public void paramCheck() {
        switch (level) {
            case LEVEL_1:
                this.setParentId(0L);
                break;
            case LEVEL_2:
                List<PlatformCategoryNameImgDTO> deductionRatioList = getNameImgs();
                deductionRatioList.forEach(
                        ratio -> {
                            Long deductionRatio = ratio.getDeductionRatio();
                            if (deductionRatio == null) {
                                throw new ServiceException("二级类目倍率不可为空");
                            }
                            String categoryImg = ratio.getCategoryImg();
                            if (StrUtil.isEmpty(categoryImg)) {
                                throw new ServiceException("categoryImg不能为空", SystemCode.PARAM_VALID_ERROR_CODE);
                            }
                            if (!ReUtil.isMatch(RegexPool.URL_HTTP, categoryImg)) {
                                throw new ServiceException("不符合格式要求", SystemCode.PARAM_VALID_ERROR_CODE);
                            }
                        }
                );
                break;
            case LEVEL_3:
                List<PlatformCategoryNameImgDTO> nameImgs = getNameImgs();
                nameImgs.forEach(
                        nameImg -> {
                            String categoryImg = nameImg.getCategoryImg();
                            if (StrUtil.isEmpty(categoryImg)) {
                                throw new ServiceException("categoryImg不能为空", SystemCode.PARAM_VALID_ERROR_CODE);
                            }
                            if (!ReUtil.isMatch(RegexPool.URL_HTTP, categoryImg)) {
                                throw new ServiceException("不符合格式要求", SystemCode.PARAM_VALID_ERROR_CODE);
                            }
                        }
                );
            default:
                break;
        }
    }

    public PlatformCategory coverCategory() {
        PlatformCategory category = new PlatformCategory();
        BeanUtil.copyProperties(this, category);
        return category;
    }
}
