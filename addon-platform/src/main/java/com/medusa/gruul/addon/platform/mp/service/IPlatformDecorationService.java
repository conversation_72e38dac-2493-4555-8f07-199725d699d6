package com.medusa.gruul.addon.platform.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.addon.platform.mp.entity.PlatformDecoration;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.system.model.model.Platform;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Description
 * @date 2022-11-01 19:45
 */
public interface IPlatformDecorationService extends IService<PlatformDecoration> {


    /**
     * 获取聚合装修信息 类型+首页名称
     *
     * @return List<AggregationDecorationVO>
     */
    List<AggregationDecorationVO>  getAggregationPlatformDecorate();
}
