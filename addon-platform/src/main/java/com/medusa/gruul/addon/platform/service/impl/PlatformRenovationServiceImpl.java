package com.medusa.gruul.addon.platform.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.addon.platform.addon.PlatformAddonSupporter;
import com.medusa.gruul.addon.platform.mp.entity.PlatformDecoration;
import com.medusa.gruul.addon.platform.mp.entity.PlatformDecorationDetails;
import com.medusa.gruul.addon.platform.mp.service.IPlatformDecorationDetailService;
import com.medusa.gruul.addon.platform.mp.service.IPlatformDecorationService;
import com.medusa.gruul.addon.platform.service.PlatformRenovationService;
import com.medusa.gruul.addon.platform.util.PlatformUtil;
import com.medusa.gruul.common.custom.aggregation.decoration.dto.FunctionDecorationDTO;
import com.medusa.gruul.common.custom.aggregation.decoration.entity.DecorateDetailsEntity;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.FunctionType;
import com.medusa.gruul.common.custom.aggregation.decoration.util.AggregationPlatformUtil;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.AggregationDecorationVO;
import com.medusa.gruul.common.custom.aggregation.decoration.vo.PropertiesVO;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.SupplierGoodsSku;
import com.medusa.gruul.goods.api.model.param.ProductRenderParam;
import com.medusa.gruul.goods.api.model.vo.ProductRenderVO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.model.vo.ShopRenderVo;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import io.vavr.control.Option;
import jodd.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description PlatformRenovationServiceImpl.java
 * @date 2022-11-01 09:10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PlatformRenovationServiceImpl implements PlatformRenovationService {

    private final IPlatformDecorationDetailService platformDecorationDetailService;

    private final IPlatformDecorationService platformDecorationService;

    private final PlatformAddonSupporter platformAddonSupporter;
    private final GoodsRpcService goodsRpcService;

    private final ShopRpcService shopRpcService;

    /**
     * 删除装修页面
     *
     * @param ids 装修页面ids
     */
    @Override
    public void delDecorationPage(Long[] ids, AggregationPlatform aggregationPlatform) {
        // 无论删除页面是否为首页都清空redis
        Boolean flag = RedisUtil.doubleDeletion(
                () -> platformDecorationDetailService.lambdaUpdate()
                        .in(BaseEntity::getId, Arrays.asList(ids))
                        .eq(DecorateDetailsEntity::getFunctionType, FunctionType.PAGE)
                        .remove(),
                () -> RedisUtil.delete(PlatformUtil.platformCacheKey(aggregationPlatform)));
        if (!flag) {
            throw new ServiceException("页面删除失败");
        }
    }

    /**
     * 修改页面名称
     *
     * @param name 修改名称
     * @param id   修改页面id
     */
    @Override
    public void updatePageName(String name, Long id) {
        boolean update = platformDecorationDetailService.lambdaUpdate()
                .eq(BaseEntity::getId, id)
                .eq(DecorateDetailsEntity::getFunctionType, FunctionType.PAGE)
                .set(DecorateDetailsEntity::getPageName, name)
                .update();
        if (!update) {
            throw new ServiceException("页面名称修改失败");
        }
    }

    /**
     * 获取装修数据
     *
     * @param id 装修详情id
     * @return PropertiesVO.java
     */
    @Override
    public PropertiesVO getDecorationInfoById(Long id) {
        PlatformDecorationDetails platformDecorationDetails = platformDecorationDetailService.lambdaQuery()
                .select(DecorateDetailsEntity::getProperties)
                .eq(BaseEntity::getId, id).one();
        return assemblingPropertiesVO(platformDecorationDetails);
    }


    /**
     * 编辑装修功能
     *
     * @param functionDecoration FunctionDecorationDTO.java
     */
    @Override
    public Long editFunctionDecoration(FunctionDecorationDTO functionDecoration) {
        PlatformDecoration platformDecoration = platformDecorationInit(functionDecoration.getPlatforms());
        PlatformDecorationDetails platformDecorationDetails = new PlatformDecorationDetails();
        String properties = JSON.toJSONString(functionDecoration.getProperties());
        // id为null 新增装修功能
        if (functionDecoration.getId() == null) {
            Long count = platformDecorationDetailService.lambdaQuery()
                    .eq(DecorateDetailsEntity::getFunctionType, functionDecoration.getFunctionType())
                    .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId()).count();
            platformDecorationDetails
                    .setFunctionType(functionDecoration.getFunctionType())
                    .setPageName(functionDecoration.getPageName() != null ? functionDecoration.getPageName() : "自定义页面")
                    .setProperties(properties)
                    .setIsDef(count < CommonPool.NUMBER_ONE ? Boolean.TRUE : Boolean.FALSE);
            platformDecorationDetails.setPlatformDecorationId(platformDecoration.getId());
            if (functionDecoration.getFunctionType() != FunctionType.PAGE && count > CommonPool.NUMBER_ZERO) {
                throw new GlobalException("装修控件重复保存");
            }
            platformDecorationDetailService.save(platformDecorationDetails);
            return platformDecorationDetails.getId();
        }
        /*修改装修信息
         */
        platformDecorationDetails = platformDecorationDetailService.lambdaQuery()
                .eq(BaseEntity::getId, functionDecoration.getId())
                .one();
        if (platformDecorationDetails == null) {
            throw new GlobalException("当前装修信息不存在");
        }
        String pageName = platformDecorationDetails.getPageName();
        Supplier<Boolean> supplier = () -> platformDecorationDetailService.lambdaUpdate()
                .set(DecorateDetailsEntity::getPageName, pageName)
                .set(DecorateDetailsEntity::getProperties, properties)
                .set(functionDecoration.getIsDef() != null, DecorateDetailsEntity::getIsDef, functionDecoration.getIsDef())
                .eq(BaseEntity::getId, functionDecoration.getId())
                .update();

        /*功能类型不为page时 不进行延迟双删
         */
        if (functionDecoration.getFunctionType() != FunctionType.PAGE) {
            if (!supplier.get()) {
                throw new GlobalException("控件更新失败");
            }
            return platformDecorationDetails.getId();
        }
        //延迟双删
        RedisUtil.doubleDeletion(
                supplier,
                () -> RedisUtil.delete(PlatformUtil.platformCacheKey(functionDecoration.getPlatforms()))
        );
        return platformDecorationDetails.getId();
    }


    /**
     * 设置页面为首页
     *
     * @param id                  页面id
     * @param aggregationPlatform 聚合 APP平台类型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setHomePage(Long id, AggregationPlatform aggregationPlatform) {
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        // 修改首页页面信息并清空redis
        RedisUtil.doubleDeletion(() -> platformDecorationDetailService.lambdaUpdate()
                        .set(DecorateDetailsEntity::getIsDef, Boolean.FALSE)
                        .eq(DecorateDetailsEntity::getFunctionType, FunctionType.PAGE)
                        .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId())
                        .eq(DecorateDetailsEntity::getIsDef, Boolean.TRUE)
                        .ne(PlatformDecorationDetails::getIsLocal, Boolean.TRUE)
                        .update(),
                () -> RedisUtil.delete(PlatformUtil.platformCacheKey(aggregationPlatform))
        );
        //设置页面为首页
        platformDecorationDetailService.lambdaUpdate()
                .set(DecorateDetailsEntity::getIsDef, Boolean.TRUE)
                .eq(BaseEntity::getId, id)
                .update();
    }


    /**
     * 获取页面基础信息 list
     *
     * @param aggregationPlatform 聚合APP平台类型
     * @return List<页面基础信息>
     */
    @Override
    public List<PlatformDecorationDetails> getPageBasicsInfoList(AggregationPlatform aggregationPlatform) {
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        return platformDecorationDetailService.lambdaQuery()
                .select(
                        BaseEntity::getId,
                        DecorateDetailsEntity::getFunctionType,
                        DecorateDetailsEntity::getPageName,
                        DecorateDetailsEntity::getIsDef,
                        PlatformDecorationDetails::getIsLocal,
                        BaseEntity::getCreateTime
                )
                .eq(DecorateDetailsEntity::getFunctionType, FunctionType.PAGE)
                .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId())
                .list();
    }

    /**
     * @param aggregationPlatform 聚合平台类型
     * @param functionType        装修功能类型
     * @return PropertiesVO.java
     */
    @Override
    public PropertiesVO getNotPageDecorationInfo(AggregationPlatform aggregationPlatform, FunctionType functionType) {
        if (functionType == FunctionType.PAGE) {
            throw new ServiceException("查询装修功能类型错误");
        }
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        PlatformDecorationDetails platformDecorationDetails = platformDecorationDetailService.lambdaQuery()
                .select(BaseEntity::getId,
                        DecorateDetailsEntity::getProperties)
                .eq(DecorateDetailsEntity::getFunctionType, functionType)
                .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId()).one();
        return assemblingPropertiesVO(platformDecorationDetails);
    }
    /**
     * 获取页面装修数据信息
     *
     * @param aggregationPlatform 聚合平台类型
     * @return PropertiesVO.java
     */
    @Override
    public PropertiesVO getDecorationInfo(AggregationPlatform aggregationPlatform) {
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        //获取首页装修数据
        PlatformDecorationDetails platformDecorationDetails = platformDecorationDetailService.lambdaQuery()
                .eq(DecorateDetailsEntity::getFunctionType, FunctionType.PAGE)
                .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId())
                .eq(DecorateDetailsEntity::getIsDef, Boolean.TRUE).one();
        return assemblingPropertiesVO(platformDecorationDetails);
    }

    /**
     * 获取聚合装修类型及对应的首页名称
     *
     * @param aggregationPlatformList 聚合平台类型
     * @return List<AggregationDecorationVO>
     */
    @Override
    public List<AggregationDecorationVO> getAggregationPlatformDecorate(List<AggregationPlatform> aggregationPlatformList) {
        List<AggregationDecorationVO> aggregationDecorationList = platformDecorationService.getAggregationPlatformDecorate();
        if (aggregationDecorationList.size() < CommonPool.NUMBER_THREE) {
            aggregationDecorationList.clear();
            aggregationPlatformList.forEach(
                    bean -> {
                        PlatformDecoration platformDecoration = platformDecorationInit(bean);
                        aggregationDecorationList.add(new AggregationDecorationVO(platformDecoration.getPlatforms(), "自定义页面"));
                    }
            );
        }
        return aggregationDecorationList;


    }

    /**
     * 获取非装修页面基础信息
     *
     * @param aggregationPlatform 聚合app类型
     * @param functionType        装修功能类型
     * @return PlatformDecorationDetails
     */
    @Override
    public PlatformDecorationDetails getNotPageBasicDecorationInfo(AggregationPlatform aggregationPlatform, FunctionType functionType) {
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        return platformDecorationDetailService.lambdaQuery()
                .select(BaseEntity::getId,
                        DecorateDetailsEntity::getFunctionType,
                        DecorateDetailsEntity::getPageName)
                .eq(DecorateDetailsEntity::getFunctionType, functionType)
                .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId()).one();
    }


    /**
     * 获取首页装修功能详情
     *
     * @return List<Object>
     */
    @Override
    public List<Object> getHomePage() {
        SecureUser user = ISecurity.secureUser();
        Platform platform = ISystem.platformMust();
        AggregationPlatform aggregationPlatform = AggregationPlatformUtil.getAggregationPlatform(platform);
        PlatformDecoration platformDecoration = platformDecorationService.lambdaQuery().eq(PlatformDecoration::getPlatforms, aggregationPlatform).one();
        if (platformDecoration == null) {
            throw new ServiceException("当前装修数据不存在,请联系商户进行设置", SystemCode.DATA_NOT_EXIST_CODE);
        }
        // 获取数据
        String properties = RedisUtil.getCacheMap(
                () -> RedisUtil.getCacheObject(PlatformUtil.platformCacheKey(aggregationPlatform)),
                () -> {
                    PlatformDecorationDetails platformDecorationDetails = platformDecorationDetailService.lambdaQuery()
                            .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId())
                            .eq(DecorateDetailsEntity::getIsDef, Boolean.TRUE)
                            .eq(DecorateDetailsEntity::getFunctionType, FunctionType.PAGE)
                            .one();
                    // 增加判空 预防空指针
                    if (platformDecorationDetails == null) {
                        return null;
                    }
                    return platformDecorationDetails.getProperties();
                },
                Duration.ofSeconds(RedisUtil.expireWithRandom(CommonPool.UNIT_CONVERSION_TEN_THOUSAND)),
                Duration.ofMillis(CommonPool.NUMBER_FIFTEEN),
                PlatformUtil.platformCacheKey(aggregationPlatform)
        );
        return properties != null ? JSON.parseObject(properties, new TypeReference<>() {
        }) : null;
    }

    /**
     * 处理渲染首页的JSON数据
     *
     * @param properties 首页渲染的JSONString
     * @return
     */
    public String handleRenderProperties(String properties, Long userId) {
        final long querySize = 100;
        Long userShopId = 0L;
        ShopInfoVO shopInfoByShopId = new ShopInfoVO();
        //首页店铺配置的商品数量
        int shopGoodsSize = 0;
        List<ProductRenderVO> records = new ArrayList<>();
        List<JSONObject> propertiesList = JSON.parseObject(properties, List.class);
        HashMap<String, Object> goodsProperty = new HashMap<>();
        HashMap<String, Object> lunboProductProperty = new HashMap<>();
        for (int i = 0; i < propertiesList.size(); i++) {
            if ("shangchengshezhi".equals(propertiesList.get(i).get("icon"))) {
                goodsProperty.put("index", i);
                goodsProperty.put("data", propertiesList.get(i));
            }
            if ("lunbotu".equals(propertiesList.get(i).get("icon"))) {
                lunboProductProperty.put("index", i);
                lunboProductProperty.put("data", propertiesList.get(i));
            }
        }
        //处理店铺商品数据
        JSONObject data = (JSONObject) goodsProperty.get("data");
        JSONObject formData = (JSONObject) data.get("formData");
        List<JSONObject> shopInfo = (List<JSONObject>) formData.get("shopInfo");
        if (shopInfo.size() > 0) {
            JSONObject shopInfoData = shopInfo.get(0);
            userShopId = platformAddonSupporter.getUserDistributorShopId(userId);
            //获取店铺信息
            shopInfoByShopId = shopRpcService.getShopInfoByShopId(userShopId);
            ShopRenderVo shopRenderVo = new ShopRenderVo();
            BeanUtils.copyProperties(shopInfoByShopId, shopRenderVo);
            JSONObject shop = JSONObject.from(shopRenderVo);
            shopGoodsSize = ((List<JSONObject>) shopInfoData.get("goods")).size();
            if (shopGoodsSize > 0) {
                ProductRenderParam pageParam = ProductRenderParam.builder().shopId(userShopId).pCurrent(1).pSize(querySize).build();
                Option<IPage<ProductRenderVO>> of = Option.of(ISystem.shopId(pageParam.getShopId(), () -> goodsRpcService.queryProductRenderVO(pageParam)));
                IPage<ProductRenderVO> productRenderVOIPage = of.get();
                records = productRenderVOIPage.getRecords();
                records.forEach(each -> {
                    if (each.getId() != null) {
                        each.setPrice(each.getPrice() / 10000);
                    }
                });
                List<JSONObject> goodsRecords = JSON.parseObject(JSON.toJSONString(records), List.class);
                //店铺下的所有商品数量
                int goodsRecordsSize = goodsRecords.size();
                if (goodsRecordsSize < shopGoodsSize) {
                    HashMap<String, String> goodsMap = new HashMap<>();
                    goodsMap.put("name", "");
                    goodsMap.put("id", "");
                    goodsMap.put("logo", "");
                    goodsMap.put("price", "");
                    for (int i = 0; i < shopGoodsSize - goodsRecordsSize; i++) {
                        goodsRecords.add(new JSONObject(goodsMap));
                    }
                }
                goodsRecords = goodsRecords.subList(0, shopGoodsSize);
                shopInfoData.put("goods", goodsRecords);
                shopInfoData.put("shop", shop);
                shopInfo.remove(0);
                shopInfo.add(shopInfoData);
                formData.put("shopInfo", shopInfo);
                data.put("formData", formData);
                propertiesList.remove((int) goodsProperty.get("index"));
                propertiesList.add((int) goodsProperty.get("index"), data);
            }
        }
        //处理轮播图数据
        JSONObject lPData = (JSONObject) lunboProductProperty.get("data");
        JSONObject lPFormData = (JSONObject) lPData.get("formData");
        List<JSONObject> swiperList = (List<JSONObject>) lPFormData.get("swiperList");
        List<JSONObject> productRenderRecords = JSON.parseObject(JSON.toJSONString(records), List.class);
        //店铺下的所有商品数量和goodsRecordsSize作用一样，为了区别变量
        int productRecordsSize = records.size();
        //模板配置的轮播图列表数据长度
        int lunboProductSize = swiperList.size();
        for (int i = 0; i < lunboProductSize; i++) {
            for (int j = 0; j < productRecordsSize; j++) {
                if (i == j) {
                    JSONObject swiperEach = swiperList.get(i);
                    ProductRenderVO productRenderVO = records.get(j);
                    JSONObject linkObject = (JSONObject) swiperEach.get("link");
                    String linkType = String.valueOf(linkObject.get("type"));
                    if ("1".equals(linkType) || StrUtil.isEmpty(linkType)) {
                        linkObject.put("id", productRenderVO.getId());
                        linkObject.put("type", 1);
                        linkObject.put("url", "/pluginPackage/goods/commodityInfo/InfoEntrance");
                        linkObject.put("shopId", userShopId);
//                        swiperEach.put("img", productRenderVO.getLogo());
                        swiperEach.put("link", linkObject);
                        swiperList.remove(i);
                        swiperList.add(i, swiperEach);
                    }

                }
            }
        }


        if (productRecordsSize < lunboProductSize) {
            HashMap<String, Object> productMap = new HashMap<>();
            HashMap<String, String> productLinkMap = new HashMap<>();
            productMap.put("linkName", "");
            productMap.put("title", "");
            productMap.put("img", "");
            productLinkMap.put("append", "");
            productLinkMap.put("id", "");
            productLinkMap.put("name", "");
            productLinkMap.put("shopId", "");
            productLinkMap.put("type", "");
            productLinkMap.put("id", "");
            productLinkMap.put("url", "");
            productMap.put("link", productLinkMap);
            for (int i = productRecordsSize; i < lunboProductSize; i++) {
                swiperList.remove(i);
                swiperList.add(i, JSONObject.from(productMap));
            }
        }
        lPFormData.put("swiperList", swiperList);
        lPData.put("formData", lPFormData);
        propertiesList.remove((int) lunboProductProperty.get("index"));
        propertiesList.add((int) lunboProductProperty.get("index"), lPData);
        return JSON.toJSONString(propertiesList);
    }

    /**
     * 获取装修类型详情
     *
     * @param functionType 装修功能类型
     * @return PropertiesVO.java
     */
    @Override
    public PropertiesVO getFunctionTypeInfo(FunctionType functionType) {
        Platform platform = ISystem.platformMust();
        AggregationPlatform aggregationPlatform = AggregationPlatformUtil.getAggregationPlatform(platform);
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        PlatformDecorationDetails platformDecorationDetails = platformDecorationDetailService.lambdaQuery()
                .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId())
                .eq(DecorateDetailsEntity::getFunctionType, functionType)
                .one();
        return assemblingPropertiesVO(platformDecorationDetails);
    }

    /**
     * 设置为同城页面
     *
     * @param id                  页面id
     * @param aggregationPlatform 聚合平台类型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setLocalPage(Long id, AggregationPlatform aggregationPlatform) {
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        platformDecorationDetailService.lambdaUpdate()
                .set(PlatformDecorationDetails::getIsLocal, Boolean.FALSE)
                .eq(DecorateDetailsEntity::getFunctionType, FunctionType.PAGE)
                .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId())
                .eq(PlatformDecorationDetails::getIsLocal, Boolean.TRUE)
                .ne(PlatformDecorationDetails::getIsDef, Boolean.TRUE)
                .update();
        //设置页面为首页
        platformDecorationDetailService.lambdaUpdate()
                .set(PlatformDecorationDetails::getIsLocal, Boolean.TRUE)
                .eq(BaseEntity::getId, id).update();

    }

    @Override
    public PropertiesVO getLocalPage() {
        Platform platform = ISystem.platformMust();
        AggregationPlatform aggregationPlatform = AggregationPlatformUtil.getAggregationPlatform(platform);
        PlatformDecoration platformDecoration = platformDecorationInit(aggregationPlatform);
        PlatformDecorationDetails platformDecorationDetails = platformDecorationDetailService.lambdaQuery()
                .eq(PlatformDecorationDetails::getIsLocal, Boolean.TRUE)
                .eq(PlatformDecorationDetails::getPlatformDecorationId, platformDecoration.getId())
                .one();
        return assemblingPropertiesVO(platformDecorationDetails);
    }


    /**
     * PropertiesVO 组装
     *
     * @param platformDecorationDetails PlatformDecorationDetails.class
     * @return PropertiesVO
     */
    private PropertiesVO assemblingPropertiesVO(PlatformDecorationDetails platformDecorationDetails) {
        if (platformDecorationDetails == null) {
            return null;
        }
        PropertiesVO properties = new PropertiesVO();
        properties.setId(platformDecorationDetails.getId());
        List<Object> objects = JSON.parseObject(platformDecorationDetails.getProperties(), new TypeReference<>() {
        });
        properties.setProperties(objects);
        return properties;
    }


    /**
     * 初始化 装修主表数据
     *
     * @return ShopDecoration.java
     */
    private PlatformDecoration platformDecorationInit(AggregationPlatform platforms) {
        PlatformDecoration platformDecoration = platformDecorationService.lambdaQuery().eq(PlatformDecoration::getPlatforms, platforms).one();
        if (platformDecoration == null) {
            platformDecoration = new PlatformDecoration();
            platformDecoration.setPlatforms(platforms);
            platformDecorationService.save(platformDecoration);
        }
        return platformDecoration;
    }

}
