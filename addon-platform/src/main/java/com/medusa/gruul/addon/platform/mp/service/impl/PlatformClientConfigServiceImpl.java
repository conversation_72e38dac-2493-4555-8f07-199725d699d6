package com.medusa.gruul.addon.platform.mp.service.impl;

import com.medusa.gruul.addon.platform.mp.entity.PlatformClientConfig;
import com.medusa.gruul.addon.platform.mp.mapper.PlatformClientConfigMapper;
import com.medusa.gruul.addon.platform.mp.service.IPlatformClientConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 平台商城配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Service
public class PlatformClientConfigServiceImpl extends ServiceImpl<PlatformClientConfigMapper, PlatformClientConfig> implements IPlatformClientConfigService {

}
