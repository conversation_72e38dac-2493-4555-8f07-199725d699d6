package com.medusa.gruul.addon.platform.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.custom.aggregation.decoration.entity.DecorateEntity;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 平台装修
 *
 * <AUTHOR>
 * @description PlatformDecoration.java
 * @date 2022-10-31 20:52
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_platform_decoration")
public class PlatformDecoration extends DecorateEntity {


}


