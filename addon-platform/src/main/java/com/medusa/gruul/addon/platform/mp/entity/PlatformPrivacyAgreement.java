package com.medusa.gruul.addon.platform.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.custom.aggregation.classify.enums.CategoryLevel;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * 平台隐私协议
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_platform_privacy_agreement")
public class PlatformPrivacyAgreement extends BaseEntity {

    /**
     * 平台隐私协议
     */
    private String platformPrivacyAgreementText;
}
