<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.addon.platform.mp.mapper.PlatformShopSigningCategoryMapper">


    <resultMap id="BaseResultMap" type="com.medusa.gruul.addon.platform.model.vo.SigningCategoryVO">
        <result column="shopId" property="shopId"/>
        <result column="id" property="id"/>
        <result column="parentId" property="parentId"/>
        <result column="parentName" property="parentName"/>
        <result column="currentCategoryId" property="currentCategoryId"/>
        <result column="currentCategoryName" property="currentCategoryName"/>
        <result column="customDeductionRatio" property="customDeductionRatio"/>
        <result column="deductionRatio" property="deductionRatio"/>
    </resultMap>

    <resultMap id="BaseCategoryResultMap" type="com.medusa.gruul.addon.platform.model.vo.CategoryVO">
        <result column="id" property="id"/>
        <result column="parentId" property="parentId"/>
        <result column="level" property="level"/>
        <result column="name" property="name"/>
        <result column="shopId" property="shopId"/>
        <result column="categoryId" property="categoryId"/>
        <collection property="secondCategoryVos" ofType="com.medusa.gruul.addon.platform.model.vo.CategorySecondVO"
                    column="{categoryId=categoryId,shopId=shopId}" select="queryChoosableCategorySecond">
        </collection>
    </resultMap>

    <resultMap id="SecondChoosableResultMap" type="com.medusa.gruul.addon.platform.model.vo.CategorySecondVO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="level" property="level"/>
        <result column="category_id" property="categoryId"/>
        <result column="deduction_ratio" property="deductionRatio"/>
        <result column="sort" property="sort"/>
        <collection property="categoryThirdlyVos" ofType="com.medusa.gruul.addon.platform.model.vo.CategoryThirdlyVO"
                    column="id" select="queryChoosableCategoryThirdly"></collection>
    </resultMap>


    <resultMap id="ThirdlyChoosableResultMap" type="com.medusa.gruul.addon.platform.model.vo.CategoryThirdlyVO">


    </resultMap>


    <select id="querySigningCategoryListByShopId" resultMap="BaseResultMap">
        SELECT ANY_VALUE(signingCategory.id)        AS id,
               ANY_VALUE(shop_id)                   AS shopId,
               ANY_VALUE(custom_deduction_ratio)    AS customDeductionRatio,
               ANY_VALUE(signingCategory.parent_id) AS parentId,
               ANY_VALUE(categoryOne.`name`)        AS parentName,
               ANY_VALUE(current_category_id)       AS currentCategoryId,
               ANY_VALUE(categoryTwo.`name`)        AS currentCategoryName,
               CASE
                   WHEN #{match} = TRUE THEN categoryTwo.deduction_ratio
                   ELSE NULL
                   END                              AS deductionRatio
        FROM t_platform_shop_signing_category AS signingCategory
                 JOIN t_platform_category AS categoryOne ON signingCategory.parent_Id = categoryOne.id
                 JOIN t_platform_category AS categoryTwo ON signingCategory.current_category_id = categoryTwo.id
        WHERE signingCategory.shop_id = #{shopId}
          AND signingCategory.deleted = 0
        ORDER BY parentId, currentCategoryId, signingCategory.create_time
    </select>
    <select id="queryChoosableCategoryInfo" resultMap="BaseCategoryResultMap">
        SELECT
            signingCategory.id AS id,
            signingCategory.parent_id  AS categoryId,
            signingCategory.shop_id AS shopId,
            categoryOne.level AS level,
            categoryOne.name AS name ,
            categoryOne.parent_id AS parentId
        FROM
            t_platform_shop_signing_category AS signingCategory
            JOIN t_platform_category AS categoryOne ON signingCategory.parent_id = categoryOne.id
        WHERE signingCategory.shop_id = #{shopId}
        AND signingCategory.deleted = 0
        GROUP BY signingCategory.parent_id;
    </select>


    <select id="queryChoosableCategorySecond" resultMap="SecondChoosableResultMap">
        SELECT
            signingCategory.current_category_id AS id,
            signingCategory.current_category_id  AS categoryId,
            categoryTwo.level AS level,
            categoryTwo.name AS name,
            categoryTwo.parent_id AS parentId
        FROM
            t_platform_shop_signing_category AS signingCategory
            JOIN t_platform_category AS categoryTwo ON signingCategory.current_category_id = categoryTwo.id
        WHERE
            signingCategory.shop_id = #{shopId}
        AND
            signingCategory.parent_id = #{categoryId}
        AND signingCategory.deleted = 0
    </select>


    <select id="queryChoosableCategoryThirdly" resultMap="ThirdlyChoosableResultMap">
        SELECT
            *
        FROM
            t_platform_category
        WHERE
            parent_id = #{id}
          AND
            deleted = 0
        GROUP BY
            id,`name`
        ORDER BY
            sort
    </select>
</mapper>
