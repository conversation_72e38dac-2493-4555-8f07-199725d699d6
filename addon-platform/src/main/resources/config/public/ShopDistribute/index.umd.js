(function(e,H){typeof exports=="object"&&typeof module<"u"?module.exports=H(require("vue"),require("@/components/PageManage.vue"),require("element-plus"),require("decimal.js"),require("@element-plus/icons-vue"),require("@/apis/good"),require("@/composables/useConvert"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("@/components/MCard.vue"),require("vue-clipboard3"),require("@/utils/date"),require("@/components/q-icon/q-icon.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/components/PageManage.vue","element-plus","decimal.js","@element-plus/icons-vue","@/apis/good","@/composables/useConvert","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","@/components/MCard.vue","vue-clipboard3","@/utils/date","@/components/q-icon/q-icon.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/apis/http"],H):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopDistribute=H(e.ShopDistributeContext.Vue,e.ShopDistributeContext.PageManageTwo,e.ShopDistributeContext.ElementPlus,e.ShopDistributeContext.Decimal,e.ShopDistributeContext.ElementPlusIconsVue,e.ShopDistributeContext.GoodAPI,e.ShopDistributeContext.UseConvert,e.ShopDistributeContext.QChooseGoodsPopup,e.ShopDistributeContext.MCard,e.ShopDistributeContext.VueClipboard3,e.ShopDistributeContext.DateUtil,e.ShopDistributeContext.QIcon,e.ShopDistributeContext.QTable,e.ShopDistributeContext.QTableColumn,e.ShopDistributeContext.Request))})(this,function(e,H,S,U,ce,Ve,oe,Ce,_e,ye,we,Ee,Te,me,L){"use strict";var ue=document.createElement("style");ue.textContent=`@charset "UTF-8";.dis[data-v-9795116f]{padding:0 30px;position:relative}.table-height-fit[data-v-b42c2ab7]{height:calc(100vh - 225px);overflow:auto}.dis[data-v-b42c2ab7]{padding:0}.dis__header[data-v-b42c2ab7]{display:flex;justify-content:space-between;align-items:center;margin-bottom:14px}.com[data-v-b42c2ab7]{width:242px;font-size:12px;color:#515151;display:flex;justify-content:space-between;align-items:center}.com__img[data-v-b42c2ab7]{width:56px;height:56px;margin-right:12px}.com__right[data-v-b42c2ab7]{text-align:left}.com__right--name[data-v-b42c2ab7]{width:174px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-bottom:10px}.dialogCom[data-v-b42c2ab7]{display:flex;justify-content:center;align-items:center}.dialogCom__img[data-v-b42c2ab7]{width:60px;height:60px;margin-right:12px;border-radius:10px}.dialogCom__right--name[data-v-b42c2ab7]{width:210px;font-size:14px;color:#333;line-height:20px}.tableCom__img[data-v-b42c2ab7]{width:36px;height:36px;margin-right:12px;border-radius:10px}.color51[data-v-b42c2ab7]{color:#515151}.colorRed[data-v-b42c2ab7]{color:#fd0505}.color33[data-v-b42c2ab7]{color:#333}.column[data-v-b42c2ab7]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column button[data-v-b42c2ab7]{margin:0}.flex[data-v-b42c2ab7]{display:flex;justify-content:flex-start;align-items:center}.amount[data-v-b42c2ab7]:before{content:"￥";display:inline-block}.percentage[data-v-b42c2ab7]:after{content:"%";display:inline-block}.tiShi[data-v-b42c2ab7]{color:#fd0505;font-size:12px;margin:10px 0 20px}.head[data-v-66efc4e8]{display:flex;align-items:center;justify-content:space-around;background-color:#e6f7ff;height:40px;margin-top:15px;font-size:10px}.content[data-v-66efc4e8]{display:flex}.content__right1[data-v-66efc4e8]{border:1px solid #ebeef5;display:flex;align-items:center;justify-content:center;line-height:26px;width:470px;padding-left:10px}.content__right1--item[data-v-66efc4e8]{width:120px}.content__right2[data-v-66efc4e8]{width:155px;font-size:13px;border:1px solid #ebeef5;display:flex;flex-wrap:wrap;justify-content:center;flex-direction:column;padding-left:5px;line-height:26px}.content__right2--item[data-v-66efc4e8]{width:100px}.goods[data-v-66efc4e8]{display:flex;align-items:center}.goods__pic[data-v-66efc4e8]{margin-right:10px;width:60px;height:50px;position:relative}.goods__pic--state[data-v-66efc4e8]{background:#7f83f7;position:absolute;width:40px;height:40px;border-radius:50%;top:5px;left:10px;color:#fff;line-height:40px;text-align:center;font-size:10px}.goods__info-flex[data-v-66efc4e8]{display:flex;align-items:center;justify-content:space-between;margin-top:10px}.goods__info-flex--name[data-v-66efc4e8]{width:185px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-right:10px}.goods__info-flex--specs[data-v-66efc4e8]{width:80px;font-size:10px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}[data-v-66efc4e8] .el-table thead{display:none}.amount[data-v-66efc4e8]:before{content:"￥";display:inline-block}.percentage[data-v-66efc4e8]:after{content:"%";display:inline-block}.ellipsis[data-v-66efc4e8]{width:250px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.dialog-footer button[data-v-b509af32]:first-child{margin-right:10px}.fw[data-v-b509af32]{font-weight:700;margin:20px 0 10px}.count[data-v-ed432cdc]{height:56px;line-height:56px;font-weight:700}.count span[data-v-ed432cdc]{margin-right:30px}.tbhead[data-v-ed432cdc]{display:flex;align-items:center;height:35px;font-weight:700;background-color:#f2f2f280}.tbhead__goods[data-v-ed432cdc]{margin-left:150px}.tbhead__parameter[data-v-ed432cdc]{margin-left:170px}.tbhead__detail[data-v-ed432cdc]{margin-left:185px}.tbhead__total[data-v-ed432cdc]{margin-left:190px}.ml[data-v-ed432cdc]{margin-left:30px}.tool[data-v-5215f2ec]{width:100%;height:56px;background:#fff;position:relative}.tool__btn[data-v-5215f2ec]{position:absolute;left:0}.tool__btn--drop[data-v-5215f2ec]{width:120px}.tool__input[data-v-5215f2ec]{width:250px;font-size:14px;position:absolute;right:0;top:50%;margin-top:-24px}.color51[data-v-5215f2ec]{color:#515151}.color58[data-v-5215f2ec]{color:#586884}.color33[data-v-5215f2ec]{color:#333}.column[data-v-5215f2ec]{display:flex;flex-direction:column;align-items:flex-start;justify-content:flex-start}.column button[data-v-5215f2ec]{margin:0}.ellipsis[data-v-5215f2ec]{width:135px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ml[data-v-5215f2ec]{margin-left:30px}
`,document.head.appendChild(ue);const Se={class:"dis"},De=e.defineComponent({__name:"ShopDistribute",setup(m){const _={distributionCom:e.defineAsyncComponent(()=>Promise.resolve().then(()=>gt)),distributionOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>no)),wholeSaler:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Co))},c=e.ref("distributionCom"),n=e.computed(()=>_[c.value]);return(N,p)=>{const u=e.resolveComponent("el-tab-pane"),g=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",Se,[e.createVNode(g,{modelValue:c.value,"onUpdate:modelValue":p[0]||(p[0]=s=>c.value=s),class:"demo-tabs"},{default:e.withCtx(()=>[e.createVNode(u,{label:"分销商品",name:"distributionCom"}),e.createVNode(u,{label:"分销订单",name:"distributionOrder"}),e.createVNode(u,{label:"分销商",name:"wholeSaler"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(n.value),{ref:"componentRef"},null,512))])}}}),Bo="",ee=(m,_)=>{const c=m.__vccOpts||m;for(const[n,N]of _)c[n]=N;return c},ke=ee(De,[["__scopeId","data-v-9795116f"]]),Ie=m=>L.post({url:"addon-distribute/distribute/product/",data:m}),Be=(m,_)=>L.put({url:`addon-distribute/distribute/product/${m}`,data:_}),Fe=m=>L.post({url:"addon-distribute/distribute/product/page",data:m}),Oe=m=>L.del({url:`addon-distribute/distribute/product/${m.join(",")}`}),Ne=m=>L.put({url:`addon-distribute/distribute/product/cancel/${m.join(",")}`}),Ue=m=>L.put({url:`addon-distribute/distribute/product/again/${m}`}),ze=()=>L.get({url:"addon-distribute/distribute/config/"}),Ae=m=>L.get({url:"addon-distribute/distribute/order/",params:m}),$e=m=>L.get({url:"addon-distribute/distribute/distributor/",params:m}),Le=m=>L.get({url:"addon-distribute/distribute/distributor/team",params:m}),Re=m=>L.get({url:"addon-distribute/distribute/distributor/rank",params:m}),Pe=m=>(e.pushScopeId("data-v-b42c2ab7"),m=m(),e.popScopeId(),m),qe={class:"dis"},je={class:"dis__header"},Xe={class:"dis__header-left"},He={class:"dis__header-right"},Me={class:"com f12 color51"},We={class:"com__right"},Ge={class:"com__right--name"},Ye={key:0},Qe={key:1},Je={class:"f12 color51"},Ke={class:"f12 color51 column"},Ze={class:"f12 color51"},ve={key:0,class:"f12 color51"},et={key:1,class:"f12 color51"},tt={class:"f12 color33"},ot={class:"f12 color33"},nt={key:0,class:"f12 colorRed"},lt={key:1,class:"f12 colorRed"},at={class:"f12 color51"},rt={style:{display:"flex",gap:"20px"}},st=Pe(()=>e.createElementVNode("p",{class:"tiShi"},"温馨提示：一级佣金：应大于等于 0 ； 二级佣金：应小于一级佣金 ； 三级佣金：应小于二级佣金",-1)),dt={class:"flex"},it={class:"tableCom flex"},ct={class:"f12 color51"},mt={key:0,class:"f12 colorRed"},pt={key:1,class:"f12 colorRed"},ht={key:0,class:"f12 colorRed"},ft={key:1,class:"f12 colorRed"},_t={key:0,class:"f12 colorRed"},ut={key:1,class:"f12 colorRed"},Nt=["onClick"],bt=e.defineComponent({__name:"DistributionCom",setup(m){const _=e.reactive({list:[],current:1,pages:1,total:0,size:10,productName:"",distributionStatus:"ALL"}),{mulTenThousand:c,divTenThousand:n,mulHundred:N,divHundred:p}=oe(),u=e.ref("add"),g=e.ref(!1);e.ref(!1);const s=e.ref("ONE"),r=e.ref({}),y=e.ref(),K=e.computed(()=>u.value==="add"?"新增":u.value==="edit"?"编辑":"查看"),x=e.reactive({pages:1,current:1,list:[],total:0,size:10,name:"",loading:!1,excludeProductIds:[]}),a=e.reactive({shareType:"UNIFIED",one:"0",two:"0",three:"0",productIds:[],listId:""}),w=e.reactive({one:[{required:!0,validator:G,trigger:"blur"}],two:[{required:!0,validator:re,trigger:"blur"}],three:[{required:!0,validator:se,trigger:"blur"}]}),z=e.ref([]),F=e.ref([]);e.ref([]),$(),Eo();const M=()=>{u.value="add",g.value=!0},P=()=>{if(u.value==="see"){g.value=!1;return}y.value&&y.value.validate(async l=>{if(l){const t=JSON.parse(JSON.stringify(a));t.shareType==="RATE"||t.shareType==="FIXED_AMOUNT"?(t.one=String(c(t.one)),t.two=String(c(t.two)),t.three=String(c(t.three))):(t.shareType=r.value.shareType,(r.value.shareType==="RATE"||r.value.shareType==="FIXED_AMOUNT")&&(t.one=String(c(r.value.one)),t.two=String(c(r.value.two)),t.three=String(c(r.value.three))));const{code:d,msg:i}=u.value==="add"?await Ie(de(t)):await Be(t.listId,de(t));d===200?(S.ElMessage.success(u.value==="add"?"新增成功":"修改成功"),g.value=!1,$()):S.ElMessage.error(i||(u.value==="add"?"新增失败":"修改失败"))}})},Y=()=>{pe.value=!0,O()},E=l=>{_.current=l,$()},B=l=>{_.size=l,$()},V=()=>{le()},q=()=>{_.current=1,$()},j=()=>{console.log(111),_.current=1,$()},W=l=>{l||(_.current=1,$())},T=async l=>{if((l.status==="SELL_ON"||l.status==="UNUSABLE")&&l.distributionStatus==="CANCEL_DISTRIBUTION"){const{code:t,success:d}=await Ue(l.id);t===200&&d?(S.ElMessage.success("重新分销成功"),$()):S.ElMessage.error("重新分销失败"),$()}},b=e.ref(""),A=async l=>{u.value="edit",a.productIds=[l.productId],a.shareType=l.shareType,a.listId=l.id,(l.one||l.two||l.three)&&(a.one=(l.shareType==="FIXED_AMOUNT",String(n(l.one))),a.two=(l.shareType==="FIXED_AMOUNT",String(n(l.two))),a.three=(l.shareType==="FIXED_AMOUNT",String(n(l.three)))),g.value=!0,b.value=l.distributionStatus},Z=l=>{S.ElMessageBox.confirm("请确认是否取消分销，确定后商品将从停止分销！！！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:t,success:d}=await Ne([l]);t===200&&d?(S.ElMessage.success("取消分销成功"),$()):S.ElMessage.error("取消分销失败")})},Q=l=>{console.log(l),S.ElMessageBox.confirm("请确认是否移除商品，确定后商品将从该列表中移除！！！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:t,success:d}=await Oe([l]);t===200&&d?(S.ElMessage.success("商品移除成功"),$()):S.ElMessage.error("商品移除失败")})},ne=()=>{if(!z.value.length)return S.ElMessage.warning("请选中商品");S.ElMessageBox.confirm("请确认是否取消分销，确定后商品将从该列表中移除！！！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const l=z.value.map(i=>i.id),{code:t,success:d}=await Ne(l);t===200&&d?(S.ElMessage.success("取消分销成功"),$()):S.ElMessage.error("取消分销失败")})},v=l=>{z.value=l};async function O(){x.loading=!0;const{code:l,data:t}=await Ve.doGetRetrieveProduct({current:x.current,size:x.size,excludeProductIds:x.excludeProductIds});l===200?(x.list=t.list,x.size=t.pageSize,x.current=t.pageNum,x.total=t.total):S.ElMessage.error("获取商品失败"),x.loading=!1}function le(){a.productIds=[],a.shareType="UNIFIED",a.one="0",a.two="0",a.three="0",x.excludeProductIds=[],F.value=[]}function k(l){F.value.forEach((t,d,i)=>{t.productId===l&&i.splice(d,1)}),x.excludeProductIds.forEach((t,d,i)=>{t===l&&i.splice(d,1)}),a.productIds.forEach((t,d,i)=>{t===l&&i.splice(d,1)})}function G(l,t,d){const i=a.shareType;if(i==="UNIFIED")return d();const h=Number(t);if(h<=0)return d(new Error("一级佣金应大于等于零"));if(s.value!=="ONE"&&a.two&&h<=Number(a.two))return d(new Error("一级佣金值应大于二级佣金"));if(i==="RATE"&&(h>100||h<0))return d(new Error("一级佣金比例应设置在0-100之间"));if(i==="FIXED_AMOUNT"&&(h>9e3||h<0))return d(new Error("一级佣金应设置在0-9000之间"));d()}function re(l,t,d){const i=a.shareType;if(i==="UNIFIED"||s.value==="ONE")return d();const h=Number(t);if(i==="RATE"&&(h>100||h<0))return d(new Error("一级佣金比例应设置在0-100之间"));if(i==="FIXED_AMOUNT"&&(h>9e3||h<0))return d(new Error("一级佣金应设置在0-9000之间"));if(a.one&&h>=Number(a.one))return d(new Error("二级佣金应小于一级佣金"));if(s.value==="THREE"&&a.three&&h<=Number(a.three))return d(new Error("二级佣金应大于三级佣金"));d()}function se(l,t,d){const i=a.shareType;if(i==="UNIFIED"||s.value!=="THREE")return d();const h=Number(t);if(i==="RATE"&&(h>100||h<0))return d(new Error("一级佣金比例应设置在0-100之间"));if(i==="FIXED_AMOUNT"&&(h>9e3||h<0))return d(new Error("一级佣金应设置在0-9000之间"));if(a.two&&h>=Number(a.two))return d(new Error("三级佣金应小于二级佣金"));d()}function f(l,t){return l==="UNIFIED"&&r.value.shareType==="RATE",n(t)}function de(l){return s.value==="ONE"?(delete l.two,delete l.three):s.value==="TWO"&&delete l.three,l}async function $(){const{code:l,data:t}=await Fe({size:_.size,current:_.current,productName:_.productName,distributionStatus:_.distributionStatus==="ALL"?null:_.distributionStatus});l===200&&t?(_.list=t.records,_.total=t.total,console.log(t)):S.ElMessage.error("获取分销商品列表失败")}function yo(l){if(l)return l==="IN_DISTRIBUTION"?"分销中":"取消分销"}function wo(l){if(l)return l==="REFUSE"?"已拒绝":l==="UNDER_REVIEW"?"审核中":l==="SELL_OFF"?"下架":l==="SELL_ON"?"上架":l==="SELL_OUT"?"已售完":l==="PLATFORM_SELL_OFF"?"平台下架":"店铺不可用"}async function Eo(){const{code:l,data:t}=await ze();l===200?(s.value=t.level,(t.one||t.two||t.three)&&(t.one=t.shareType==="FIXED_AMOUNT"?String(n(t.one)):String(p(t.one)),t.two=t.shareType==="FIXED_AMOUNT"?String(n(t.two)):String(p(t.two)),t.three=t.shareType==="FIXED_AMOUNT"?String(n(t.three)):String(p(t.three)),r.value=t)):S.ElMessage.error("获取分销配置失败")}const te=l=>a.shareType!=="UNIFIED"?a.shareType==="RATE"?s.value==="ONE"?l.mul(n(a.one)).toFixed(2):s.value==="TWO"?l.mul(n(a.one)).add(l.mul(n(a.two))).toFixed(2):l.mul(p(a.one)).add(l.mul(p(a.two))).add(l.mul(p(a.three))).toFixed(2):s.value==="ONE"?new U(a.one).toFixed(2):s.value==="TWO"?new U(a.one).add(a.two).toFixed(2):new U(a.one).add(a.two).add(a.three).toFixed(2):r.value.shareType==="RATE"?s.value==="ONE"?l.mul(p(r.value.one)).toFixed(2):s.value==="TWO"?l.mul(p(r.value.one)).add(l.mul(p(r.value.two))).toFixed(2):l.mul(p(r.value.one)).add(l.mul(p(r.value.two))).add(l.mul(p(r.value.three))).toFixed(2):s.value==="ONE"?new U(r.value.one).toFixed(2):s.value==="TWO"?new U(r.value.one).add(r.value.two).toFixed(2):new U(r.value.one).add(r.value.two).add(r.value.three).toFixed(2),ie=(l,t)=>t.shareType!=="UNIFIED"?t.shareType==="RATE"?s.value==="ONE"?l.mul(n(p(t.one))).toFixed(2):s.value==="TWO"?l.mul(n(p(t.one))).add(l.mul(p(p(t.two)))).toFixed(2):l.mul(n(p(t.one))).add(l.mul(n(p(t.two)))).add(l.mul(n(p(t.three)))).toFixed(2):s.value==="ONE"?n(t.one).toFixed(2):s.value==="TWO"?n(t.one).add(n(t.two)).toFixed(2):n(t.one).add(n(t.two)).add(n(t.three)).toFixed(2):r.value.shareType==="RATE"?s.value==="ONE"?l.mul(p(r.value.one)).toFixed(2):s.value==="TWO"?l.mul(p(r.value.one)).add(l.mul(p(r.value.two))).toFixed(2):l.mul(p(r.value.one)).add(l.mul(p(r.value.two))).add(l.mul(p(r.value.three))).toFixed(2):s.value==="ONE"?new U(r.value.one).toFixed(2):s.value==="TWO"?new U(r.value.one).add(r.value.two).toFixed(2):new U(r.value.one).add(r.value.two).add(r.value.three).toFixed(2);function To(l,t,d,i,h,D){if(l==="FIXED_AMOUNT"){i=+n(i),h=+n(h),D=+n(D);const C=(i+h+D).toFixed(2);return i+" + "+h+" +"+D+" = "+C}else if(t=+n(t),d=+n(d),i=+p(n(i)),h=+p(n(h)),D=+p(n(D)),D)if(h){const C=(d*i+d*h+d*D).toFixed(2),I=(t*i+t*h+t*D).toFixed(2);return t===d?t+" * "+i+" + "+t+" * "+h+" + "+t+" * "+D+" = "+I:t+" * "+i+" + "+t+" * "+h+" + "+t+" * "+D+" = "+I+" </br> "+d+" * "+i+" + "+d+" * "+h+" + "+d+" * "+D+" = "+C}else{const C=(d*i).toFixed(2),I=(t*i).toFixed(2);return t===d?t+" * "+i+"   = "+I:t+" * "+i+"   = "+I+" </br> "+d+" * "+i+" = "+C}else{const C=(d*i+d*h).toFixed(2),I=(t*i+t*h).toFixed(2);return t===d?t+" * "+i+" + "+t+" * "+h+" = "+I:t+" * "+i+" + "+t+" * "+h+" = "+I+" </br> "+d+" * "+i+" + "+d+" * "+h+" = "+C}}const ge=e.computed(()=>(l,t,d,i,h,D)=>{if(l==="FIXED_AMOUNT"){const C=+i+ +h+ +D;return i+" + "+h+" +"+D+" = "+C}else if(t=+n(t),d=+n(d),a.shareType==="RATE"&&(i=+p(i),h=+p(h),D=+p(D)),D)if(h){const C=+(d*i+d*h+d*D).toFixed(2),I=+(t*i+t*h+t*D).toFixed(2);return t===d?t+" * "+i+" + "+t+" * "+h+" + "+t+" * "+D+"   = "+I:t+" * "+i+" + "+t+" * "+h+" + "+t+" * "+D+"   = "+I+" </br> "+d+" * "+i+" + "+d+" * "+h+" + "+d+" * "+D+" = "+C}else{const C=(d*i).toFixed(2),I=(t*i).toFixed(2);return t===d?t+" * "+i+" = "+I:t+" * "+i+"   = "+I+" </br> "+d+" * "+i+" = "+C}else{const C=(d*i+d*h).toFixed(2),I=(t*i+t*h).toFixed(2);return t===d?t+" * "+i+" + "+t+" * "+h+" = "+I:t+" * "+i+" + "+t+" * "+h+" = "+I+" </br> "+d+" * "+i+" + "+d+" * "+h+" = "+C}}),pe=e.ref(!1),So=l=>{console.log(l),F.value=l.tempGoods;const t=l.tempGoods.map(d=>d.productId);a.productIds=[...a.productIds,...t]};return(l,t)=>{const d=e.resolveComponent("el-button"),i=e.resolveComponent("el-input"),h=e.resolveComponent("el-tab-pane"),D=e.resolveComponent("el-tabs"),C=e.resolveComponent("el-table-column"),I=e.resolveComponent("el-image"),he=e.resolveComponent("el-tooltip"),xe=e.resolveComponent("el-table"),fe=e.resolveComponent("el-radio"),Do=e.resolveComponent("el-radio-group"),J=e.resolveComponent("el-form-item"),ko=e.resolveComponent("el-form"),Io=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",qe,[e.createElementVNode("div",je,[e.createElementVNode("div",Xe,[e.createVNode(d,{type:"primary",round:"",style:{width:"100px",height:"36px"},onClick:M},{default:e.withCtx(()=>[e.createTextVNode("新增商品")]),_:1}),e.createVNode(d,{type:"primary",round:"",plain:"",style:{width:"120px",height:"36px"},onClick:ne},{default:e.withCtx(()=>[e.createTextVNode("批量取消分销")]),_:1})]),e.createElementVNode("div",He,[e.createVNode(i,{modelValue:_.productName,"onUpdate:modelValue":t[0]||(t[0]=o=>_.productName=o),clearable:"",placeholder:"请输入商品名称",onChange:W},{append:e.withCtx(()=>[e.createVNode(d,{icon:e.unref(ce.Search),onClick:q},null,8,["icon"])]),_:1},8,["modelValue"])])]),e.createVNode(D,{modelValue:_.distributionStatus,"onUpdate:modelValue":t[1]||(t[1]=o=>_.distributionStatus=o),type:"card",class:"demo-tabs",style:{"margin-top":"20px"},onTabChange:j},{default:e.withCtx(()=>[e.createVNode(h,{label:"全部",name:"ALL"}),e.createVNode(h,{label:"分销中",name:"IN_DISTRIBUTION"}),e.createVNode(h,{label:"取消分销",name:"CANCEL_DISTRIBUTION"})]),_:1},8,["modelValue"]),e.createVNode(xe,{data:_.list,"header-cell-style":{color:"#909399",fontSize:"12px",background:"#F6F8FA"},class:"table-height-fit",onSelectionChange:v},{default:e.withCtx(()=>[e.createVNode(C,{type:"selection",width:"55"}),e.createVNode(C,{label:"商品信息",width:"260",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("div",Me,[e.createVNode(I,{class:"com__img",src:o.row.pic},null,8,["src"]),e.createElementVNode("div",We,[e.createElementVNode("div",Ge,e.toDisplayString(o.row.name),1),o.row.salePrices[0]!==o.row.salePrices[o.row.salePrices.length-1]&&o.row.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",Ye," ￥"+e.toDisplayString(e.unref(n)(o.row.salePrices[0]))+" ~ ￥"+e.toDisplayString(ie(e.unref(n)(o.row.salePrices[o.row.salePrices.length-1]),o.row)),1)):(e.openBlock(),e.createElementBlock("div",Qe,"￥"+e.toDisplayString(e.unref(n)(o.row.salePrices[0])),1))])])]),_:1}),e.createVNode(C,{label:"总库存",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",Je,e.toDisplayString(o.row.stock),1)]),_:1}),e.createVNode(C,{label:"分佣参数",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("div",Ke,[e.createElementVNode("div",Ze,[e.createTextVNode(" 一级:"),e.createElementVNode("span",{class:e.normalizeClass([o.row.shareType==="UNIFIED"?r.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":o.row.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(f(o.row.shareType,o.row.one)),3)]),s.value!=="ONE"&&o.row.two?(e.openBlock(),e.createElementBlock("div",ve,[e.createTextVNode(" 二级:"),e.createElementVNode("span",{class:e.normalizeClass([o.row.shareType==="UNIFIED"?r.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":o.row.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(f(o.row.shareType,o.row.two)),3)])):e.createCommentVNode("",!0),s.value==="THREE"&&o.row.three?(e.openBlock(),e.createElementBlock("div",et,[e.createTextVNode(" 三级:"),e.createElementVNode("span",{class:e.normalizeClass([o.row.shareType==="UNIFIED"?r.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":o.row.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(f(o.row.shareType,o.row.three)),3)])):e.createCommentVNode("",!0)])]),_:1}),e.createVNode(C,{label:"状态",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",tt,e.toDisplayString(wo(o.row.status)),1)]),_:1}),e.createVNode(C,{label:"分销状态",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",ot,e.toDisplayString(yo(o.row.distributionStatus)),1)]),_:1}),e.createVNode(C,{label:"分销佣金(预计)",width:"160",align:"center"},{default:e.withCtx(o=>{var X,ae;return[e.createVNode(he,{"raw-content":!0,content:To(o.row.shareType,o.row.salePrices[0],o.row.salePrices[o.row.salePrices.length-1],o.row.one,(X=o.row)==null?void 0:X.two,(ae=o.row)==null?void 0:ae.three),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.row.salePrices[0]!==o.row.salePrices[o.row.salePrices.length-1]&&o.row.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",nt,[e.createElementVNode("span",null,"￥"+e.toDisplayString(ie(e.unref(n)(o.row.salePrices[0]),o.row)),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(ie(e.unref(n)(o.row.salePrices[o.row.salePrices.length-1]),o.row)),1)])):(e.openBlock(),e.createElementBlock("div",lt,"￥"+e.toDisplayString(ie(e.unref(n)(o.row.salePrices[0]),o.row)),1))]),_:2},1032,["content"])]}),_:1}),e.createVNode(C,{label:"添加时间",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",at,e.toDisplayString(o.row.createTime),1)]),_:1}),e.createVNode(C,{label:"操作",align:"center",width:"160",fixed:"right"},{default:e.withCtx(({row:o})=>[(o.status==="SELL_ON"||o.status==="UNUSABLE")&&o.distributionStatus==="CANCEL_DISTRIBUTION"?(e.openBlock(),e.createBlock(d,{key:0,link:"",type:"primary",class:"f12",onClick:X=>T(o)},{default:e.withCtx(()=>[e.createTextVNode("重新分销")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),o.status!=="FORBIDDEN"?(e.openBlock(),e.createBlock(d,{key:1,link:"",type:"primary",class:"f12",onClick:X=>A(o)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),o.distributionStatus==="IN_DISTRIBUTION"&&o.status==="SELL_ON"?(e.openBlock(),e.createBlock(d,{key:2,link:"",type:"primary",class:"f12 colorRed",onClick:X=>Z(o.id)},{default:e.withCtx(()=>[e.createTextVNode("取消分销")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),(o.status==="SELL_OFF"||o.status==="PLATFORM_SELL_OFF"||o.status==="SELL_OUT"||o.status==="UNUSABLE")&&o.distributionStatus==="CANCEL_DISTRIBUTION"||o.distributionStatus==="CANCEL_DISTRIBUTION"?(e.openBlock(),e.createBlock(d,{key:3,style:{color:"red"},link:"",type:"primary",class:"f12",onClick:X=>Q(o.id)},{default:e.withCtx(()=>[e.createTextVNode("移除")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(H,{"page-size":_.size,"page-num":_.current,total:_.total,onHandleCurrentChange:E,onHandleSizeChange:B},null,8,["page-size","page-num","total"]),e.createVNode(Io,{modelValue:g.value,"onUpdate:modelValue":t[10]||(t[10]=o=>g.value=o),title:K.value,width:"900px",onClose:V},{footer:e.withCtx(()=>[e.createVNode(d,{onClick:t[9]||(t[9]=o=>g.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(d,{type:"primary",onClick:P},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})]),default:e.withCtx(()=>[e.createVNode(ko,{ref_key:"formRef",ref:y,model:a,rules:w},{default:e.withCtx(()=>[e.createVNode(J,{label:"分销类型"},{default:e.withCtx(()=>[e.createVNode(Do,{modelValue:a.shareType,"onUpdate:modelValue":t[2]||(t[2]=o=>a.shareType=o)},{default:e.withCtx(()=>[e.createVNode(fe,{label:"UNIFIED",disabled:u.value==="see"},{default:e.withCtx(()=>[e.createTextVNode("与平台一致")]),_:1},8,["disabled"]),e.createVNode(fe,{label:"FIXED_AMOUNT",disabled:u.value==="see"},{default:e.withCtx(()=>[e.createTextVNode("固定金额")]),_:1},8,["disabled"]),e.createVNode(fe,{label:"RATE",disabled:u.value==="see"},{default:e.withCtx(()=>[e.createTextVNode("百分比")]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1}),e.createElementVNode("div",rt,[a.shareType!=="UNIFIED"?(e.openBlock(),e.createBlock(J,{key:0,label:"一级佣金",prop:"one"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:a.one,"onUpdate:modelValue":t[3]||(t[3]=o=>a.one=o),min:.01,max:a.shareType==="FIXED_AMOUNT"?1e4:100,type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(a.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","max","disabled"])]),_:1})):e.createCommentVNode("",!0),a.shareType!=="UNIFIED"&&(s.value==="TWO"||s.value==="THREE")?(e.openBlock(),e.createBlock(J,{key:1,label:"二级佣金",prop:"two"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:a.two,"onUpdate:modelValue":t[4]||(t[4]=o=>a.two=o),type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(a.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","disabled"])]),_:1})):e.createCommentVNode("",!0),a.shareType!=="UNIFIED"&&s.value==="THREE"?(e.openBlock(),e.createBlock(J,{key:2,label:"三级佣金",prop:"three"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:a.three,"onUpdate:modelValue":t[5]||(t[5]=o=>a.three=o),type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(a.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","disabled"])]),_:1})):e.createCommentVNode("",!0),a.shareType==="UNIFIED"?(e.openBlock(),e.createBlock(J,{key:3,label:"一级佣金",prop:"one"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:r.value.one,"onUpdate:modelValue":t[6]||(t[6]=o=>r.value.one=o),min:.01,max:r.value.shareType==="FIXED_AMOUNT"?1e4:100,type:"number",style:{width:"200px"},disabled:!0},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r.value.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","max"])]),_:1})):e.createCommentVNode("",!0),a.shareType==="UNIFIED"&&(s.value==="TWO"||s.value==="THREE")?(e.openBlock(),e.createBlock(J,{key:4,label:"二级佣金",prop:"two"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:r.value.two,"onUpdate:modelValue":t[7]||(t[7]=o=>r.value.two=o),type:"number",style:{width:"200px"},disabled:!0},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r.value.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0),a.shareType==="UNIFIED"&&s.value==="THREE"?(e.openBlock(),e.createBlock(J,{key:5,label:"三级佣金",prop:"three"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:r.value.three,"onUpdate:modelValue":t[8]||(t[8]=o=>r.value.three=o),type:"number",style:{width:"200px"},disabled:!0},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r.value.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0)]),st,u.value!=="edit"?(e.openBlock(),e.createBlock(J,{key:0,label:"关联商品",prop:"productId"},{default:e.withCtx(()=>[e.createVNode(d,{type:"primary",round:"",onClick:Y},{default:e.withCtx(()=>[e.createTextVNode("选择商品")]),_:1})]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model","rules"]),u.value!=="edit"?(e.openBlock(),e.createBlock(xe,{key:0,height:"370",data:F.value},{default:e.withCtx(()=>[e.createVNode(C,{label:"商品信息"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",dt,[e.createElementVNode("div",it,[e.createVNode(I,{class:"tableCom__img",src:o.pic,fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src"]),e.createElementVNode("div",null,[e.createElementVNode("div",ct,e.toDisplayString(o.productName),1),o.salePrices[0]!==o.salePrices[o.salePrices.length-1]&&o.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",mt,[e.createElementVNode("span",null,"￥"+e.toDisplayString(e.unref(n)(o.salePrices[0])),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(e.unref(n)(o.salePrices[o.salePrices.length-1])),1)])):(e.openBlock(),e.createElementBlock("div",pt,"￥"+e.toDisplayString(e.unref(n)(o.salePrices[0])),1))])])])]),_:1}),a.shareType!=="UNIFIED"?(e.openBlock(),e.createBlock(C,{key:0,label:"分销佣金(预计)",align:"center",width:"200"},{default:e.withCtx(({row:o})=>[e.createVNode(he,{"raw-content":!0,content:ge.value(a.shareType,o.salePrices[0],o.salePrices[o.salePrices.length-1],a.one,a==null?void 0:a.two,a==null?void 0:a.three),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.salePrices[0]!==o.salePrices[o.salePrices.length-1]&&o.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",ht,[e.createElementVNode("span",null,"￥"+e.toDisplayString(te(e.unref(n)(o.salePrices[0]))),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(te(e.unref(n)(o.salePrices[o.salePrices.length-1]))),1)])):(e.openBlock(),e.createElementBlock("div",ft,"￥"+e.toDisplayString(te(e.unref(n)(o.salePrices[0]))),1))]),_:2},1032,["content"])]),_:1})):e.createCommentVNode("",!0),a.shareType==="UNIFIED"?(e.openBlock(),e.createBlock(C,{key:1,label:"分销佣金(预计)",align:"center",width:"200"},{default:e.withCtx(({row:o})=>{var X,ae;return[e.createVNode(he,{"raw-content":!0,content:ge.value(r.value.shareType,o.salePrices[0],o.salePrices[o.salePrices.length-1],r.value.one,(X=r.value)==null?void 0:X.two,(ae=r.value)==null?void 0:ae.three),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.salePrices[0]!==o.salePrices[o.salePrices.length-1]&&o.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",_t,[e.createElementVNode("span",null,"￥"+e.toDisplayString(te(e.unref(n)(o.salePrices[0]))),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(te(e.unref(n)(o.salePrices[o.salePrices.length-1]))),1)])):(e.openBlock(),e.createElementBlock("div",ut,"￥"+e.toDisplayString(te(e.unref(n)(o.salePrices[0]))),1))]),_:2},1032,["content"])]}),_:1})):e.createCommentVNode("",!0),e.createVNode(C,{label:"操作",align:"center",width:"80"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",{class:"f12 colorRed",style:{cursor:"pointer"},onClick:X=>k(o.productId)},"移出",8,Nt)]),_:1})]),_:1},8,["data"])):e.createCommentVNode("",!0)]),_:1},8,["modelValue","title"]),e.createVNode(Ce,{modelValue:pe.value,"onUpdate:modelValue":t[11]||(t[11]=o=>pe.value=o),searchConsignmentProduct:!0,"point-goods-list":F.value,onOnConfirm:So},null,8,["modelValue","point-goods-list"])])}}}),Fo="",gt=Object.freeze(Object.defineProperty({__proto__:null,default:ee(bt,[["__scopeId","data-v-b42c2ab7"]])},Symbol.toStringTag,{value:"Module"})),{divTenThousand:R}=oe();function be(m,_){return R(_)}const xt=()=>({useSingleCalculationFormula:(c,n,N)=>{var s,r;const p=(s=c==null?void 0:c.bonusShare)==null?void 0:s.shareType,u=(r=c==null?void 0:c.bonusShare)==null?void 0:r[n],g=R((c==null?void 0:c.dealPrice)*(c==null?void 0:c.num));return p==="RATE"?`${g} * ${be(p,u)}% = ${N}`:be(p,u)},useTotalPrice:c=>{let n=[],N=[],p=[],u=[];return c.items.forEach(s=>{let r=[];s.orderStatus==="PAID"?r=p:s.orderStatus==="COMPLETED"?r=n:s.orderStatus==="CLOSED"&&(r=N),s.one.bonus&&(u.push(R(s.one.bonus)),r.push(R(s.one.bonus))),s.two.bonus&&(u.push(R(s.two.bonus)),r.push(R(s.two.bonus))),s.three.bonus&&(u.push(R(s.three.bonus)),r.push(R(s.three.bonus))),s.purchase&&s.one.userId&&(u.push(R(s.one.bonus)),r.push(R(s.one.bonus)))}),{completePrice:n.join(" + ")+" = "+n.reduce((s,r)=>s.plus(r),new U(0)),closedPrice:N.join(" + ")+" = "+N.reduce((s,r)=>s.plus(r),new U(0)),toSelledPrice:p.join(" + ")+" = "+p.reduce((s,r)=>s.plus(r),new U(0)),totalPrice:u.join(" + ")+" = "+u.reduce((s,r)=>s.plus(r),new U(0))}}}),Vt={class:"head"},Ct={style:{color:"#f00"}},yt={class:"ellipsis"},wt={class:"content"},Et={class:"goods"},Tt={class:"goods__pic"},St=["src"],Dt={class:"goods__info"},kt={class:"goods__info-flex"},It={class:"goods__info-flex--name"},Bt={class:"goods__info-flex--price"},Ft={class:"goods__info-flex"},Ot={class:"goods__info-flex--specs"},Ut={class:"goods__info-flex--num"},zt={class:"f12 color51"},At={key:0,class:"f12 color51"},$t={key:1,class:"f12 color51"},Lt={class:"content__right1"},Rt={class:"content__right1--item"},Pt={class:"content__right1--item"},qt={class:"content__right1--item"},jt={class:"content__right2"},Xt={key:0,class:"content__right2--item"},Ht={key:1,class:"content__right2--item"},Mt={key:2,class:"content__right2--item"},Wt={class:"content__right2--item"},Gt=e.defineComponent({__name:"distributionOrderTable",props:{orderInfo:{type:Object,default(){return{id:null,type:null,name:"",url:"",append:""}}}},setup(m){const _=m,{useSingleCalculationFormula:c,useTotalPrice:n}=xt(),{toClipboard:N}=ye(),{divTenThousand:p,divHundred:u}=oe();function g(x,a){return x==="RATE"?u(a):p(a)}function s(x,a){let w=new U(0);return"items"in _.orderInfo&&_.orderInfo.items.length&&_.orderInfo.items.forEach(z=>{z[x].bonus&&(w=w.add(p(z[x].bonus))),a&&z[x].userId&&(w=w.add(p(z[x].bonus)))}),w}function r(x){let a=new U(0);return"items"in _.orderInfo&&_.orderInfo.items.length&&_.orderInfo.items.forEach(w=>{x&&w.orderStatus!==x||(w.one.bonus&&(a=a.add(p(w.one.bonus))),w.two.bonus&&(a=a.add(p(w.two.bonus))),w.three.bonus&&(a=a.add(p(w.three.bonus))),w.purchase&&w.one.userId&&(a=a.add(p(w.one.bonus))))}),a.toNumber()}function y(x){return x==="COMPLETED"?"已赚":x==="CLOSED"?"已失效":"待结算"}async function K(x){try{await N(x),S.ElMessage.success("复制成功")}catch{S.ElMessage.error("复制失败")}}return(x,a)=>{var M,P,Y;const w=e.resolveComponent("el-table-column"),z=e.resolveComponent("el-table"),F=e.resolveComponent("el-tooltip");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",Vt,[e.createElementVNode("div",null,[e.createTextVNode(" 订单号："+e.toDisplayString(m.orderInfo.orderNo),1),e.createElementVNode("span",{style:{"margin-left":"10px",color:"#1890ff",cursor:"pointer"},onClick:a[0]||(a[0]=E=>K(m.orderInfo.orderNo))},"复制")]),e.createElementVNode("div",null,"下单时间："+e.toDisplayString(m.orderInfo.createTime),1),e.createElementVNode("div",null,"买家："+e.toDisplayString(m.orderInfo.buyerName),1),e.createElementVNode("div",null,"实付款："+e.toDisplayString(e.unref(p)(m.orderInfo.payAmount).toFixed(2))+" 元",1),e.createElementVNode("div",Ct,e.toDisplayString(m.orderInfo.items[0].purchase===!0?"内购":""),1),e.createElementVNode("div",yt,"所属店铺："+e.toDisplayString(m.orderInfo.shopName),1)]),e.createElementVNode("div",wt,[e.createVNode(z,{data:m.orderInfo.items,border:"",width:"100%","row-style":{height:"110px"}},{default:e.withCtx(()=>[e.createVNode(w,{prop:"name"},{default:e.withCtx(({row:E})=>[e.createElementVNode("div",Et,[e.createElementVNode("div",Tt,[e.createElementVNode("img",{src:E.image,style:{width:"60px",height:"50px"}},null,8,St),e.createElementVNode("div",{class:"goods__pic--state",style:e.normalizeStyle(E.orderStatus==="CLOSED"?"background:#9A9A9A;":E.orderStatus==="COMPLETED"?"background:#FD0505 ;":"")},e.toDisplayString(y(E.orderStatus)),5)]),e.createElementVNode("div",Dt,[e.createElementVNode("div",kt,[e.createElementVNode("div",It,e.toDisplayString(E.productName),1),e.createElementVNode("div",Bt,e.toDisplayString(E.num)+"件",1)]),e.createElementVNode("div",Ft,[e.createElementVNode("div",Ot,e.toDisplayString(E.specs&&E.specs.join("-")),1),e.createElementVNode("div",Ut,"￥"+e.toDisplayString(e.unref(p)(E.dealPrice)),1)])])])]),_:1}),e.createVNode(w,{prop:"address",width:"125px"},{default:e.withCtx(({row:E})=>[e.createElementVNode("div",zt,[e.createTextVNode(" 一级:"),e.createElementVNode("span",{class:e.normalizeClass([E.bonusShare.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(g(E.bonusShare.shareType,E.bonusShare.one)),3)]),E.bonusShare.two?(e.openBlock(),e.createElementBlock("div",At,[e.createTextVNode(" 二级:"),e.createElementVNode("span",{class:e.normalizeClass([E.bonusShare.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(g(E.bonusShare.shareType,E.bonusShare.two)),3)])):e.createCommentVNode("",!0),E.bonusShare.three?(e.openBlock(),e.createElementBlock("div",$t,[e.createTextVNode(" 三级:"),e.createElementVNode("span",{class:e.normalizeClass([E.bonusShare.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(g(E.bonusShare.shareType,E.bonusShare.three)),3)])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createElementVNode("div",Lt,[e.createElementVNode("div",Rt,[e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].one.name),1),e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].one.mobile),1),e.createElementVNode("div",null,[e.createVNode(F,{"raw-content":!0,content:(m.orderInfo.items[0].one.bonus!=="0"?e.unref(c)((M=m.orderInfo.items)==null?void 0:M[0],"one",s("one")):"该层级无分销员，不计算对应佣金").toString(),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createElementVNode("span",null,[e.createTextVNode(" 一级佣金："),e.createElementVNode("span",{style:e.normalizeStyle(m.orderInfo.items[0].purchase?"color:#FD0505":"")},e.toDisplayString(s("one",m.orderInfo.items[0].purchase)),5)])]),_:1},8,["content"])])]),e.createElementVNode("div",Pt,[e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].two.name),1),e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].two.mobile),1),e.createVNode(F,{"raw-content":!0,content:(m.orderInfo.items[0].two.userId?e.unref(c)((P=m.orderInfo.items)==null?void 0:P[0],"two",s("two")):"该层级无分销员，不计算对应佣金").toString(),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createElementVNode("div",null,"二级佣金："+e.toDisplayString(s("two")),1)]),_:1},8,["content"])]),e.createElementVNode("div",qt,[e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].three.name),1),e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].three.mobile),1),e.createVNode(F,{"raw-content":!0,content:(m.orderInfo.items[0].two.userId?e.unref(c)((Y=m.orderInfo.items)==null?void 0:Y[0],"two",s("two")):"该层级无分销员，不计算对应佣金").toString(),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createElementVNode("div",null,"二级佣金："+e.toDisplayString(s("three")),1)]),_:1},8,["content"])])]),e.createElementVNode("div",jt,[r("PAID")?(e.openBlock(),e.createElementBlock("div",Xt,[e.createVNode(F,{"raw-content":!0,content:e.unref(n)(m.orderInfo).toSelledPrice,placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("待结算："+e.toDisplayString(r("PAID")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),r("COMPLETED")?(e.openBlock(),e.createElementBlock("div",Ht,[e.createVNode(F,{"raw-content":!0,content:e.unref(n)(m.orderInfo).completePrice,placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("已赚："+e.toDisplayString(r("COMPLETED")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),r("CLOSED")?(e.openBlock(),e.createElementBlock("div",Mt,[e.createVNode(F,{"raw-content":!0,content:e.unref(n)(m.orderInfo).closedPrice,placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("已失效："+e.toDisplayString(r("CLOSED")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),e.createElementVNode("div",Wt,[e.createVNode(F,{"raw-content":!0,content:e.unref(n)(m.orderInfo).totalPrice,placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("合计："+e.toDisplayString(r()),1)]),_:1},8,["content"])])])])],64)}}}),zo="",Yt=ee(Gt,[["__scopeId","data-v-66efc4e8"]]),Qt=(m=>(e.pushScopeId("data-v-b509af32"),m=m(),e.popScopeId(),m))(()=>e.createElementVNode("div",{style:{"padding-bottom":"30px"}},[e.createElementVNode("p",null,"数据范围为本店铺内所有的分销订单（仅分销商品产生的订单。内购是指内购订单买家可获得对应商品的一级分销佣金。"),e.createElementVNode("p",{class:"fw"},"查询结果统计"),e.createElementVNode("p",null,"累计佣金：已完成订单状态中已赚的佣金之和"),e.createElementVNode("p",null,"待结算佣金：已付款订单状态中待结算的佣金之和"),e.createElementVNode("p",null,"已失效佣金：已关闭订单状态中已生效的佣金之和"),e.createElementVNode("p",{class:"fw"},"结算状态"),e.createElementVNode("p",null,"待结算：是指订单未完结(可能退款退货等情况)暂不计入累计佣金；"),e.createElementVNode("p",null,"已赚：是指对应商品没有退款(或退款失败) ，对应佣金统计到【累计佣金】中"),e.createElementVNode("p",null,"已失效：是指对应商品已退款成功，无法获得佣金。")],-1)),Jt=e.defineComponent({__name:"orderIllustration",props:{orderIllustrationShow:{type:Boolean,default:!1}},emits:["update:orderIllustrationShow","Illustration"],setup(m,{emit:_}){const c=m,n=e.computed({get:()=>c.orderIllustrationShow,set:N=>{_("update:orderIllustrationShow",N)}});return(N,p)=>{const u=e.resolveComponent("el-dialog");return e.openBlock(),e.createBlock(u,{modelValue:n.value,"onUpdate:modelValue":p[0]||(p[0]=g=>n.value=g),title:"分销订单说明",width:"45%",center:"","close-on-click-modal":!1},{default:e.withCtx(()=>[Qt]),_:1},8,["modelValue"])}}}),$o="",Kt=ee(Jt,[["__scopeId","data-v-b509af32"]]),Zt={class:"count"},vt={style:{float:"right"}},eo=["onClick"],to=e.createStaticVNode('<div class="tbhead" data-v-ed432cdc><div class="tbhead__goods" data-v-ed432cdc>商品</div><div class="tbhead__parameter" data-v-ed432cdc>分佣参数</div><div class="tbhead__detail" data-v-ed432cdc>分佣详情</div><div class="tbhead__total" data-v-ed432cdc>佣金结算</div></div>',1),oo=e.defineComponent({__name:"DistributionOrder",setup(m){const{divTenThousand:_}=oe(),c=e.ref(!1),n=e.reactive({orderNo:"",productName:"",shopName:"",buyerNickname:"",date:"",startTime:"",endTime:"",status:""}),N=e.reactive({current:1,size:10,total:0}),p=e.ref([]),u=e.ref({total:"",unsettled:"",invalid:""});g();async function g(){const{code:B,data:V,msg:q}=await Ae({current:N.current,size:N.size,...n});B===200&&V?(p.value=V.page.records,u.value=V.statistic,N.total=V.page.total):S.ElMessage.error(q||"获取分销订单失败")}const s=()=>{N.current=1,(n==null?void 0:n.date.length)>0&&(n.startTime=n.date[0],n.endTime=n.date[1]),g()},r=()=>{n.orderNo="",n.productName="",n.shopName="",n.buyerNickname="",n.date="",n.startTime="",n.endTime="",N.current=1,g()},y=B=>{N.current=B,g()},K=B=>{N.current=1,N.size=B,g()},x=new we,a=e.ref(" "),w=e.ref("全部订单"),z=B=>{n.status=B,g()},F=B=>{if(a.value=" ",w.value=B,w.value==="近一个月订单"){const V=x.getLastMonth(new Date);M(V)}else if(w.value==="近三个月订单"){const V=x.getLastThreeMonth(new Date);M(V)}else n.startTime="",n.endTime="",g()},M=async B=>{const V=x.getYMDs(new Date);n.startTime=B,n.endTime=V,g()},P=e.ref(!1),Y=B=>{P.value=B},E=()=>{P.value=!0};return(B,V)=>{const q=e.resolveComponent("el-input"),j=e.resolveComponent("el-form-item"),W=e.resolveComponent("el-col"),T=e.resolveComponent("el-date-picker"),b=e.resolveComponent("el-row"),A=e.resolveComponent("el-button"),Z=e.resolveComponent("el-form"),Q=e.resolveComponent("el-dropdown-item"),ne=e.resolveComponent("el-dropdown-menu"),v=e.resolveComponent("el-dropdown"),O=e.resolveComponent("el-tab-pane"),le=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(_e,{modelValue:c.value,"onUpdate:modelValue":V[4]||(V[4]=k=>c.value=k)},{default:e.withCtx(()=>[e.createVNode(Z,{ref:"ruleForm",model:n},{default:e.withCtx(()=>[e.createVNode(b,null,{default:e.withCtx(()=>[e.createVNode(W,{span:8},{default:e.withCtx(()=>[e.createVNode(j,{label:"订单号",prop:"orderNo","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(q,{modelValue:n.orderNo,"onUpdate:modelValue":V[0]||(V[0]=k=>n.orderNo=k),placeholder:"请填写订单号",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(W,{span:8},{default:e.withCtx(()=>[e.createVNode(j,{label:"商品名称",prop:"productName","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(q,{modelValue:n.productName,"onUpdate:modelValue":V[1]||(V[1]=k=>n.productName=k),placeholder:"请填写商品名称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(W,{span:8},{default:e.withCtx(()=>[e.createVNode(j,{label:"买家昵称",prop:"buyerNickname","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(q,{modelValue:n.buyerNickname,"onUpdate:modelValue":V[2]||(V[2]=k=>n.buyerNickname=k),placeholder:"请填写买家昵称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(W,{span:16},{default:e.withCtx(()=>[e.createVNode(j,{label:"下单时间",prop:"date","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(T,{modelValue:n.date,"onUpdate:modelValue":V[3]||(V[3]=k=>n.date=k),clearable:!1,type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","value-format":"YYYY-MM-DD HH:mm:ss","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(b,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(W,{span:8},{default:e.withCtx(()=>[e.createVNode(A,{type:"primary",round:"",onClick:s},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1}),e.createVNode(A,{type:"primary",round:"",onClick:r},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createElementVNode("div",Zt,[e.createElementVNode("span",null,"累计总佣金： ￥ "+e.toDisplayString(e.unref(_)(u.value.total)),1),e.createElementVNode("span",null,"待结算总佣金： ￥ "+e.toDisplayString(e.unref(_)(u.value.unsettled)),1),e.createElementVNode("span",null,"已失效总佣金： ￥"+e.toDisplayString(e.unref(_)(u.value.invalid)),1),e.createElementVNode("span",vt,[e.createVNode(Ee,{name:"icon-jingshi",size:"30",color:"#5b6982",style:{cursor:"pointer"},onClick:E})])]),e.createVNode(le,{modelValue:a.value,"onUpdate:modelValue":V[5]||(V[5]=k=>a.value=k),onTabChange:z},{default:e.withCtx(()=>[e.createVNode(O,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(w.value),1),e.createVNode(v,{placement:"bottom-end",trigger:"click",onCommand:F},{dropdown:e.withCtx(()=>[e.createVNode(ne,null,{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(["近一个月订单","近三个月订单","全部订单"],k=>e.createVNode(Q,{key:k,command:k},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(k),1)]),_:2},1032,["command"])),64))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(e.unref(S.ElIcon),{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(e.unref(ce.ArrowDown))]),_:1})],8,eo),e.createVNode(e.unref(S.ElIcon),null,{default:e.withCtx(()=>[e.createVNode(e.unref(ce.ArrowDown))]),_:1})]),_:1})]),_:1}),e.createVNode(O,{label:"已付款",name:"PAID"}),e.createVNode(O,{label:"已完成",name:"COMPLETED"}),e.createVNode(O,{label:"已关闭",name:"CLOSED"})]),_:1},8,["modelValue"]),to,(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(p.value,k=>(e.openBlock(),e.createBlock(Yt,{key:k.orderNo,"order-info":k},null,8,["order-info"]))),128)),e.createVNode(H,{"page-num":N.current,"page-size":N.size,total:N.total,onHandleCurrentChange:y,onHandleSizeChange:K},null,8,["page-num","page-size","total"]),e.createVNode(Kt,{"order-illustration-show":P.value,"onUpdate:orderIllustrationShow":V[6]||(V[6]=k=>P.value=k),onIllustration:Y},null,8,["order-illustration-show"])],64)}}}),Lo="",no=Object.freeze(Object.defineProperty({__proto__:null,default:ee(oo,[["__scopeId","data-v-ed432cdc"]])},Symbol.toStringTag,{value:"Module"})),lo={style:{width:"190px"}},ao={key:0,style:{width:"135px"},class:"ml"},ro={key:1,style:{width:"200px"},class:"ml"},so=["onClick"],io={class:"f12 color51",style:{"margin-left":"6px"}},co={class:"ellipsis"},mo={key:0},po={style:{display:"flex","align-items":"center","justify-content":"space-around",width:"260px"}},ho={style:{"font-size":"20px"}},fo=["onClick"],_o={class:"f12 color58",style:{"margin-bottom":"20px"}},uo={class:"f12",style:{display:"flex"}},No={class:"ml"},bo={class:"ml"},go={class:"dialog-footer"},xo={class:"dialog-footer"},Vo=e.defineComponent({__name:"WholeSaler",setup(m){const _=e.ref(!1),c=e.reactive({name:"",mobile:"",date:"",startTime:"",endTime:"",status:"SUCCESS"}),{divTenThousand:n}=oe(),N=e.reactive({current:1,size:10,total:0}),p=e.ref([]),u=e.ref([]),g=e.ref(!1),s=e.reactive({current:1,total:0,size:10,list:[],userId:""}),r=e.ref(!1),y=e.reactive({current:1,total:0,size:10,list:[],userId:"",rank:""});V();const K=T=>{N.current=T,V()},x=T=>{N.current=1,N.size=T,V()},a=()=>{N.current=1,c.date.length>0&&(console.log("searchConfig.date",c.date),c.startTime=c.date[0],c.endTime=c.date[1]),V()},w=T=>{c.status==="SUCCESS"&&(g.value=!0,s.userId=T,q(T))},z=T=>{c.status==="SUCCESS"&&(r.value=!0,y.userId=T,j(T))},F=()=>{s.current=1,s.size=10,s.list=[],s.userId=""},M=()=>{y.current=1,y.size=10,y.list=[],y.userId="",y.rank=""},P=T=>{s.current=T,q()},Y=T=>{s.current=1,s.size=T,q()},E=T=>{y.current=T,j()},B=T=>{y.current=1,y.size=T,j()};async function V(){const{code:T,data:b}=await $e({...c,...N});T===200?(p.value=b.records,N.total=b.total):S.ElMessage.error("获取分销商失败")}async function q(T){const{code:b,data:A}=await Le({userId:T||s.userId,current:s.current,size:s.size});b&&b===200?(s.list=A.records,s.total=A.total,g.value=!0):(S.ElMessage.error("获取下线失败"),g.value=!1)}async function j(T){const{code:b,data:A}=await Re({userId:T||y.userId,current:y.current,size:y.size});b&&b===200?(y.list=A.records,y.total=A.total,y.rank=A.rank,r.value=!0):(S.ElMessage.error("获取排行失败"),r.value=!1)}const W=()=>{N.current=1,c.date="",c.endTime="",c.startTime="",c.mobile="",c.name="",V()};return(T,b)=>{const A=e.resolveComponent("el-input"),Z=e.resolveComponent("el-form-item"),Q=e.resolveComponent("el-col"),ne=e.resolveComponent("el-date-picker"),v=e.resolveComponent("el-row"),O=e.resolveComponent("el-button"),le=e.resolveComponent("el-form"),k=e.resolveComponent("el-image"),G=e.resolveComponent("el-table-column"),re=e.resolveComponent("el-table"),se=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(_e,{modelValue:_.value,"onUpdate:modelValue":b[3]||(b[3]=f=>_.value=f)},{default:e.withCtx(()=>[e.createVNode(le,{ref:"ruleForm",model:c},{default:e.withCtx(()=>[e.createVNode(v,null,{default:e.withCtx(()=>[e.createVNode(Q,{span:8},{default:e.withCtx(()=>[e.createVNode(Z,{label:"姓名",prop:"name","label-width":"50px"},{default:e.withCtx(()=>[e.createVNode(A,{modelValue:c.name,"onUpdate:modelValue":b[0]||(b[0]=f=>c.name=f),placeholder:"请填写姓名检索",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(Q,{span:8},{default:e.withCtx(()=>[e.createVNode(Z,{label:"手机号",prop:"mobile","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(A,{modelValue:c.mobile,"onUpdate:modelValue":b[1]||(b[1]=f=>c.mobile=f),placeholder:"请填写手机号检索",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(Q,{span:16},{default:e.withCtx(()=>[e.createVNode(Z,{label:"申请时间",prop:"date","label-width":"75px"},{default:e.withCtx(()=>[e.createVNode(ne,{modelValue:c.date,"onUpdate:modelValue":b[2]||(b[2]=f=>c.date=f),type:"datetimerange","range-separator":"-",clearable:!1,"start-placeholder":"开始时间","value-format":"YYYY-MM-DD HH:mm:ss","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(v,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(Q,{span:8},{default:e.withCtx(()=>[e.createVNode(O,{type:"primary",round:"",onClick:a},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1}),e.createVNode(O,{type:"primary",round:"",onClick:W},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createVNode(e.unref(Te),{"checked-item":u.value,"onUpdate:checkedItem":b[4]||(b[4]=f=>u.value=f),data:p.value,selection:!0},{header:e.withCtx(({row:f})=>[e.createElementVNode("div",lo,"申请时间："+e.toDisplayString(f.createTime),1),c.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",ao,"审核人员："+e.toDisplayString(f.auditor),1)):e.createCommentVNode("",!0),c.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",ro,"审核(通过)："+e.toDisplayString(f.passTime),1)):e.createCommentVNode("",!0),c.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",{key:2,style:{"margin-left":"200px",color:"#0f40f5",cursor:"pointer"},onClick:de=>z(f.userId)}," 佣金排行榜 ",8,so)):e.createCommentVNode("",!0)]),default:e.withCtx(()=>[e.createVNode(me,{label:"分销商信息",width:"90"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",null,[e.createVNode(v,{justify:"space-between"},{default:e.withCtx(()=>[e.createVNode(k,{src:f.avatar,style:{width:"40px",height:"40px","border-radius":"4px"}},null,8,["src"]),e.createElementVNode("div",io,[e.createElementVNode("div",co,e.toDisplayString(f.name),1),e.createElementVNode("div",null,e.toDisplayString(f.mobile),1),c.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",mo,"上级 ："+e.toDisplayString(f.referrer||"平台"),1)):e.createCommentVNode("",!0)])]),_:2},1024)])]),_:1}),c.status==="SUCCESS"?(e.openBlock(),e.createBlock(me,{key:0,label:"团队成员",width:"100"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",po,[e.createElementVNode("div",null,[e.createTextVNode(" 总人数： "),e.createElementVNode("span",ho,e.toDisplayString(Number(f.one)+Number(f.two)+Number(f.three)),1)]),e.createElementVNode("div",{class:"column",onClick:de=>w(f.userId)},[e.createVNode(O,{type:"primary",link:""},{default:e.withCtx(()=>[e.createTextVNode("一级："+e.toDisplayString(f.one),1)]),_:2},1024),e.createVNode(O,{type:"primary",link:""},{default:e.withCtx(()=>[e.createTextVNode("二级："+e.toDisplayString(f.two),1)]),_:2},1024),e.createVNode(O,{type:"primary",link:""},{default:e.withCtx(()=>[e.createTextVNode("三级："+e.toDisplayString(f.three),1)]),_:2},1024)],8,fo)])]),_:1})):e.createCommentVNode("",!0),c.status==="SUCCESS"?(e.openBlock(),e.createBlock(me,{key:1,label:"佣金",width:"150"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",null,[e.createElementVNode("div",_o,"累计佣金："+e.toDisplayString(e.unref(n)(f.total)),1),e.createElementVNode("div",uo,[e.createElementVNode("div",null,"待提现佣金："+e.toDisplayString(e.unref(n)(f.undrawn)),1),e.createElementVNode("div",No,"待结算佣金："+e.toDisplayString(e.unref(n)(f.unsettled)),1),e.createElementVNode("div",bo,"已失效佣金："+e.toDisplayString(e.unref(n)(f.invalid)),1)])])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["checked-item","data"]),e.createVNode(H,{"page-num":N.current,"page-size":N.size,total:N.total,onHandleCurrentChange:K,onHandleSizeChange:x},null,8,["page-num","page-size","total"]),e.createVNode(se,{modelValue:g.value,"onUpdate:modelValue":b[7]||(b[7]=f=>g.value=f),width:"554px",title:"团队成员",onClose:F},{footer:e.withCtx(()=>[e.createElementVNode("span",go,[e.createVNode(O,{onClick:b[5]||(b[5]=f=>g.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(O,{type:"primary",onClick:b[6]||(b[6]=f=>g.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(re,{data:s.list,"header-cell-style":{background:"#f6f8fa",fontSize:"12px",color:"#909399","font-weight":"bold"},"cell-style":{fontSize:"12px",color:"#333333"},height:"400"},{default:e.withCtx(()=>[e.createVNode(G,{label:"姓名",align:"center"},{default:e.withCtx(({row:f})=>[e.createTextVNode(e.toDisplayString(f.name?f.name:f.nickname),1)]),_:1}),e.createVNode(G,{label:"层级",align:"center"},{default:e.withCtx(({row:f})=>[e.createTextVNode(e.toDisplayString(f.level==="ONE"?"一级":f.level==="TWO"?"二级":"三级"),1)]),_:1}),e.createVNode(G,{label:"累计消费",align:"center",prop:"consumption"},{default:e.withCtx(({row:f})=>[e.createTextVNode(e.toDisplayString(e.unref(n)(f.consumption)),1)]),_:1}),e.createVNode(G,{label:"订单数",align:"center",prop:"orderCount"})]),_:1},8,["data"]),e.createVNode(H,{"page-num":s.current,"page-size":s.size,total:s.total,onHandleCurrentChange:P,onHandleSizeChange:Y},null,8,["page-num","page-size","total"])]),_:1},8,["modelValue"]),e.createVNode(se,{modelValue:r.value,"onUpdate:modelValue":b[10]||(b[10]=f=>r.value=f),width:"554px",title:"佣金排行榜",onClose:M},{footer:e.withCtx(()=>[e.createElementVNode("span",xo,[e.createVNode(O,{onClick:b[8]||(b[8]=f=>r.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(O,{type:"primary",onClick:b[9]||(b[9]=f=>r.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("div",null,"您的累计佣金排名为："+e.toDisplayString(y.rank.rank),1),e.createVNode(re,{data:y.list,"header-cell-style":{background:"#f6f8fa",fontSize:"12px",color:"#909399","font-weight":"bold"},"cell-style":{fontSize:"12px",color:"#333333"},height:"400"},{default:e.withCtx(()=>[e.createVNode(G,{label:"名次",align:"center",type:"index"}),e.createVNode(G,{label:"姓名",align:"center",prop:"name"}),e.createVNode(G,{label:"累计佣金",align:"center",prop:"total"},{default:e.withCtx(({row:f})=>[e.createTextVNode(e.toDisplayString(e.unref(n)(f.total)),1)]),_:1})]),_:1},8,["data"]),e.createVNode(H,{"page-num":y.current,"page-size":y.size,total:y.total,onHandleCurrentChange:E,onHandleSizeChange:B},null,8,["page-num","page-size","total"])]),_:1},8,["modelValue"])],64)}}}),Po="",Co=Object.freeze(Object.defineProperty({__proto__:null,default:ee(Vo,[["__scopeId","data-v-5215f2ec"]])},Symbol.toStringTag,{value:"Module"}));return ke});
