server:
  port: 8270
spring:
  main:
    allow-circular-references: true
  application:
    name: gruul-mall-addon-platform
  profiles:
    active: prod
  cloud:
    nacos:
      server-addr: **************:8884
#      server-addr: 127.0.0.1:8848
      discovery:
        namespace: ${spring.profiles.active}
        ip: *************
#        register-enabled: false
      config:
        namespace: ${spring.profiles.active}
        file-extension: yml
        shared-configs:
          - dataId: application-common.${spring.cloud.nacos.config.file-extension}
          - dataId: addon.${spring.cloud.nacos.config.file-extension}

