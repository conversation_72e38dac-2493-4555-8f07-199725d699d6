package com.medusa.gruul.addon.distribute.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.medusa.gruul.addon.distribute.model.DistributeConstant;
import com.medusa.gruul.addon.distribute.model.dto.ProductBindDTO;
import com.medusa.gruul.addon.distribute.model.dto.ProductQueryDTO;
import com.medusa.gruul.addon.distribute.model.enums.DistributionStatus;
import com.medusa.gruul.addon.distribute.model.enums.DistributorIdentity;
import com.medusa.gruul.addon.distribute.model.enums.Precompute;
import com.medusa.gruul.addon.distribute.mp.entity.DistributeConf;
import com.medusa.gruul.addon.distribute.mp.entity.DistributeProduct;
import com.medusa.gruul.addon.distribute.mp.entity.DistributeShop;
import com.medusa.gruul.addon.distribute.mp.entity.Distributor;
import com.medusa.gruul.addon.distribute.mp.mapper.DistributeProductMapper;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeProductService;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeShopService;
import com.medusa.gruul.addon.distribute.service.DistributeConfHandleService;
import com.medusa.gruul.addon.distribute.service.DistributeProductHandleService;
import com.medusa.gruul.addon.distribute.service.DistributorHandleService;
import com.medusa.gruul.addon.distribute.util.DistributeUtil;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.dto.ProductUpdateStatusDTO;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.vo.DistributeProductVO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import com.medusa.gruul.storage.api.vo.ProductStatisticsVO;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/11/14
 */
@Service
@RequiredArgsConstructor
public class DistributeProductHandleServiceImpl implements DistributeProductHandleService, DisposableBean {

    private final GoodsRpcService goodsRpcService;
    private final ShopRpcService shopRpcService;
    private final StorageRpcService storageRpcService;
    private final SqlSessionFactory sqlSessionFactory;
    private final IDistributeShopService distributeShopService;
    private final IDistributeProductService distributeProductService;
    private final DistributorHandleService distributorHandleService;
    private final DistributeConfHandleService distributeConfHandleService;
    private final Map<ShopProductKey, AtomicLong> productSalesLockMap = new ConcurrentHashMap<>();

    @Override
    @Redisson(value = DistributeConstant.DISTRIBUTE_PRODUCT_BIND_LOCK, key = "#product.shopId+':'+#product.id")
    public void updateProductByProduct(Product product) {
        Long shopId = product.getShopId();
        Long productId = product.getId();
        if (!distributeProductService.lambdaQuery()
                .select(DistributeProduct::getId)
                .eq(DistributeProduct::getProductId, productId)
                .eq(DistributeProduct::getShopId, shopId)
                .exists()) {
            return;
        }

        distributeProductService.lambdaUpdate()
                .set(DistributeProduct::getName, product.getName())
                .set(DistributeProduct::getPic, product.getPic())
                .set(DistributeProduct::getSalePrices, JSON.toJSONString(product.getSalePrices()))
                .eq(DistributeProduct::getShopId, shopId)
                .eq(DistributeProduct::getProductId, productId)
                .update();
    }

    /**
     * 检查店铺id 并保存店铺信息到分销服务
     *
     * @param shopId 店铺id
     */
    private void checkShopIdAndSaveShopInfo(Long shopId) {
        boolean exists = distributeShopService.lambdaQuery()
                .select(DistributeShop::getId)
                .eq(DistributeShop::getShopId, shopId)
                .exists();
        if (exists) {
            return;
        }
        Option.of(shopRpcService.getShopInfoByShopId(shopId))
                .peek(
                        shopInfo -> distributeShopService.save(
                                new DistributeShop().setShopId(shopId)
                                        .setShopName(shopInfo.getName())
                                        .setShopLogo(shopInfo.getLogo())
                        )
                )
                .getOrElseThrow(() -> new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, SystemCode.PARAM_TYPE_ERROR.getMsg()));
    }

    @Override
    @Redisson(value = DistributeConstant.DISTRIBUTE_PRODUCT_BIND_LOCK, key = "#shopId")
    public void newProduct(Long shopId, ProductBindDTO productBind) {
        productBind.validParam(distributeConfHandleService.configMust().getLevel());
        Set<Long> productIds = productBind.getProductIds();
        /* 检查店铺id 并保存店铺信息到分销服务
         */
        this.checkShopIdAndSaveShopInfo(shopId);
        /* 检查商品信息
         */
        Map<ShopProductKey, Product> productBatch = goodsRpcService.getProductBatch(
                productIds.stream()
                        .map(productId -> new ShopProductKey().setShopId(shopId).setProductId(productId))
                        .collect(Collectors.toSet())
        );
        if (productBatch.size() != productIds.size()) {
            throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, SystemCode.PARAM_TYPE_ERROR.getMsg());
        }
        List<DistributeProduct> distributeProducts = productBatch.entrySet()
                .stream()
                .map(
                        entry -> {
                            Product product = entry.getValue();
                            Long productId = entry.getKey().getProductId();
                            if (product == null || ProductStatus.SELL_ON != product.getStatus()) {
                                throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "商品不处于正常状态:" + productId);
                            }
                            if (distributeProductService.lambdaQuery()
                                    .eq(DistributeProduct::getShopId, shopId)
                                    .eq(DistributeProduct::getProductId, productId)
                                    .exists()) {
                                throw new GlobalException(SystemCode.DATA_EXISTED_CODE, StrUtil.format("商品名:{},已存在绑定关系", product.getName()));
                            }
                            return new DistributeProduct()
                                    .setShopId(shopId)
                                    .setProductId(productId)
                                    .setProductNo(product.getNo())
                                    .setSales(0L)
                                    .setName(product.getName())
                                    .setPic(product.getPic())
                                    .setSalePrices(product.getSalePrices())
                                    .setStatus(product.getStatus())
                                    .setDistributionStatus(DistributionStatus.IN_DISTRIBUTION)
                                    .setShareType(productBind.getShareType())
                                    .setOne(productBind.getOne())
                                    .setTwo(productBind.getTwo())
                                    .setThree(productBind.getThree());
                        }
                ).toList();
        boolean success = distributeProductService.saveBatch(distributeProducts);
        if (!success) {
            throw new GlobalException(SystemCode.DATA_ADD_FAILED_CODE, SystemCode.PARAM_TYPE_ERROR.getMsg());
        }
    }

    @Override
    @Redisson(value = DistributeConstant.DISTRIBUTE_PRODUCT_BIND_LOCK, key = "#shopId+':'+#productBind.productIds[0]")
    public void editProduct(Long shopId, Long bindId, ProductBindDTO productBind) {
        Set<Long> productIds = productBind.getProductIds();
        if (productIds.size() != 1) {
            throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, SystemCode.PARAM_TYPE_ERROR.getMsg());
        }
        Long productId = productIds.iterator().next();
        productBind.validParam(distributeConfHandleService.configMust().getLevel());

        /* 检查是否已存在绑定关系
         */
        if (distributeProductService.lambdaQuery()
                .select(DistributeProduct::getId)
                .eq(DistributeProduct::getShopId, shopId)
                .eq(DistributeProduct::getProductId, productId)
                .ne(DistributeProduct::getId, bindId)
                .exists()) {
            throw new GlobalException(SystemCode.DATA_EXISTED_CODE, SystemCode.PARAM_TYPE_ERROR.getMsg());
        }
        /* 检查商品信息
         */
        Product product = goodsRpcService.getProductInfo(shopId, productId);
        if (product == null || ProductStatus.SELL_ON != product.getStatus()) {
            throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, SystemCode.PARAM_VALID_ERROR.getMsg());
        }
        boolean success = distributeProductService.lambdaUpdate()
                .set(DistributeProduct::getProductId, productId)
                .set(DistributeProduct::getName, product.getName())
                .set(DistributeProduct::getPic, product.getPic())
                .set(DistributeProduct::getSalePrices, JSON.toJSONString(product.getSalePrices()))
                .set(DistributeProduct::getStatus, product.getStatus())
                .set(DistributeProduct::getShareType, productBind.getShareType())
                .set(DistributeProduct::getOne, productBind.getOne())
                .set(DistributeProduct::getTwo, productBind.getTwo())
                .set(DistributeProduct::getThree, productBind.getThree())
                .eq(DistributeProduct::getShopId, shopId)
                .eq(DistributeProduct::getId, bindId)
                .update();
        if (!success) {
            throw new GlobalException(SystemCode.DATA_UPDATE_FAILED_CODE, SystemCode.DATA_UPDATE_FAILED.getMsg());
        }
    }

    @Override
    public void deleteBatch(Long shopId, Set<Long> bindIds) {
        distributeProductService.lambdaUpdate()
                .eq(DistributeProduct::getShopId, shopId)
                .in(DistributeProduct::getId, bindIds)
                .remove();
    }

    /**
     * 批量取消分销商品
     *
     * @param shopId  店铺id
     * @param bindIds 分销商品绑定关系id集合
     */
    @Override
    public void cancelBatch(Long shopId, Set<Long> bindIds) {
        bindIds.forEach(bindId ->
                SystemCode.DATA_UPDATE_FAILED.falseThrow(
                        distributeProductService.lambdaUpdate()
                                .set(DistributeProduct::getDistributionStatus, DistributionStatus.CANCEL_DISTRIBUTION)
                                .eq(shopId != 0, DistributeProduct::getShopId, shopId)
                                .in(DistributeProduct::getId, bindIds)
                                .update()
                )
        );
    }

    /**
     * 重新分销
     *
     * @param bindId 分销商品绑定关系id
     */
    @Override
    public void againDistribute(Long shopId, Long bindId) {
        SystemCode.DATA_UPDATE_FAILED.falseThrow(
                distributeProductService.lambdaUpdate()
                        .eq(DistributeProduct::getShopId, shopId)
                        .eq(DistributeProduct::getId, bindId)
                        .ne(DistributeProduct::getStatus, ProductStatus.PLATFORM_SELL_OFF)
                        .set(DistributeProduct::getDistributionStatus, DistributionStatus.IN_DISTRIBUTION)
                        .update()
        );
    }

    @Override
    public List<DistributeProductVO> getDistributeProductList(Long shopId, Set<Long> productIds) {
        List<DistributeProduct> distributeProductList = distributeProductService.lambdaQuery()
                .eq(shopId != null && shopId > 0, DistributeProduct::getShopId, shopId)
                .in(DistributeProduct::getProductId, productIds).list();
        List<DistributeProductVO> distributeProductVOList = CollUtil.list(false);
        distributeProductList.stream().forEach(eachProduct -> {
            DistributeProductVO distributeProductVO = new DistributeProductVO();
            BeanUtils.copyProperties(eachProduct, distributeProductVO, DistributeProductVO.class);
            distributeProductVO.setDistributionStatus(eachProduct.getDistributionStatus().name());
            distributeProductVO.setShareType(eachProduct.getShareType().getValue());
            distributeProductVOList.add(distributeProductVO);
        });
        return distributeProductVOList;
    }

    @Override
    public IPage<DistributeProduct> productPage(ProductQueryDTO query) {
        List<OrderItem> orders = query.getOrders();
        if (CollUtil.isEmpty(orders)) {
            query.setOrders(List.of(OrderItem.desc("createTime")));
        }
        IPage<DistributeProduct> result = distributeProductService.productPage(query);
        List<DistributeProduct> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return result;
        }
        Consumer<SecureUser> secureUserConsumer = secureUser -> {
            Map<ShopProductKey, DistributeProduct> keyMap = records.stream()
                    .collect(
                            Collectors.toMap(
                                    product -> new ShopProductKey().setShopId(product.getShopId()).setProductId(product.getProductId()),
                                    v -> v)
                    );

            Map<ShopProductKey, ProductStatisticsVO> productStatisticsMap = storageRpcService.getProductStatisticsMap(keyMap.keySet());
            keyMap.forEach(
                    (key, value) -> {
                        if (Option.of(productStatisticsMap.get(key)).peek(statistics -> value.setStock(statistics.getRemainingStock())).isEmpty()) {
                            value.setStock(0L);
                        }
                    }
            );
        };
        ISecurity.match()
                .ifAnySuperAdmin(secureUserConsumer)
                .ifAnyShopAdmin(secureUserConsumer);

        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStatus(List<ProductUpdateStatusDTO> productUpdateStatus) {
        productUpdateStatus.forEach(
                shopProducts -> distributeProductService.lambdaUpdate()
                        .set(DistributeProduct::getStatus, shopProducts.getProductStatus())
                        .set(DistributionStatus.toDistributeStatus(shopProducts.getProductStatus()) != null, DistributeProduct::getDistributionStatus, DistributionStatus.CANCEL_DISTRIBUTION)
                        .eq(DistributeProduct::getShopId, shopProducts.getShopId())
                        .in(DistributeProduct::getProductId, shopProducts.getProductIds())
                        .update()
        );
    }

    @Override
    public Map<ShopProductKey, DistributeProduct> shopProductConfMap(Set<ShopProductKey> shopProductKeys) {
        List<DistributeProduct> distributeProducts = distributeProductService.getShoppProductConfigs(shopProductKeys);
        if (CollUtil.isEmpty(distributeProducts)) {
            return Collections.emptyMap();
        }
        return distributeProducts.stream()
                .collect(
                        Collectors.toMap(
                                product -> new ShopProductKey().setShopId(product.getShopId()).setProductId(product.getProductId()),
                                product -> product
                        )
                );
    }

    @Override
    public void updateProductSales(Map<ShopProductKey, Long> productSales) {
        if (CollUtil.isEmpty(productSales)) {
            return;
        }
        productSales.forEach(
                (shopProductKey, sales) -> {
                    if (sales == null || sales <= 0) {
                        return;
                    }
                    AtomicLong volume = productSalesLockMap.computeIfAbsent(shopProductKey, key -> new AtomicLong());
                    volume.addAndGet(sales);
                }
        );
    }

    @Override
    public Long productPrecompute(ShopProductKey key) {
        Option<DistributeConf> config = distributeConfHandleService.config();
        if (config.isEmpty()) {
            return null;
        }
        DistributeConf conf = config.get();
        Precompute precompute = conf.getPrecompute();
        //从不展示
        if (precompute == null || Precompute.NEVER == precompute) {
            return null;
        }
        //仅对分销商/分销员展示
        if (Precompute.DISTRIBUTOR == precompute) {
            Option<SecureUser> secureUsers = ISecurity.userOpt();
            if (secureUsers.isEmpty()) {
                return null;
            }
            Option<Distributor> distributorOption = distributorHandleService.getByUserId(secureUsers.get().getId());
            if (distributorOption.isEmpty()) {
                return null;
            }
            Distributor distributor = distributorOption.get();
            //不是分销商 且 上级分销商为空 表示 该用户不是分销员
            if (distributor.getIdentity() != DistributorIdentity.AFFAIRS && distributor.getOne() == null) {
                return null;
            }
        }
        Optional<DistributeProduct> productOptional = distributeProductService.lambdaQuery()
                .eq(DistributeProduct::getDistributionStatus, DistributionStatus.IN_DISTRIBUTION)
                .eq(DistributeProduct::getShopId, key.getShopId())
                .eq(DistributeProduct::getProductId, key.getProductId())
                .oneOpt();
        if (productOptional.isEmpty()) {
            return null;
        }
        DistributeProduct product = productOptional.get();
        //获取最大的金额
        List<Long> salePrices = product.getSalePrices();
        if (CollUtil.isEmpty(salePrices)) {
            return null;
        }
        Long maxPrice = salePrices.get(salePrices.size() - CommonPool.NUMBER_ONE);
        //计算分佣金额
        return DistributeUtil.getCurrentLevelBonus(CommonPool.NUMBER_ONE, maxPrice, product.getShareType(), product.getOne());
    }


    @Scheduled(initialDelay = 5, fixedRate = 10, timeUnit = TimeUnit.SECONDS)
    public void updateProductSales() {
        if (CollUtil.isEmpty(productSalesLockMap)) {
            return;
        }
        Set<ShopProductKey> shopProductKeys = productSalesLockMap.keySet();
        Map<ShopProductKey, Long> productSalesMap = new HashMap<>(CommonPool.NUMBER_THIRTY);
        for (ShopProductKey key : shopProductKeys) {
            AtomicLong sales = productSalesLockMap.remove(key);
            if (sales == null || sales.get() <= 0) {
                continue;
            }
            productSalesMap.put(key, sales.get());
        }
        this.updateProductSalesBatch(productSalesMap);
    }

    /**
     * 容器销毁之前先同步数据
     */
    @Override
    public void destroy() {
        this.updateProductSales();
    }

    /**
     * 更新商品销量
     *
     * @param productSalesMap 商品销量map
     */
    private void updateProductSalesBatch(Map<ShopProductKey, Long> productSalesMap) {
        if (CollUtil.isEmpty(productSalesMap)) {
            return;
        }
        RedissonClient redissonClient = RedisUtil.getRedissonClient();
        RLock multiLock = redissonClient.getMultiLock(
                productSalesMap.keySet().stream()
                        .map(productId -> RedisUtil.key(DistributeConstant.PRODUCT_SALES_LOCK_KEY, productId.getShopId(), productId.getProductId()))
                        .map(redissonClient::getLock)
                        .toArray(RLock[]::new)
        );
        multiLock.lock();
        try {
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            try {
                DistributeProductMapper mapper = sqlSession.getMapper(DistributeProductMapper.class);
                productSalesMap.forEach(
                        (shopProductKey, sales) ->
                                mapper.update(
                                        null,
                                        Wrappers.lambdaUpdate(DistributeProduct.class)
                                                .eq(DistributeProduct::getShopId, shopProductKey.getShopId())
                                                .eq(DistributeProduct::getProductId, shopProductKey.getProductId())
                                                .setSql(StrUtil.format(DistributeConstant.PRODUCT_SALES_INCREMENT_SQL_TEMPLATE, sales))
                                )

                );
                sqlSession.commit();
            } catch (Exception exception) {
                this.updateProductSales(productSalesMap);
                sqlSession.rollback();
                throw exception;
            } finally {
                sqlSession.close();
            }
        } finally {
            multiLock.unlock();
        }
    }

}
