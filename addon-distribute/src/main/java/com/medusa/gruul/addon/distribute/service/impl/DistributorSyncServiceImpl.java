package com.medusa.gruul.addon.distribute.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.medusa.gruul.addon.distribute.mp.entity.DistributeProduct;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeProductService;
import com.medusa.gruul.addon.distribute.service.DistributeProductHandleService;
import com.medusa.gruul.addon.distribute.service.DistributorSyncService;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.shop.api.model.dto.ShopProductSyncDTO;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import com.medusa.gruul.shop.api.enums.*;
import com.medusa.gruul.shop.api.model.dto.ShopQueryDTO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributorSyncServiceImpl implements DistributorSyncService {
    @Value("${gruul.shop.defaultShopId}")
    private Long defaultShopId;

    private final DistributeProductHandleService distributeProductHandleService;
    private final ShopRpcService shopRpcService;
    private final IDistributeProductService distributeProductService;

    @Override
    public void distributorProduct(ShopProductSyncDTO shopProductSyncDTO) {
        //检查当前操作的店铺是否为默认店铺
        if (!ISystem.shopIdOpt().get().equals(defaultShopId)) {
            throw new ServiceException("操作权限不足！");
        }
        log.debug("分销商品同步数据条件:{}", JSONUtil.toJsonStr(shopProductSyncDTO));
        //查询需要同步店铺
        ShopQueryDTO shopQueryDTO = new ShopQueryDTO();
        shopQueryDTO.setStatus(ShopStatus.NORMAL).setShopType(ShopType.PREFERRED);
        List<Shop> syncShopAll = shopRpcService.getShop(shopQueryDTO);
        //只同步指定类型
        if (shopProductSyncDTO.getTargetShopId() != null) {
            syncShopAll = syncShopAll.stream()
                    .filter(shop -> shop.getId().equals(shopProductSyncDTO.getTargetShopId()))
                    .filter(shop -> !shop.getId().equals(defaultShopId))
                    .collect(Collectors.toList());
        } else {
            syncShopAll = syncShopAll.stream()
                    .filter(shop -> !shop.getId().equals(defaultShopId))
                    .collect(Collectors.toList());
        }
        //是否有店铺需要同步
        if (ObjectUtils.isEmpty(syncShopAll)) {
            log.debug("没有需要同步的店铺");
            return;
        }
        //1.查询默认店铺分销商品数据
        List<DistributeProduct> distributeShopList = ISystem.shopId(defaultShopId, () -> distributeProductService.getProductListByShopId(defaultShopId));
        if (null == distributeShopList || distributeShopList.isEmpty()) {
            return;
        }
        //2.查询目标店铺分销商品数据
        syncShopAll.forEach(shop -> ISystem.shopId(shop.getId(), () -> {
            //原同步的商品数据
            List<ShopDataSyncRecord> recordTargetGoodsShopId = shopRpcService.getLatestRecordByTargetShopId(shop.getId(), ShopDataSyncType.PRODUCT);
            Map<Long, ShopDataSyncRecord> oldGoodsRecordMap = recordTargetGoodsShopId.stream().collect(Collectors.toMap(ShopDataSyncRecord::getSyncDataId, v -> v));

            List<DistributeProduct> targetProductList = distributeProductService.getProductListByShopId(shop.getId());
            //原同步数据
            List<ShopDataSyncRecord> recordTargetShopId = shopRpcService.getLatestRecordByTargetShopId(shop.getId(), ShopDataSyncType.DISTRIBUTOR_PRODUCT);
            //处理删除
            delDistributorProduct(recordTargetShopId, distributeShopList, targetProductList);
            //处理需要同步的数据
            Tuple3<List<DistributeProduct>, List<DistributeProduct>, List<ShopDataSyncRecord>> tuple3 = processProductChangesV2(recordTargetShopId, defaultShopId, distributeShopList, shop.getId(), targetProductList, oldGoodsRecordMap);
            //新增
            for (DistributeProduct product : tuple3._1()) {
                try {
                    log.debug("新增分销商品同步:{}", JSONUtil.toJsonStr(product));
                    distributeProductService.save(product);
                } catch (Exception e) {
                    log.error("新增分销商品同步失败, sycnShopId:{}, syncInfo:{}", shop.getId(), JSONUtil.toJsonStr(product), e);
                    Optional<ShopDataSyncRecord> optionalShopDataSyncRecord = tuple3._3().stream()
                            .filter(p -> p.getTargetDataId().equals(product.getId()))
                            .findFirst();
                    if (optionalShopDataSyncRecord.isPresent()) {
                        ShopDataSyncRecord shopDataSyncRecord = optionalShopDataSyncRecord.get();
                        shopDataSyncRecord.setStatus(ShopDataStatus.FAIL)
                                .setSyncMessage(JSON.parseObject(JSONUtil.toJsonStr(e)));
                    }

                }
            }
            //修改
            for (DistributeProduct product : tuple3._2()) {
                try {
                    log.debug("修改分销商品同步:{}", JSONUtil.toJsonStr(product));
                    distributeProductService.updateById(product);
                } catch (Exception e) {
                    log.error("修改分销商品同步失败, sycnShopId:{}, syncInfo:{}", shop.getId(), JSONUtil.toJsonStr(product), e);
                    Optional<ShopDataSyncRecord> optionalShopDataSyncRecord = tuple3._3().stream()
                            .filter(p -> p.getTargetDataId().equals(product.getId()))
                            .findFirst();
                    if (optionalShopDataSyncRecord.isPresent()) {
                        ShopDataSyncRecord shopDataSyncRecord = optionalShopDataSyncRecord.get();
                        shopDataSyncRecord.setStatus(ShopDataStatus.FAIL)
                                .setSyncMessage(JSON.parseObject(JSONUtil.toJsonStr(e)));
                    }
                }
            }
            shopRpcService.createShopSyncRecord(tuple3._3());
        }));
    }

    //保存同步记录
    private void convertSyncRecordV2(ShopDataSyncChangeType type, ShopDataSyncRecord shopDataSyncRecord, Long shopId,
                                     DistributeProduct product, Long targetShopId, DistributeProduct targetProduct) {

        if (Objects.equals(type.getValue(), ShopDataSyncChangeType.ADD.getValue())) {
            shopDataSyncRecord.setTargetDataId(product.getId());
        } else {
            shopDataSyncRecord.setTargetDataId(targetProduct.getId());
        }
        if (shopDataSyncRecord.getSyncDataId() == null) {
            shopDataSyncRecord.setSyncDataId(product.getId());
        }
        if (shopDataSyncRecord.getStatus() == null) {
            shopDataSyncRecord.setStatus(ShopDataStatus.SUCCESS);
        }
        shopDataSyncRecord.setShopId(shopId)
                .setSyncType(ShopDataSyncType.DISTRIBUTOR_PRODUCT)
                .setTargetShopId(targetShopId)
                .setSyncChangeType(type)
                .setSyncChangeData(JSON.parseObject(JSONUtil.toJsonStr(product)))
        ;
    }

    /**
     * 处理是新增还是修改，如果是新增，则去除原id
     *
     * @param recordTargetShopId    此店铺之前的同步记录
     * @param shopId                需要同步的产品店铺id
     * @param productDTOList        需要同步的产品
     * @param targetShopId          需要同步的目标店铺id
     * @param targetShopProductList 目标店铺分销产品
     * @return List<ProductDTO> add, List<ProductDTO> update, List<ShopDataSyncRecord> 记录
     */
    private Tuple3<List<DistributeProduct>, List<DistributeProduct>, List<ShopDataSyncRecord>> processProductChangesV2(List<ShopDataSyncRecord> recordTargetShopId,
                                                                                                                       Long shopId, List<DistributeProduct> productDTOList,
                                                                                                                       Long targetShopId, List<DistributeProduct> targetShopProductList,
                                                                                                                       Map<Long, ShopDataSyncRecord> oldGoodsRecordMap) {
        //同步记录
        List<ShopDataSyncRecord> recordList = Lists.newArrayList();
        //targetShopProductList转id map<no, Product>
        List<DistributeProduct> updateProductList = Lists.newArrayList();
        List<DistributeProduct> addProductList = Lists.newArrayList();
        //原同步记录, syncDataId -> v
        Map<Long, ShopDataSyncRecord> oldRecordMap = recordTargetShopId.stream().collect(Collectors.toMap(ShopDataSyncRecord::getSyncDataId, v -> v));
        //targetShopProductList转id map<no, Product>
        Map<String, List<DistributeProduct>> targetShopProductIds = targetShopProductList.stream().collect(Collectors.groupingBy(DistributeProduct::getProductNo));

        for (DistributeProduct product : productDTOList) {
            //不处理删除数据
            if (product.getDeleted()) {
                continue;
            }
            JSONObject productJson = JSON.parseObject(JSONUtil.toJsonStr(product));
            //判断数据是否变更
            ShopDataSyncRecord oldRecord = oldRecordMap.get(product.getId());
            if (null != oldRecord && null != oldRecord.getSyncData() && equalsJson(productJson, oldRecord.getSyncData())) {
                continue;
            }

            DistributeProduct distributeProduct = new DistributeProduct();
            BeanUtils.copyProperties(product, distributeProduct);
            distributeProduct.setSalePrices(product.getSalePrices());

            ShopDataSyncRecord shopDataSyncRecord = new ShopDataSyncRecord();
            shopDataSyncRecord.setSyncDataId(product.getId()).setSyncData(productJson);
            //如果目标店铺存在且未删除则是更新， 否则算新增
            List<DistributeProduct> distributeProductList = targetShopProductIds.get(product.getProductNo());
            List<DistributeProduct> notDelProduct = null;
            if (!ObjectUtils.isEmpty(distributeProductList)) {
                //只会有一个未删除数据
                notDelProduct = distributeProductList.stream().filter(p -> !p.getDeleted()).collect(Collectors.toList());
            }
            if (!ObjectUtils.isEmpty(notDelProduct)) {
                ShopDataSyncRecord goodsRecord = oldGoodsRecordMap.get(product.getProductId());
                DistributeProduct targetProduct = notDelProduct.get(0);
                //判断数据是否变更过
//                if (product.getVersion() <= targetProduct.getVersion()) {
//                    continue;
//                }
                //处理id
                distributeProduct.setId(targetProduct.getId());
                distributeProduct.setShopId(targetShopId);
                distributeProduct.setProductId(goodsRecord.getTargetDataId());
                distributeProduct.setVersion(targetProduct.getVersion());
                updateProductList.add(distributeProduct);

                convertSyncRecordV2(ShopDataSyncChangeType.UPDATE, shopDataSyncRecord, shopId, distributeProduct, targetShopId, targetProduct);
            } else {
                //新增
                ShopDataSyncRecord goodsRecord = oldGoodsRecordMap.get(product.getProductId());
                if (null == goodsRecord) {
                    log.warn(product.getProductNo() + " 商品不存在" + product.getProductId());
                    throw new GlobalException("请先同步商品");
                }
                distributeProduct.setId(MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(product).longValue());
                distributeProduct.setShopId(targetShopId);
                distributeProduct.setProductId(goodsRecord.getTargetDataId());
                addProductList.add(distributeProduct);

                convertSyncRecordV2(ShopDataSyncChangeType.ADD, shopDataSyncRecord, shopId, distributeProduct, targetShopId, null);
            }

            recordList.add(shopDataSyncRecord);
        }
        return Tuple.of(addProductList, updateProductList, recordList);
    }

    //判断是否相同,相同true
    private boolean equalsJson(JSONObject a, JSONObject b) {
        cn.hutool.json.JSON obj1 = JSONUtil.parse(a.toJSONString());
        cn.hutool.json.JSON obj2 = JSONUtil.parse(b.toJSONString());
        return obj1.equals(obj2);
    }

    /**
     * 处理删除，
     *
     * @param recordTargetShopId       原同步记录
     * @param productList       默认店铺数据
     * @param targetProductList 同步目标店铺数据
     */
    private void delDistributorProduct(List<ShopDataSyncRecord> recordTargetShopId, List<DistributeProduct> productList, List<DistributeProduct> targetProductList) {
        Map<Long, ShopDataSyncRecord> oldRecordMap = recordTargetShopId.stream().collect(Collectors.toMap(ShopDataSyncRecord::getSyncDataId, v -> v));

        List<ShopDataSyncRecord> shopDataSyncRecordList = Lists.newArrayList();

        List<DistributeProduct> delProductList = productList.stream().filter(product -> product.getDeleted())
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(delProductList)) {
            return;
        }
        //根据ProductNo分组
        Map<String, DistributeProduct> targetShopProductIds = targetProductList.stream()
                .filter(product -> !product.getDeleted())
                .collect(Collectors.toMap(DistributeProduct::getProductNo, product -> product));

        //处理删除
        Set<Long> delProductIds = Sets.newHashSet();
        delProductList.forEach(p -> {
            //如果目标店铺不存在，则不处理
            if (!targetShopProductIds.containsKey(p.getProductNo())) {
                return;
            }
            //判断数据是否产生变化
            ShopDataSyncRecord shopDataSyncRecord = oldRecordMap.get(p.getId());
            if (null != shopDataSyncRecord && null != shopDataSyncRecord.getSyncData() && null != shopDataSyncRecord.getTargetDataId()) {
                JSONObject syncData = shopDataSyncRecord.getSyncData();
                if (!equalsJson(syncData, JSONObject.parseObject(JSONUtil.toJsonStr(p)))) {
                    //记录
                    DistributeProduct targetProduct = JSONUtil.toBean(syncData.toJSONString(), DistributeProduct.class);
                    ShopDataSyncRecord delRecord = new ShopDataSyncRecord();
                    delRecord.setSyncDataId(p.getId()).setSyncData(JSON.parseObject(JSONUtil.toJsonStr(p)));
                    convertSyncRecordV2(ShopDataSyncChangeType.DELETE, delRecord, p.getShopId(), p,
                            targetProduct.getShopId(), targetProduct);
                    shopDataSyncRecordList.add(delRecord);
                    //del
                    delProductIds.add(shopDataSyncRecord.getTargetDataId());
                }
            }
        });
        if (ObjectUtils.isEmpty(delProductIds)) {
            return;
        }
        try {
            Long shopId = targetProductList.get(0).getShopId();
            log.debug("同步删除分销商品shopId:{}, ids:{}", shopId, JSONUtil.toJsonStr(delProductIds));
            distributeProductHandleService.deleteBatch(shopId, delProductIds);
            shopRpcService.createShopSyncRecord(shopDataSyncRecordList);
            //将删除的数据改为删除
            targetProductList.forEach(t -> {
                if (delProductIds.contains(t.getId())) {
                    t.setDeleted(Boolean.TRUE);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
