package com.medusa.gruul.addon.distribute.addon;

import com.medusa.gruul.addon.distribute.mp.entity.DistributeProduct;
import com.medusa.gruul.goods.api.model.vo.DistributeProductVO;
import com.medusa.gruul.order.api.model.OrderCompletedDTO;
import com.medusa.gruul.overview.api.model.OverViewStatementResp;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2023/6/10
 */
public interface DistributeAddonProvider {

    /**
     * 创建对账单 (暂时只用于 分销插件)
     *
     * @param orderCompleted 订单完成数据
     * @return 分销对账单数据
     */
    OverViewStatementResp createStatements(OrderCompletedDTO orderCompleted);


    /**
     * 查询用户分销码
     *
     * @param userId 用户 id
     * @return 分销码
     */
    String distributorCode(Long userId);

    /**
     * 平台服务查询用户店铺id
     * @param userId 用户 id
     * @return 店铺id
     */
    Long getUserDistributorShopId(Long userId);


    /**
     * Uaa服务查询用户店铺id
     * @param userId 用户 id
     * @return 店铺id
     */
    Long getUserDistributorShopIdFromUaa(Long userId);

    /**
     *  查询店铺分销商品
     * @param productIds 商品id
     * @return
     */
    List<DistributeProductVO> getDistributeProductList(Set<Long> productIds);

    /**
     *  查询店铺分销商品
     * @param productIds 商品id
     * @return
     */
    List<DistributeProductVO> getPlatformDistributeProductList(Set<Long> productIds);


}
