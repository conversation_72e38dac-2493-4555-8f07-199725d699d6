package com.medusa.gruul.addon.distribute;

import com.medusa.gruul.addon.distribute.properties.DistributeConfigurationProperties;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * date 2022/11/14
 */
@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties(DistributeConfigurationProperties.class)
@EnableDubbo(scanBasePackages = "com.medusa.gruul.addon.distribute.addon.impl")
public class AddonDistributeApplication {
    public static void main(String[] args) {
        SpringApplication.run(AddonDistributeApplication.class, args);
    }
}
