package com.medusa.gruul.addon.distribute.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.distribute.model.dto.ApplyAffairsDTO;
import com.medusa.gruul.addon.distribute.model.dto.DistributorInCodeQueryDTO;
import com.medusa.gruul.addon.distribute.model.dto.GenerateCodeDTO;
import com.medusa.gruul.addon.distribute.mp.entity.DistributorInCode;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeInCodeService;
import com.medusa.gruul.common.idem.annotation.Idem;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 分销商邀请码控制器
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Validated
//@PreAuthorize("@S.user")
@RequestMapping("/distribute/in")
public class DistributorInCodeController {
    private final IDistributeInCodeService distributeInCodeService;

    /**
     * 分页查询邀请码
     *
     * @param query 分销商查询条件
     * @return 分页查询结果
     */
    @Log("分页查询分销商")
    @GetMapping
    public Result<IPage<DistributorInCode>> distributorPage(DistributorInCodeQueryDTO query) {
        return Result.ok(distributeInCodeService.distributorInCodePage(query));
    }

    /**
     * 创建邀请码
     *
     * @param generateCodeDTO
     * @return void
     */
    @Idem
    @Log("创建邀请码")
    @PostMapping("/code/generate")
    public Result<Void> generateCode(@RequestBody @Valid GenerateCodeDTO generateCodeDTO) {
        distributeInCodeService.generateCode(generateCodeDTO.getShopId(), generateCodeDTO.getNum(), 6);
        return Result.ok();
    }

    /**
     * 查询邀请码
     *
     * @param distributorCode 邀请码
     * @return void
     */
    @Log("查询邀请码是否存在")
    @GetMapping("/code/{distributorCode}")
    public Result<DistributorInCode> queryDistributorCode(@PathVariable @NotBlank @Length(min = 6, max = 6) String distributorCode) {
        SecureUser secureUser = ISecurity.userMust();
        DistributorInCode distributorInCode = distributeInCodeService.checkCode(distributorCode, secureUser.getId(), Boolean.FALSE);
        return Result.ok(distributorInCode);
    }

}
