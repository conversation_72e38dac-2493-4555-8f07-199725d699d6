package com.medusa.gruul.addon.distribute.mp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.addon.distribute.model.dto.DistributorInCodeQueryDTO;
import com.medusa.gruul.addon.distribute.mp.entity.Distributor;
import com.medusa.gruul.addon.distribute.mp.entity.DistributorInCode;

import java.util.Set;

/**
 * 分销商邀请码 服务类
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface IDistributeInCodeService extends IService<DistributorInCode> {

    IPage<DistributorInCode> distributorInCodePage(DistributorInCodeQueryDTO queryDTO);
    /**
     * 生成邀请码
     * @param shopId 店铺id
     * @param num 位数
     * @param length 长度
     * @return
     */
    Set<String> generateCode(Long shopId, Integer num, Integer length);

    /**
     * 校验验证码是否正确
     * @param code
     * @param userId 使用人id
     * @param isConfirm true， 核销
     */
    DistributorInCode checkCode(String code, Long userId, Boolean isConfirm);

}
