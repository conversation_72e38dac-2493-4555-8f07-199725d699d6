package com.medusa.gruul.addon.distribute.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2022/11/14
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductBindDTO extends BonusConfigDTO implements Serializable {

	/**
	 * 商品id集合
	 */
	@NotNull
	@NotEmpty
	private Set<Long> productIds;


}
