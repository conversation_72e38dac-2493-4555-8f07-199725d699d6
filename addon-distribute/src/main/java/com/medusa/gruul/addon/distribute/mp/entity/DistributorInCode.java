package com.medusa.gruul.addon.distribute.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 分销商邀请码
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "t_distributor_in_code", excludeProperty = "deleted")
public class DistributorInCode extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邀请code
     */
    private String code;
    /**
     * 是否使用，1 已使用
     */
    private Boolean isUse;
    /**
     * 使用截止有效期
     */
    private LocalDateTime useEndDate;
    /**
     * 使用人
     */
    private Long userId;
    /**
     * 店铺id
     */
    private Long shopId;


}
