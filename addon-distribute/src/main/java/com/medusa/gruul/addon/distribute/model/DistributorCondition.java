package com.medusa.gruul.addon.distribute.model;

import com.medusa.gruul.addon.distribute.model.enums.ConditionType;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.web.valid.annotation.Price;
import com.medusa.gruul.global.model.o.BaseDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 成为分销员的条件
 *
 * <AUTHOR>
 * date 2022/11/15
 */
@Getter
@Setter
@Accessors(chain = true)
public class DistributorCondition implements BaseDTO {

    /**
     * 成为分销商的条件
     */
    @NotNull
    @Size(min = 1, max = 2)
    private Set<ConditionType> types;

    /**
     * 含 满足消费金额 时需要的金额
     */
    @Price
    private Long requiredAmount;

    @Override
    public void validParam() {
        if (types.contains(ConditionType.CONSUMPTION) && requiredAmount == null) {
            throw new ServiceException(SystemCode.PARAM_VALID_ERROR);
        }
    }
}
