package com.medusa.gruul.addon.distribute.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.medusa.gruul.addon.distribute.model.DistributeConstant;
import com.medusa.gruul.addon.distribute.model.DistributeErrorCode;
import com.medusa.gruul.addon.distribute.model.DistributorCondition;
import com.medusa.gruul.addon.distribute.model.dto.ApplyAffairsDTO;
import com.medusa.gruul.addon.distribute.model.dto.DistributorQueryDTO;
import com.medusa.gruul.addon.distribute.model.dto.DistributorRankDTO;
import com.medusa.gruul.addon.distribute.model.dto.DistributorTeamQueryDTO;
import com.medusa.gruul.addon.distribute.model.enums.ConditionType;
import com.medusa.gruul.addon.distribute.model.enums.DistributorIdentity;
import com.medusa.gruul.addon.distribute.model.enums.DistributorStatus;
import com.medusa.gruul.addon.distribute.model.enums.Level;
import com.medusa.gruul.addon.distribute.model.vo.DistributorRankPageVO;
import com.medusa.gruul.addon.distribute.mp.entity.*;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeAffairRecordsService;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeInCodeService;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeShopService;
import com.medusa.gruul.addon.distribute.mp.service.IDistributorService;
import com.medusa.gruul.addon.distribute.service.DistributeConfHandleService;
import com.medusa.gruul.addon.distribute.service.DistributorHandleService;
import com.medusa.gruul.common.fastjson2.FastJson2;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.overview.api.enums.DrawType;
import com.medusa.gruul.overview.api.model.WithdrawAccountReq;
import com.medusa.gruul.overview.api.rpc.OverviewRpcService;
import com.medusa.gruul.payment.api.model.param.PaymentAccountOpenParam;
import com.medusa.gruul.payment.api.model.param.PaymentAccountOpenResult;
import com.medusa.gruul.payment.api.rpc.PaymentRpcService;
import com.medusa.gruul.service.uaa.api.entity.UserData;
import com.medusa.gruul.service.uaa.api.enums.SmsType;
import com.medusa.gruul.service.uaa.api.rpc.UaaRpcService;
import com.medusa.gruul.service.uaa.api.vo.UserIdentityVO;
import com.medusa.gruul.service.uaa.api.vo.UserInfoVO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.user.api.rpc.UserRpcService;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/11/17
 */
@Service
@RequiredArgsConstructor
public class DistributorHandleServiceImpl implements DistributorHandleService {

    private final UaaRpcService uaaRpcService;
    private final UserRpcService userRpcService;
    private final PaymentRpcService paymentRpcService;
    private final IDistributorService distributorService;
    private final IDistributeShopService distributeShopService;
    private final DistributeConfHandleService platformDistributeService;
    private final OverviewRpcService overviewRpcService;
    private final IDistributeInCodeService distributeInCodeService;
    private final IDistributeAffairRecordsService distributeAffairRecordsService;
    @Value("${gruul.shop.defaultShopId}")
    private Long defaultShopId;

    @Override
    public Distributor getAffairsInfoByUserId(Long userId) {
        Distributor distributor = distributorService.lambdaQuery()
                .eq(Distributor::getUserId, userId)
                .eq(Distributor::getIdentity, DistributorIdentity.AFFAIRS)
                .one();
        if (distributor == null) {
            throw new GlobalException(DistributeErrorCode.AFFAIRS_NOT_EXISTED, "当前用户不存在分销商信息");
        }
        if (distributor.getOne() != null) {
            distributor.setReferrer(
                    distributorService.lambdaQuery()
                            .select(Distributor::getName)
                            .eq(Distributor::getUserId, distributor.getOne())
                            .oneOpt()
                            .map(Distributor::getName)
                            .orElse(null)
            );
        }
        distributor.setConfig(platformDistributeService.configMust());
        distributor.setStatistics(distributorService.affairsStatistics(userId));
        return distributor;
    }

    @Override
    @Redisson(value = DistributeConstant.DISTRIBUTOR_LOCK_KEY, key = "#userId")
    public Option<String> getCode(SecureUser secureUser) {
        Long userId = secureUser.getId();
        Distributor distributor = this.getByUserId(userId).getOrNull();
        //已经是分销商
        if (distributor != null && DistributorIdentity.AFFAIRS == distributor.getIdentity()) {
            return Option.of(distributor.getCode());
        }
        //查询配置
        DistributeConf config = platformDistributeService.configMust();
        DistributorCondition condition = config.getCondition();
        //如果需要申请
        if (!condition.getTypes().contains(ConditionType.CONSUMPTION)) {
            return Option.none();
        }
        //可以通过消费额自动成为分销商
        //消费额不足
        if (userRpcService.getUserConsumption(userId) < condition.getRequiredAmount()) {
            return Option.none();
        }
        LocalDateTime now = LocalDateTime.now();
        //不存在
        if (distributor == null) {
            distributor = new Distributor()
                    .setUserId(userId)
                    .setTotal(0L)
                    .setUndrawn(0L)
                    .setUnsettled(0L)
                    .setInvalid(0L)
                    .setApplyTime(now);
        }
        distributor.setCode(RedisUtil.noStr(DistributeConstant.NO))
                .setIdentity(DistributorIdentity.AFFAIRS)
                .setStatus(DistributorStatus.SUCCESS)
                .setAuditor("system")
                .setPassTime(now);
        //手机号为空 或姓名为空 表示用户未注册成为分销商，此时绑定用户手机号 以及昵称等
        if (StrUtil.isEmpty(distributor.getMobile()) || StrUtil.isEmpty(distributor.getName())) {
            UserInfoVO currentUser = uaaRpcService.getUserDataByUserId(userId).getOrElseThrow(() -> new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "用户信息异常"));
            if (distributorService.lambdaQuery().eq(Distributor::getMobile, distributor.getMobile()).ne(Distributor::getUserId, userId).exists()) {
                throw new GlobalException(DistributeErrorCode.MOBILE_BEAN_USED, "手机号已被使用");
            }
            distributor.setMobile(currentUser.getMobile())
                    .setName(currentUser.getNickname())
                    .setMobile(currentUser.getMobile())
                    .setNickname(currentUser.getNickname())
                    .setAvatar(currentUser.getAvatar());
        }
        boolean success = distributorService.saveOrUpdate(distributor);
        SystemCode.DATA_UPDATE_FAILED.falseThrow(success);
        return Option.of(distributor.getCode());
    }

    @Override
    public Option<Distributor> getByUserId(Long userId) {
        return Option.of(distributorService.lambdaQuery().eq(Distributor::getUserId, userId).one());
    }

    @Override
    public boolean isAffairs(Long userId) {
        return distributorService.lambdaQuery()
                .eq(Distributor::getIdentity, DistributorIdentity.AFFAIRS)
                .eq(Distributor::getStatus, DistributorStatus.SUCCESS)
                .eq(Distributor::getUserId, userId)
                .exists();
    }

    @Override
    @Redisson(value = DistributeConstant.DISTRIBUTOR_LOCK_KEY, key = "#userId")
    public void scanCode(Long userId, String code) {
        //检查当前用户是否已是分销员/商
        Distributor currentDistributor = this.getByUserId(userId).getOrNull();
        if (currentDistributor != null && DistributorIdentity.AFFAIRS.getValue().equals(currentDistributor.getIdentity().getValue())) {
            throw new GlobalException(DistributeErrorCode.DISTRIBUTOR_BOUND, "您是分销商,无法绑定其它分销商");
        }
        //查询分销码对应分销商信息
        Distributor distributor = Option.of(distributorService.lambdaQuery().eq(Distributor::getCode, code).one()).getOrElseThrow(() -> new GlobalException(DistributeErrorCode.WRONG_CODE_OF_DISTRIBUTOR, "错误的分销码"));
        if (DistributorIdentity.USER.getValue().equals(distributor.getIdentity().getValue())) {
            throw new GlobalException(DistributeErrorCode.WRONG_CODE_OF_DISTRIBUTOR, "只能绑定分销商");
        }
        //分销商与当前用户不能是同一人
        Long distributorUserId = distributor.getUserId();
        if (userId.equals(distributorUserId)) {
            throw new GlobalException(DistributeErrorCode.CANNOT_BIND_OWN_CODE, "不能绑定自己的分销码");
        }
        currentDistributor = getDistributor(userId, currentDistributor);
        currentDistributor.setOne(distributorUserId)
                .setTwo(distributor.getOne())
                .setThree(distributor.getTwo())
                .setShopId(distributor.getShopId());
        //需不需要更新 当前分销商的二级乃至三级分销员
        if (currentDistributor.getIdentity() == DistributorIdentity.AFFAIRS) {
            //更新下面一级分销员的二级分销商
            distributorService.lambdaUpdate()
                    .eq(Distributor::getOne, userId)
                    .isNull(Distributor::getTwo)
                    .set(Distributor::getTwo, distributorUserId)
                    .ne(Distributor::getUserId, distributorUserId)
                    .update();
            //更新下面二级分销员的三级分销商
            distributorService.lambdaUpdate()
                    .eq(Distributor::getTwo, userId)
                    .isNull(Distributor::getThree)
                    .set(Distributor::getThree, distributorUserId)
                    .ne(Distributor::getUserId, distributorUserId)
                    .update();
        }
        //保存用户为分销员
        SystemCode.DATA_UPDATE_FAILED.falseThrow(
                distributorService.saveOrUpdate(currentDistributor)
        );

    }

    private Distributor getDistributor(Long userId, Distributor currentDistributor) {
        if (currentDistributor == null) {
            //远程查询用户资料
            UserInfoVO currentUser = uaaRpcService.getUserDataByUserId(userId).getOrElseThrow(() -> new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "用户信息异常"));
            currentDistributor = new Distributor()
                    .setUserId(userId)
                    .setIdentity(DistributorIdentity.USER)
                    .setTotal(0L)
                    .setUndrawn(0L)
                    .setUnsettled(0L)
                    .setInvalid(0L)
                    .setStatus(DistributorStatus.NOT_APPLIED)
                    .setNickname(currentUser.getNickname())
                    .setAvatar(currentUser.getAvatar());
        }
        return currentDistributor;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = DistributeConstant.DISTRIBUTOR_LOCK_KEY, key = "#userId")
    public void applyAffairs(Long userId, String openid, ApplyAffairsDTO applyAffairs) {
        //检查是否开启了服务商模式
        if (paymentRpcService.serviceEnable() && StrUtil.isEmpty(openid)) {
            throw new GlobalException(DistributeErrorCode.WECHAT_NOT_BOUND, "请先绑定微信");
        }
        String mobile = applyAffairs.getMobile();
        String smsCode = applyAffairs.getCode();
        //检查验证码是否正确
        uaaRpcService.checkSmsCodeByType(SmsType.DISTRIBUTOR, mobile, smsCode);
        //校验是否可申请
        DistributeConf configMust = platformDistributeService.configMust();
        if (!configMust.getCondition().getTypes().contains(ConditionType.APPLY)) {
            throw new GlobalException(DistributeErrorCode.CANNOT_APPLY, "申请不可用");
        }
        //查询用户是否已存在
        Distributor distributor = getByUserId(userId).getOrNull();
        //不存在 生成新的用户信息
        distributor = getDistributor(userId, distributor);
        if (DistributorStatus.APPLYING == distributor.getStatus()) {
            throw new GlobalException(DistributeErrorCode.REPEATED_APPLY_SUBMISSION, "请勿重复提交");
        }
        if (DistributorIdentity.AFFAIRS == distributor.getIdentity()) {
            throw new GlobalException(DistributeErrorCode.USER_IS_DISTRIBUTOR_AFFAIRS, "已是分销商");
        }
        if (distributorService.lambdaQuery().eq(Distributor::getMobile, mobile).ne(Distributor::getUserId, userId).exists()) {
            throw new GlobalException(DistributeErrorCode.MOBILE_BEAN_USED, "手机号已被使用");
        }

        distributor.setShopId(this.defaultShopId)
                .setName(StrUtil.trim(applyAffairs.getName()))
                .setMobile(mobile)
                .setName(applyAffairs.getName())
                .setStatus(DistributorStatus.APPLYING)
                .setApplyTime(LocalDateTime.now());
        DistributeAffairRecords distributeAffairRecords = new DistributeAffairRecords();
        UserIdentityVO userIdentityByUserId = uaaRpcService.getUserIdentityByUserId(userId);
        distributeAffairRecords.setIdentityInfo(JSON.toJSONString(userIdentityByUserId));
        BeanUtils.copyProperties(applyAffairs, distributeAffairRecords);
        distributeAffairRecords.setStatus(DistributorStatus.APPLYING).setUserId(userId);
        SystemCode.DATA_UPDATE_FAILED.falseThrow(
                distributorService.saveOrUpdate(distributor) && distributeAffairRecordsService.save(distributeAffairRecords));
//        RedisUtil.setCacheObject(RedisUtil.key(DistributeConstant.APPLY_AFFAIRS_CACHE_KEY, userId), applyAffairs);
    }


    @Override
    public IPage<Distributor> distributorPage(DistributorQueryDTO query) {
        IPage<Distributor> result = distributorService.distributorPage(query);
        List<Distributor> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return result;
        }
        //审核的分销商额外查询累计消费
        if (DistributorStatus.APPLYING != query.getStatus()) {
            return result;
        }
        Set<Long> userIds = records.stream().map(Distributor::getUserId).collect(Collectors.toSet());
        Map<Long, Long> userConsumptionBatch = userRpcService.getUserConsumptionBatch(userIds);
        records.forEach(distributor -> distributor.setConsumption(userConsumptionBatch.getOrDefault(distributor.getUserId(), 0L)));
        return result;
    }


    @Override
    public DistributorRankPageVO rank(DistributorRankDTO query) {
        IPage<Distributor> page = distributorService.rank(query);
        DistributorRankPageVO result = new DistributorRankPageVO();
        result.setRecords(page.getRecords())
                .setCurrent(page.getCurrent())
                .setSize(page.getSize())
                .setTotal(page.getTotal());
        Option.of(query.getUserId())
                .peek(usrId -> result.setRank(distributorService.getUserRank(usrId, query.getShopId())));
        return result;
    }

    @Override
    public IPage<Distributor> distributorTeamPage(DistributorTeamQueryDTO query) {
        IPage<Distributor> result = distributorService.distributorTeamPage(query);
        ISecurity.match()
                .ifUser(
                        secureUser -> {
                            DistributorTeamQueryDTO queryPage = (DistributorTeamQueryDTO) result;
                            Level level = query.getLevel();
                            if (level == null) {
                                return;
                            }
                            Long userId = secureUser.getId();
                            switch (level) {
                                case ONE -> {
                                    queryPage.setCount1(queryPage.getTotal());
                                    queryPage.setCount2(distributorService.lambdaQuery().eq(Distributor::getTwo, userId).count());
                                    queryPage.setCount3(distributorService.lambdaQuery().eq(Distributor::getThree, userId).count());
                                }
                                case TWO -> {
                                    queryPage.setCount1(distributorService.lambdaQuery().eq(Distributor::getOne, userId).count());
                                    queryPage.setCount2(queryPage.getTotal());
                                    queryPage.setCount3(distributorService.lambdaQuery().eq(Distributor::getThree, userId).count());
                                }
                                case THREE -> {
                                    queryPage.setCount1(distributorService.lambdaQuery().eq(Distributor::getOne, userId).count());
                                    queryPage.setCount2(distributorService.lambdaQuery().eq(Distributor::getTwo, userId).count());
                                    queryPage.setCount3(queryPage.getTotal());
                                }
                                default -> {
                                }
                            }
                        }
                );
        return result;
    }

    @Override
    @Redisson(value = DistributeConstant.DISTRIBUTOR_LOCK_KEY, key = "#userId")
    @Transactional(rollbackFor = Exception.class)
    public void updateDistributorStatus(Long userId, DistributorStatus currentStatus, DistributorStatus targetStatus) {
        DistributeAffairRecords distributeAffairRecords = null;
        Distributor distributor = distributorService.lambdaQuery()
                .eq(Distributor::getUserId, userId)
                .eq(Distributor::getStatus, currentStatus)
                .eq(Distributor::getIdentity, DistributorIdentity.USER)
                .one();
        if (distributor == null) {
            throw new GlobalException(DistributeErrorCode.APPLY_NOT_EXISTED, "申请不存在");
        }
        boolean isAffairsStatus = DistributorStatus.SUCCESS == targetStatus;
        SecureUser secureUser = ISecurity.userMust();
//        boolean success = distributorService.lambdaUpdate()
//                .set(isAffairsStatus, Distributor::getCode, RedisUtil.no(DistributeConstant.NO).toString())
//                .set(isAffairsStatus, Distributor::getIdentity, DistributorIdentity.AFFAIRS)
//                .set(Distributor::getPassTime, LocalDateTime.now())
//                .set(Distributor::getAuditor, StrUtil.emptyToDefault(secureUser.getUsername(), secureUser.getMobile()))
//                .set(Distributor::getStatus, targetStatus)
//                .eq(Distributor::getUserId, userId)
//                .eq(Distributor::getStatus, currentStatus)
//                .eq(Distributor::getIdentity, DistributorIdentity.USER)
//                .update();
//        SystemCode.DATA_UPDATE_FAILED.falseThrow(success);
//        boolean resetDistributorParentSuccess =distributorService.lambdaUpdate().set(Distributor::getOne, null).set(Distributor::getTwo, null).set(Distributor::getThree, null).eq(Distributor::getOne, userId).or().eq(Distributor::getTwo, userId).or().eq(Distributor::getThree, userId).update();
//        SystemCode.DATA_UPDATE_FAILED.falseThrow(resetDistributorParentSuccess);

        distributor.setPassTime(LocalDateTime.now())
                .setAuditor(StrUtil.emptyToDefault(secureUser.getUsername(), secureUser.getMobile()))
                .setStatus(targetStatus);
        distributeAffairRecords = distributeAffairRecordsService.lambdaQuery().eq(DistributeAffairRecords::getUserId, userId).eq(DistributeAffairRecords::getStatus, currentStatus).one();
        if (distributeAffairRecords == null) {
            throw new GlobalException("分销申请记录不存在！");
        }
        distributeAffairRecords.setAuditor(StrUtil.emptyToDefault(secureUser.getUsername(), secureUser.getMobile())).setStatus(targetStatus).setUpdateTime(LocalDateTime.now());
        if (isAffairsStatus) {
            distributor.setCode(RedisUtil.no(DistributeConstant.NO).toString())
                    .setIdentity(DistributorIdentity.AFFAIRS)
                    .setOne(null)
                    .setTwo(null)
                    .setThree(null);
//            Map applyAffairsMap = RedisUtil.getCacheObject(RedisUtil.key(DistributeConstant.APPLY_AFFAIRS_CACHE_KEY, userId));
//            ApplyAffairsDTO applyAffairs = FastJson2.convert(applyAffairsMap, ApplyAffairsDTO.class);
            this.openHuifuAccount(distributeAffairRecords, userId);

        }
        int effectRows = distributorService.updateDistributorStatus(distributor);
        boolean updateAffairRecords = distributeAffairRecordsService.updateById(distributeAffairRecords);
        SystemCode.DATA_UPDATE_FAILED.falseThrow(effectRows > 0 && updateAffairRecords);

    }


    public void openHuifuAccount(DistributeAffairRecords applyAffairs, Long userId) {
        if (applyAffairs == null) {
            throw new GlobalException("分销申请信息不存在！");
        }
//        开通汇付账号
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        UserIdentityVO userIdentityVO = uaaRpcService.getUserIdentityByUserId(userId);
        PaymentAccountOpenParam paymentAccountOpenParam = new PaymentAccountOpenParam();
        paymentAccountOpenParam.setUserId(userId);
        paymentAccountOpenParam.setName(applyAffairs.getName());
        paymentAccountOpenParam.setMobileNo(applyAffairs.getMobile());
        paymentAccountOpenParam.setCertType(userIdentityVO.getCertificateType());
        paymentAccountOpenParam.setCertValidityType(Integer.valueOf(1).equals(userIdentityVO.getCertificateStatus()) ? "0" : "1");
        String format = df.format(userIdentityVO.getCertificateStartTime());
        String format2 = null;
        if (Integer.valueOf(1).equals(userIdentityVO.getCertificateStatus())) {
            format2 = df.format(userIdentityVO.getCertificateExpiredTime());
        }
        paymentAccountOpenParam.setCertBeginDate(format);
        paymentAccountOpenParam.setCertEndDate(format2);
        paymentAccountOpenParam.setCardNo(applyAffairs.getCardNo());
        paymentAccountOpenParam.setCertNo(userIdentityVO.getCertificateNo());
        paymentAccountOpenParam.setProvId(applyAffairs.getProvId());
        paymentAccountOpenParam.setAreaId(applyAffairs.getAreaId());
        PaymentAccountOpenResult paymentAccountOpenResult = paymentRpcService.openAccount(paymentAccountOpenParam);
        //存储账户信息
        WithdrawAccountReq withdrawAccountReq = new WithdrawAccountReq();
        withdrawAccountReq.setType(DrawType.HUIFU_JSPAY);
        HashMap<String, Object> detailMap = new HashMap<>();
        JSONObject detail = new JSONObject();
        detailMap.put("name", applyAffairs.getName());
        detailMap.put("mobile", applyAffairs.getMobile());
        detailMap.put("provId", applyAffairs.getProvId());
        detailMap.put("areaId", applyAffairs.getAreaId());
        detailMap.put("bank", DrawType.HUIFU_JSPAY);
        detailMap.put("cardNo", applyAffairs.getCardNo());
        detailMap.put("accountId", paymentAccountOpenResult.getAccountId());
        detailMap.put("cardId", paymentAccountOpenResult.getCardId());
        detail.putAll(detailMap);
        withdrawAccountReq.setDetail(detail);
        overviewRpcService.editAccount(userId, withdrawAccountReq);
//        //删除缓存数据
//        RedisUtil.delete(RedisUtil.key(DistributeConstant.APPLY_AFFAIRS_CACHE_KEY, userId));
    }

    @Override
    public Option<Distributor> getParentDistributorByUserId(Long userId) {
        Distributor currentUserDis = distributorService.lambdaQuery().eq(Distributor::getUserId, userId).one();
        if (currentUserDis == null || currentUserDis.getOne() == null) {
            return Option.none();
        }
        return Option.of(distributorService.lambdaQuery().eq(Distributor::getUserId, currentUserDis.getOne()).one());

    }

    @Override
    @Redisson(value = DistributeConstant.UPDATE_SHOP_LOCK_KEY, key = "#shopInfo.id")
    public void updateShopInfo(ShopInfoVO shopInfo) {
        distributeShopService.lambdaQuery()
                .eq(DistributeShop::getShopId, shopInfo.getId())
                .oneOpt()
                .ifPresent(
                        distributeShop -> distributeShopService.lambdaUpdate()
                                .eq(DistributeShop::getShopId, shopInfo.getId())
                                .set(DistributeShop::getShopName, shopInfo.getName())
                                .set(DistributeShop::getShopLogo, shopInfo.getLogo())
                                .update()
                );
    }

    @Override
    public void updateUserData(UserData userData) {
        distributorService.lambdaUpdate()
                .set(Distributor::getNickname, userData.getNickname())
                .set(Distributor::getAvatar, userData.getAvatar())
                .eq(Distributor::getUserId, userData.getUserId())
                .update();
    }

}
