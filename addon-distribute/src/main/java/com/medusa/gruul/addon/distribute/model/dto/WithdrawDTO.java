package com.medusa.gruul.addon.distribute.model.dto;

import com.medusa.gruul.overview.api.enums.DrawType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2023/5/15
 */
@Getter
@Setter
@ToString
public class WithdrawDTO implements Serializable {

	/**
	 * 提现类型
	 */
	@NotNull
	private DrawType type;

	/**
	 * 提现金额
	 */
	@NotNull
	@Min(value = 0, message = "提现金额必须大于0")
	private Long amount;
}
