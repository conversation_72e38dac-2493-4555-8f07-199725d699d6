package com.medusa.gruul.addon.distribute.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * date 2022/11/15
 */
@Getter
@RequiredArgsConstructor
public enum DistributeProductStatus {

    /**
     * 上架
     */
    ENABLE(1),

    /**
     * 下架
     */
    DISABLE(2),

    /**
     * 违规禁用
     */
    FORBIDDEN(3);

    @EnumValue
    private final Integer value;

    public static DistributeProductStatus toDistributeStatus(ProductStatus productStatus) {
        switch (productStatus) {
            case REFUSE:
            case UNDER_REVIEW:
            case SELL_OFF:
            case UNUSABLE:
                return DistributeProductStatus.DISABLE;
            case PLATFORM_SELL_OFF:
                return DistributeProductStatus.FORBIDDEN;
            default:
                return DistributeProductStatus.ENABLE;
        }
    }
}
