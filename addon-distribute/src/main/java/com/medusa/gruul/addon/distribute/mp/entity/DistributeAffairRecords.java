package com.medusa.gruul.addon.distribute.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.addon.distribute.model.enums.DistributorStatus;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分销申请记录表
 */
@Data
@Accessors(chain = true)
@TableName("t_distributor_affair_records")
public class DistributeAffairRecords extends BaseEntity {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户姓名
     */
    private String name;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 身份证号
     */
    private String cardNo;
    /**
     * 省编码
     */
    private String provId;
    /**
     * 市编码
     */
    private String areaId;
    /**
     * 分销邀请码
     */
    private String distributorCode;
    /**
     * 审核员
     */
    private String auditor;
    /**
     * 审核状态
     */
    private DistributorStatus status;


    /**
     * 实名信息
     */
    private String identityInfo;


}
