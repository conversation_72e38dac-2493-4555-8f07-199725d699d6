package com.medusa.gruul.addon.distribute.model.dto;

import cn.hutool.core.lang.RegexPool;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2022/11/17
 */
@Getter
@Setter
@ToString
public class ApplyAffairsDTO implements Serializable {
    private static final long serialVersionUID = -5187491209464796118L;
    /**
     * 姓名
     */
    @NotBlank
    private String name;

    /**
     * 手机号
     */
    @NotBlank
    @Pattern(regexp = RegexPool.MOBILE)
    private String mobile;

    /**
     * 银行卡
     */
    @NotBlank
    private String cardNo;

    /**
     * 省编码
     */
    @NotBlank
    private String provId;


    /**
     * 市编码
     */
    @NotBlank
    private String areaId;


    /**
     * 短信验证码
     */
    @NotBlank
    @Length(min = 4, max = 4)
    private String code;

    /**
     * 分销商验证码
     */
    private String distributorCode;
}
