package com.medusa.gruul.addon.distribute.mp.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.addon.distribute.model.dto.DistributorInCodeQueryDTO;
import com.medusa.gruul.addon.distribute.mp.entity.DistributorInCode;
import com.medusa.gruul.addon.distribute.mp.mapper.DistributorInCodeMapper;
import com.medusa.gruul.addon.distribute.mp.service.IDistributeInCodeService;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分销商邀请码 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Service
public class DistributeInCodeServiceImpl extends ServiceImpl<DistributorInCodeMapper, DistributorInCode> implements IDistributeInCodeService {

    private final String RANDOM_STR = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    @Override
    public IPage<DistributorInCode> distributorInCodePage(DistributorInCodeQueryDTO queryDTO) {
        return baseMapper.selectPage(queryDTO, new QueryWrapper<DistributorInCode>().eq(queryDTO.getShopId()!=null,"shop_id",queryDTO.getShopId()).orderByDesc("create_time"));
    }

    @Override
    public Set<String> generateCode(Long shopId, Integer num, Integer length) {
        Set<String> setCode = this.lambdaQuery()
                .select(DistributorInCode::getCode)
                .list()
                .stream()
                .map(DistributorInCode::getCode)
                .collect(Collectors.toSet());

        Set<String> retCodes = new HashSet<>();
        while (true) {
            String newCode = RandomUtil.randomString(RANDOM_STR, length);
            if (setCode.contains(newCode)) {
                continue;
            } else {
                retCodes.add(newCode);
                setCode.add(newCode);

            }
            if (retCodes.size() >= num) {
                break;
            }
        }
        this.saveBatch(retCodes.stream().map(code -> {
            DistributorInCode distributorInCode = new DistributorInCode();
            distributorInCode.setShopId(shopId);
            distributorInCode.setCode(code);
            distributorInCode.setIsUse(false);
            distributorInCode.setUseEndDate(LocalDateTime.now().plusHours(24));
            return distributorInCode;
        }).collect(Collectors.toList()));
        return retCodes;
    }

    @Override
    public DistributorInCode checkCode(String code, Long userId, Boolean isConfirm) {
        DistributorInCode distributorInCode = this.lambdaQuery()
                .eq(DistributorInCode::getCode, code)
                .oneOpt()
                .orElseThrow(() -> new ServiceException("邀请码不存在!", SystemCode.DATA_NOT_EXIST_CODE));
        if (distributorInCode.getIsUse()) {
            throw new ServiceException("邀请码已使用!", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if (distributorInCode.getUseEndDate().isBefore(LocalDateTime.now())) {
            throw new ServiceException("邀请码已过期!", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if (isConfirm) {
            distributorInCode.setUserId(userId);
            distributorInCode.setIsUse(true);
            baseMapper.updateById(distributorInCode);
        }
        return distributorInCode;
    }
}
