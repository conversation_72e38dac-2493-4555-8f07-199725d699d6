(function(e,J){typeof exports=="object"&&typeof module<"u"?module.exports=J(require("vue"),require("@/components/PageManage.vue"),require("element-plus"),require("decimal.js"),require("@element-plus/icons-vue"),require("@/apis/good"),require("@/composables/useConvert"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("@/components/MCard.vue"),require("vue-clipboard3"),require("@/utils/date"),require("@/components/q-icon/q-icon.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/components/PageManage.vue","element-plus","decimal.js","@element-plus/icons-vue","@/apis/good","@/composables/useConvert","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","@/components/MCard.vue","vue-clipboard3","@/utils/date","@/components/q-icon/q-icon.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/apis/http"],J):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopDistribute=J(e.ShopDistributeContext.Vue,e.ShopDistributeContext.PageManageTwo,e.ShopDistributeContext.ElementPlus,e.ShopDistributeContext.Decimal,e.ShopDistributeContext.ElementPlusIconsVue,e.ShopDistributeContext.GoodAPI,e.ShopDistributeContext.UseConvert,e.ShopDistributeContext.QChooseGoodsPopup,e.ShopDistributeContext.MCard,e.ShopDistributeContext.VueClipboard3,e.ShopDistributeContext.DateUtil,e.ShopDistributeContext.QIcon,e.ShopDistributeContext.QTable,e.ShopDistributeContext.QTableColumn,e.ShopDistributeContext.Request))})(this,function(e,J,T,$,me,De,de,Ie,ue,Be,Oe,Fe,Ue,pe,P){"use strict";var be=document.createElement("style");be.textContent=`@charset "UTF-8";.dis[data-v-beb90c42]{padding:0 30px;position:relative}.table-height-fit[data-v-3ad198bb]{height:calc(100vh - 225px);overflow:auto}.dis[data-v-3ad198bb]{padding:0}.dis__header[data-v-3ad198bb]{display:flex;justify-content:space-between;align-items:center;margin-bottom:14px}.com[data-v-3ad198bb]{width:242px;font-size:12px;color:#515151;display:flex;justify-content:space-between;align-items:center}.com__img[data-v-3ad198bb]{width:56px;height:56px;margin-right:12px}.com__right[data-v-3ad198bb]{text-align:left}.com__right--name[data-v-3ad198bb]{width:174px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-bottom:10px}.dialogCom[data-v-3ad198bb]{display:flex;justify-content:center;align-items:center}.dialogCom__img[data-v-3ad198bb]{width:60px;height:60px;margin-right:12px;border-radius:10px}.dialogCom__right--name[data-v-3ad198bb]{width:210px;font-size:14px;color:#333;line-height:20px}.tableCom__img[data-v-3ad198bb]{width:36px;height:36px;margin-right:12px;border-radius:10px}.color51[data-v-3ad198bb]{color:#515151}.colorRed[data-v-3ad198bb]{color:#fd0505}.color33[data-v-3ad198bb]{color:#333}.column[data-v-3ad198bb]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column button[data-v-3ad198bb]{margin:0}.flex[data-v-3ad198bb]{display:flex;justify-content:flex-start;align-items:center}.amount[data-v-3ad198bb]:before{content:"￥";display:inline-block}.percentage[data-v-3ad198bb]:after{content:"%";display:inline-block}.tiShi[data-v-3ad198bb]{color:#fd0505;font-size:12px;margin:10px 0 20px}.head[data-v-421286ab]{display:flex;align-items:center;justify-content:space-around;background-color:#e6f7ff;height:40px;margin-top:15px;font-size:10px}.content[data-v-421286ab]{display:flex}.content__right1[data-v-421286ab]{border:1px solid #ebeef5;display:flex;align-items:center;justify-content:center;line-height:26px;width:470px;padding-left:10px}.content__right1--item[data-v-421286ab]{width:120px}.content__right2[data-v-421286ab]{width:155px;font-size:13px;border:1px solid #ebeef5;display:flex;flex-wrap:wrap;justify-content:center;flex-direction:column;padding-left:5px;line-height:26px}.content__right2--item[data-v-421286ab]{width:100px}.goods[data-v-421286ab]{display:flex;align-items:center}.goods__pic[data-v-421286ab]{margin-right:10px;width:60px;height:50px;position:relative}.goods__pic--state[data-v-421286ab]{background:#7f83f7;position:absolute;width:40px;height:40px;border-radius:50%;top:5px;left:10px;color:#fff;line-height:40px;text-align:center;font-size:10px}.goods__info-flex[data-v-421286ab]{display:flex;align-items:center;justify-content:space-between;margin-top:10px}.goods__info-flex--name[data-v-421286ab]{width:185px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-right:10px}.goods__info-flex--specs[data-v-421286ab]{width:80px;font-size:10px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}[data-v-421286ab] .el-table thead{display:none}.amount[data-v-421286ab]:before{content:"￥";display:inline-block}.percentage[data-v-421286ab]:after{content:"%";display:inline-block}.ellipsis[data-v-421286ab]{width:250px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.dialog-footer button[data-v-b509af32]:first-child{margin-right:10px}.fw[data-v-b509af32]{font-weight:700;margin:20px 0 10px}.count[data-v-ed432cdc]{height:56px;line-height:56px;font-weight:700}.count span[data-v-ed432cdc]{margin-right:30px}.tbhead[data-v-ed432cdc]{display:flex;align-items:center;height:35px;font-weight:700;background-color:#f2f2f280}.tbhead__goods[data-v-ed432cdc]{margin-left:150px}.tbhead__parameter[data-v-ed432cdc]{margin-left:170px}.tbhead__detail[data-v-ed432cdc]{margin-left:185px}.tbhead__total[data-v-ed432cdc]{margin-left:190px}.ml[data-v-ed432cdc]{margin-left:30px}.tool[data-v-f4094659]{width:100%;height:56px;background:#fff;position:relative}.tool__btn[data-v-f4094659]{position:absolute;left:0}.tool__btn--drop[data-v-f4094659]{width:120px}.tool__input[data-v-f4094659]{width:250px;font-size:14px;position:absolute;right:0;top:50%;margin-top:-24px}.color51[data-v-f4094659]{color:#515151}.color58[data-v-f4094659]{color:#586884}.color33[data-v-f4094659]{color:#333}.column[data-v-f4094659]{display:flex;flex-direction:column;align-items:flex-start;justify-content:flex-start}.column button[data-v-f4094659]{margin:0}.ellipsis[data-v-f4094659]{width:135px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ml[data-v-f4094659]{margin-left:30px}.table-height-fit[data-v-6623ef4c]{height:calc(100vh - 225px);overflow:auto}.dis[data-v-6623ef4c]{padding:0}.dis__header[data-v-6623ef4c]{display:flex;justify-content:space-between;align-items:center;margin-bottom:14px}.com[data-v-6623ef4c]{width:242px;font-size:12px;color:#515151;display:flex;justify-content:space-between;align-items:center}.com__img[data-v-6623ef4c]{width:56px;height:56px;margin-right:12px}.com__right[data-v-6623ef4c]{text-align:left}.com__right--name[data-v-6623ef4c]{width:174px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-bottom:10px}.dialogCom[data-v-6623ef4c]{display:flex;justify-content:center;align-items:center}.dialogCom__img[data-v-6623ef4c]{width:60px;height:60px;margin-right:12px;border-radius:10px}.dialogCom__right--name[data-v-6623ef4c]{width:210px;font-size:14px;color:#333;line-height:20px}.tableCom__img[data-v-6623ef4c]{width:36px;height:36px;margin-right:12px;border-radius:10px}.color51[data-v-6623ef4c]{color:#515151}.colorRed[data-v-6623ef4c]{color:#fd0505}.color33[data-v-6623ef4c]{color:#333}.column[data-v-6623ef4c]{display:flex;flex-direction:column;align-items:center;justify-content:center}.column button[data-v-6623ef4c]{margin:0}.flex[data-v-6623ef4c]{display:flex;justify-content:flex-start;align-items:center}.amount[data-v-6623ef4c]:before{content:"￥";display:inline-block}.percentage[data-v-6623ef4c]:after{content:"%";display:inline-block}.tiShi[data-v-6623ef4c]{color:#fd0505;font-size:12px;margin:10px 0 20px}
`,document.head.appendChild(be);const ze={class:"dis"},Le=e.defineComponent({__name:"ShopDistribute",setup(m){const b={distributionCom:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Lt)),distributionOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>_o)),wholeSaler:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Fo)),distributionCode:e.defineAsyncComponent(()=>Promise.resolve().then(()=>$o))},i=e.ref("distributionCode"),a=e.computed(()=>b[i.value]);return(N,_)=>{const u=e.resolveComponent("el-tab-pane"),C=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",ze,[e.createVNode(C,{modelValue:i.value,"onUpdate:modelValue":_[0]||(_[0]=p=>i.value=p),class:"demo-tabs"},{default:e.withCtx(()=>[e.createVNode(u,{label:"邀请码",name:"distributionCode"}),e.createVNode(u,{label:"分销商品",name:"distributionCom"}),e.createVNode(u,{label:"分销订单",name:"distributionOrder"}),e.createVNode(u,{label:"分销商",name:"wholeSaler"})]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(a.value),{ref:"componentRef",platform:"shop"},null,512))])}}}),Wo="",oe=(m,b)=>{const i=m.__vccOpts||m;for(const[a,N]of b)i[a]=N;return i},Ae=oe(Le,[["__scopeId","data-v-beb90c42"]]),$e=m=>P.post({url:"/distribute/product/syncProduct",data:m}),Pe=m=>P.post({url:"addon-distribute/distribute/product/",data:m}),Re=(m,b)=>P.put({url:`addon-distribute/distribute/product/${m}`,data:b}),qe=m=>P.post({url:"addon-distribute/distribute/product/page",data:m}),je=m=>P.del({url:`addon-distribute/distribute/product/${m.join(",")}`}),ge=m=>P.put({url:`addon-distribute/distribute/product/cancel/${m.join(",")}`}),Xe=m=>P.put({url:`addon-distribute/distribute/product/again/${m}`}),Ne=()=>P.get({url:"addon-distribute/distribute/config/"}),He=m=>P.get({url:"addon-distribute/distribute/order/",params:m}),Me=m=>P.get({url:"addon-distribute/distribute/distributor/",params:m}),We=m=>P.get({url:"addon-distribute/distribute/distributor/team",params:m}),Ge=m=>P.get({url:"addon-distribute/distribute/distributor/rank",params:m}),Ye=m=>P.get({url:"gruul-mall-shop/shop",params:m}),Qe=m=>P.post({url:"addon-distribute/distribute/in/code/generate",data:m}),Je=m=>P.get({url:"addon-distribute/distribute/in",params:m}),Ke=m=>(e.pushScopeId("data-v-3ad198bb"),m=m(),e.popScopeId(),m),Ze={class:"dis"},ve={class:"dis__header"},et={class:"dis__header-left"},tt={class:"dis__header-right"},ot={class:"com f12 color51"},lt={class:"com__right"},at={class:"com__right--name"},nt={key:0},rt={key:1},dt={class:"f12 color51"},st={class:"f12 color51 column"},it={class:"f12 color51"},ct={class:"f12 color51 column"},mt={class:"f12 color51"},pt={class:"f12 color33"},ht={class:"f12 color33"},ft={key:0,class:"f12 colorRed"},_t={key:1,class:"f12 colorRed"},ut={key:0,class:"f12 colorRed"},bt={key:1,class:"f12 colorRed"},gt={class:"f12 color51"},Nt={style:{display:"flex",gap:"20px"}},xt={style:{display:"flex",gap:"20px"}},Ct=Ke(()=>e.createElementVNode("p",{class:"tiShi"},"温馨提示：一级佣金：应大于等于 0 ； 二级佣金：应小于一级佣金 ； 三级佣金：应小于二级佣金",-1)),Vt={class:"flex"},yt={class:"tableCom flex"},wt={class:"f12 color51"},Tt={key:0,class:"f12 colorRed"},Et={key:1,class:"f12 colorRed"},St={key:0,class:"f12 colorRed"},kt={key:1,class:"f12 colorRed"},Dt={key:0,class:"f12 colorRed"},It={key:1,class:"f12 colorRed"},Bt={key:0,class:"f12 colorRed"},Ot={key:1,class:"f12 colorRed"},Ft=["onClick"],Ut={class:"dialog-footer"},zt=e.defineComponent({__name:"DistributionCom",setup(m){const b=e.reactive({list:[],current:1,pages:1,total:0,size:10,productName:"",distributionStatus:"ALL"}),{mulTenThousand:i,divTenThousand:a,mulHundred:N,divHundred:_}=de(),u=e.ref("add"),C=e.ref(!1),p=e.ref(!1);e.ref(!1);const h=e.ref("ONE"),s=e.ref({}),I=e.ref(),D=e.computed(()=>u.value==="add"?"新增":u.value==="edit"?"编辑":"查看"),V=e.reactive({pages:1,current:1,list:[],total:0,size:10,name:"",loading:!1,excludeProductIds:[]}),n=e.reactive({shareType:"FIXED_AMOUNT",one:"0",two:"0",three:"0",productIds:[],listId:"",memberOne:"0",memberTwo:"0",memberThree:"0"}),O=e.reactive({productId:"",shopId:""}),K=e.reactive({one:[{required:!0}],two:[{required:!0,validator:he,trigger:"blur"}],three:[{required:!0,validator:Po,trigger:"blur"}],memberOne:[{required:!0}]}),F=e.ref([]),R=e.ref([]);e.ref([]),W(),jo();const le=()=>{u.value="add",C.value=!0,ne(),n.shareType="RATE"},ae=()=>{p.value=!0},z=()=>{if(u.value==="see"){C.value=!1;return}I.value&&I.value.validate(async l=>{if(l){const t=JSON.parse(JSON.stringify(n));t.shareType==="RATE"||t.shareType==="FIXED_AMOUNT"?(t.one=String(i(t.one)),t.two=String(i(t.two)),t.three=String(i(t.three)),t.memberOne=String(i(t.memberOne)),t.memberTwo=String(i(t.memberTwo)),t.memberThree=String(i(t.memberThree))):(t.shareType=s.value.shareType,(s.value.shareType==="RATE"||s.value.shareType==="FIXED_AMOUNT")&&(t.one=String(i(s.value.one)),t.two=String(i(s.value.two)),t.three=String(i(s.value.three))));const{code:r,msg:d}=u.value==="add"?await Pe(Ve(t)):await Re(t.listId,Ve(t));r===200?(T.ElMessage.success(u.value==="add"?"新增成功":"修改成功"),C.value=!1,W()):T.ElMessage.error(d||(u.value==="add"?"新增失败":"修改失败"))}})},w=async()=>{const{code:l,success:t,msg:r}=await $e(O);l===200&&t?(T.ElMessage.success("同步商品成功"),p.value=!1):T.ElMessage.error("同步商品失败,"+r)},j=()=>{_e.value=!0,se()},M=l=>{b.current=l,W()},q=l=>{b.size=l,W()},S=()=>{ne()},y=()=>{b.current=1,W()},x=()=>{console.log(111),b.current=1,W()},g=l=>{l||(b.current=1,W())},U=async l=>{if((l.status==="SELL_ON"||l.status==="UNUSABLE")&&l.distributionStatus==="CANCEL_DISTRIBUTION"){const{code:t,success:r}=await Xe(l.id);t===200&&r?(T.ElMessage.success("重新分销成功"),W()):T.ElMessage.error("重新分销失败"),W()}},Y=e.ref(""),Z=async l=>{u.value="edit",n.productIds=[l.productId],n.shareType=l.shareType,n.listId=l.id,(l.one||l.two||l.three)&&(n.one=(l.shareType==="FIXED_AMOUNT",String(a(l.one))),n.two=(l.shareType==="FIXED_AMOUNT",String(a(l.two))),n.three=(l.shareType==="FIXED_AMOUNT",String(a(l.three)))),(l.memberOne||l.memberTwo||l.memberThree)&&(n.memberOne=(l.shareType==="FIXED_AMOUNT",String(a(l.memberOne))),n.memberTwo=(l.shareType==="FIXED_AMOUNT",String(a(l.memberTwo))),n.memberThree=(l.shareType==="FIXED_AMOUNT",String(a(l.memberThree)))),C.value=!0,Y.value=l.distributionStatus},A=l=>{T.ElMessageBox.confirm("请确认是否取消分销，确定后商品将从停止分销！！！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:t,success:r}=await ge([l]);t===200&&r?(T.ElMessage.success("取消分销成功"),W()):T.ElMessage.error("取消分销失败")})},ee=l=>{console.log(l),T.ElMessageBox.confirm("请确认是否移除商品，确定后商品将从该列表中移除！！！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:t,success:r}=await je([l]);t===200&&r?(T.ElMessage.success("商品移除成功"),W()):T.ElMessage.error("商品移除失败")})},B=()=>{if(!F.value.length)return T.ElMessage.warning("请选中商品");T.ElMessageBox.confirm("请确认是否取消分销，确定后商品将从该列表中移除！！！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const l=F.value.map(d=>d.id),{code:t,success:r}=await ge(l);t===200&&r?(T.ElMessage.success("取消分销成功"),W()):T.ElMessage.error("取消分销失败")})},Q=l=>{F.value=l};async function se(){V.loading=!0;const{code:l,data:t}=await De.doGetRetrieveProduct({current:V.current,size:V.size,excludeProductIds:V.excludeProductIds});l===200?(V.list=t.list,V.size=t.pageSize,V.current=t.pageNum,V.total=t.total):T.ElMessage.error("获取商品失败"),V.loading=!1}function ne(){n.productIds=[],n.shareType="UNIFIED",n.one="0",n.two="0",n.three="0",V.excludeProductIds=[],R.value=[],n.memberOne="0",n.memberTwo="0",n.memberThree="0"}function c(l){R.value.forEach((t,r,d)=>{t.productId===l&&d.splice(r,1)}),V.excludeProductIds.forEach((t,r,d)=>{t===l&&d.splice(r,1)}),n.productIds.forEach((t,r,d)=>{t===l&&d.splice(r,1)})}function he(l,t,r){const d=n.shareType;if(d==="UNIFIED"||h.value==="ONE")return r();const f=Number(t);if(d==="RATE"&&(f>100||f<0))return r(new Error("一级佣金比例应设置在0-100之间"));if(d==="FIXED_AMOUNT"&&(f>9e3||f<0))return r(new Error("一级佣金应设置在0-9000之间"));if(n.one&&f>=Number(n.one))return r(new Error("二级佣金应小于一级佣金"));if(h.value==="THREE"&&n.three&&f<=Number(n.three))return r(new Error("二级佣金应大于三级佣金"));r()}function Po(l,t,r){const d=n.shareType;if(d==="UNIFIED"||h.value!=="THREE")return r();const f=Number(t);if(d==="RATE"&&(f>100||f<0))return r(new Error("一级佣金比例应设置在0-100之间"));if(d==="FIXED_AMOUNT"&&(f>9e3||f<0))return r(new Error("一级佣金应设置在0-9000之间"));if(n.two&&f>=Number(n.two))return r(new Error("三级佣金应小于二级佣金"));r()}function Ce(l,t){return l==="UNIFIED"&&s.value.shareType==="RATE",a(t)}function Ve(l){return h.value==="ONE"?(delete l.two,delete l.three,delete l.memberTwo,delete l.memberThree):h.value==="TWO"&&(delete l.three,delete l.memberThree),l}async function W(){const{code:l,data:t}=await qe({size:b.size,current:b.current,productName:b.productName,distributionStatus:b.distributionStatus==="ALL"?null:b.distributionStatus});l===200&&t?(b.list=t.records,b.total=t.total,console.log(t)):T.ElMessage.error("获取分销商品列表失败")}function Ro(l){if(l)return l==="IN_DISTRIBUTION"?"分销中":"取消分销"}const ie=e.computed(()=>(console.log(window.permissionList,"window?.permissionList权限"),l=>{var t;return(t=window==null?void 0:window.permissionList)==null?void 0:t.includes(l)}));function qo(l){if(l)return l==="REFUSE"?"已拒绝":l==="UNDER_REVIEW"?"审核中":l==="SELL_OFF"?"下架":l==="SELL_ON"?"上架":l==="SELL_OUT"?"已售完":l==="PLATFORM_SELL_OFF"?"平台下架":"店铺不可用"}async function jo(){const{code:l,data:t}=await Ne();l===200?(h.value=t.level,(t.one||t.two||t.three)&&(t.one=t.shareType==="FIXED_AMOUNT"?String(a(t.one)):String(_(t.one)),t.two=t.shareType==="FIXED_AMOUNT"?String(a(t.two)):String(_(t.two)),t.three=t.shareType==="FIXED_AMOUNT"?String(a(t.three)):String(_(t.three)),s.value=t)):T.ElMessage.error("获取分销配置失败")}const v=(l,t)=>{if(n.shareType!=="UNIFIED"){let r=n.one,d=n.two,f=n.three;return t&&(r=n.memberOne,d=n.memberTwo,f=n.memberThree),n.shareType==="RATE"?h.value==="ONE"?l.mul(_(r)).toFixed(2):h.value==="TWO"?l.mul(_(r)).add(l.mul(_(d))).toFixed(2):l.mul(_(r)).add(l.mul(_(d))).add(l.mul(_(f))).toFixed(2):h.value==="ONE"?new $(r).toFixed(2):h.value==="TWO"?new $(r).add(d).toFixed(2):new $(r).add(d).add(f).toFixed(2)}else return s.value.shareType==="RATE"?h.value==="ONE"?l.mul(_(s.value.one)).toFixed(2):h.value==="TWO"?l.mul(_(s.value.one)).add(l.mul(_(s.value.two))).toFixed(2):l.mul(_(s.value.one)).add(l.mul(_(s.value.two))).add(l.mul(_(s.value.three))).toFixed(2):h.value==="ONE"?new $(s.value.one).toFixed(2):h.value==="TWO"?new $(s.value.one).add(s.value.two).toFixed(2):new $(s.value.one).add(s.value.two).add(s.value.three).toFixed(2)},re=(l,t,r)=>{if(t.shareType!=="UNIFIED"){let d=t.one,f=t.two,k=t.three;return r&&(d=t.memberOne,f=t.memberTwo,k=t.memberThree),t.shareType==="RATE"?h.value==="ONE"?l.mul(a(_(d))).toFixed(2):h.value==="TWO"?l.mul(a(_(d))).add(l.mul(a(_(f)))).toFixed(2):l.mul(a(_(d))).add(l.mul(a(_(f)))).add(l.mul(a(_(k)))).toFixed(2):h.value==="ONE"?a(d).toFixed(2):h.value==="TWO"?a(d).add(a(f)).toFixed(2):a(d).add(a(f)).add(a(k)).toFixed(2)}else return s.value.shareType==="RATE"?h.value==="ONE"?l.mul(_(s.value.one)).toFixed(2):h.value==="TWO"?l.mul(_(s.value.one)).add(l.mul(_(s.value.two))).toFixed(2):l.mul(_(s.value.one)).add(l.mul(_(s.value.two))).add(l.mul(_(s.value.three))).toFixed(2):h.value==="ONE"?new $(s.value.one).toFixed(2):h.value==="TWO"?new $(s.value.one).add(s.value.two).toFixed(2):new $(s.value.one).add(s.value.two).add(s.value.three).toFixed(2)};function ye(l,t,r,d,f,k){if(l==="FIXED_AMOUNT"){d=+a(d),f=+a(f),k=+a(k);const E=(d+f+k).toFixed(2);return d+" + "+f+" +"+k+" = "+E}else if(t=+a(t),r=+a(r),d=+_(a(d)),f=+_(a(f)),k=+_(a(k)),k)if(f){const E=(r*d+r*f+r*k).toFixed(2),L=(t*d+t*f+t*k).toFixed(2);return t===r?t+" * "+d+" + "+t+" * "+f+" + "+t+" * "+k+" = "+L:t+" * "+d+" + "+t+" * "+f+" + "+t+" * "+k+" = "+L+" </br> "+r+" * "+d+" + "+r+" * "+f+" + "+r+" * "+k+" = "+E}else{const E=(r*d).toFixed(2),L=(t*d).toFixed(2);return t===r?t+" * "+d+"   = "+L:t+" * "+d+"   = "+L+" </br> "+r+" * "+d+" = "+E}else{const E=(r*d+r*f).toFixed(2),L=(t*d+t*f).toFixed(2);return t===r?t+" * "+d+" + "+t+" * "+f+" = "+L:t+" * "+d+" + "+t+" * "+f+" = "+L+" </br> "+r+" * "+d+" + "+r+" * "+f+" = "+E}}const fe=e.computed(()=>(l,t,r,d,f,k)=>{if(l==="FIXED_AMOUNT"){const E=+d+ +f+ +k;return d+" + "+f+" +"+k+" = "+E}else if(t=+a(t),r=+a(r),n.shareType==="RATE"&&(d=+_(d),f=+_(f),k=+_(k)),k)if(f){const E=+(r*d+r*f+r*k).toFixed(2),L=+(t*d+t*f+t*k).toFixed(2);return t===r?t+" * "+d+" + "+t+" * "+f+" + "+t+" * "+k+"   = "+L:t+" * "+d+" + "+t+" * "+f+" + "+t+" * "+k+"   = "+L+" </br> "+r+" * "+d+" + "+r+" * "+f+" + "+r+" * "+k+" = "+E}else{const E=(r*d).toFixed(2),L=(t*d).toFixed(2);return t===r?t+" * "+d+" = "+L:t+" * "+d+"   = "+L+" </br> "+r+" * "+d+" = "+E}else{const E=(r*d+r*f).toFixed(2),L=(t*d+t*f).toFixed(2);return t===r?t+" * "+d+" + "+t+" * "+f+" = "+L:t+" * "+d+" + "+t+" * "+f+" = "+L+" </br> "+r+" * "+d+" + "+r+" * "+f+" = "+E}}),_e=e.ref(!1),Xo=l=>{console.log(l),R.value=l.tempGoods;const t=l.tempGoods.map(r=>r.productId);n.productIds=[...n.productIds,...t]};return(l,t)=>{const r=e.resolveComponent("el-button"),d=e.resolveComponent("el-input"),f=e.resolveComponent("el-tab-pane"),k=e.resolveComponent("el-tabs"),E=e.resolveComponent("el-table-column"),L=e.resolveComponent("el-image"),ce=e.resolveComponent("el-tooltip"),we=e.resolveComponent("el-table"),Ho=e.resolveComponent("el-radio"),Mo=e.resolveComponent("el-radio-group"),X=e.resolveComponent("el-form-item"),Te=e.resolveComponent("el-form"),Ee=e.resolveComponent("el-dialog"),Se=e.resolveComponent("el-option"),ke=e.resolveComponent("el-select");return e.openBlock(),e.createElementBlock("div",Ze,[e.createElementVNode("div",ve,[e.createElementVNode("div",et,[ie.value("distribution:product:add")?(e.openBlock(),e.createBlock(r,{key:0,type:"primary",round:"",style:{width:"100px",height:"36px"},onClick:le},{default:e.withCtx(()=>[e.createTextVNode("新增商品")]),_:1})):e.createCommentVNode("",!0),ie.value("distribution:product:sync")?(e.openBlock(),e.createBlock(r,{key:1,type:"primary",round:"",style:{width:"100px",height:"36px"},onClick:ae},{default:e.withCtx(()=>[e.createTextVNode("同步商品")]),_:1})):e.createCommentVNode("",!0),ie.value("distribution:product:delete")?(e.openBlock(),e.createBlock(r,{key:2,type:"primary",round:"",plain:"",style:{width:"120px",height:"36px"},onClick:B},{default:e.withCtx(()=>[e.createTextVNode("批量取消分销")]),_:1})):e.createCommentVNode("",!0)]),e.createElementVNode("div",tt,[e.createVNode(d,{modelValue:b.productName,"onUpdate:modelValue":t[0]||(t[0]=o=>b.productName=o),clearable:"",placeholder:"请输入商品名称",onChange:g},{append:e.withCtx(()=>[e.createVNode(r,{icon:e.unref(me.Search),onClick:y},null,8,["icon"])]),_:1},8,["modelValue"])])]),e.createVNode(k,{modelValue:b.distributionStatus,"onUpdate:modelValue":t[1]||(t[1]=o=>b.distributionStatus=o),type:"card",class:"demo-tabs",style:{"margin-top":"20px"},onTabChange:x},{default:e.withCtx(()=>[e.createVNode(f,{label:"全部",name:"ALL"}),e.createVNode(f,{label:"分销中",name:"IN_DISTRIBUTION"}),e.createVNode(f,{label:"取消分销",name:"CANCEL_DISTRIBUTION"})]),_:1},8,["modelValue"]),e.createVNode(we,{data:b.list,"header-cell-style":{color:"#909399",fontSize:"12px",background:"#F6F8FA"},class:"table-height-fit",onSelectionChange:Q},{default:e.withCtx(()=>[e.createVNode(E,{type:"selection",width:"55"}),e.createVNode(E,{label:"商品信息",width:"260",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("div",ot,[e.createVNode(L,{class:"com__img",src:o.row.pic},null,8,["src"]),e.createElementVNode("div",lt,[e.createElementVNode("div",at,e.toDisplayString(o.row.name),1),o.row.salePrices[0]!==o.row.salePrices[o.row.salePrices.length-1]&&o.row.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",nt," ￥"+e.toDisplayString(e.unref(a)(o.row.salePrices[0]))+" ~ ￥"+e.toDisplayString(re(e.unref(a)(o.row.salePrices[o.row.salePrices.length-1]),o.row,!1)),1)):(e.openBlock(),e.createElementBlock("div",rt,"￥"+e.toDisplayString(e.unref(a)(o.row.salePrices[0])),1))])])]),_:1}),e.createVNode(E,{label:"总库存",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",dt,e.toDisplayString(o.row.stock),1)]),_:1}),e.createVNode(E,{label:"分佣参数",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("div",st,[e.createElementVNode("div",it,[e.createTextVNode(" 金额:"),e.createElementVNode("span",{class:e.normalizeClass([o.row.shareType==="UNIFIED"?s.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":o.row.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(Ce(o.row.shareType,o.row.one)),3)])])]),_:1}),e.createVNode(E,{label:"分佣参数（购买人是会员时）",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("div",ct,[e.createElementVNode("div",mt,[e.createTextVNode(" 金额:"),e.createElementVNode("span",{class:e.normalizeClass([o.row.shareType==="UNIFIED"?s.value.shareType==="FIXED_AMOUNT"?"amount":"percentage":o.row.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(Ce(o.row.shareType,o.row.memberOne)),3)])])]),_:1}),e.createVNode(E,{label:"状态",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",pt,e.toDisplayString(qo(o.row.status)),1)]),_:1}),e.createVNode(E,{label:"分销状态",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",ht,e.toDisplayString(Ro(o.row.distributionStatus)),1)]),_:1}),e.createVNode(E,{label:"分销佣金(预计)",width:"160",align:"center"},{default:e.withCtx(o=>{var H,te;return[e.createVNode(ce,{"raw-content":!0,content:ye(o.row.shareType,o.row.salePrices[0],o.row.salePrices[o.row.salePrices.length-1],o.row.one,(H=o.row)==null?void 0:H.two,(te=o.row)==null?void 0:te.three),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.row.salePrices[0]!==o.row.salePrices[o.row.salePrices.length-1]&&o.row.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",ft,[e.createElementVNode("span",null,"￥"+e.toDisplayString(re(e.unref(a)(o.row.salePrices[0]),o.row,!1)),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(re(e.unref(a)(o.row.salePrices[o.row.salePrices.length-1]),o.row,!1)),1)])):(e.openBlock(),e.createElementBlock("div",_t,"￥"+e.toDisplayString(re(e.unref(a)(o.row.salePrices[0]),o.row,!1)),1))]),_:2},1032,["content"])]}),_:1}),e.createVNode(E,{label:"分销佣金(预计)(购买人是会员时)",width:"160",align:"center"},{default:e.withCtx(o=>{var H,te;return[e.createVNode(ce,{"raw-content":!0,content:ye(o.row.shareType,o.row.salePrices[0],o.row.salePrices[o.row.salePrices.length-1],o.row.memberOne,(H=o.row)==null?void 0:H.memberTwo,(te=o.row)==null?void 0:te.memberThree),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.row.salePrices[0]!==o.row.salePrices[o.row.salePrices.length-1]&&o.row.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",ut,[e.createElementVNode("span",null,"￥"+e.toDisplayString(re(e.unref(a)(o.row.salePrices[0]),o.row,!0)),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(re(e.unref(a)(o.row.salePrices[o.row.salePrices.length-1]),o.row,!0)),1)])):(e.openBlock(),e.createElementBlock("div",bt,"￥"+e.toDisplayString(re(e.unref(a)(o.row.salePrices[0]),o.row,!0)),1))]),_:2},1032,["content"])]}),_:1}),e.createVNode(E,{label:"添加时间",width:"160",align:"center"},{default:e.withCtx(o=>[e.createElementVNode("span",gt,e.toDisplayString(o.row.createTime),1)]),_:1}),e.createVNode(E,{label:"操作",align:"center",width:"160",fixed:"right"},{default:e.withCtx(({row:o})=>[(o.status==="SELL_ON"||o.status==="UNUSABLE")&&o.distributionStatus==="CANCEL_DISTRIBUTION"?(e.openBlock(),e.createBlock(r,{key:0,link:"",type:"primary",class:"f12",onClick:H=>U(o)},{default:e.withCtx(()=>[e.createTextVNode("重新分销")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),o.status!=="FORBIDDEN"&&ie.value("distribution:product:edit")?(e.openBlock(),e.createBlock(r,{key:1,link:"",type:"primary",class:"f12",onClick:H=>Z(o)},{default:e.withCtx(()=>[e.createTextVNode("编辑")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),o.distributionStatus==="IN_DISTRIBUTION"&&o.status==="SELL_ON"&&ie.value("distribution:product:delete")?(e.openBlock(),e.createBlock(r,{key:2,link:"",type:"primary",class:"f12 colorRed",onClick:H=>A(o.id)},{default:e.withCtx(()=>[e.createTextVNode("取消分销")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),(o.status==="SELL_OFF"||o.status==="PLATFORM_SELL_OFF"||o.status==="SELL_OUT"||o.status==="UNUSABLE")&&o.distributionStatus==="CANCEL_DISTRIBUTION"||o.distributionStatus==="CANCEL_DISTRIBUTION"?(e.openBlock(),e.createBlock(r,{key:3,style:{color:"red"},link:"",type:"primary",class:"f12",onClick:H=>ee(o.id)},{default:e.withCtx(()=>[e.createTextVNode("移除")]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(J,{"page-size":b.size,"page-num":b.current,total:b.total,onHandleCurrentChange:M,onHandleSizeChange:q},null,8,["page-size","page-num","total"]),C.value?(e.openBlock(),e.createBlock(Ee,{key:0,modelValue:C.value,"onUpdate:modelValue":t[13]||(t[13]=o=>C.value=o),title:D.value,width:"900px",onClose:S},{footer:e.withCtx(()=>[e.createVNode(r,{onClick:t[12]||(t[12]=o=>C.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(r,{type:"primary",onClick:z},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})]),default:e.withCtx(()=>[e.createVNode(Te,{ref_key:"formRef",ref:I,model:n,rules:K},{default:e.withCtx(()=>[e.createVNode(X,{label:"分销类型"},{default:e.withCtx(()=>[e.createVNode(Mo,{modelValue:n.shareType,"onUpdate:modelValue":t[2]||(t[2]=o=>n.shareType=o)},{default:e.withCtx(()=>[e.createVNode(Ho,{label:"RATE",disabled:u.value==="see"},{default:e.withCtx(()=>[e.createTextVNode("百分比")]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1}),e.createElementVNode("div",Nt,[n.shareType!=="UNIFIED"?(e.openBlock(),e.createBlock(X,{key:0,label:"一级佣金",prop:"one"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:n.one,"onUpdate:modelValue":t[3]||(t[3]=o=>n.one=o),min:.01,max:n.shareType==="FIXED_AMOUNT"?1e4:100,type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","max","disabled"])]),_:1})):e.createCommentVNode("",!0),n.shareType!=="UNIFIED"&&(h.value==="TWO"||h.value==="THREE")?(e.openBlock(),e.createBlock(X,{key:1,label:"二级佣金",prop:"two"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:n.two,"onUpdate:modelValue":t[4]||(t[4]=o=>n.two=o),type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","disabled"])]),_:1})):e.createCommentVNode("",!0),n.shareType!=="UNIFIED"&&h.value==="THREE"?(e.openBlock(),e.createBlock(X,{key:2,label:"三级佣金",prop:"three"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:n.three,"onUpdate:modelValue":t[5]||(t[5]=o=>n.three=o),type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","disabled"])]),_:1})):e.createCommentVNode("",!0),n.shareType==="UNIFIED"?(e.openBlock(),e.createBlock(X,{key:3,label:"一级佣金",prop:"one"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:s.value.one,"onUpdate:modelValue":t[6]||(t[6]=o=>s.value.one=o),min:.01,max:s.value.shareType==="FIXED_AMOUNT"?1e4:100,type:"number",style:{width:"200px"},disabled:!0},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(s.value.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","max"])]),_:1})):e.createCommentVNode("",!0),n.shareType==="UNIFIED"&&(h.value==="TWO"||h.value==="THREE")?(e.openBlock(),e.createBlock(X,{key:4,label:"二级佣金",prop:"two"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:s.value.two,"onUpdate:modelValue":t[7]||(t[7]=o=>s.value.two=o),type:"number",style:{width:"200px"},disabled:!0},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(s.value.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0),n.shareType==="UNIFIED"&&h.value==="THREE"?(e.openBlock(),e.createBlock(X,{key:5,label:"三级佣金",prop:"three"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:s.value.three,"onUpdate:modelValue":t[8]||(t[8]=o=>s.value.three=o),type:"number",style:{width:"200px"},disabled:!0},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(s.value.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0)]),e.createElementVNode("div",xt,[n.shareType!=="UNIFIED"?(e.openBlock(),e.createBlock(X,{key:0,label:"一级佣金（购买人是会员时）",prop:"memberOne"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:n.memberOne,"onUpdate:modelValue":t[9]||(t[9]=o=>n.memberOne=o),min:.01,max:n.shareType==="FIXED_AMOUNT"?1e4:100,type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","max","disabled"])]),_:1})):e.createCommentVNode("",!0),n.shareType!=="UNIFIED"&&(h.value==="TWO"||h.value==="THREE")?(e.openBlock(),e.createBlock(X,{key:1,label:"二级佣金",prop:"memberTwo"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:n.memberTwo,"onUpdate:modelValue":t[10]||(t[10]=o=>n.memberTwo=o),type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","disabled"])]),_:1})):e.createCommentVNode("",!0),n.shareType!=="UNIFIED"&&h.value==="THREE"?(e.openBlock(),e.createBlock(X,{key:2,label:"三级佣金",prop:"memberThree"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:n.memberThree,"onUpdate:modelValue":t[11]||(t[11]=o=>n.memberThree=o),type:"number",style:{width:"200px"},disabled:u.value==="see"},{append:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(n.shareType==="FIXED_AMOUNT"?"元":"%"),1)]),_:1},8,["modelValue","disabled"])]),_:1})):e.createCommentVNode("",!0)]),Ct,u.value!=="edit"?(e.openBlock(),e.createBlock(X,{key:0,label:"关联商品",prop:"productId"},{default:e.withCtx(()=>[e.createVNode(r,{type:"primary",round:"",onClick:j},{default:e.withCtx(()=>[e.createTextVNode("选择商品")]),_:1})]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model","rules"]),u.value!=="edit"?(e.openBlock(),e.createBlock(we,{key:0,height:"370",data:R.value},{default:e.withCtx(()=>[e.createVNode(E,{label:"商品信息"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",Vt,[e.createElementVNode("div",yt,[e.createVNode(L,{class:"tableCom__img",src:o.pic,fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src"]),e.createElementVNode("div",null,[e.createElementVNode("div",wt,e.toDisplayString(o.productName),1),o.salePrices[0]!==o.salePrices[o.salePrices.length-1]&&o.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",Tt,[e.createElementVNode("span",null,"￥"+e.toDisplayString(e.unref(a)(o.salePrices[0])),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(e.unref(a)(o.salePrices[o.salePrices.length-1])),1)])):(e.openBlock(),e.createElementBlock("div",Et,"￥"+e.toDisplayString(e.unref(a)(o.salePrices[0])),1))])])])]),_:1}),n.shareType!=="UNIFIED"?(e.openBlock(),e.createBlock(E,{key:0,label:"分销佣金(预计)",align:"center",width:"200"},{default:e.withCtx(({row:o})=>[e.createVNode(ce,{"raw-content":!0,content:fe.value(n.shareType,o.salePrices[0],o.salePrices[o.salePrices.length-1],n.one,n==null?void 0:n.two,n==null?void 0:n.three),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.salePrices[0]!==o.salePrices[o.salePrices.length-1]&&o.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",St,[e.createElementVNode("span",null,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[0]),!1)),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[o.salePrices.length-1]),!1)),1)])):(e.openBlock(),e.createElementBlock("div",kt,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[0]),!1)),1))]),_:2},1032,["content"])]),_:1})):e.createCommentVNode("",!0),n.shareType==="UNIFIED"?(e.openBlock(),e.createBlock(E,{key:1,label:"分销佣金(预计)",align:"center",width:"200"},{default:e.withCtx(({row:o})=>{var H,te;return[e.createVNode(ce,{"raw-content":!0,content:fe.value(s.value.shareType,o.salePrices[0],o.salePrices[o.salePrices.length-1],s.value.one,(H=s.value)==null?void 0:H.two,(te=s.value)==null?void 0:te.three),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.salePrices[0]!==o.salePrices[o.salePrices.length-1]&&o.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",Dt,[e.createElementVNode("span",null,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[0]),!1)),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[o.salePrices.length-1]),!1)),1)])):(e.openBlock(),e.createElementBlock("div",It,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[0]),!1)),1))]),_:2},1032,["content"])]}),_:1})):e.createCommentVNode("",!0),n.shareType!=="UNIFIED"?(e.openBlock(),e.createBlock(E,{key:2,label:"分销佣金(预计)(购买人是会员时)",align:"center",width:"200"},{default:e.withCtx(({row:o})=>[e.createVNode(ce,{"raw-content":!0,content:fe.value(n.shareType,o.salePrices[0],o.salePrices[o.salePrices.length-1],n.memberOne,n==null?void 0:n.memberTwo,n==null?void 0:n.memberThree),placement:"bottom",effect:"light"},{default:e.withCtx(()=>[o.salePrices[0]!==o.salePrices[o.salePrices.length-1]&&o.salePrices.length>1?(e.openBlock(),e.createElementBlock("div",Bt,[e.createElementVNode("span",null,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[0]),!0)),1),e.createTextVNode(" ~ "),e.createElementVNode("span",null,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[o.salePrices.length-1]),!0)),1)])):(e.openBlock(),e.createElementBlock("div",Ot,"￥"+e.toDisplayString(v(e.unref(a)(o.salePrices[0]),!0)),1))]),_:2},1032,["content"])]),_:1})):e.createCommentVNode("",!0),e.createVNode(E,{label:"操作",align:"center",width:"80"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",{class:"f12 colorRed",style:{cursor:"pointer"},onClick:H=>c(o.productId)},"移出",8,Ft)]),_:1})]),_:1},8,["data"])):e.createCommentVNode("",!0)]),_:1},8,["modelValue","title"])):e.createCommentVNode("",!0),e.createVNode(Ie,{modelValue:_e.value,"onUpdate:modelValue":t[14]||(t[14]=o=>_e.value=o),"search-consignment-product":!0,"point-goods-list":R.value,onOnConfirm:Xo},null,8,["modelValue","point-goods-list"]),e.createVNode(Ee,{modelValue:p.value,"onUpdate:modelValue":t[18]||(t[18]=o=>p.value=o),title:"同步分销商品(请先确保商品已同步)",center:"",onClose:S},{footer:e.withCtx(()=>[e.createElementVNode("span",Ut,[e.createVNode(r,{onClick:t[17]||(t[17]=o=>p.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(r,{type:"primary",onClick:w},{default:e.withCtx(()=>[e.createTextVNode("提交")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(Te,null,{default:e.withCtx(()=>[e.createVNode(X,{label:"同步的目标店铺"},{default:e.withCtx(()=>[e.createVNode(ke,{modelValue:O.shopId,"onUpdate:modelValue":t[15]||(t[15]=o=>O.shopId=o),class:"inputWidth",placeholder:"请选择目标店铺",style:{},disabled:""},{default:e.withCtx(()=>[e.createVNode(Se,{label:"所有店铺",value:""})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(X,{label:"需要同步的商品"},{default:e.withCtx(()=>[e.createVNode(ke,{modelValue:O.productId,"onUpdate:modelValue":t[16]||(t[16]=o=>O.productId=o),class:"inputWidth",placeholder:"请选择需要同步的商品",style:{},disabled:""},{default:e.withCtx(()=>[e.createVNode(Se,{label:"所有商品",value:""})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),Go="",Lt=Object.freeze(Object.defineProperty({__proto__:null,default:oe(zt,[["__scopeId","data-v-3ad198bb"]])},Symbol.toStringTag,{value:"Module"})),{divTenThousand:G}=de();function xe(m,b){return G(b)}const At=()=>({useSingleCalculationFormula:(i,a,N)=>{var p,h;const _=(p=i==null?void 0:i.bonusShare)==null?void 0:p.shareType,u=(h=i==null?void 0:i.bonusShare)==null?void 0:h[a],C=G((i==null?void 0:i.dealPrice)*(i==null?void 0:i.num));return _==="RATE"?`${C} * ${xe(_,u)}% = ${N}`:xe(_,u)},useTotalPrice:i=>{let a=[],N=[],_=[],u=[];return i.items.forEach(p=>{let h=[];p.orderStatus==="PAID"?h=_:p.orderStatus==="COMPLETED"?h=a:p.orderStatus==="CLOSED"&&(h=N),p.one.bonus&&(u.push(G(p.one.bonus)),h.push(G(p.one.bonus))),p.two.bonus&&(u.push(G(p.two.bonus)),h.push(G(p.two.bonus))),p.three.bonus&&(u.push(G(p.three.bonus)),h.push(G(p.three.bonus))),p.purchase&&p.one.userId&&(u.push(G(p.one.bonus)),h.push(G(p.one.bonus)))}),{completePrice:a.join(" + ")+" = "+a.reduce((p,h)=>p.plus(h),new $(0)),closedPrice:N.join(" + ")+" = "+N.reduce((p,h)=>p.plus(h),new $(0)),toSelledPrice:_.join(" + ")+" = "+_.reduce((p,h)=>p.plus(h),new $(0)),totalPrice:u.join(" + ")+" = "+u.reduce((p,h)=>p.plus(h),new $(0))}}}),$t={class:"head"},Pt={style:{color:"#f00"}},Rt={class:"ellipsis"},qt={class:"content"},jt={class:"goods"},Xt={class:"goods__pic"},Ht=["src"],Mt={class:"goods__info"},Wt={class:"goods__info-flex"},Gt={class:"goods__info-flex--name"},Yt={class:"goods__info-flex--price"},Qt={class:"goods__info-flex"},Jt={class:"goods__info-flex--specs"},Kt={class:"goods__info-flex--num"},Zt={class:"f12 color51"},vt={class:"content__right1"},eo={class:"content__right1--item"},to={class:"content__right2"},oo={key:0,class:"content__right2--item"},lo={key:1,class:"content__right2--item"},ao={key:2,class:"content__right2--item"},no=e.defineComponent({__name:"distributionOrderTable",props:{orderInfo:{type:Object,default(){return{id:null,type:null,name:"",url:"",append:""}}}},setup(m){const{useSingleCalculationFormula:b,useTotalPrice:i}=At(),{toClipboard:a}=Be(),{divTenThousand:N,divHundred:_}=de(),u=m;function C(I,D){let V=new $(0);return"items"in u.orderInfo&&u.orderInfo.items.length&&u.orderInfo.items.forEach(n=>{n[I].bonus&&(V=V.add(N(n[I].bonus))),D&&n[I].userId&&(V=V.add(N(n[I].bonus)))}),V}function p(I){let D=new $(0);return"items"in u.orderInfo&&u.orderInfo.items.length&&u.orderInfo.items.forEach(V=>{I&&V.orderStatus!==I||(V.one.bonus&&(D=D.add(N(V.one.bonus))),V.two.bonus&&(D=D.add(N(V.two.bonus))),V.three.bonus&&(D=D.add(N(V.three.bonus))),V.purchase&&V.one.userId&&(D=D.add(N(V.one.bonus))))}),D.toNumber()}function h(I){return I==="COMPLETED"?"已赚":I==="CLOSED"?"已失效":"待结算"}async function s(I){try{await a(I),T.ElMessage.success("复制成功")}catch{T.ElMessage.error("复制失败")}}return(I,D)=>{var K;const V=e.resolveComponent("el-table-column"),n=e.resolveComponent("el-table"),O=e.resolveComponent("el-tooltip");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",$t,[e.createElementVNode("div",null,[e.createTextVNode(" 订单号："+e.toDisplayString(m.orderInfo.orderNo),1),e.createElementVNode("span",{style:{"margin-left":"10px",color:"#1890ff",cursor:"pointer"},onClick:D[0]||(D[0]=F=>s(m.orderInfo.orderNo))},"复制")]),e.createElementVNode("div",null,"下单时间："+e.toDisplayString(m.orderInfo.createTime),1),e.createElementVNode("div",null,"买家："+e.toDisplayString(m.orderInfo.buyerName),1),e.createElementVNode("div",null,"实付款："+e.toDisplayString(e.unref(N)(m.orderInfo.payAmount).toFixed(2))+" 元",1),e.createElementVNode("div",Pt,e.toDisplayString(m.orderInfo.items[0].purchase===!0?"内购":""),1),e.createElementVNode("div",Rt,"所属店铺："+e.toDisplayString(m.orderInfo.shopName),1)]),e.createElementVNode("div",qt,[e.createVNode(n,{data:m.orderInfo.items,border:"",width:"100%","row-style":{height:"110px"}},{default:e.withCtx(()=>[e.createVNode(V,{prop:"name"},{default:e.withCtx(({row:F})=>[e.createElementVNode("div",jt,[e.createElementVNode("div",Xt,[e.createElementVNode("img",{src:F.image,style:{width:"60px",height:"50px"}},null,8,Ht),e.createElementVNode("div",{class:"goods__pic--state",style:e.normalizeStyle(F.orderStatus==="CLOSED"?"background:#9A9A9A;":F.orderStatus==="COMPLETED"?"background:#FD0505 ;":"")},e.toDisplayString(h(F.orderStatus)),5)]),e.createElementVNode("div",Mt,[e.createElementVNode("div",Wt,[e.createElementVNode("div",Gt,e.toDisplayString(F.productName),1),e.createElementVNode("div",Yt,e.toDisplayString(F.num)+"件",1)]),e.createElementVNode("div",Qt,[e.createElementVNode("div",Jt,e.toDisplayString(F.specs&&F.specs.join("-")),1),e.createElementVNode("div",Kt,"￥"+e.toDisplayString(e.unref(N)(F.dealPrice)),1)])])])]),_:1}),e.createVNode(V,{prop:"address",width:"125px"},{default:e.withCtx(({row:F})=>[e.createElementVNode("div",Zt,[e.createTextVNode(" 佣金:"),e.createElementVNode("span",{class:e.normalizeClass([F.bonusShare.shareType==="FIXED_AMOUNT"?"amount":"percentage"])},e.toDisplayString(e.unref(N)(F.bonusShare.one)),3)])]),_:1})]),_:1},8,["data"]),e.createElementVNode("div",vt,[e.createElementVNode("div",eo,[e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].one.name),1),e.createElementVNode("div",null,e.toDisplayString(m.orderInfo.items[0].one.mobile),1),e.createElementVNode("div",null,[e.createVNode(O,{"raw-content":!0,content:(m.orderInfo.items[0].one.bonus!=="0"?e.unref(b)((K=m.orderInfo.items)==null?void 0:K[0],"one",C("one")):"该层级无分销员，不计算对应佣金").toString(),placement:"bottom",effect:"light"},null,8,["content"])])])]),e.createElementVNode("div",to,[p("PAID")?(e.openBlock(),e.createElementBlock("div",oo,[e.createVNode(O,{"raw-content":!0,content:e.unref(i)(m.orderInfo).toSelledPrice,placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("待结算："+e.toDisplayString(p("PAID")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),p("COMPLETED")?(e.openBlock(),e.createElementBlock("div",lo,[e.createVNode(O,{"raw-content":!0,content:e.unref(i)(m.orderInfo).completePrice,placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("已赚："+e.toDisplayString(p("COMPLETED")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0),p("CLOSED")?(e.openBlock(),e.createElementBlock("div",ao,[e.createVNode(O,{"raw-content":!0,content:e.unref(i)(m.orderInfo).closedPrice,placement:"bottom",effect:"light"},{default:e.withCtx(()=>[e.createTextVNode("已失效："+e.toDisplayString(p("CLOSED")),1)]),_:1},8,["content"])])):e.createCommentVNode("",!0)])])],64)}}}),Jo="",ro=oe(no,[["__scopeId","data-v-421286ab"]]),so=(m=>(e.pushScopeId("data-v-b509af32"),m=m(),e.popScopeId(),m))(()=>e.createElementVNode("div",{style:{"padding-bottom":"30px"}},[e.createElementVNode("p",null,"数据范围为本店铺内所有的分销订单（仅分销商品产生的订单。内购是指内购订单买家可获得对应商品的一级分销佣金。"),e.createElementVNode("p",{class:"fw"},"查询结果统计"),e.createElementVNode("p",null,"累计佣金：已完成订单状态中已赚的佣金之和"),e.createElementVNode("p",null,"待结算佣金：已付款订单状态中待结算的佣金之和"),e.createElementVNode("p",null,"已失效佣金：已关闭订单状态中已生效的佣金之和"),e.createElementVNode("p",{class:"fw"},"结算状态"),e.createElementVNode("p",null,"待结算：是指订单未完结(可能退款退货等情况)暂不计入累计佣金；"),e.createElementVNode("p",null,"已赚：是指对应商品没有退款(或退款失败) ，对应佣金统计到【累计佣金】中"),e.createElementVNode("p",null,"已失效：是指对应商品已退款成功，无法获得佣金。")],-1)),io=e.defineComponent({__name:"orderIllustration",props:{orderIllustrationShow:{type:Boolean,default:!1}},emits:["update:orderIllustrationShow","Illustration"],setup(m,{emit:b}){const i=m,a=b,N=e.computed({get:()=>i.orderIllustrationShow,set:_=>{a("update:orderIllustrationShow",_)}});return(_,u)=>{const C=e.resolveComponent("el-dialog");return e.openBlock(),e.createBlock(C,{modelValue:N.value,"onUpdate:modelValue":u[0]||(u[0]=p=>N.value=p),title:"分销订单说明",width:"45%",center:"","close-on-click-modal":!1},{default:e.withCtx(()=>[so]),_:1},8,["modelValue"])}}}),Zo="",co=oe(io,[["__scopeId","data-v-b509af32"]]),mo={class:"count"},po={style:{float:"right"}},ho=e.createStaticVNode('<div class="tbhead" data-v-ed432cdc><div class="tbhead__goods" data-v-ed432cdc>商品</div><div class="tbhead__parameter" data-v-ed432cdc>分佣参数</div><div class="tbhead__detail" data-v-ed432cdc>分佣详情</div><div class="tbhead__total" data-v-ed432cdc>佣金结算</div></div>',1),fo=e.defineComponent({__name:"DistributionOrder",setup(m){const{divTenThousand:b}=de(),i=e.ref(!1),a=e.reactive({orderNo:"",productName:"",shopName:"",buyerNickname:"",date:"",startTime:"",endTime:"",status:""}),N=e.reactive({current:1,size:10,total:0}),_=e.ref([]),u=e.ref({total:"",unsettled:"",invalid:""});C();async function C(){const{code:z,data:w,msg:j}=await He({current:N.current,size:N.size,...a});z===200&&w?(_.value=w.page.records,u.value=w.statistic,N.total=w.page.total):T.ElMessage.error(j||"获取分销订单失败")}const p=()=>{N.current=1,(a==null?void 0:a.date.length)>0&&(a.startTime=a.date[0],a.endTime=a.date[1]),C()},h=()=>{a.orderNo="",a.productName="",a.shopName="",a.buyerNickname="",a.date="",a.startTime="",a.endTime="",N.current=1,C()},s=z=>{N.current=z,C()},I=z=>{N.current=1,N.size=z,C()},D=new Oe,V=e.ref(" "),n=e.ref("全部订单"),O=z=>{a.status=z,C()},K=z=>{if(V.value=" ",n.value=z,n.value==="近一个月订单"){const w=D.getLastMonth(new Date);F(w)}else if(n.value==="近三个月订单"){const w=D.getLastThreeMonth(new Date);F(w)}else a.startTime="",a.endTime="",C()},F=async z=>{const w=D.getYMDs(new Date);a.startTime=z,a.endTime=w,C()},R=e.ref(!1),le=z=>{R.value=z},ae=()=>{R.value=!0};return(z,w)=>{const j=e.resolveComponent("el-input"),M=e.resolveComponent("el-form-item"),q=e.resolveComponent("el-col"),S=e.resolveComponent("el-date-picker"),y=e.resolveComponent("el-row"),x=e.resolveComponent("el-button"),g=e.resolveComponent("el-form"),U=e.resolveComponent("el-dropdown-item"),Y=e.resolveComponent("el-dropdown-menu"),Z=e.resolveComponent("el-dropdown"),A=e.resolveComponent("el-tab-pane"),ee=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(ue,{modelValue:i.value,"onUpdate:modelValue":w[4]||(w[4]=B=>i.value=B)},{default:e.withCtx(()=>[e.createVNode(g,{ref:"ruleForm",model:a},{default:e.withCtx(()=>[e.createVNode(y,null,{default:e.withCtx(()=>[e.createVNode(q,{span:8},{default:e.withCtx(()=>[e.createVNode(M,{label:"订单号",prop:"orderNo","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(j,{modelValue:a.orderNo,"onUpdate:modelValue":w[0]||(w[0]=B=>a.orderNo=B),placeholder:"请填写订单号",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(q,{span:8},{default:e.withCtx(()=>[e.createVNode(M,{label:"商品名称",prop:"productName","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(j,{modelValue:a.productName,"onUpdate:modelValue":w[1]||(w[1]=B=>a.productName=B),placeholder:"请填写商品名称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(q,{span:8},{default:e.withCtx(()=>[e.createVNode(M,{label:"买家昵称",prop:"buyerNickname","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(j,{modelValue:a.buyerNickname,"onUpdate:modelValue":w[2]||(w[2]=B=>a.buyerNickname=B),placeholder:"请填写买家昵称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(q,{span:16},{default:e.withCtx(()=>[e.createVNode(M,{label:"下单时间",prop:"date","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(S,{modelValue:a.date,"onUpdate:modelValue":w[3]||(w[3]=B=>a.date=B),clearable:!1,type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","value-format":"YYYY-MM-DD HH:mm:ss","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(y,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(q,{span:8},{default:e.withCtx(()=>[e.createVNode(x,{type:"primary",round:"",onClick:p},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1}),e.createVNode(x,{type:"primary",round:"",onClick:h},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createElementVNode("div",mo,[e.createElementVNode("span",null,"累计总佣金： ￥ "+e.toDisplayString(e.unref(b)(u.value.total)),1),e.createElementVNode("span",null,"待结算总佣金： ￥ "+e.toDisplayString(e.unref(b)(u.value.unsettled)),1),e.createElementVNode("span",null,"已失效总佣金： ￥"+e.toDisplayString(e.unref(b)(u.value.invalid)),1),e.createElementVNode("span",po,[e.createVNode(Fe,{name:"icon-jingshi",size:"30",color:"#5b6982",style:{cursor:"pointer"},onClick:ae})])]),e.createVNode(ee,{modelValue:V.value,"onUpdate:modelValue":w[5]||(w[5]=B=>V.value=B),onTabChange:O},{default:e.withCtx(()=>[e.createVNode(A,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(n.value),1),e.createVNode(Z,{placement:"bottom-end",trigger:"click",onCommand:K},{dropdown:e.withCtx(()=>[e.createVNode(Y,null,{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(["近一个月订单","近三个月订单","全部订单"],B=>e.createVNode(U,{key:B,command:B},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(B),1)]),_:2},1032,["command"])),64))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(e.unref(T.ElIcon),{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(e.unref(me.ArrowDown))]),_:1})]),e.createVNode(e.unref(T.ElIcon),null,{default:e.withCtx(()=>[e.createVNode(e.unref(me.ArrowDown))]),_:1})]),_:1})]),_:1}),e.createVNode(A,{label:"已付款",name:"PAID"}),e.createVNode(A,{label:"已完成",name:"COMPLETED"}),e.createVNode(A,{label:"已关闭",name:"CLOSED"})]),_:1},8,["modelValue"]),ho,(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(_.value,B=>(e.openBlock(),e.createBlock(ro,{key:B.orderNo,"order-info":B},null,8,["order-info"]))),128)),e.createVNode(J,{"page-num":N.current,"page-size":N.size,total:N.total,onHandleCurrentChange:s,onHandleSizeChange:I},null,8,["page-num","page-size","total"]),e.createVNode(co,{"order-illustration-show":R.value,"onUpdate:orderIllustrationShow":w[6]||(w[6]=B=>R.value=B),onIllustration:le},null,8,["order-illustration-show"])],64)}}}),vo="",_o=Object.freeze(Object.defineProperty({__proto__:null,default:oe(fo,[["__scopeId","data-v-ed432cdc"]])},Symbol.toStringTag,{value:"Module"})),uo={style:{width:"190px"}},bo={key:0,style:{width:"135px"},class:"ml"},go={key:1,style:{width:"200px"},class:"ml"},No=["onClick"],xo={class:"f12 color51",style:{"margin-left":"6px"}},Co={class:"ellipsis"},Vo={key:0},yo={style:{display:"flex","align-items":"center","justify-content":"space-around",width:"260px"}},wo={style:{"font-size":"20px"}},To=["onClick"],Eo={class:"f12 color58",style:{"margin-bottom":"20px"}},So={class:"f12",style:{display:"flex"}},ko={class:"ml"},Do={class:"ml"},Io={class:"dialog-footer"},Bo={class:"dialog-footer"},Oo=e.defineComponent({__name:"WholeSaler",setup(m){const b=e.ref(!1),i=e.reactive({name:"",mobile:"",date:"",startTime:"",endTime:"",status:"SUCCESS"}),{divTenThousand:a}=de(),N=e.reactive({current:1,size:10,total:0}),_=e.ref([]),u=e.ref([]),C=e.ref(!1),p=e.reactive({current:1,total:0,size:10,list:[],userId:""}),h=e.ref(!1),s=e.reactive({current:1,total:0,size:10,list:[],userId:"",rank:""});w();const I=S=>{N.current=S,w()},D=S=>{N.current=1,N.size=S,w()},V=()=>{N.current=1,i.date.length>0&&(console.log("searchConfig.date",i.date),i.startTime=i.date[0],i.endTime=i.date[1]),w()},n=S=>{i.status==="SUCCESS"&&(C.value=!0,p.userId=S,j(S))},O=S=>{i.status==="SUCCESS"&&(h.value=!0,s.userId=S,M(S))},K=()=>{p.current=1,p.size=10,p.list=[],p.userId=""},F=()=>{s.current=1,s.size=10,s.list=[],s.userId="",s.rank=""},R=S=>{p.current=S,j()},le=S=>{p.current=1,p.size=S,j()},ae=S=>{s.current=S,M()},z=S=>{s.current=1,s.size=S,M()};async function w(){const{code:S,data:y}=await Me({...i,...N});S===200?(_.value=y.records,N.total=y.total):T.ElMessage.error("获取分销商失败")}async function j(S){const{code:y,data:x}=await We({userId:S||p.userId,current:p.current,size:p.size});y&&y===200?(p.list=x.records,p.total=x.total,C.value=!0):(T.ElMessage.error("获取下线失败"),C.value=!1)}async function M(S){const{code:y,data:x}=await Ge({userId:S||s.userId,current:s.current,size:s.size});y&&y===200?(s.list=x.records,s.total=x.total,s.rank=x.rank,h.value=!0):(T.ElMessage.error("获取排行失败"),h.value=!1)}const q=()=>{N.current=1,i.date="",i.endTime="",i.startTime="",i.mobile="",i.name="",w()};return(S,y)=>{const x=e.resolveComponent("el-input"),g=e.resolveComponent("el-form-item"),U=e.resolveComponent("el-col"),Y=e.resolveComponent("el-date-picker"),Z=e.resolveComponent("el-row"),A=e.resolveComponent("el-button"),ee=e.resolveComponent("el-form"),B=e.resolveComponent("el-image"),Q=e.resolveComponent("el-table-column"),se=e.resolveComponent("el-table"),ne=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(ue,{modelValue:b.value,"onUpdate:modelValue":y[3]||(y[3]=c=>b.value=c)},{default:e.withCtx(()=>[e.createVNode(ee,{ref:"ruleForm",model:i},{default:e.withCtx(()=>[e.createVNode(Z,null,{default:e.withCtx(()=>[e.createVNode(U,{span:8},{default:e.withCtx(()=>[e.createVNode(g,{label:"姓名",prop:"name","label-width":"50px"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:i.name,"onUpdate:modelValue":y[0]||(y[0]=c=>i.name=c),placeholder:"请填写姓名检索",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(U,{span:8},{default:e.withCtx(()=>[e.createVNode(g,{label:"手机号",prop:"mobile","label-width":"70px"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:i.mobile,"onUpdate:modelValue":y[1]||(y[1]=c=>i.mobile=c),placeholder:"请填写手机号检索",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(U,{span:16},{default:e.withCtx(()=>[e.createVNode(g,{label:"申请时间",prop:"date","label-width":"75px"},{default:e.withCtx(()=>[e.createVNode(Y,{modelValue:i.date,"onUpdate:modelValue":y[2]||(y[2]=c=>i.date=c),type:"datetimerange","range-separator":"-",clearable:!1,"start-placeholder":"开始时间","value-format":"YYYY-MM-DD HH:mm:ss","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e.createVNode(Z,{style:{"margin-left":"30px"}},{default:e.withCtx(()=>[e.createVNode(U,{span:8},{default:e.withCtx(()=>[e.createVNode(A,{type:"primary",round:"",onClick:V},{default:e.withCtx(()=>[e.createTextVNode("搜索")]),_:1}),e.createVNode(A,{type:"primary",round:"",onClick:q},{default:e.withCtx(()=>[e.createTextVNode("重置")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createVNode(e.unref(Ue),{"checked-item":u.value,"onUpdate:checkedItem":y[4]||(y[4]=c=>u.value=c),data:_.value,selection:!0},{header:e.withCtx(({row:c})=>[e.createElementVNode("div",uo,"申请时间："+e.toDisplayString(c.createTime),1),i.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",bo,"审核人员："+e.toDisplayString(c.auditor),1)):e.createCommentVNode("",!0),i.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",go,"审核(通过)："+e.toDisplayString(c.passTime),1)):e.createCommentVNode("",!0),i.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",{key:2,style:{"margin-left":"200px",color:"#0f40f5",cursor:"pointer"},onClick:he=>O(c.userId)}," 佣金排行榜 ",8,No)):e.createCommentVNode("",!0)]),default:e.withCtx(()=>[e.createVNode(pe,{label:"分销商信息",width:"90"},{default:e.withCtx(({row:c})=>[e.createElementVNode("div",null,[e.createVNode(Z,{justify:"space-between"},{default:e.withCtx(()=>[e.createVNode(B,{src:c.avatar,style:{width:"40px",height:"40px","border-radius":"4px"}},null,8,["src"]),e.createElementVNode("div",xo,[e.createElementVNode("div",Co,e.toDisplayString(c.name),1),e.createElementVNode("div",null,e.toDisplayString(c.mobile),1),i.status==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",Vo,"上级 ："+e.toDisplayString(c.referrer||"平台"),1)):e.createCommentVNode("",!0)])]),_:2},1024)])]),_:1}),i.status==="SUCCESS"?(e.openBlock(),e.createBlock(pe,{key:0,label:"团队成员",width:"100"},{default:e.withCtx(({row:c})=>[e.createElementVNode("div",yo,[e.createElementVNode("div",null,[e.createTextVNode(" 总人数： "),e.createElementVNode("span",wo,e.toDisplayString(Number(c.one)+Number(c.two)+Number(c.three)),1)]),e.createElementVNode("div",{class:"column",onClick:he=>n(c.userId)},[e.createVNode(A,{type:"primary",link:""},{default:e.withCtx(()=>[e.createTextVNode("成员："+e.toDisplayString(c.one),1)]),_:2},1024)],8,To)])]),_:1})):e.createCommentVNode("",!0),i.status==="SUCCESS"?(e.openBlock(),e.createBlock(pe,{key:1,label:"佣金",width:"150"},{default:e.withCtx(({row:c})=>[e.createElementVNode("div",null,[e.createElementVNode("div",Eo,"累计佣金："+e.toDisplayString(e.unref(a)(c.total)),1),e.createElementVNode("div",So,[e.createElementVNode("div",null,"待提现佣金："+e.toDisplayString(e.unref(a)(c.undrawn)),1),e.createElementVNode("div",ko,"待结算佣金："+e.toDisplayString(e.unref(a)(c.unsettled)),1),e.createElementVNode("div",Do,"已失效佣金："+e.toDisplayString(e.unref(a)(c.invalid)),1)])])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["checked-item","data"]),e.createVNode(J,{"page-num":N.current,"page-size":N.size,total:N.total,onHandleCurrentChange:I,onHandleSizeChange:D},null,8,["page-num","page-size","total"]),e.createVNode(ne,{modelValue:C.value,"onUpdate:modelValue":y[7]||(y[7]=c=>C.value=c),width:"554px",title:"团队成员",onClose:K},{footer:e.withCtx(()=>[e.createElementVNode("span",Io,[e.createVNode(A,{onClick:y[5]||(y[5]=c=>C.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(A,{type:"primary",onClick:y[6]||(y[6]=c=>C.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createVNode(se,{data:p.list,"header-cell-style":{background:"#f6f8fa",fontSize:"12px",color:"#909399","font-weight":"bold"},"cell-style":{fontSize:"12px",color:"#333333"},height:"400"},{default:e.withCtx(()=>[e.createVNode(Q,{label:"姓名",align:"center"},{default:e.withCtx(({row:c})=>[e.createTextVNode(e.toDisplayString(c.name?c.name:c.nickname),1)]),_:1}),e.createVNode(Q,{label:"层级",align:"center"},{default:e.withCtx(({row:c})=>[e.createTextVNode(e.toDisplayString(c.level==="ONE"?"一级":c.level==="TWO"?"二级":"三级"),1)]),_:1}),e.createVNode(Q,{label:"累计消费",align:"center",prop:"consumption"},{default:e.withCtx(({row:c})=>[e.createTextVNode(e.toDisplayString(e.unref(a)(c.consumption)),1)]),_:1}),e.createVNode(Q,{label:"订单数",align:"center",prop:"orderCount"})]),_:1},8,["data"]),e.createVNode(J,{"page-num":p.current,"page-size":p.size,total:p.total,onHandleCurrentChange:R,onHandleSizeChange:le},null,8,["page-num","page-size","total"])]),_:1},8,["modelValue"]),e.createVNode(ne,{modelValue:h.value,"onUpdate:modelValue":y[10]||(y[10]=c=>h.value=c),width:"554px",title:"佣金排行榜",onClose:F},{footer:e.withCtx(()=>[e.createElementVNode("span",Bo,[e.createVNode(A,{onClick:y[8]||(y[8]=c=>h.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(A,{type:"primary",onClick:y[9]||(y[9]=c=>h.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("div",null,"您的累计佣金排名为："+e.toDisplayString(s.rank.rank),1),e.createVNode(se,{data:s.list,"header-cell-style":{background:"#f6f8fa",fontSize:"12px",color:"#909399","font-weight":"bold"},"cell-style":{fontSize:"12px",color:"#333333"},height:"400"},{default:e.withCtx(()=>[e.createVNode(Q,{label:"名次",align:"center",type:"index"}),e.createVNode(Q,{label:"姓名",align:"center",prop:"name"}),e.createVNode(Q,{label:"累计佣金",align:"center",prop:"total"},{default:e.withCtx(({row:c})=>[e.createTextVNode(e.toDisplayString(e.unref(a)(c.total)),1)]),_:1})]),_:1},8,["data"]),e.createVNode(J,{"page-num":s.current,"page-size":s.size,total:s.total,onHandleCurrentChange:ae,onHandleSizeChange:z},null,8,["page-num","page-size","total"])]),_:1},8,["modelValue"])],64)}}}),tl="",Fo=Object.freeze(Object.defineProperty({__proto__:null,default:oe(Oo,[["__scopeId","data-v-f4094659"]])},Symbol.toStringTag,{value:"Module"})),Uo={class:"dis"},zo={class:"dis__header"},Lo={class:"dis__header-left"},Ao=e.defineComponent({__name:"DistributionCode",props:{platform:{type:String,default:""}},setup(m){const b=e.reactive({list:[],current:1,pages:1,total:0,size:10,productName:"",distributionStatus:"ALL"}),{mulTenThousand:i,divTenThousand:a,mulHundred:N,divHundred:_}=de(),u=e.ref("add"),C=e.ref(!1),p=e.ref("ONE"),h=e.ref({}),s=e.ref([]),I=e.ref({id:"",name:""}),D=e.ref(),V=e.computed(()=>u.value==="add"?"新增":u.value==="edit"?"编辑":"查看"),n=m;setTimeout(()=>{console.log(n.platform)},1e3),e.reactive({pages:1,current:1,list:[],total:0,size:10,name:"",loading:!1,excludeProductIds:[]});const O=e.reactive({quantity:null,shopId:null}),K=e.reactive({quantity:[{required:!0,message:"请填写数量",trigger:"blur"}],shopId:[{required:!0,message:"请选择绑定的店铺",trigger:"blur"}]}),F=()=>{Ye({}).then(x=>{var g;x.code===200?s.value=((g=x==null?void 0:x.data)==null?void 0:g.records)??[]:(s.value=[],T.ElMessage.error((x==null?void 0:x.msg)??"获取商店列表失败！"))})};let R=e.computed(()=>x=>{const g=s.value.filter(U=>U.id==x);return g.length>0?g[0].name:""});const le=()=>{const x=JSON.parse(localStorage.getItem("storlocalshopStore"));I.value={id:x.value.shopId,name:x.value.name}};S(),n.platform=="shop"?le():F(),setTimeout(()=>{q()},200);const ae=()=>{u.value="add",C.value=!0},z=()=>{D.value&&D.value.validate(async x=>{if(x){console.log(O,"distributeForm");const g={num:Number(O.quantity),shopId:n.platform=="shop"?I.value.id:O.shopId};console.log(g,"param");const U=await Qe(g);U.code===200?(T.ElMessage.success("激活码创建成功！"),q(),C.value=!1,O.quantity=null,O.shopId=null):T.ElMessage.error((U==null?void 0:U.msg)??"创建邀请码失败")}})},w=x=>{b.current=x,q()},j=x=>{b.size=x,q()},M=()=>{};e.ref("");async function q(){const{code:x,data:g,msg:U}=await Je({size:b.size,current:b.current,shopId:n.platform=="shop"?I.value.id:null});x===200&&g?(b.list=g.records,b.total=g.total):T.ElMessage.error(U??"获取邀请码列表失败")}async function S(){const{code:x,data:g}=await Ne();x===200?(p.value=g.level,(g.one||g.two||g.three)&&(g.one=g.shareType==="FIXED_AMOUNT"?String(a(g.one)):String(_(g.one)),g.two=g.shareType==="FIXED_AMOUNT"?String(a(g.two)):String(_(g.two)),g.three=g.shareType==="FIXED_AMOUNT"?String(a(g.three)):String(_(g.three)),h.value=g)):T.ElMessage.error("获取分销配置失败")}const y=e.computed(()=>x=>{var g,U;return console.log((g=window==null?void 0:window.permissionList)==null?void 0:g.includes(x),"是否包含"),(U=window==null?void 0:window.permissionList)==null?void 0:U.includes(x)});return(x,g)=>{const U=e.resolveComponent("el-button"),Y=e.resolveComponent("el-table-column"),Z=e.resolveComponent("el-table"),A=e.resolveComponent("el-input"),ee=e.resolveComponent("el-form-item"),B=e.resolveComponent("el-option"),Q=e.resolveComponent("el-select"),se=e.resolveComponent("el-form"),ne=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",Uo,[e.createElementVNode("div",zo,[e.createElementVNode("div",Lo,[y.value("distribution:invitation:code:add")?(e.openBlock(),e.createBlock(U,{key:0,type:"primary",round:"",style:{width:"100px",height:"36px"},onClick:ae},{default:e.withCtx(()=>[e.createTextVNode("新增邀请码")]),_:1})):e.createCommentVNode("",!0)])]),e.createVNode(Z,{data:b.list,"header-cell-style":{color:"#909399",fontSize:"12px",background:"#F6F8FA"},class:"table-height-fit"},{default:e.withCtx(()=>[e.createVNode(Y,{label:"邀请码",width:"220",align:"center",prop:"code"}),e.createVNode(Y,{label:"状态",width:"100",align:"center",prop:"isUse"},{default:e.withCtx(c=>[e.createTextVNode(e.toDisplayString(new Date(c.row.useEndDate).getTime()<new Date().getTime()?"已过期":c.row.isUse==!0?"已使用":"待使用"),1)]),_:1}),e.createVNode(Y,{label:"所属店铺",width:"100",align:"center",prop:"shopId"},{default:e.withCtx(c=>[e.createTextVNode(e.toDisplayString(n.platform=="shop"?I.value.name:e.unref(R)(c.row.shopId)),1)]),_:1}),e.createVNode(Y,{label:"创建时间",width:"240",align:"center",prop:"createTime"}),e.createVNode(Y,{label:"过期时间",width:"240",align:"center",prop:"useEndDate"})]),_:1},8,["data"]),e.createVNode(J,{"page-size":b.size,"page-num":b.current,total:b.total,onHandleCurrentChange:w,onHandleSizeChange:j},null,8,["page-size","page-num","total"]),e.createVNode(ne,{modelValue:C.value,"onUpdate:modelValue":g[3]||(g[3]=c=>C.value=c),title:V.value,width:"30%",onClose:M},{footer:e.withCtx(()=>[e.createVNode(U,{onClick:g[2]||(g[2]=c=>C.value=!1)},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(U,{type:"primary",onClick:z},{default:e.withCtx(()=>[e.createTextVNode("确定")]),_:1})]),default:e.withCtx(()=>[e.createVNode(se,{ref_key:"formRef",ref:D,model:O,rules:K},{default:e.withCtx(()=>[e.createVNode(ee,{label:"数量",prop:"quantity"},{default:e.withCtx(()=>[e.createVNode(A,{modelValue:O.quantity,"onUpdate:modelValue":g[0]||(g[0]=c=>O.quantity=c),style:{width:"70%"},min:1,type:"number"},null,8,["modelValue"])]),_:1}),n.platform!="shop"?(e.openBlock(),e.createBlock(ee,{key:0,label:"店铺",prop:"shopId"},{default:e.withCtx(()=>[e.createVNode(Q,{modelValue:O.shopId,"onUpdate:modelValue":g[1]||(g[1]=c=>O.shopId=c),style:{width:"70%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value,c=>(e.openBlock(),e.createBlock(B,{key:c.id+"$shop",label:c.name,value:c.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}}),ll="",$o=Object.freeze(Object.defineProperty({__proto__:null,default:oe(Ao,[["__scopeId","data-v-6623ef4c"]])},Symbol.toStringTag,{value:"Module"}));return Ae});
