package com.medusa.gruul.global.model.express;

import com.medusa.gruul.global.model.constant.RegexPools;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2022/8/9
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class UserAddressDTO implements Serializable {
    /**
     * 姓名
     */
    @NotBlank
    private String name;
    /**
     * 联系方式
     */
    @NotBlank
    @Pattern(regexp = RegexPools.MOBILE_TEL)
    private String mobile;
    /**
     * 详细地址
     */
    @NotBlank
    private String address;

}
