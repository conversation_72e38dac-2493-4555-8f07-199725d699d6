package com.medusa.gruul.global.model.helper;

import cn.hutool.core.lang.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额计算辅助类
 *
 * <AUTHOR>
 * date 2022/11/9
 */
public interface AmountCalculateHelper {

    long BASE_MULTIPLE = 10000L;

    BigDecimal BASE_MULTIPLE_DECIMAL = BigDecimal.valueOf(BASE_MULTIPLE);

    int BASE_SCALE = 2;

    int MILLI_SCALE = -2;
    int DEFAULT_SCALE = 10;

    /**
     * 元 转为 毫
     *
     * @param yuanPrice 单位元的价格
     * @return 单位为毫的价格
     */
    static Long toMilli(BigDecimal yuanPrice) {
        Assert.notNull(yuanPrice);
        return yuanPrice.setScale(BASE_SCALE, RoundingMode.UNNECESSARY)
                .multiply(BASE_MULTIPLE_DECIMAL)
                .setScale(MILLI_SCALE, RoundingMode.HALF_UP)
                .longValue();
    }

    /**
     * 将分转换为元
     *
     * @param fen 分值
     * @return 元值，类型为BigDecimal
     */
    static BigDecimal fenToYuan(long fen) {
        // 创建一个BigDecimal表示100
        BigDecimal hundred = BigDecimal.valueOf(100);
        // 创建一个BigDecimal表示输入的fen
        BigDecimal fenValue = BigDecimal.valueOf(fen);
        // 执行除法运算，得到元值
        BigDecimal yuanValue = fenValue.divide(hundred, 2, BigDecimal.ROUND_DOWN);
        // 返回结果
        return yuanValue;
    }

    /**
     * 将分转换为毫
     *
     * @param fen 分值
     * @return 元值，类型为BigDecimal
     */
    static Long fenToMilli(long fen) {
        return fen * 100;
    }


    /**
     * 格式化金额/毫
     *
     * @param milli 金额
     * @param mode  模式
     * @return 格式化后的结果
     */
    static Long formatMilli(Long milli, RoundingMode mode) {
        Assert.notNull(milli);
        Assert.notNull(mode);
        return BigDecimal.valueOf(milli)
                .setScale(MILLI_SCALE, mode)
                .longValue();
    }

    /**
     * 毫 转为 元
     *
     * @param milliPrice 单位毫的价格
     * @return 单位为元的价格
     */
    static BigDecimal toYuan(Long milliPrice) {
        Assert.notNull(milliPrice);
        return BigDecimal.valueOf(milliPrice).divide(BASE_MULTIPLE_DECIMAL, BASE_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 将毫转换为分
     *
     * @param milliPrice 单位毫的价格
     * @return 分
     */
    static Long toFen(Long milliPrice) {
        return milliPrice / 100;
    }

    /**
     * 获取实际成交价 = 销售价 - 销售价*(折扣/总价)
     *
     * @param salePrice      销售价
     * @param discountAmount 折扣价
     * @param totalAmount    折扣钱的总价
     * @return 实际成交价
     */
    static Long getDealAmount(Long salePrice, Long discountAmount, Long totalAmount) {
        Assert.notNull(salePrice);
        return salePrice - getDiscountAmount(salePrice, discountAmount, totalAmount);

    }

    /**
     * 计算实际成交价 = 销售价 - 所有优惠项的优惠价
     *
     * @param salePrice       销售价
     * @param discountAmounts 所有优惠价
     * @return 实际成交价
     */
    static Long getDealAmount(Long salePrice, Long... discountAmounts) {
        Assert.notNull(salePrice);
        long result = salePrice;
        for (Long current : discountAmounts) {
            Assert.notNull(current);
            result = result - ((current == null || current < 0) ? 0 : current);
            if (result <= 0) {
                return 0L;
            }
        }
        return result;
    }

    /**
     * 获取实际优惠价 = 销售价 *（总折扣/总价）
     *
     * @param salePrice      销售价
     * @param discountAmount 折扣价
     * @param totalAmount    折扣钱的总价
     * @return 实际优惠价
     */
    static Long getDiscountAmount(Long salePrice, Long discountAmount, Long totalAmount) {
        return AmountCalculateHelper.getDiscountAmount(salePrice, AmountCalculateHelper.getDiscountRate(discountAmount, totalAmount));
    }

    /**
     * 获取实际优惠价 = 成交价 * 优惠比率
     *
     * @param totalAmount  商品总额
     * @param discountRate 折扣比率
     * @return 实际优惠价
     */
    static Long getDiscountAmount(Long totalAmount, BigDecimal discountRate) {
        Assert.notNull(totalAmount);
        Assert.notNull(discountRate);
        return BigDecimal.valueOf(totalAmount)
                .multiply(discountRate)
                .setScale(MILLI_SCALE, RoundingMode.DOWN)
                .longValue();
    }

    /**
     * 根据 总价 与打折额度 情况 获取折扣价  = 总价 x((10-折扣额度)/10)
     *
     * @param totalAmount 总价
     * @param discount    折扣额度 比如 9。5折
     * @return 折扣价
     */
    static Long getDiscountAmountByDiscount(Long totalAmount, BigDecimal discount) {
        Assert.notNull(totalAmount);
        Assert.notNull(discount);
        BigDecimal totalAmountDecimal = BigDecimal.valueOf(totalAmount);
        return totalAmountDecimal.multiply(
                        (BigDecimal.TEN.subtract(discount))
                                .divide(BigDecimal.TEN, AmountCalculateHelper.DEFAULT_SCALE, RoundingMode.DOWN)
                ).setScale(AmountCalculateHelper.MILLI_SCALE, RoundingMode.DOWN)
                .longValue();
    }

    /**
     * 根据 总价 与 现金券额度 获取折扣价 = 总价>= 现金额度？现金额度：总价
     *
     * @param totalAmount 总价
     * @param amount      现金额度
     * @return 折扣价
     */
    static Long getDiscountAmountByAmount(Long totalAmount, Long amount) {
        Assert.notNull(totalAmount);
        Assert.notNull(amount);
        return totalAmount >= amount ? amount : totalAmount;
    }

    /**
     * 获取优惠比率 = 折扣价/总价
     *
     * @param discountAmount 折扣价
     * @param totalAmount    总价
     * @return 优惠比率
     */
    static BigDecimal getDiscountRate(Long discountAmount, Long totalAmount) {
        Assert.notNull(discountAmount);
        Assert.notNull(totalAmount);
        if (discountAmount >= totalAmount) {
            return BigDecimal.ONE;
        }
        return BigDecimal.valueOf(discountAmount).divide(BigDecimal.valueOf(totalAmount), DEFAULT_SCALE, RoundingMode.DOWN);
    }


    /**
     * 根据商品总价与商品数量 计算平均价
     *
     * @param totalAmount 总价
     * @param num         商品数量
     * @param mode        舍入模式
     * @return 平均价
     */
    static Long avgAmount(Long totalAmount, Integer num, RoundingMode mode) {
        return BigDecimal.valueOf(totalAmount)
                .divide(BigDecimal.valueOf(num), -2, mode)
                .longValue();
    }

}
