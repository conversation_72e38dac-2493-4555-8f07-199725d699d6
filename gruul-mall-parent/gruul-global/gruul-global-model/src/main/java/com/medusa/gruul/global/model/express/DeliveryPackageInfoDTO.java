package com.medusa.gruul.global.model.express;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class DeliveryPackageInfoDTO implements Serializable {
    /**
     * 类型
     */
    @NotBlank
    private String type;
    /**
     * 是否供应商api发货
     */
    private Boolean isSupplierApi = Boolean.FALSE;
    /**
     * 供应商识别号
     */
    @NotBlank
    private String supplierSn;
    /**
     * 供应商id
     */
    @NotNull
    private Long supplierId;
    /**
     * 供应商产品id
     */
    private Long supplierProductId;

}
