package com.medusa.gruul.global.model.constant;

import cn.hutool.core.lang.RegexPool;

/**
 * <AUTHOR>
 * date 2022/4/15
 */
public interface RegexPools {

    /**
     * 任意字符
     */
    String ANY = "[\\s\\S]*";

    /**
     * 非空字符
     */
    String NOT_BLANK = ANY + "\\S" + ANY;


    /**
     * 日期 -
     */
    String DATE = "((\\d{3}[1-9]|\\d{2}[1-9]\\d{1}|\\d{1}[1-9]\\d{2}|[1-9]\\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\\d|30))|(02-(0[1-9]|[1]\\d|2[0-8]))))|(((\\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)((\\d{3}[1-9]|\\d{2}[1-9]\\d{1}|\\d{1}[1-9]\\d{2}|[1-9]\\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\\d|30))|(02-(0[1-9]|[1]\\d|2[0-8]))))|(((\\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)";

    /**
     * 时间：
     */
    String TIME = RegexPool.TIME;

    /**
     * 日期时间
     */
    String DATETIME = "(" + DATE + ")" + "\\s" + "(" + TIME + ")";

    /**
     * 用户昵称 只允许中英文与数字
     */
    String NICKNAME = "^[a-z0-9A-Z\u4e00-\u9fa5]+$";

    /**
     * 跟密码相同
     */
    String USERNAME = RegexPools.PASSWORD;

    /**
     * 密码包含数字、小写字母、大写字母 至少两种 6-20位
     */
    String PASSWORD = "^(?![A-Z]+$)(?![a-z]+$)(?!\\d+$)(?![\\W_]+$)\\S{6,20}$";

    /**
     * 手机号|座机号正则
     */
    String MOBILE_TEL = "((?:0|86|\\+86)?1[3-9]\\d{9})|((010|02\\d|0[3-9]\\d{2})-?(\\d{6,8}))";
}
