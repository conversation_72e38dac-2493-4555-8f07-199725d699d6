package com.medusa.gruul.global.model.o;

import com.medusa.gruul.global.model.enums.InputType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * "key":"asdass","type":"phone","required":true,"placeholder":"请输入asdasdasd"
 * 店铺交易表单设置
 *
 * <AUTHOR>
 * date 2022/10/26
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class FormInput implements Serializable {

    /**
     * 表单名称
     */
    @NotBlank
    @Size(max = 8)
    private String key;

    /**
     * 表单类型
     * 电话，身份证 文本 数字 图片 日期 时间 日期时间
     */
    @NotNull
    private InputType type;

    /**
     * 是否必须
     */
    @NotNull
    private Boolean required;

    /**
     * 占位符
     */
    private String placeholder;

}
