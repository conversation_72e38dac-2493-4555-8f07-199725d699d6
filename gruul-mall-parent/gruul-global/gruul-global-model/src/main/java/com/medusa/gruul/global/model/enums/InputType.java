package com.medusa.gruul.global.model.enums;

import cn.hutool.core.lang.RegexPool;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.medusa.gruul.global.model.constant.RegexPools;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 店铺表达设置 输入框 （电话，身份证 文本 数字 图片 日期 时间 日期时间）
 *
 * <AUTHOR>
 * date 2022/10/26
 */
@Getter
@RequiredArgsConstructor
public enum InputType {

    /**
     * 手机号
     */
    MOBILE(1, RegexPool.MOBILE),

    /**
     * 身份证
     */
    CITIZEN_ID(2, RegexPool.CITIZEN_ID),

    /**
     * 文本
     */
    TEXT(3, RegexPools.NOT_BLANK),

    /**
     * 数字
     */
    NUMBER(4, RegexPool.NUMBERS),

    /**
     * 图片
     */
    IMAGE(5, RegexPool.URL_HTTP),

    /**
     * 日期
     */
    DATE(6, RegexPools.DATE),

    /**
     * 时间
     */
    TIME(7, RegexPools.TIME),

    /**
     * 日期时间
     */
    DATETIME(8, RegexPools.DATETIME);


    @EnumValue
    private final Integer value;
    /**
     * 正则表达式
     */
    private final String regex;

}
