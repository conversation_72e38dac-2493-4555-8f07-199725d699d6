package com.medusa.gruul.global.model.constant;

/**
 * 服务名称常量
 *
 * <AUTHOR>
 * date 2022/9/15
 */
public interface Services {

    /**
     * 账号与认证服务
     */
    String GRUUL_MALL_UAA = "gruul-mall-uaa";

    /**
     * 用户服务
     */
    String GRUUL_MALL_USER = "gruul-mall-user";

    /**
     * 店铺商户服务
     */
    String GRUUL_MALL_SHOP = "gruul-mall-shop";

    /**
     * 商品服务
     */
    String GRUUL_MALL_GOODS = "gruul-mall-goods";

    /**
     * 库存服务
     */
    String GRUUL_MALL_STORAGE = "gruul-mall-storage";

    /**
     * 购物车服务
     */
    String GRUUL_MALL_CART = "gruul-mall-cart";

    /**
     * 订单服务
     */
    String GRUUL_MALL_ORDER = "gruul-mall-order";

    /**
     * 支付服务
     */
    String GRUUL_MALL_PAYMENT = "gruul-mall-payment";


    /**
     * 售后服务
     */
    String GRUUL_MALL_AFS = "gruul-mall-afs";

    /**
     * 物流服务
     */
    String GRUUL_MALL_FREIGHT = "gruul-mall-freight";

    /**
     * 检索服务
     */
    String GRUUL_MALL_SEARCH = "gruul-mall-search";

    /**
     * 概况服务
     */
    String GRUUL_MALL_OVERVIEW = "gruul-mall-overview";

    /**
     * 信鸽服务
     */
    String GRUUL_MALL_CARRIER_PIGEON = "gruul-mall-carrier-pigeon";
}
