package com.medusa.gruul.global.model.helper;

import com.medusa.gruul.global.model.constant.GlobalCode;
import com.medusa.gruul.global.model.exception.GlobalException;

/**
 * 可以控制是否为空
 *
 * <AUTHOR>
 * date 2021/12/7
 * @deprecated 推荐使用 Option
 */

public class Sc<T> {

	private final T data;

	private Sc(T data) {
		this.data = data;
	}

	public static <T> Sc<T> of(T data) {
		return new Sc<>(data);
	}

	/**
	 * 可以为空值
	 */
	public T can() {
		return data;
	}

	/**
	 * 必须有值
	 */
	public T must() {
		return must(
				new GlobalException(GlobalCode.REQUEST_INVALID, "bad request")
		);
	}

	/**
	 * 必须有值
	 */
	public T must(RuntimeException exception) {
		if (data == null) {
			throw exception;
		}
		if (data instanceof String && ((String) data).isEmpty()) {
			throw exception;
		}
		return data;
	}

}
