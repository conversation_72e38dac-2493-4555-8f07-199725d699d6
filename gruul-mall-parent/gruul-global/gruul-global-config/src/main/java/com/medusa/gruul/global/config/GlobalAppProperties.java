package com.medusa.gruul.global.config;

import com.medusa.gruul.global.model.enums.Mode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.env.Environment;


/**
 * <AUTHOR>
 * date 2022/3/23
 */
@Getter
@Setter
@RequiredArgsConstructor
@ConfigurationProperties(prefix = "gruul")
public class GlobalAppProperties implements InitializingBean {

    private final Environment environment;

    /**
     * 应用服务名
     */
    private String name = "${spring.application.name}";
    /**
     * 应用程序版本号
     */
    private String version = "1.0";

    /**
     * 是否是单体应用
     */
    private boolean single = false;

    /**
     * 业务运行模式 默认B2B2C
     */
    private Mode mode = Mode.B2B2C;
    

    @Override
    public void afterPropertiesSet() throws Exception {
        this.name = environment.resolvePlaceholders(this.name);
    }
}

