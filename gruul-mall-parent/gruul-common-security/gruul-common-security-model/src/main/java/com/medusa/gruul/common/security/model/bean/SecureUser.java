package com.medusa.gruul.common.security.model.bean;

import com.medusa.gruul.common.security.model.enums.Roles;
import com.medusa.gruul.common.security.model.enums.UserStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * 安全用户 匿名用户 id为空
 *
 * <AUTHOR>
 * date 2022/2/26
 */
@Getter
@Setter
@Accessors(chain = true)
public class SecureUser implements Serializable {
	/**
	 * 平台id  预留字段
	 */
	private Long platformId;

	/**
	 * shopId
	 */
	private Long shopId;
	/**
	 * 用户所属店铺id
	 */
	private Long belongShopId;

	/**
	 * 分销的店铺id
	 */
	private Long distributorShopId;

	/**
	 * 用户id 匿名用户id为空
	 */
	private Long id;

	/**
	 * 用户唯一代码
	 */
	private String idCode;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 密码
	 */
	private String mobile;

	/**
	 * 邮件
	 */
	private String email;

	/**
	 * 微信 open id
	 */
	private String openid;

	/**
	 * 用户状态
	 */
	private UserStatus status;

	/**
	 * 用户角色集合
	 */
	private Set<Roles> roles = Set.of();

	/**
	 * 副角色
	 */
	private Set<Roles> subRoles = Set.of();

	/**
	 * 用户权限集合
	 */
	private Set<String> perms = Set.of();


	/**
	 * 是否是匿名用户
	 *
	 * @return true 是 false 不是
	 */
	public boolean isAnonymous() {
		return getId() == null;
	}

	/**
	 * 是否是黑名单用户
	 */
	public boolean isBlack() {
		return getSubRoles().contains(Roles.BLACK_LIST);
	}
}
