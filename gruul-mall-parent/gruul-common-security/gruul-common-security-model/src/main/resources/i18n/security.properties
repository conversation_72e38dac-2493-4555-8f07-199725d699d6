auth.access.denied=没有访问权限
auth.account.expired=该账号已失效
auth.account.invalid=账号不可用
auth.client.id.invalid=认证客户端不可用
auth.client.password.invalid=客户端用户名密码错误
auth.credentials.expired=认证凭据已过期
auth.grant.invalid=不支持的授权类型
auth.need.login=需要登录后才能访问
auth.redirect.uri.invalid=重定向地址错误
auth.refresh.token.expired=凭据已过期
auth.refresh.token.invalid=凭据已失效
auth.request.invalid=无效的请求
auth.response.type.invalid=不支持的响应类型
auth.scope.invalid=没有该资源的访问权限
auth.server.error=认证服务发生异常
auth.token.expired=凭证已失效
auth.token.invalid=无效的访问凭证
auth.user.denied.authorization=用户拒绝授权
auth.username.password.invalid=用户名或密码错误
