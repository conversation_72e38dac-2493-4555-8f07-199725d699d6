<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-common-module</artifactId>
        <groupId>com.medusa.gruul</groupId>
        <version>2022.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-common-module-single</artifactId>
    <description>单体架构依赖</description>

    <dependencies>
        <!-- 插件支持 -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-addon-supporter</artifactId>
        </dependency>
        <!-- 插件提供 -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-addon-provider</artifactId>
        </dependency>
        <!-- 安全认证 资源服务-->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-security-resource</artifactId>
        </dependency>
        <!-- log -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-log</artifactId>
        </dependency>
        <!-- web mvc -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-web</artifactId>
        </dependency>
        <!-- datasource  -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <!-- mybatis plus -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-mybatis-plus</artifactId>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- aop -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
    </dependencies>

</project>