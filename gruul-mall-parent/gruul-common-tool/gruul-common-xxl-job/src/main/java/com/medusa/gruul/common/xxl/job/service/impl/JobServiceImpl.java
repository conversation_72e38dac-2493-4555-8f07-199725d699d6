package com.medusa.gruul.common.xxl.job.service.impl;

import com.medusa.gruul.common.xxl.job.XxlJobProperties;
import com.medusa.gruul.common.xxl.job.model.XxlJobInfo;
import com.medusa.gruul.common.xxl.job.service.JobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.util.XxlJobRemotingUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2023/3/17
 */
public class JobServiceImpl implements JobService {

    private final String adminAddress;
    private final String accessToken;
    private final int groupId;


    public JobServiceImpl(XxlJobProperties xxlJobProperties) {
        this.adminAddress = xxlJobProperties.getAdmin().getAdminAddresses();
        this.accessToken = xxlJobProperties.getExecutor().getAccessToken();
        this.groupId = xxlJobProperties.getExecutor().getId();
    }


    @Override
    public String add(XxlJobInfo jobParam) {
        if (jobParam.getJobGroup() == 0) {
            jobParam.setJobGroup(groupId);
        }
        ReturnT<String> result = XxlJobRemotingUtil.postBody(adminAddress + ADD_TASK_URL, accessToken, 6000, jobParam, String.class);
        if (result.getCode() != ReturnT.SUCCESS_CODE) {
            throw new RuntimeException(result.getMsg());
        }
        return result.getContent();
    }

    @Override
    public void update(XxlJobInfo jobParam) {
        if (jobParam.getJobGroup() == 0) {
            jobParam.setJobGroup(groupId);
        }
        ReturnT<String> result = XxlJobRemotingUtil.postBody(adminAddress + UPDATE_TASK_URL, accessToken, 6000, jobParam, String.class);
        if (result.getCode() != ReturnT.SUCCESS_CODE) {
            throw new RuntimeException(result.getMsg());
        }
    }

    @Override
    public void remove(int jobId) {
        ReturnT<String> result = XxlJobRemotingUtil.postBody(adminAddress + REMOVE_TASK_URL, accessToken, 6000, Map.of("id", jobId), String.class);
        if (result.getCode() != ReturnT.SUCCESS_CODE) {
            throw new RuntimeException(result.getMsg());
        }

    }

    @Override
    public void start(int jobId) {
        ReturnT<String> result = XxlJobRemotingUtil.postBody(adminAddress + START_TASK_URL, accessToken, 6000, Map.of("id", jobId), String.class);
        if (result.getCode() != ReturnT.SUCCESS_CODE) {
            throw new RuntimeException(result.getMsg());
        }

    }

    @Override
    public void stop(int jobId) {
        ReturnT<String> result = XxlJobRemotingUtil.postBody(adminAddress + STOP_TASK_URL, accessToken, 6000, Map.of("id", jobId), String.class);
        if (result.getCode() != ReturnT.SUCCESS_CODE) {
            throw new RuntimeException(result.getMsg());
        }
    }
}
