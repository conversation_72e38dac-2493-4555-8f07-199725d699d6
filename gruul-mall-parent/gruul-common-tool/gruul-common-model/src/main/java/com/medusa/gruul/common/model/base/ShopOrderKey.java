package com.medusa.gruul.common.model.base;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2023/6/9
 */
@Getter
@Setter
@EqualsAndHashCode
@Accessors(chain = true)
public class ShopOrderKey implements Serializable {
	/**
	 * 订单号
	 */
	private String orderNo;
	/**
	 * 店铺id
	 */
	private Long shopId;
}
