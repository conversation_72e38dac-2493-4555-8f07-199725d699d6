package com.medusa.gruul.common.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 销售类型
 *
 * <AUTHOR>
 * @Description SellType.java
 * @date 2023-07-17 10:10
 */
@Getter
@RequiredArgsConstructor
public enum SellType {
    /**
     * 采购
     */
    PURCHASE(0),

    /**
     * 代销
     */
    CONSIGNMENT(1),

    /**
     * 自有商品
     */
    OWN(2),
    ;
    @EnumValue
    private final Integer value;
}
