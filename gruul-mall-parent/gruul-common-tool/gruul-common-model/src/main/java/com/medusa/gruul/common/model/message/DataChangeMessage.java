package com.medusa.gruul.common.model.message;

import com.medusa.gruul.common.model.enums.ChangeType;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.global.model.o.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serial;

/**
 * <AUTHOR>
 * @Description 数据变化消息
 * @date 2022-10-08 15:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataChangeMessage implements BaseDTO {


	@Serial
	private static final long serialVersionUID = -5131834418843317810L;
	/**
	 * 用户id
	 */
	private Long userId;

	/**
	 * 变化值
	 */
	@NotNull
	private Long value;

	/**
	 * 变化类型
	 */
	@NotNull
	private ChangeType changeType;

	/**
	 * 扩展字段
	 */
	private String extendInfo;

	@Override
	public void validParam() {
		if (getUserId() == null) {
			throw new ServiceException(SystemCode.PARAM_VALID_ERROR);
		}
	}
}
