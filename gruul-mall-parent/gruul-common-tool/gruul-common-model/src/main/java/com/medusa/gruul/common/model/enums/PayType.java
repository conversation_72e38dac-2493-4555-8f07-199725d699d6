package com.medusa.gruul.common.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 支付渠道枚举
 *
 * <AUTHOR>
 * @ xdescription 支付渠道枚举
 * @date 2022-07-11 10:49
 */
@Getter
@RequiredArgsConstructor
public enum PayType {

    /**
     * aliPay 支付宝， wxPay微信..等等
     */
    BALANCE("balance"),
    /**
     * 微信支付
     */
    WECHAT("wxPay"),
    /**
     * 支付宝
     */
    ALIPAY("aliPay"),
    /**
     * 汇付聚合正扫h5
     */
    HUIFU_JSPAY("huifuJspay"),

    ;

    /**
     * 支付类型(支付渠道) 详情查看com.egzosn.pay.spring.boot.core.merchant.PaymentPlatform对应子类，
     */
    @EnumValue
    private final String platform;
}
