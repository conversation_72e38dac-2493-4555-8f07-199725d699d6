package com.medusa.gruul.common.model.exception;

import com.medusa.gruul.global.model.exception.Error;
import com.medusa.gruul.global.model.exception.GlobalException;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * description: ServiceException 未来将移除
 *
 * <AUTHOR>
 * @deprecated 请使用GlobalException {@link GlobalException}
 * Date: 2019/7/13 19:22
 */
@Getter
@Setter
@Deprecated
@NoArgsConstructor
public class ServiceException extends GlobalException {

	public ServiceException(String message, int code) {
		super(code, message);
	}

	public ServiceException(String message) {
		super(message);
	}

	public ServiceException(Error code) {
		super(code.code(), code.msg());
		super.setData(code.data());
	}


	public ServiceException(String message, Throwable cause) {
		super(message, cause);
	}

	public ServiceException(String message, int code, Object data) {
		super(code, message);
		super.setData(data);
	}

}
