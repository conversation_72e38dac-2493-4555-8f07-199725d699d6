package com.medusa.gruul.common.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * date 2022/4/21
 */
@Getter
@RequiredArgsConstructor
public enum DistributionMode {
    /**
     * 商家配送
     */
    MERCHANT(0, func -> Map.of()),

    /**
     * 快递配送
     */
    EXPRESS(1, new Function<>() {
        @Override
        public Map<String, BigDecimal> apply(Function<DistributionMode, Map<String, BigDecimal>> distributionModeLongFunction) {
            return distributionModeLongFunction.apply(EXPRESS);
        }
    }),

    /**
     * 同城配送
     */
    INTRA_CITY_DISTRIBUTION(2, new Function<>() {
        @Override
        public Map<String, BigDecimal> apply(Function<DistributionMode, Map<String, BigDecimal>> distributionModeLongFunction) {
            return distributionModeLongFunction.apply(INTRA_CITY_DISTRIBUTION);
        }
    }),
    /**
     * 店铺门店
     */
    SHOP_STORE(3, func -> Map.of()),


    /**
     * 虚拟配送
     */
    VIRTUAL(4, func -> Map.of());
    @EnumValue
    private final Integer value;

    /**
     * 计算运费的函数
     */
    private final Function<Function<DistributionMode, Map<String, BigDecimal>>, Map<String, BigDecimal>> function;

    public String getName() {
        return name();
    }
}
