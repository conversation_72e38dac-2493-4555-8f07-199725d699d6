package com.medusa.gruul.common.model.base;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2022/10/21
 */
@Getter
@Setter
@EqualsAndHashCode
@Accessors(chain = true)
public class ShopProductSkuKey implements Serializable {
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * sku id
     */
    private Long skuId;
}
