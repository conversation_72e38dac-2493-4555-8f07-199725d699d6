package com.medusa.gruul.common.mp.handler.type;

import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.mp.IFastJson2TypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.Set;

/**
 * string[] type to Set<Long>
 *
 * <AUTHOR>
 */
@MappedTypes({Set.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class LongSetTypeHandler extends IFastJson2TypeHandler {

	@Override
	public TypeReference<?> getTypeReference() {
		return new TypeReference<Set<Long>>() {
		};
	}
}
