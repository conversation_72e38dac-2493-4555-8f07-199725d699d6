package com.medusa.gruul.common.mp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.medusa.gruul.common.fastjson2.FastJson2;

/**
 * 自定义抽象 type handler 处理器
 *
 * <AUTHOR>
 */
public abstract class IFastJson2TypeHandler extends AbstractJsonTypeHandler<Object> {


	/**
	 * 获取泛型的 TypeReference
	 *
	 * @return TypeReference
	 */
	protected abstract TypeReference<?> getTypeReference();

	@Override
	protected Object parse(String json) {
		return JSON.parseObject(json, getTypeReference(), FastJson2.readFeature());
	}

	@Override
	protected String toJson(Object obj) {
		return JSON.toJSONString(obj, FastJson2.writeFeature());
	}
}
