package com.medusa.gruul.common.mp.handler.type;

import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.mp.IFastJson2TypeHandler;
import com.medusa.gruul.global.model.enums.ServiceBarrier;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * string[] type to List<ServiceBarrier>
 *
 * <AUTHOR>
 */
@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class ServiceBarrierTypeHandler extends IFastJson2TypeHandler {
	@Override
	public TypeReference<?> getTypeReference() {
		return new TypeReference<List<ServiceBarrier>>() {
		};
	}
}
