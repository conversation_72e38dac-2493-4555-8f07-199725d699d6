(function(e,u){typeof exports=="object"&&typeof module<"u"?module.exports=u(require("vue"),require("element-plus"),require("@/composables/useConvert"),require("@/apis/http"),require("vue-router"),require("@/components/q-upload/q-upload.vue"),require("@element-plus/icons-vue"),require("vue-draggable-next"),require("@/components/q-editor/q-edit.vue")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/composables/useConvert","@/apis/http","vue-router","@/components/q-upload/q-upload.vue","@element-plus/icons-vue","vue-draggable-next","@/components/q-editor/q-edit.vue"],u):(e=typeof globalThis<"u"?globalThis:e||self,e.AddIntegralGoods=u(e.AddIntegralGoodsContext.Vue,e.AddIntegralGoodsContext.ElementPlus,e.AddIntegralGoodsContext.UseConvert,e.AddIntegralGoodsContext.Request,e.AddIntegralGoodsContext.VueRouter,e.AddIntegralGoodsContext.QUpload,e.AddIntegralGoodsContext.ElementPlusIconsVue,e.AddIntegralGoodsContext.VueDraggableNext,e.AddIntegralGoodsContext.QEdit))})(this,function(e,u,G,I,A,$,R,U,M){"use strict";var S=document.createElement("style");S.textContent=`.commodityForm[data-v-5ecd8c40]{box-sizing:border-box;padding-bottom:62px}.commodityForm__tool[data-v-5ecd8c40]{width:1010px;align-items:center;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;margin-left:-14px;z-index:100}.navLine[data-v-55018149]{margin:25px 0;height:40px;line-height:40px;background-color:#f8f8f8;padding-left:15px;font-weight:700}.serveMsg_hours[data-v-55018149]{display:flex;justify-content:center;align-items:center}.inputWidth[data-v-55018149]{width:550px}.serveMsg[data-v-55018149]{width:400px;display:flex}.com__attr[data-v-55018149]{margin-top:10px;width:620px;padding:20px;border:1px solid #d7d7d7}.com__attr-header[data-v-55018149]{display:flex;justify-content:space-between;align-items:center}.com__attr-content[data-v-55018149]{display:flex;justify-content:space-between;align-items:center;margin:10px 0}.com__attr-input[data-v-55018149]{width:230px}.com__attr-del[data-v-55018149]{color:red}.com__imgText[data-v-55018149]{position:absolute;right:0;bottom:0;font-size:12px;text-align:center;width:100%;background-color:#0000004d;border-radius:0 0 6px 6px;color:#fff}.avatar-uploader .el-upload{border:1px dashed var(--el-border-color);border-radius:6px;cursor:pointer;position:relative;overflow:hidden;transition:var(--el-transition-duration-fast)}.main-uploader .el-upload{border:1px dashed var(--el-border-color);border-radius:6px;cursor:pointer;position:relative;overflow:hidden;width:100px;height:100px;transition:var(--el-transition-duration-fast)}.avatar-uploader .el-upload:hover,.main-uploader .el-upload:hover{border-color:var(--el-color-primary)}.el-icon.avatar-uploader-icon{font-size:28px;color:#8c939d;width:250px;height:120px;text-align:center}.el-icon.main-uploader-icon{font-size:28px;color:#8c939d;width:100px;height:100px;text-align:center}.input_number .el-input__inner{text-align:left}.info[data-v-60353015]{display:flex}.info__edit[data-v-60353015]{border:1px solid #ccc;margin-top:18px;z-index:99}.info__edit[data-v-60353015] ::-webkit-scrollbar{display:none}
`,document.head.appendChild(S);const k="addon-integral/integral/"+"product/",j=a=>I.post({url:k+"issue",data:a}),z=a=>I.get({url:k+"info",params:{id:a}}),L=a=>I.put({url:k+"update",data:a}),D={class:"commodityForm"},W={class:"commodityForm__tool"},O=e.defineComponent({__name:"AddIntegralGoods",setup(a){const g={NewBasicInfo:{prev:"",next:"NewProductInfo",stepIndex:0},NewProductInfo:{prev:"NewBasicInfo",next:"",stepIndex:1}},s=A.useRouter(),c=A.useRoute(),{mulTenThousand:t,divTenThousand:l}=G(),f=e.ref(),h=[],y={NewBasicInfo:e.defineAsyncComponent(()=>Promise.resolve().then(()=>X)),NewProductInfo:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ne))},_=e.ref("NewBasicInfo"),b=e.computed(()=>N.value.stepIndex),r=e.ref({id:"",name:"",price:1,integralPrice:0,stock:0,virtualSalesVolume:0,detail:"",pic:"",albumPics:"",freightPrice:0,integralProductAttributes:[{attributeName:"",attributeValue:""}],salePrice:0}),o=e.ref([]),N=e.computed(()=>g[_.value]),i=e.computed(()=>N.value.prev),p=e.computed(()=>N.value.next);e.onBeforeMount(B);async function B(){if(c.query.id){const{code:d,msg:x,data:m}=await z(c.query.id);if(d!==200){u.ElMessage.error(x||"获取积分商品信息失败");return}m.salePrice=l(m.salePrice),r.value=m,le()}}const E=()=>{C(f.value),_.value=i.value},C=d=>{h[b.value]||h.push(f.value)},n=()=>h[b.value]?h[b.value]:f.value,V=async()=>{let d=!0;p.value==="NewProductInfo"&&(d=T()),d&&n().currentFormRef&&n().currentFormRef.validate(x=>{x&&(C(f.value),_.value=p.value)})};function T(){return!0}const re=async()=>{const{id:d}=r.value;r.value.salePrice=+t(r.value.salePrice);const x={...r.value},{code:m,data:F,msg:w}=await(d?L(x):j(x));if(m!==200){u.ElMessage.error(w||`${d?"更新":"提交"}失败`);return}s.push({name:"integralMall"}),o.value=[],u.ElMessage.success(`${d?"更新":"提交"}成功`)};function le(){let d=[];r.value.albumPics&&(d=r.value.albumPics.split(",")),o.value=d}const ae=()=>{s.back()};return e.provide("form",{submitForm:r,commodityImgList:o}),(d,x)=>{const m=e.resolveComponent("el-step"),F=e.resolveComponent("el-steps"),w=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",D,[e.createVNode(F,{active:b.value,simple:""},{default:e.withCtx(()=>[e.createVNode(m,{title:"1.编辑基本信息",icon:"none"}),e.createVNode(m,{title:"2.编辑商品信息",icon:"none"})]),_:1},8,["active"]),(e.openBlock(),e.createBlock(e.KeepAlive,{exclude:["NewProductInfo"]},[(e.openBlock(),e.createBlock(e.resolveDynamicComponent(y[_.value]),{ref_key:"componentRef",ref:f},null,512))],1024)),e.createElementVNode("div",W,[i.value!==""?(e.openBlock(),e.createBlock(w,{key:0,type:"primary",round:"",onClick:E},{default:e.withCtx(()=>[e.createTextVNode("上一步")]),_:1})):e.createCommentVNode("",!0),p.value!==""?(e.openBlock(),e.createBlock(w,{key:1,type:"primary",round:"",onClick:V},{default:e.withCtx(()=>[e.createTextVNode("下一步")]),_:1})):e.createCommentVNode("",!0),i.value!==""?(e.openBlock(),e.createBlock(w,{key:2,type:"primary",round:"",onClick:re},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(c).query.id?"更新":"上架"),1)]),_:1})):e.createCommentVNode("",!0),i.value!==""?(e.openBlock(),e.createBlock(w,{key:3,type:"",round:"",onClick:ae},{default:e.withCtx(()=>[e.createTextVNode(" 取消 ")]),_:1})):e.createCommentVNode("",!0)])])}}}),de="",P=(a,g)=>{const s=a.__vccOpts||a;for(const[c,t]of g)s[c]=t;return s},H=P(O,[["__scopeId","data-v-5ecd8c40"]]),q=a=>(e.pushScopeId("data-v-55018149"),a=a(),e.popScopeId(),a),K=q(()=>e.createElementVNode("div",{class:"navLine"},"基本信息",-1)),v=["src"],Q={key:1,class:"com__imgText"},Y=q(()=>e.createElementVNode("div",{style:{color:"rgba(69, 64, 60, 0.6)","font-size":"12px"}}," 尺寸建议750x750（正方形模式）像素以上，大小1M以下，最多6张 (可拖拽图片调整顺序 ) ",-1)),J=e.defineComponent({__name:"NewBasicInfo",setup(a,{expose:g}){const s=e.ref(),c=e.inject("form"),t=c.submitForm,l=c.commodityImgList,f=e.reactive({name:[{required:!0,message:"请填写商品名称",trigger:"blur"}],stock:[{required:!0,message:"请填写商品库存",trigger:"blur"}],integralPrice:[{required:!0,message:"请填写积分价",trigger:"blur"}],albumPics:[{required:!0,message:"请选择商品主图",trigger:"change"}]});e.ref([{name:"7天退换",state:!1,text:"商家承诺7天无理由退换货",enum:"SEVEN_END_BACK"},{name:"小时发货",state:!1,text:"商家承诺订单发货时间",enum:"DAY_SEND"},{name:"假一赔十",state:!1,text:"若收到商品是假冒品牌，可获得十倍赔偿",enum:"FAKE_COMPENSATE"},{name:"正品保证",state:!1,text:"商家承诺商品正品质量",enum:"ALL_ENSURE"}]),e.onMounted(()=>{_()}),e.watch(t.value,r=>{_()},{immediate:!0});const h=r=>{l.value.splice(r,1),t.value.albumPics=y(l)};function y(r){return r.value.join(",")}function _(){let r=[];t.value.albumPics&&(r=t.value.albumPics.split(",")),l.value=r}const b=(r,o)=>{l.value.push(r),t.value.albumPics=y(l)};return g({currentFormRef:s}),(r,o)=>{const N=e.resolveComponent("el-input"),i=e.resolveComponent("el-form-item"),p=e.resolveComponent("el-input-number"),B=e.resolveComponent("el-icon"),E=e.resolveComponent("el-row"),C=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock(e.Fragment,null,[K,e.createVNode(C,{ref_key:"currentFormRef",ref:s,model:e.unref(t),rules:f},{default:e.withCtx(()=>[e.createVNode(i,{label:"商品名称",prop:"name","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:e.unref(t).name,"onUpdate:modelValue":o[0]||(o[0]=n=>e.unref(t).name=n),class:"inputWidth",placeholder:"请填写商品名称",maxlength:"35"},null,8,["modelValue"])]),_:1}),e.createVNode(i,{label:"划线价",prop:"price","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(t).price,"onUpdate:modelValue":o[1]||(o[1]=n=>e.unref(t).price=n),class:"input_number inputWidth",placeholder:"请填写划线价",min:0,precision:2,controls:!1},null,8,["modelValue"])]),_:1}),e.createVNode(i,{label:"积分价",prop:"integralPrice","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{"model-value":+e.unref(t).integralPrice,class:"inputWidth input_number",max:999999,min:0,placeholder:"请填写积分价",precision:0,controls:!1,"onUpdate:modelValue":o[2]||(o[2]=n=>e.unref(t).integralPrice=n)},null,8,["model-value"])]),_:1}),e.createVNode(i,{label:"销售价",prop:"salePrice","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{"model-value":+e.unref(t).salePrice,max:999999,min:0,class:"inputWidth input_number",placeholder:"请填写销量(注水)",precision:0,controls:!1,"onUpdate:modelValue":o[3]||(o[3]=n=>e.unref(t).salePrice=n)},null,8,["model-value"])]),_:1}),e.createVNode(i,{label:"库存",prop:"stock","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(t).stock,"onUpdate:modelValue":o[4]||(o[4]=n=>e.unref(t).stock=n),class:"input_number inputWidth",max:999999,placeholder:"请填写库存",min:0,precision:0,controls:!1},null,8,["modelValue"])]),_:1}),e.createVNode(i,{label:"销量(注水)",prop:"initSalesVolume","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(t).virtualSalesVolume,"onUpdate:modelValue":o[5]||(o[5]=n=>e.unref(t).virtualSalesVolume=n),max:999999,min:0,class:"inputWidth input_number",placeholder:"请填写销量(注水)",precision:0,controls:!1},null,8,["modelValue"])]),_:1}),e.createVNode(i,{label:"运费",prop:"freightPrice","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:e.unref(t).freightPrice,"onUpdate:modelValue":o[6]||(o[6]=n=>e.unref(t).freightPrice=n),class:"inputWidth input_number",placeholder:"请填写重量（KG）",max:999999,min:0,precision:0,controls:!1},null,8,["modelValue"])]),_:1}),e.createVNode(i,{"label-width":"100px"}),e.createVNode(i,{label:"商品主图",prop:"albumPics","label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(E,{style:{width:"100%"}},{default:e.withCtx(()=>[e.unref(l).length?(e.openBlock(),e.createBlock(e.unref(U.VueDraggableNext),{key:0,modelValue:e.unref(l),"onUpdate:modelValue":o[7]||(o[7]=n=>e.isRef(l)?l.value=n:null),style:{display:"flex"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(l),(n,V)=>(e.openBlock(),e.createElementBlock("div",{key:n,style:{position:"relative","margin-right":"20px",width:"100px",height:"100px"}},[e.createElementVNode("img",{src:e.unref(l)[V],width:"100",height:"100",style:{"border-radius":"7px"}},null,8,v),n?(e.openBlock(),e.createBlock(B,{key:0,style:{position:"absolute",right:"-5px",top:"-5px",background:"#fff","border-radius":"50%"},color:"#7f7f7f",size:"20px",onClick:T=>h(V)},{default:e.withCtx(()=>[e.createVNode(e.unref(R.CircleClose))]),_:2},1032,["onClick"])):e.createCommentVNode("",!0),V===0?(e.openBlock(),e.createElementBlock("div",Q,"封面图")):e.createCommentVNode("",!0)]))),128))]),_:1},8,["modelValue"])):e.createCommentVNode("",!0),e.withDirectives(e.createVNode($,{width:100,height:100,format:{size:1},onChange:b},null,512),[[e.vShow,e.unref(l).length<=5]])]),_:1}),Y]),_:1})]),_:1},8,["model","rules"])],64)}}}),se="",ce="",X=Object.freeze(Object.defineProperty({__proto__:null,default:P(J,[["__scopeId","data-v-55018149"]])},Symbol.toStringTag,{value:"Module"})),Z={class:"info"},ee=["src"],te={class:"info__edit"},oe=e.defineComponent({__name:"NewProductInfo",setup(a){const s=e.inject("form").submitForm;return(c,t)=>(e.openBlock(),e.createElementBlock("div",Z,[e.createElementVNode("img",{class:"info__img",src:c.exampleImg},null,8,ee),e.createElementVNode("div",te,[e.createVNode(M,{content:e.unref(s).detail,"onUpdate:content":t[0]||(t[0]=l=>e.unref(s).detail=l),height:"711px"},null,8,["content"])])]))}}),me="",ne=Object.freeze(Object.defineProperty({__proto__:null,default:P(oe,[["__scopeId","data-v-60353015"]])},Symbol.toStringTag,{value:"Module"}));return H});
