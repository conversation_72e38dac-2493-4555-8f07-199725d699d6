(function(e,b){typeof exports=="object"&&typeof module<"u"?module.exports=b(require("vue"),require("@/views/order/orderDetails/components/remark-view.vue"),require("element-plus"),require("@/components/q-address/q-address.vue"),require("@/composables/useConvert"),require("vue-router"),require("@/apis/http"),require("@/apis/afs"),require("@/assets/json/data.json")):typeof define=="function"&&define.amd?define(["vue","@/views/order/orderDetails/components/remark-view.vue","element-plus","@/components/q-address/q-address.vue","@/composables/useConvert","vue-router","@/apis/http","@/apis/afs","@/assets/json/data.json"],b):(e=typeof globalThis<"u"?globalThis:e||self,e.OrderDetail=b(e.OrderDetailContext.Vue,e.OrderDetailContext.RemarkView,e.OrderDetailContext.ElementPlus,e.OrderDetailContext.QAddress,e.OrderDetailContext.UseConvert,e.OrderDetailContext.VueRouter,e.OrderDetailContext.Request,e.OrderDetailContext.ApisAfs,e.OrderDetailContext.handleGetCompanyName))})(this,function(e,b,x,V,S,C,B,R,$){"use strict";var E=document.createElement("style");E.textContent=`.orderInfo__title[data-v-ebd6df4b]{padding:20px;font-size:18px;font-weight:700;color:#515151}.orderInfo__priceInfo[data-v-ebd6df4b]{height:40px;color:#999;display:flex;justify-content:space-between;align-items:flex-end;flex-direction:column;margin:50px 0 20px}.orderInfo__shopName[data-v-ebd6df4b]{font-size:14px;color:#333;font-weight:700;margin:22px 0 12px 5px}.orderInfo__steps[data-v-ebd6df4b]{padding:20px;margin:20px;border:1px solid #d5d5d5}.orderInfo__footer[data-v-ebd6df4b]{text-align:right}.orderInfo__footer span[data-v-ebd6df4b]:nth-child(n+1){font-size:20px;font-weight:700;color:#2e99f3}.orderInfo__userInfo[data-v-ebd6df4b]{display:flex;background:#f7f8fa;margin-bottom:22px;padding:0 30px}.orderInfo__userInfo--left[data-v-ebd6df4b]{flex:.5}.orderInfo__userInfo--left div[data-v-ebd6df4b]:nth-of-type(n+2){margin-bottom:11px}.orderInfo__userInfo--right[data-v-ebd6df4b]{flex:.5}.orderInfo__userInfo--right div[data-v-ebd6df4b]:nth-of-type(n+2){margin-bottom:11px}.orderInfo__userInfo--title[data-v-ebd6df4b]{font-size:14px;color:#333;font-weight:700;margin:17px 0}.orderInfo__tab--goods[data-v-ebd6df4b]{display:flex;justify-content:space-between}.orderInfo__tab--goods-right[data-v-ebd6df4b]{font-size:12px;color:#586884;width:132px;margin-left:10px;display:flex;justify-content:space-between;align-items:flex-start;flex-direction:column}.orderInfo__tab--goods-right-show[data-v-ebd6df4b]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.orderInfo__tab--price[data-v-ebd6df4b]{display:flex;justify-content:center;align-items:center;font-size:12px;color:#50596d;flex-direction:column}.el-table td[data-v-ebd6df4b]{border-bottom:none}.tableStyle[data-v-ebd6df4b]:before{width:0}.el-table[data-v-ebd6df4b]{border-radius:9px;border:1px solid #d5d5d5}.el-table th.is-leaf[data-v-ebd6df4b]{border-bottom:none}.logisticsInfo__overflow[data-v-4612af06]{height:calc(100vh - 400px);overflow-y:auto;padding:0 10px}.logisticsInfo__title[data-v-4612af06]{font-size:14px;color:#333;font-weight:700;margin:17px 0}.logisticsInfo__text[data-v-4612af06]{margin-bottom:11px}.logisticsInfo__timeline[data-v-4612af06]{margin-top:42px}.logisticsInfo__timeline li[data-v-4612af06]:nth-child(1) .el-timeline-item__timestamp{color:#000}.logisticsInfo__timeline--status[data-v-4612af06]{font-size:13px;font-weight:700;margin-right:10px;color:#838383}.logisticsInfo__timeline--time[data-v-4612af06]{color:#838383}.logisticsInfo__divider[data-v-4612af06]{height:4px;margin:0 -15px;background:#f2f2f2}
`,document.head.appendChild(E);const P=e.defineComponent({__name:"OrderDetail",setup(n){const p=[{label:"订单信息",name:"orderInfo"},{label:"物流信息",name:"logisticsInfo"}],s={orderInfo:e.defineAsyncComponent(()=>Promise.resolve().then(()=>te)),logisticsInfo:e.defineAsyncComponent(()=>Promise.resolve().then(()=>fe))},a=e.ref("orderInfo");return(i,l)=>{const t=e.resolveComponent("el-tab-pane"),u=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(u,{modelValue:a.value,"onUpdate:modelValue":l[0]||(l[0]=f=>a.value=f)},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(p,f=>e.createVNode(t,{key:f.label,label:f.label,name:f.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(s[a.value])))])}}}),q="addon-integral/integral/"+"order/",k=n=>B.get({url:q+`get/${n}`}),A={UNPAID:"未支付",PAID:"待发货",ON_DELIVERY:"已发货",ACCOMPLISH:"已完成",SYSTEM_CLOSED:"已关闭"},D=n=>(e.pushScopeId("data-v-ebd6df4b"),n=n(),e.popScopeId(),n),j={class:"orderInfo"},z={class:"orderInfo__title"},L={class:"orderInfo__steps"},F={class:"orderInfo__userInfo"},M={class:"orderInfo__userInfo--left"},U=D(()=>e.createElementVNode("div",{class:"orderInfo__userInfo--title"},"物流信息",-1)),G={class:"orderInfo__userInfo--right"},W=D(()=>e.createElementVNode("div",{class:"orderInfo__userInfo--title"},"订单信息",-1)),H={class:"orderInfo__tab--goods"},Y={class:"orderInfo__tab--goods-right"},J={class:"orderInfo__tab--goods-right-show"},K={class:"orderInfo__tab--price"},Q={class:"orderInfo__priceInfo"},X={class:"orderInfo__footer"},Z={style:{"font-size":"16px"}},ee=e.defineComponent({__name:"IntegralOrderDetail",setup(n){const{divTenThousand:p}=S(),s=e.ref(0),i=C.useRoute().query.no,l=e.ref({}),t=e.ref({sellerRemark:"",buyerRemark:"",payTime:"",deliveryTime:"",accomplishTime:"",buyerId:"",buyerNickname:"",createTime:"",freightPrice:0,image:"",integralOrderReceiver:{address:"",areaCode:[],createTime:"",id:"",mobile:"",name:"",orderNo:"",updateTime:""},no:"",num:1,price:"",productName:"",status:"PAID",salePrice:""});u();async function u(){if(i){const{code:d,msg:m,data:c}=await k(i);if(d!==200||!c){x.ElMessage.error(m||"订单详情获取失败");return}t.value=c,f()}}function f(){var d,m,c;(d=t.value)!=null&&d.accomplishTime?s.value=2:(m=t.value)!=null&&m.deliveryTime?s.value=1:(c=t.value)!=null&&c.payTime&&(s.value=0)}const h=e.computed(()=>t.value.price&&t.value.num?t.value.price:0),o=e.computed(()=>t.value.freightPrice?p(t.value.freightPrice):0),r=e.computed(()=>t.value.salePrice&&t.value.num?t.value.salePrice:0);return(d,m)=>{var T;const c=e.resolveComponent("el-step"),N=e.resolveComponent("el-steps"),y=e.resolveComponent("el-image"),g=e.resolveComponent("el-table-column"),me=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",null,[e.createElementVNode("div",j,[e.createElementVNode("div",z,"订单状态："+e.toDisplayString(e.unref(A)[(T=t.value)==null?void 0:T.status]),1),e.createElementVNode("div",L,[e.createVNode(N,{active:s.value,"align-center":"","process-status":"finish"},{default:e.withCtx(()=>[e.createVNode(c,{title:"买家已付款",description:t.value.payTime},null,8,["description"]),e.createVNode(c,{title:"商家发货",description:t.value.deliveryTime},null,8,["description"]),e.createVNode(c,{title:"成功订单",description:t.value.accomplishTime},null,8,["description"])]),_:1},8,["active"])]),e.createElementVNode("div",F,[e.createElementVNode("div",M,[U,e.createElementVNode("div",null,"用户昵称："+e.toDisplayString(t.value.buyerNickname||"无"),1),e.createElementVNode("div",null,"买家姓名："+e.toDisplayString(t.value.integralOrderReceiver.name||"无"),1),e.createElementVNode("div",null,"买家手机号："+e.toDisplayString(t.value.integralOrderReceiver.mobile||"无"),1),e.createElementVNode("div",null,[e.createTextVNode(" 收货地址："),e.createVNode(V,{address:t.value.integralOrderReceiver.areaCode},null,8,["address"]),e.createTextVNode(e.toDisplayString(t.value.integralOrderReceiver.address),1)]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,(_,v,w)=>(e.openBlock(),e.createBlock(b,{key:w,class:"detail__item","remark-key":v,remark:l.value},null,8,["remark-key","remark"]))),128))]),e.createElementVNode("div",G,[W,e.createElementVNode("div",null,"订单编号："+e.toDisplayString(t.value.no),1),e.createElementVNode("div",null,"创建时间："+e.toDisplayString(t.value.createTime),1)])]),e.createVNode(me,{"cell-style":({row:_,column:v,rowIndex:w,columnIndex:ge})=>{if(ge===0)return{borderBottom:"none"}},data:[t.value],style:{width:"100%"},calss:"orderInfo__tab",border:""},{default:e.withCtx(()=>[e.createVNode(g,{label:"商品",width:"230",align:"center"},{default:e.withCtx(({row:_})=>[e.createElementVNode("div",H,[e.createVNode(y,{fits:"cover",style:{width:"70px",height:"70px"},shape:"square",size:"large",src:_.image,title:_.productName},null,8,["src","title"]),e.createElementVNode("div",Y,[e.createElementVNode("div",J,e.toDisplayString(_.productName),1)])])]),_:1}),e.createVNode(g,{label:"单价&数量",align:"center"},{default:e.withCtx(({row:_})=>[e.createElementVNode("div",K,[e.createElementVNode("div",null,"单价："+e.toDisplayString(_.price)+" 积分",1),e.createElementVNode("div",null,"数量： *"+e.toDisplayString(_.num),1)])]),_:1}),e.createVNode(g,{label:"总价",align:"center"},{default:e.withCtx(({row:_})=>[e.createElementVNode("div",null,"总价： "+e.toDisplayString(_.price)+" 积分 + ￥ "+e.toDisplayString(e.unref(p)(_.salePrice)),1)]),_:1})]),_:1},8,["cell-style","data"])]),e.createElementVNode("div",Q,[e.createElementVNode("span",null,"商品总价:"+e.toDisplayString(h.value)+" 积分 + ￥ "+e.toDisplayString(e.unref(p)(r.value)),1),e.createElementVNode("span",null," 运费:￥"+e.toDisplayString(o.value),1)]),e.createElementVNode("div",X,[e.createTextVNode(" 实收款："),e.createElementVNode("span",null,e.toDisplayString(h.value)+" 积分 + ￥ "+e.toDisplayString(e.unref(p)(r.value)),1),e.createElementVNode("span",Z," (含运费"+e.toDisplayString(o.value)+") ",1),e.createTextVNode("元 ")])])}}}),ue="",O=(n,p)=>{const s=n.__vccOpts||n;for(const[a,i]of p)s[a]=i;return s},te=Object.freeze(Object.defineProperty({__proto__:null,default:O(ee,[["__scopeId","data-v-ebd6df4b"]])},Symbol.toStringTag,{value:"Module"})),I=n=>(e.pushScopeId("data-v-4612af06"),n=n(),e.popScopeId(),n),oe={class:"logisticsInfo"},re={key:0},ne=I(()=>e.createElementVNode("div",{class:"logisticsInfo__title"},"物流信息",-1)),ae={class:"logisticsInfo__text"},ie={class:"logisticsInfo__text"},le={class:"logisticsInfo__text",style:{"margin-bottom":"20px"}},se={key:1,class:"logisticsInfo__text"},de=I(()=>e.createElementVNode("div",{class:"logisticsInfo__divider"},null,-1)),ce=I(()=>e.createElementVNode("div",{class:"logisticsInfo__title"},"物流详情",-1)),_e={class:"logisticsInfo__overflow"},pe=e.defineComponent({__name:"integral-order-logistics",setup(n){const s=C.useRoute().query.no,a=e.ref(!1),i=e.ref({sellerRemark:"",buyerRemark:"",payTime:"",deliveryTime:"",accomplishTime:"",buyerId:"",buyerNickname:"",createTime:"",freightPrice:0,image:"",salePrice:"",integralOrderReceiver:{address:"",areaCode:[],createTime:"",id:"",mobile:"",name:"",orderNo:"",updateTime:""},no:"",num:1,price:"",productName:"",status:"PAID"}),l=e.ref([{context:"",status:"",time:""}]);u();function t(o){return o==="WITHOUT"}async function u(){if(s){const{code:o,data:r,msg:d}=await k(s);if(o!==200||!r){x.ElMessage.error(d||"订单详情获取失败");return}if(i.value=r,t(r.integralOrderDeliverType))return;f(r)}}const f=async o=>{if(o.integralOrderDeliverType==="WITHOUT"){l.value=[{context:"无需物流",time:o.createTime,status:"无需物流"}];return}try{if(o.expressCompanyName&&o.expressNo){const{data:r,code:d,msg:m}=await R.doGetLogisticsTrajectoryByWaybillNo(o.expressCompanyName,o.expressNo);if(d!==200)return x.ElMessage.error(m||"物流轨迹获取失败");if(!r.data){h(r,o);return}l.value=r.data}else throw new Error("包裹信息获取失败")}catch{a.value=!0,l.value=[{context:"包裹异常",time:o.integralOrderReceiver.updateTime,status:"包裹异常"}]}};function h(o,r){switch(o.returnCode){case"401":a.value=!0,l.value=[{status:"包裹异常",time:r.integralOrderReceiver.updateTime,context:o.message}];break;case"400":a.value=!0,l.value=[{status:"包裹异常",time:r.integralOrderReceiver.updateTime,context:o.message}];break;default:a.value=!0,l.value=[{status:"包裹异常",time:r.integralOrderReceiver.updateTime,context:o.message}];break}}return(o,r)=>{const d=e.resolveComponent("el-row"),m=e.resolveComponent("el-timeline-item"),c=e.resolveComponent("el-timeline"),N=e.resolveComponent("el-alert");return e.openBlock(),e.createElementBlock("div",oe,[i.value.integralOrderDeliverType?(e.openBlock(),e.createElementBlock("div",re,[ne,e.createElementVNode("div",ae,[e.createTextVNode(" 收货地址："),e.createVNode(V,{address:i.value.integralOrderReceiver.areaCode},null,8,["address"]),e.createTextVNode(" "+e.toDisplayString(i.value.integralOrderReceiver.address),1)]),i.value.integralOrderDeliverType!=="WITHOUT"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createElementVNode("div",ie,"物流公司："+e.toDisplayString(e.unref($)(i.value.expressCompanyName)),1),e.createElementVNode("div",le,"物流单号："+e.toDisplayString(i.value.expressNo),1)],64)):(e.openBlock(),e.createElementBlock("div",se,"物流公司：无需物流")),de,ce,e.createElementVNode("div",_e,[e.createVNode(c,{class:"logisticsInfo__timeline"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value.reverse(),(y,g)=>(e.openBlock(),e.createBlock(m,{key:g,timestamp:`${y.context}`,style:{"padding-bottom":"42px"},color:g===0?a.value?"#F56C6C":"#409eff":" ",class:"logisticsInfo__timeline--item"},{default:e.withCtx(()=>[e.createVNode(d,null,{default:e.withCtx(()=>[e.createElementVNode("div",{style:e.normalizeStyle({color:g===0?a.value?"#F56C6C":"#409eff":" "}),class:"logisticsInfo__timeline--status"},e.toDisplayString(y.status),5),e.createElementVNode("div",{style:e.normalizeStyle({color:g===0?a.value?"#F56C6C":"#409eff":" "}),class:"logisticsInfo__timeline--time"},e.toDisplayString(y.time),5)]),_:2},1024)]),_:2},1032,["timestamp","color"]))),128))]),_:1})])])):(e.openBlock(),e.createBlock(N,{key:1,title:"暂无物流信息",type:"info",closable:!1}))])}}}),he="",fe=Object.freeze(Object.defineProperty({__proto__:null,default:O(pe,[["__scopeId","data-v-4612af06"]])},Symbol.toStringTag,{value:"Module"}));return P});
