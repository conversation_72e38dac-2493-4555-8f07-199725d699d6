(function(e,l){typeof exports=="object"&&typeof module<"u"?module.exports=l(require("vue"),require("element-plus"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/apis/http"],l):(e=typeof globalThis<"u"?globalThis:e||self,e.VipIntegralDropdown=l(e.VipIntegralDropdownContext.Vue,e.VipIntegralDropdownContext.ElementPlus,e.VipIntegralDropdownContext.Request))})(this,function(e,l,u){"use strict";const C=(a,s,o)=>u.post({url:"/gruul-mall-user/user/integral/system/change",data:{userId:a,integral:s,changeType:o}}),h=a=>u.get({url:"gruul-mall-user/user/integral/system/total",params:{userId:a}}),w=e.createElementVNode("span",null,"请输入正整数",-1),V=e.defineComponent({__name:"integral-update-dialog",props:{showDialog:{type:Boolean,default:!1},userId:{type:String,default:""}},emits:["reset"],setup(a,{emit:s}){const o=a,p=e.ref(0),n=e.ref(),t=e.reactive({num:0,changeType:"INCREASE"}),c=e.reactive({changeType:[{required:!0,message:"请选择正确的操作类型"}],num:[{required:!0,message:"请输入正确的数额"},{type:"number",message:"请输入一个正确的数字",min:1}]});e.watch(()=>o.showDialog,i=>{i&&x()},{immediate:!0});async function x(){const{code:i,data:r,msg:d}=await h(o.userId);if(i!==200)return l.ElMessage.error(d||"获取用户积分失败");p.value=r}const y=async()=>{if(!n.value||!await n.value.validate())return;const{code:r}=await C(o.userId,t.num,t.changeType);if(r!==200)return l.ElMessage.error("操作失败");l.ElMessage.success("操作成功"),p.value=0,t.num=0,t.changeType="INCREASE",s("reset")},f=()=>{t.num=0,t.changeType="INCREASE",s("reset")};return(i,r)=>{const d=e.resolveComponent("el-form-item"),_=e.resolveComponent("el-radio"),N=e.resolveComponent("el-radio-group"),E=e.resolveComponent("el-input-number"),T=e.resolveComponent("el-form"),g=e.resolveComponent("el-button"),I=e.resolveComponent("el-dialog");return e.openBlock(),e.createBlock(I,{"model-value":o.showDialog,"append-to-body":"",class:"label-view-dialog",title:"积分调整",width:"40%",onClose:f},{footer:e.withCtx(()=>[e.createVNode(g,{onClick:f},{default:e.withCtx(()=>[e.createTextVNode("取消")]),_:1}),e.createVNode(g,{type:"primary",onClick:y},{default:e.withCtx(()=>[e.createTextVNode("确认")]),_:1})]),default:e.withCtx(()=>[e.createVNode(T,{ref_key:"ruleFormRef",ref:n,model:t,rules:c,"label-width":"90px"},{default:e.withCtx(()=>[e.createVNode(d,{label:"当前数值"},{default:e.withCtx(()=>[e.createElementVNode("div",null,e.toDisplayString(p.value),1)]),_:1}),e.createVNode(d,{label:"操作",prop:"changeType"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:t.changeType,"onUpdate:modelValue":r[0]||(r[0]=m=>t.changeType=m),class:"ml-4"},{default:e.withCtx(()=>[e.createVNode(_,{label:"INCREASE",size:"large"},{default:e.withCtx(()=>[e.createTextVNode("充值")]),_:1}),e.createVNode(_,{label:"REDUCE",size:"large"},{default:e.withCtx(()=>[e.createTextVNode("扣除")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(d,{label:"调整数值",prop:"num"},{default:e.withCtx(()=>[e.createVNode(E,{modelValue:t.num,"onUpdate:modelValue":r[1]||(r[1]=m=>t.num=m),controls:!1,min:0},null,8,["modelValue"]),w]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["model-value"])}}});return e.defineComponent({__name:"VipIntegralDropdown",props:{properties:{type:Object,required:!0}},setup(a){const s=a,o=e.ref(!1);return(p,n)=>{const t=e.resolveComponent("el-dropdown-item");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(t,{onClick:n[0]||(n[0]=e.withModifiers(c=>o.value=!0,["stop"]))},{default:e.withCtx(()=>[e.createTextVNode("积分调整")]),_:1}),e.createVNode(V,{"show-dialog":o.value,"user-id":s.properties.userId,onReset:n[1]||(n[1]=c=>o.value=!1)},null,8,["show-dialog","user-id"])],64)}}})});
