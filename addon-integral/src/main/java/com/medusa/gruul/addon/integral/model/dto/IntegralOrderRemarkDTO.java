package com.medusa.gruul.addon.integral.model.dto;

import com.medusa.gruul.global.model.constant.RegexPools;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 积分订单备注DTO
 *
 * <AUTHOR>
 * @Description 积分订单备注DTO
 * @date 2023-01-31 15:39
 */
@Getter
@Setter
@ToString
public class IntegralOrderRemarkDTO {
    @NotNull
    @Size(min = 1)
    private Set<String> nos;

    /**
     * 备注
     */
    @NotNull
    @Pattern(regexp = RegexPools.NOT_BLANK)
    private String remark;
}
