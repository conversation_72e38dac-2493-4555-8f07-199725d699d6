package com.medusa.gruul.addon.integral.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.FastJson2TypeHandler;
import com.medusa.gruul.common.mp.model.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 积分订单收货表
 *
 * <AUTHOR>
 * date 2023/2/1
 * time 22:22
 **/
@Getter
@Setter
@Accessors(chain = true)
@ToString
@TableName(value = "t_integral_order_receiver", autoResultMap = true)
public class IntegralOrderReceiver extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;


    /**
     * 收货人名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 收货人电话
     */
    private String mobile;

    /**
     * 省市区代码列表
     */
    @TableField(typeHandler = FastJson2TypeHandler.class)
    private List<String> areaCode;

    /**
     * 收货人详细地址
     */
    private String address;


}