package com.medusa.gruul.addon.integral.model.dto;

import com.medusa.gruul.addon.integral.mp.entity.IntegralProduct;
import io.vavr.control.Option;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 积分商品DTO
 *
 * <AUTHOR>
 * @Description 积分商品dto
 * @date 2023-01-31 16:25
 */
@Getter
@Setter
@ToString
public class IntegralProductDTO {

    private Long id;


    /**
     * 积分商品名称
     */
    @NotNull
    @Size(max = 35)
    private String name;

    /**
     * 市场价格
     */
    private BigDecimal price;

    /**
     * 积分价
     */
    @NotNull
    private Long integralPrice;

    /**
     *混合支付金额
     */
    private Long salePrice;


    /**
     * 库存
     */
    @NotNull
    private Integer stock;

    /**
     * 虚拟销量
     */
    @NotNull
    private Integer virtualSalesVolume;

    /**
     * 商品详情
     */
    private String detail;


    /**
     * 展示主图
     */

    private String pic;

    /**
     * 画册图片
     */
    @NotBlank
    private String albumPics;

    /**
     * 运费金额
     */
    @NotNull
    private BigDecimal freightPrice;


    /**
     * 商品属性信息
     */
    private List<IntegralProductAttribute> integralProductAttributes;

    public IntegralProduct coverIntegralProduct() {
        IntegralProduct integralProduct = new IntegralProduct();
        integralProduct.setName(this.name);
        integralProduct.setIntegralPrice(this.integralPrice);
        integralProduct.setSalePrice(Option.of(this.salePrice).getOrElse(0L));
        integralProduct.setStock(this.stock);
        integralProduct.setPrice(this.price);
        integralProduct.setFreightPrice(this.freightPrice);
        integralProduct.setVirtualSalesVolume(this.virtualSalesVolume == null ? 0 : this.virtualSalesVolume);
        integralProduct.setDetail(this.detail);
        integralProduct.setPic(this.pic);
        integralProduct.setAlbumPics(this.albumPics);
        integralProduct.setIntegralProductAttributes(integralProductAttributes);
        return integralProduct;
    }


}
