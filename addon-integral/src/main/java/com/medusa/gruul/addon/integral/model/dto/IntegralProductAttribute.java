package com.medusa.gruul.addon.integral.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Size;

/**
 * 积分产品属性DTO
 *
 * <AUTHOR>
 * @Description 积分产品属性DTO
 * @date 2023-02-02 13:18
 */
@Getter
@Setter
@ToString
public class IntegralProductAttribute {

    @Size(max = 10)
    private String attributeName;

    @Size(max = 20)
    private String attributeValue;

}
