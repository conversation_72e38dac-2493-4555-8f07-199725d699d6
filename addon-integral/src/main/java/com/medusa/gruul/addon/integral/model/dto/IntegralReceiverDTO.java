package com.medusa.gruul.addon.integral.model.dto;

import com.medusa.gruul.global.model.constant.RegexPools;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 积分订单收货信息dto
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class IntegralReceiverDTO {
    /**
     * 收货人姓名
     */
    @NotBlank
    private String name;

    /**
     * 收货人电话/手机号
     */
    @NotBlank
    @Pattern(regexp = RegexPools.MOBILE_TEL)
    private String mobile;

    /**
     * 省市区代码列表
     */
    @NotNull
    @Size(min = 3, max = 3)
    private List<String> areaCode;
    /**
     * 详细地址
     */
    @NotBlank
    private String address;
}
