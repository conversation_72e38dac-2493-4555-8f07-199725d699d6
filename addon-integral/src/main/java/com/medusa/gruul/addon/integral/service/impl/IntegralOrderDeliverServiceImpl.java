package com.medusa.gruul.addon.integral.service.impl;

import com.medusa.gruul.addon.integral.model.dto.IntegralCompletionDTO;
import com.medusa.gruul.addon.integral.model.dto.IntegralOrderDeliveryDTO;
import com.medusa.gruul.addon.integral.model.enums.IntegralOrderRabbit;
import com.medusa.gruul.addon.integral.model.enums.IntegralOrderStatus;
import com.medusa.gruul.addon.integral.mp.entity.IntegralOrder;
import com.medusa.gruul.addon.integral.mp.entity.IntegralOrderPayment;
import com.medusa.gruul.addon.integral.mp.service.IIntegralOrderPaymentService;
import com.medusa.gruul.addon.integral.mp.service.IIntegralOrderService;
import com.medusa.gruul.addon.integral.service.IntegralOrderDeliverService;
import com.medusa.gruul.common.model.enums.ChangeType;
import com.medusa.gruul.common.model.enums.StatementRabbit;
import com.medusa.gruul.common.model.enums.TransactionType;
import com.medusa.gruul.common.model.message.OverviewStatementDTO;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.global.model.exception.GlobalException;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * date 2023/2/6
 * time 9:59
 **/

@Service
@RequiredArgsConstructor
public class IntegralOrderDeliverServiceImpl implements IntegralOrderDeliverService {

    private final IIntegralOrderService iIntegralOrderService;

    private final IIntegralOrderPaymentService integralOrderPaymentService;

    private final RabbitTemplate rabbitTemplate;

    @Override
    public List<IntegralOrder> undeliverBatch() {
        return iIntegralOrderService.unDeliverBatch(IntegralOrderStatus.PAID);
    }


    @Override
    public IntegralOrder undeliver(String orderNo) {
        return iIntegralOrderService.undeliver(orderNo, IntegralOrderStatus.PAID);
    }


    @Override
    public void complete(Boolean isSystem, String orderNo) {

        IntegralOrder integralOrder = this.iIntegralOrderService.lambdaQuery()
                .select(IntegralOrder::getId, IntegralOrder::getNo, IntegralOrder::getStatus, IntegralOrder::getBuyerNickname, IntegralOrder::getBuyerId, IntegralOrder::getImage)
                .eq(IntegralOrder::getNo, orderNo)
                .one();

        if (integralOrder == null) {
            throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "订单号：" + orderNo + " 的订单信息不存在");
        }

        if (!isSystem && integralOrder.getStatus() != IntegralOrderStatus.ON_DELIVERY) {
            throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "订单号：" + orderNo + " 的状态已改变");
        }

        boolean update = this.iIntegralOrderService.lambdaUpdate()
                .eq(IntegralOrder::getNo, orderNo)
                .set(IntegralOrder::getStatus, IntegralOrderStatus.ACCOMPLISH)
                .set(IntegralOrder::getAccomplishTime, LocalDateTime.now())
                .update();

        if (!update) {
            throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "订单号：" + orderNo + " 确认收获失败");
        }
        IntegralOrderPayment integralOrderPayment = integralOrderPaymentService.lambdaQuery()
                .select(IntegralOrderPayment::getSn, IntegralOrderPayment::getPayAmount)
                .eq(IntegralOrderPayment::getOrderNo, integralOrder.getNo())
                .one();
        // 生成对账单
        rabbitTemplate.convertAndSend(
                StatementRabbit.OVERVIEW_STATEMENT.exchange(),
                StatementRabbit.OVERVIEW_STATEMENT.routingKey(),
                new OverviewStatementDTO()
                        .setTransactionSerialNumber(integralOrderPayment.getSn())
                        .setUserNickname(integralOrder.getBuyerNickname())
                        .setUserAvatar(integralOrder.getImage())
                        .setUserId(integralOrder.getBuyerId())
                        .setShopId(0L)
                        .setTransactionType(TransactionType.INTEGRAL_GOODS_DEAL)
                        .setAccount(integralOrderPayment.getPayAmount())
                        .setChangeType(ChangeType.INCREASE)
                        .setTransactionTime(LocalDateTime.now())
        );


    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deliver(List<IntegralOrderDeliveryDTO> integralOrderDeliveryDTOList) {
        for (IntegralOrderDeliveryDTO dto : integralOrderDeliveryDTOList) {

            IntegralOrder one = this.iIntegralOrderService.lambdaQuery()
                    .eq(IntegralOrder::getNo, dto.getIntegralOrderNo())
                    .one();
            if (one == null) {
                throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "订单号：" + dto.getIntegralOrderNo() + " 的订单信息不存在");
            }

            if (one.getStatus() != IntegralOrderStatus.PAID) {
                throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "订单号：" + dto.getIntegralOrderNo() + " 的状态已改变");
            }
            dto.validParam();
            boolean update = this.iIntegralOrderService.lambdaUpdate()
                    .eq(IntegralOrder::getNo, dto.getIntegralOrderNo())
                    .set(IntegralOrder::getStatus, IntegralOrderStatus.ON_DELIVERY)
                    .set(IntegralOrder::getIntegralOrderDeliverType, dto.getIntegralOrderDeliverType())
                    .set(IntegralOrder::getExpressName, dto.getExpressName())
                    .set(IntegralOrder::getExpressCompanyName, dto.getExpressCompanyName())
                    .set(IntegralOrder::getExpressNo, dto.getExpressNo())
                    .set(IntegralOrder::getDeliveryTime, LocalDateTime.now())
                    .update();
            if (!update) {
                throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "订单号：" + dto.getIntegralOrderNo() + " 的订单发货失败");
            }
            rabbitTemplate.convertAndSend(
                    IntegralOrderRabbit.INTEGRAL_ORDER_SEND_COMPLETION.exchange(),
                    IntegralOrderRabbit.INTEGRAL_ORDER_SEND_COMPLETION.routingKey(),
                    new IntegralCompletionDTO()
                            .setIntegralOrderNo(dto.getIntegralOrderNo()),
                    message -> {
                        message.getMessageProperties().setHeader(MessageProperties.X_DELAY, one.getTimeout().getConfirmTimeoutMills());
                        return message;
                    }
            );
        }

    }
}
