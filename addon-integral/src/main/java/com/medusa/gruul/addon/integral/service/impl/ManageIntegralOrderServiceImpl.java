package com.medusa.gruul.addon.integral.service.impl;

import com.medusa.gruul.addon.integral.service.ManageIntegralOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 管理端积分订单数据实现层
 *
 * <AUTHOR>
 * @Description 管理端积分订单数据实现层
 * @date 2023-02-01 13:59
 */
@Service
@RequiredArgsConstructor
public class ManageIntegralOrderServiceImpl implements ManageIntegralOrderService {
}
