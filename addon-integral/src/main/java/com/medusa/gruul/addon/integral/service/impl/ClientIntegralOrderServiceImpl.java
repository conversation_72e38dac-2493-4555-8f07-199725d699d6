package com.medusa.gruul.addon.integral.service.impl;

import com.medusa.gruul.addon.integral.service.ClientIntegralOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 客户端积分订单数据实现层
 *
 * <AUTHOR>
 * @Description 客户端积分订单数据实现层
 * @date 2023-02-01 14:03
 */

@Service
@RequiredArgsConstructor
public class ClientIntegralOrderServiceImpl implements ClientIntegralOrderService {
}
