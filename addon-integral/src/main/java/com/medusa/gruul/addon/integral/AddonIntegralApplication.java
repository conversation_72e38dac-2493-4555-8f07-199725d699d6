package com.medusa.gruul.addon.integral;

import com.medusa.gruul.addon.integral.properties.IntegralOrderProperties;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR>
 * date 2022/11/2
 */
@SpringBootApplication
@EnableConfigurationProperties({IntegralOrderProperties.class})
@EnableDubbo(scanBasePackages = "com.medusa.gruul.addon.integral.addon.impl")
public class AddonIntegralApplication {
    public static void main(String[] args) {
        SpringApplication.run(AddonIntegralApplication.class, args);
    }
}
