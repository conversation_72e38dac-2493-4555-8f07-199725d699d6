-- KEYS[1]  商品ID
-- ARGV[n]  库存与销量对象
-- 统计库存扣减次数
local total = 0
for i, key in ipairs(KEYS) do
    -- 当前库存
    local curStock = tonumber(redis.call('hget', key, 'stock'))
    -- 当前库存类型 LIMITED 限量库存 UNLIMITED 无限库存
    local curStockType = redis.call('hget', key, 'stockType')
    -- 当前对应参数 库存 与 销量对象
    local stSv = cjson.decode(ARGV[i])
    local incr = tonumber(stSv.stock)
    --  参数里传来的库存类型
    local stockType = stSv.stockType == nil and '' or ('"' .. stSv.stockType .. '"')
    if stockType ~= '' then
        redis.call('hset', key, 'stockType', stockType)
        curStockType = stockType
    end
    if curStockType == '"LIMITED"' and curStock < (-1 * incr) then
        -- 库存回滚
        return total
    end
    if curStockType == '"LIMITED"' then
        -- 无限库存不需要操作库存 限量库存需要操作库存
        redis.call('hincrby', key, 'stock', incr)
    end
    -- 销量
    if stockType == '' then
        redis.call('hincrby', key, 'salesVolume', tonumber(stSv.sales))
    end
    -- 成功次数+1
    total = total + 1
end
return total
