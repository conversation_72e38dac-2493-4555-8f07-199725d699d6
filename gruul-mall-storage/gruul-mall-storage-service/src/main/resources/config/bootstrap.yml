server:
  port: 8482
spring:
  application:
    name: gruul-mall-storage
  profiles:
    active: prod
  cloud:
    nacos:
      server-addr: **************:8884
      discovery:
        namespace: ${spring.profiles.active}
        ip: *************
      config:
        namespace: ${spring.profiles.active}
        file-extension: yml
        shared-configs:
          - dataId: application-common.${spring.cloud.nacos.config.file-extension}
