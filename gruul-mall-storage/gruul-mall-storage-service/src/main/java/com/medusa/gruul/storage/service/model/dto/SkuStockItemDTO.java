package com.medusa.gruul.storage.service.model.dto;

import com.alibaba.fastjson2.support.geo.LineString;
import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.mp.FastJson2TypeHandler;
import com.medusa.gruul.storage.api.enums.StockType;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 库存消费记录 dto
 *
 * <AUTHOR>
 * date 2022/9/22
 */
@Data
public class SkuStockItemDTO implements Serializable {


    /**
     * 商品id
     */
    private Long productId;

    /**
     * sku id
     */
    @NotNull
    private Long skuId;

    /**
     * 变化数量
     */
    @NotNull
    private Integer num;

    /**
     * 库存类型
     */
    @NotNull
    private StockType stockType;


    /**
     * 实时库存
     */
    private Long stock;

    /**
     * 产品规格
     */
    @TableField(typeHandler = FastJson2TypeHandler.class)
    private List<String> specs;


}
