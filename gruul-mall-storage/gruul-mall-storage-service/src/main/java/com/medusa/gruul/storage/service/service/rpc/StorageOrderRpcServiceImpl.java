package com.medusa.gruul.storage.service.service.rpc;

import cn.hutool.core.collection.CollUtil;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.storage.api.bo.OrderStockBO;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.rpc.StorageOrderRpcService;
import com.medusa.gruul.storage.service.mq.service.StorageMqService;
import com.medusa.gruul.storage.service.service.SkuStockDetailService;
import com.medusa.gruul.storage.service.service.SkuStockService;
import com.medusa.gruul.storage.service.util.StorageUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/7/16
 */
@Service
@DubboService
@RequiredArgsConstructor
public class StorageOrderRpcServiceImpl implements StorageOrderRpcService {

    private final SkuStockService skuStockService;
    private final SkuStockDetailService stockDetailService;
    private final StorageMqService storageRabbitService;

    @Override
    public void reduceSkuStock(OrderStockBO skuStock) {
        this.getReduceSkuStock(skuStock);
    }


    @Override
    public Map<ActivityShopProductSkuKey, StorageSku> getReduceSkuStock(OrderStockBO skuStock) {
        Set<SkuKeyStSvBO> skuKeyStSvs = skuStock.getSkuKeyStSvs();
        if (CollUtil.isEmpty(skuKeyStSvs)) {
            return Collections.emptyMap();
        }
        AtomicReference<Map<ActivityShopProductSkuKey, StorageSku>> skuMapRef = new AtomicReference<>(
                Collections.emptyMap()
        );
        //redis记录订单号 用于防止重复消费与标记认可该订单下的商品库存已被处理 缓存时间固定10分钟
        StorageUtil.orderNoCheck(
                skuStock.getNo(),
                //扣除库存
                () -> {
                    //查询sku 数据 用于缓存全部数据
                    Map<ActivityShopProductSkuKey, StorageSku> skusBatch = skuStockService.getSkusBatch(skuKeyStSvs.stream().map(SkuKeyStSvBO::getKey).collect(Collectors.toSet()));
                    //批量扣除缓存库存
                    skuStockService.updateSkuStockCache(skuKeyStSvs);
                    //发送库存更新mq
                    storageRabbitService.sendUpdateStockMsg(skuKeyStSvs);
                    if (skuStock.getGenerateDetail()) {
                        stockDetailService.generateStockDetails(List.of(skuStock));
                    }
                    skuMapRef.set(skusBatch);
                },
                () -> {
                }
        );
        return skuMapRef.get();
    }
}
