package com.medusa.gruul.storage.service.service.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.medusa.gruul.common.model.base.ActivityShopProductKey;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.dto.ConsignmentPriceSettingDTO;
import com.medusa.gruul.goods.api.model.enums.PricingType;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.vo.ProductStatisticsVO;
import com.medusa.gruul.storage.service.model.vo.ProductSpecsSkusVO;
import com.medusa.gruul.storage.service.mp.service.IStorageSkuService;
import com.medusa.gruul.storage.service.mp.service.IStorageSpecGroupService;
import com.medusa.gruul.storage.service.service.SkuStockService;
import com.medusa.gruul.storage.service.service.StorageShopProductService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2022/7/14
 */
@Service
@DubboService
@RequiredArgsConstructor
public class StorageShopProductServiceImpl implements StorageShopProductService {


    private final SkuStockService skuStockService;
    private final IStorageSkuService storageSkuService;
    private final IStorageSpecGroupService storageSpecGroupService;
    private final GoodsRpcService goodsRpcService;


    @Override
    public ProductStatisticsVO getProductStatistics(Long shopId, Long productId) {
        return this.getProductStatisticsList(
                false,
                Set.of(new ShopProductKey().setShopId(shopId).setProductId(productId))
        ).stream().findFirst().orElse(null);
    }

    @Override
    public List<ProductStatisticsVO> getProductStatisticsList(boolean includeActivityStock, Set<ShopProductKey> shopProductKeys) {
        return storageSkuService.getProductStatisticsList(includeActivityStock, shopProductKeys);
    }

    @Override
    public ProductSpecsSkusVO getProductSpecsSkus(Long shopId, Long productId) {
        ActivityShopProductKey key = new ActivityShopProductKey().setProductId(productId);
        // 该处shopId 可能是suppliedId
        key.setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L);

        List<StorageSku> storageSkus = skuStockService.productSkuStockBatch(Set.of(key))
                .values()
                .stream()
                .findFirst()
                .orElse(null);
        if ((!ISystem.shopIdOpt().get().equals(shopId)) && ObjectUtil.isNotNull(storageSkus)) {
            // 说明该处信息可能为供应商的库存信息 要额外计算商品的增加比例
            Product productInfo = goodsRpcService.getProductInfo(ISystem.shopIdOpt().get(), productId);
            if (productInfo != null) {
                ConsignmentPriceSettingDTO consignmentPriceSetting = productInfo.getExtra().getConsignmentPriceSetting();
                if (consignmentPriceSetting != null) {
                    consignmentPriceSetting.consignmentCalculate(consignmentPriceSetting, storageSkus);
                }
            }
        }
        return new ProductSpecsSkusVO()
                .setSpecGroups(storageSpecGroupService.getProductSpecs(shopId, productId))
                .setSkus(
                        storageSkus
                );

    }


}
