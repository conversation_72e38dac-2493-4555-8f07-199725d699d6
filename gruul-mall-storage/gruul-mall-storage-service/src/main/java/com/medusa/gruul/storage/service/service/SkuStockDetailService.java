package com.medusa.gruul.storage.service.service;

import com.medusa.gruul.storage.api.bo.OrderStockBO;
import com.medusa.gruul.storage.api.dto.SkuCopy;
import com.medusa.gruul.storage.api.dto.StoragesCopyDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * date 2023/8/3
 */
public interface SkuStockDetailService {

    /**
     * 生成库存明细
     *
     * @param orderStocks 订单库存信息
     */
    void  generateStockDetails(List<OrderStockBO> orderStocks);

    /**
     * 采购商品入库 生成明细
     *
     * @param storagesCopy 商品库存详情
     */
    void purchaseToStorageDetails(StoragesCopyDTO storagesCopy);

}
