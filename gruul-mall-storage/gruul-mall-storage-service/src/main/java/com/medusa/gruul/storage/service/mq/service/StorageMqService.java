package com.medusa.gruul.storage.service.mq.service;

import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;

import java.util.Set;

/**
 * <AUTHOR>
 * date 2023/8/3
 */
public interface StorageMqService {

    /**
     * 发送更新库存的mq
     *
     * @param skuKeyStSvs 扣减库存参数 列表
     */
    void sendUpdateStockMsg(Set<SkuKeyStSvBO> skuKeyStSvs);

    /**
     * 发送更新价格mq
     *
     * @param productSkusVO 多规格数据
     */
    void sendUpdateSkuPriceMsg(ProductSkusVO productSkusVO);

}
