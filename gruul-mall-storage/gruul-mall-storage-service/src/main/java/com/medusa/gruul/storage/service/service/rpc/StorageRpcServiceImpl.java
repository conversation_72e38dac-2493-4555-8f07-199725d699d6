package com.medusa.gruul.storage.service.service.rpc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.base.*;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.storage.api.bo.OrderStockBO;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.bo.StSvBo;
import com.medusa.gruul.storage.api.dto.*;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.enums.LimitOrderType;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockChangeType;
import com.medusa.gruul.storage.api.enums.StockType;
import com.medusa.gruul.storage.api.rpc.StorageOrderRpcService;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import com.medusa.gruul.storage.api.vo.ProductSaleVolumeVO;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;
import com.medusa.gruul.storage.api.vo.ProductStatisticsVO;
import com.medusa.gruul.storage.service.mp.entity.StorageSpec;
import com.medusa.gruul.storage.service.mp.entity.StorageSpecGroup;
import com.medusa.gruul.storage.service.mp.service.IStorageSkuService;
import com.medusa.gruul.storage.service.mp.service.IStorageSpecGroupService;
import com.medusa.gruul.storage.service.mp.service.IStorageSpecService;
import com.medusa.gruul.storage.service.service.*;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/7/6
 */
@Service
@DubboService
@RequiredArgsConstructor
public class StorageRpcServiceImpl implements StorageRpcService {


    private final SkuService skuService;
    private final SkuStockService skuStockService;
    private final SpecSkuService specSkuService;
    private final IStorageSkuService storageSkuService;
    private final IStorageSpecService storageSpecService;
    private final StorageOrderRpcService storageOrderRpcService;
    private final SkuStockDetailService stockDetailService;
    private final IStorageSpecGroupService storageSpecGroupService;
    private final StorageShopProductService storageShopProductService;
    private final RabbitTemplate rabbitTemplate;
    @Override
    public Map<ActivityShopProductSkuKey, StorageSku> skuBatch(Set<ActivityShopProductSkuKey> shopProductSkuKeys) {
        return skuStockService.getSkusBatch(shopProductSkuKeys);
    }

    @Override
    public Map<ActivityShopProductKey, List<StorageSku>> productSkuStockBatch(Set<ActivityShopProductKey> shopProductKeys) {
        return skuStockService.productSkuStockBatch(shopProductKeys);
    }

    @Override
    @Log("获取商品sku")
    public StorageSku getProductSku(ActivityShopProductSkuKey shopProductSkuKey) {
        return skuService.getProductSku(shopProductSkuKey);
    }


    @Override
    @Log("新增/更新规格与sku")
    public void saveOrUpdateSpecSku(StorageSpecSkuDTO storageSpecSku) {
        specSkuService.saveOrUpdateSpecSku(storageSpecSku);
    }

    @Override
    public Map<String, ProductStatisticsVO> getProductStatisticsMap(List<ShopProductKeyDTO> shopProductKeys) {
        if (CollUtil.isEmpty(shopProductKeys)) {
            return Collections.emptyMap();
        }

        List<ProductStatisticsVO> productStatisticsList = storageShopProductService.getProductStatisticsList(
                false,
                shopProductKeys.stream()
                        .map(spk -> new ShopProductKey().setShopId(spk.getShopId()).setProductId(spk.getProductId()))
                        .collect(Collectors.toSet())
        );
        return CollUtil.emptyIfNull(productStatisticsList).stream()
                .collect(
                        Collectors.toMap(
                                statistics -> RedisUtil.key(statistics.getShopId(), statistics.getProductId()),
                                statistics -> statistics
                        )
                );
    }

    @Override
    public Map<ShopProductKey, ProductStatisticsVO> getProductStatisticsMap(Set<ShopProductKey> shopProductKeys) {
        if (CollUtil.isEmpty(shopProductKeys)) {
            return Collections.emptyMap();
        }
        List<ProductStatisticsVO> productStatisticsList = storageShopProductService.getProductStatisticsList(true, shopProductKeys);
        if (CollUtil.isEmpty(productStatisticsList)) {
            return Collections.emptyMap();
        }
        return productStatisticsList.stream()
                .collect(
                        Collectors.toMap(
                                statistics -> new ShopProductKey().setShopId(statistics.getShopId()).setProductId(statistics.getProductId()),
                                statistics -> statistics
                        )
                );
    }

    @Log("店铺热销")
    @Override
    public List<ProductSaleVolumeVO> getShopProductSaleVolume(Long shopId, Long size) {
        return storageSkuService.getShopProductSaleVolume(shopId, size);
    }

    @Log("获取商品规格信息")
    @Override
    public List<ProductSkusVO.SkuVO> getProductSkusByShopProductKeys(List<ShopProductKeyDTO> shopProductKeys) {
        return storageSkuService.getProductSkusByShopProductKeys(shopProductKeys);
    }

    @Log("获取店铺销量")
    @Override
    public List<ProductStatisticsVO> getShopSales(Boolean sortAsc) {

        return storageSkuService.getShopSales(sortAsc);
    }

    /**
     * 获取指定店铺销量
     *
     * @param shopId 店铺
     * @return 销量
     */
    @Override
    public Long getShopSaleVolume(Long shopId) {
        return storageSkuService.getShopSaleVolume(shopId);
    }

    /**
     * 获取 商品id
     *
     * @param skuId skuId
     * @return 商品id
     */
    @Override
    public Long getGoodsIdBySkuId(Long skuId) {
        StorageSku sku = storageSkuService.getById(skuId);
        return (sku != null) ? sku.getProductId() : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void storageCopy(StoragesCopyDTO storagesCopy) {
        Map<ActivityShopProductSkuKey, SkuCopy> targetSkuKeyAndSourceKeyMap = targetSkuKeys(storagesCopy);
        if (CollUtil.isEmpty(targetSkuKeyAndSourceKeyMap)) {
            return;
        }
        //先过滤已存在的 sku
        //查询已存在 sku
        Map<ActivityShopProductSkuKey, StorageSku> existStorageMap = this.skuBatch(targetSkuKeyAndSourceKeyMap.keySet());
        OrderStockBO stockBO = null;
        if (CollUtil.isNotEmpty(existStorageMap)) {
            //已存在数据 生成操作库存的数据
            stockBO = new OrderStockBO()
                    .setNo(storagesCopy.getTransactionId())
                    .setSkuKeyStSvs(
                            existStorageMap.keySet().stream()
                                    .map(key -> {
                                        SkuCopy remove = targetSkuKeyAndSourceKeyMap.remove(key);
                                        return new SkuKeyStSvBO().setStockChangeType(StockChangeType.PURCHASE_INBOUND).setKey(key).setStSv(new StSvBo().setStock(remove.getNum()));
                                    })
                                    .collect(Collectors.toSet())
                    );
        }
        if (CollUtil.isEmpty(targetSkuKeyAndSourceKeyMap) && stockBO != null) {
            stockBO.setGenerateDetail(Boolean.FALSE);
            //存在的sku直接批量操作库存
            storageOrderRpcService.reduceSkuStock(stockBO);

            /* 生成采购商品入库的 库存明细
             */
            stockDetailService.purchaseToStorageDetails(storagesCopy);
            return;
        }
        //剩下的都是需要新增的数据
        /*
         * 1. 根据targetSkuKeyAndSourceKeyMap 渲染出 商品 key 1.查询出所有 sku 需要的商品 key 2. 查询出所有规格组与规格需要的 key
         * 2.查询源商品规格组信息
         * 3.查询源商品sku信息
         * 执行 copy
         */
        //渲染出所有需要新增的商品 key 和活动商品 key 集合
        Tuple2<Set<ShopProductKey>, Set<ActivityShopKey>> sourceProductKeysTuple = this.productKeysTuple(targetSkuKeyAndSourceKeyMap.values());
        //复制规格组和规格信息
        Set<StorageSpecGroup> groupsBatch = storageSpecGroupService.getGroupsBatch(sourceProductKeysTuple._1());
        Long targetShopId = storagesCopy.getTargetShopId();
        this.saveSpecGroupAndSpecs(targetShopId, groupsBatch);
        //查询出所有 复制源 sku key
        Map<ActivityShopProductSkuKey, StorageSku> sourceSkuKeys = storageSkuService.getSkusBatchByPrefixMap(Boolean.TRUE, sourceProductKeysTuple._2());
        //不存在查询商品规格组信息 与 所有的 sku 信息 sku 信息库存置为0 根据参数设置库存 并保存
        Map<ActivityShopProductSkuKey, StorageSku> sourceSkusMap = this.skuBatch(sourceSkuKeys.keySet());
        sourceSkusMap.forEach(
                (key, sku) -> {
                    SkuCopy skuCopy = targetSkuKeyAndSourceKeyMap.get((ActivityShopProductSkuKey) key.setShopId(targetShopId));
                    long num = skuCopy == null ? 0L : skuCopy.getNum().longValue();
                    //初始化店铺 id
                    sku.setShopId(targetShopId);
                    //初始化销量 与初始销量
                    sku.setSalesVolume(0L).setInitSalesVolume(0L);
                    //初始化库存类型与库存
                    sku.setStockType(StockType.LIMITED).setStock(num);
                    //初始化 限购类型 与限购数量
                    sku.setLimitType(LimitType.UNLIMITED).setLimitNum(0);
                    sku.setLimitOrderType(LimitOrderType.UNLIMITED).setLimitOrderNum(0);
                    //设置最低购买数量
                    sku.setMinimumPurchase(1);
                }
        );
        //保存 sku
        storageSkuService.saveBatch(sourceSkusMap.values());
        //最后处理已存在的 sku 库存 保证事务一致性
        if (stockBO != null) {
            //存在的sku直接批量操作库存
            storageOrderRpcService.reduceSkuStock(stockBO);
        }
        /*生成采购商品入库的 库存明细
         */
        stockDetailService.purchaseToStorageDetails(storagesCopy);

    }

    /**
     * 获取多规格数据
     *
     * @param shopProductKeys 商品id + 供应商id
     *                        分解多规格返回原始数据
     */
    @Override
    public List<StorageSpecSkuDTO> getStorageSpecSku(Set<ShopProductKey> shopProductKeys) {
        if (CollUtil.isEmpty(shopProductKeys)) {
            return new ArrayList<>();
        }
        Set<ActivityShopProductKey> activityShopProductKeys = shopProductKeys.stream().map(shopProductKey -> {
            ActivityShopProductKey activityShopProductKey = new ActivityShopProductSkuKey();
            activityShopProductKey.setProductId(shopProductKey.getProductId());
            activityShopProductKey.setShopId(shopProductKey.getShopId());
            activityShopProductKey.setActivityId(0L).setActivityType(OrderType.COMMON);
            return activityShopProductKey;
        }).collect(Collectors.toSet());
        Set<StorageSpecGroup> groupsBatch = Option.of(storageSpecGroupService.getGroupsBatch(shopProductKeys))
                .getOrElse(new HashSet<>());
        Map<ActivityShopProductKey, List<StorageSku>> activityShopProductKeyListMap = skuStockService.productSkuStockBatch(activityShopProductKeys);
        if (MapUtil.isEmpty(activityShopProductKeyListMap)) {
            return new ArrayList<>();
        }
        Map<ShopProductKey, List<SpecGroupDTO>> productKeyListMap = groupsBatch.stream()
                .filter(BeanUtil::isNotEmpty)
                .collect(Collectors.groupingBy(item ->
                        new ShopProductKey()
                                .setShopId(item.getShopId())
                                .setProductId(item.getProductId())
                )).entrySet().stream()
                .map(entry -> Map.entry(
                                entry.getKey(),
                                entry.getValue()
                                        .stream()
                                        .map(storageSpecGroup -> {
                                            SpecGroupDTO specGroupDTO = new SpecGroupDTO();
                                            specGroupDTO.setName(storageSpecGroup.getName());
                                            specGroupDTO.setOrder(storageSpecGroup.getOrder());
                                            specGroupDTO.setChildren(storageSpecGroup.getSpecs()
                                                    .stream()
                                                    .map(item -> {
                                                        SpecDTO specDTO = new SpecDTO();
                                                        specDTO.setName(item.getName());
                                                        specDTO.setOrder(item.getOrder());
                                                        return specDTO;
                                                    }).collect(Collectors.toList()));
                                            return specGroupDTO;
                                        }).collect(Collectors.toList())
                        )
                ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));


        Map<ActivityShopProductKey, List<SkuDTO>> skuDTOMap = activityShopProductKeyListMap.entrySet()
                .stream()
                .map(entry ->
                        Map.entry(
                                entry.getKey(),
                                entry.getValue()
                                        .stream()
                                        .map(storageSku -> {
                                            SkuDTO skuDTO = new SkuDTO();
                                            BeanUtil.copyProperties(storageSku, skuDTO);
                                            skuDTO.setInitStock(storageSku.getStock());
                                            return skuDTO;
                                        }).collect(Collectors.toList())
                        )
                ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        return skuDTOMap.entrySet()
                .stream()
                .map(entry -> {
                    StorageSpecSkuDTO storageSpecSkuDTO = new StorageSpecSkuDTO();
                    storageSpecSkuDTO.setProductId(entry.getKey().getProductId());
                    storageSpecSkuDTO.setSkus(entry.getValue());
                    storageSpecSkuDTO.setSpecGroups(
                            productKeyListMap.get(new ShopProductKey()
                                    .setProductId(entry.getKey().getProductId())
                                    .setShopId(entry.getKey().getShopId())));
                    return storageSpecSkuDTO;
                }).collect(Collectors.toList());

    }

    @Override
    public void updateSkuStock(Long productId, List<ProductSkuStockDTO> skuStocks) {
        skuService.updateSkuStock(productId, skuStocks);
    }

    @Override
    public void updateSkuPrice(Long productId, List<ProductSkuPriceDTO> productSkuPrice) {
        skuService.updateSkuPrice(productId, productSkuPrice);
    }

    private OrderStockBO toStockDetailParam(String transactionId, OrderStockBO stockBO, Map<ActivityShopProductSkuKey, StorageSku> newSkuMap) {
        Set<SkuKeyStSvBO> skuKeyStSvs = (stockBO = stockBO == null ? new OrderStockBO().setNo(transactionId).setSkuKeyStSvs(new HashSet<>()) : stockBO).getSkuKeyStSvs();
        newSkuMap.forEach(
                (key, sku) -> skuKeyStSvs.add(
                        new SkuKeyStSvBO()
                                .setStockChangeType(StockChangeType.PURCHASE_INBOUND)
                                .setKey(key)
                                .setStSv(
                                        new StSvBo().setStock(sku.getStock().intValue())
                                )
                )
        );
        return stockBO;
    }


    private Tuple2<Set<ShopProductKey>, Set<ActivityShopKey>> productKeysTuple(Collection<SkuCopy> copies) {
        Tuple2<Set<ShopProductKey>, Set<ActivityShopKey>> productKeysTuple = Tuple.of(new HashSet<>(), new HashSet<>());
        copies.forEach(copy -> {
            ShopProductSkuKey sourceKey = copy.getSourceKey();
            Long shopId = sourceKey.getShopId();
            Long productId = sourceKey.getProductId();
            productKeysTuple._1().add(new ShopProductKey().setShopId(shopId).setProductId(productId));
            productKeysTuple._2().add((ActivityShopKey) new ActivityShopProductKey().setProductId(productId).setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L));
        });
        return productKeysTuple;

    }

    private void saveSpecGroupAndSpecs(Long shopId, Set<StorageSpecGroup> queryGroups) {
        if (CollUtil.isEmpty(queryGroups)) {
            return;
        }
        List<StorageSpec> specs = queryGroups.stream()
                .flatMap(group -> {
                    long groupId = MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(group).longValue();
                    group.setShopId(shopId).setId(groupId);
                    return group.getSpecs().stream().peek(
                            spec -> spec.setGroupId(groupId).setShopId(group.getShopId()).setProductId(group.getProductId())
                                    .setId(null)
                    );
                }).toList();
        storageSpecGroupService.saveBatch(queryGroups);
        if (CollUtil.isEmpty(specs)) {
            return;
        }
        storageSpecService.saveBatch(specs);

    }

    private Map<ActivityShopProductSkuKey, SkuCopy> targetSkuKeys(StoragesCopyDTO storagesCopy) {
        Set<SkuCopy> targets = storagesCopy.getTargets();
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyMap();
        }
        Long shopId = storagesCopy.getTargetShopId();
        Map<ActivityShopProductSkuKey, SkuCopy> targetSkuKeyMap = new HashMap<>(targets.size());
        targets.forEach(
                target -> {
                    ShopProductSkuKey sourceKey = target.getSourceKey();
                    targetSkuKeyMap.put(
                            (ActivityShopProductSkuKey) new
                                    ActivityShopProductSkuKey().setSkuId(sourceKey.getSkuId())
                                    .setProductId(sourceKey.getProductId()).setShopId(shopId)
                                    .setActivityType(OrderType.COMMON).setActivityId(0L)
                            ,
                            target
                    );
                }
        );

        return targetSkuKeyMap;
    }


}
