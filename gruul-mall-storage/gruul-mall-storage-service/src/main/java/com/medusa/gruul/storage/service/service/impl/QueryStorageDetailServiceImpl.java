package com.medusa.gruul.storage.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.storage.service.model.param.StorageDetailParam;
import com.medusa.gruul.storage.service.mp.entity.StorageDetail;
import com.medusa.gruul.storage.service.mp.service.IStorageDetailService;
import com.medusa.gruul.storage.service.service.QueryStorageDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * QueryStorageDetailServiceImpl.java
 *
 * <AUTHOR>
 * @Description QueryStorageDetailServiceImpl.java
 * @date 2023-07-27 14:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueryStorageDetailServiceImpl implements QueryStorageDetailService {
    private final IStorageDetailService storageDetailService;

    @Override
    public IPage<StorageDetail> getStorageDetailList(StorageDetailParam storageDetailParam) {
        return storageDetailService.lambdaQuery()
                .eq(StorageDetail::getShopId, ISecurity.userMust().getShopId())
                .eq(storageDetailParam.getProductId() != null,
                        StorageDetail::getProductId, storageDetailParam.getProductId())
                .eq(storageDetailParam.getId() != null,
                        BaseEntity::getId, storageDetailParam.getId())
                .eq(storageDetailParam.getSellType() != null,
                        StorageDetail::getSellType, storageDetailParam.getSellType())
                .eq(storageDetailParam.getStockChangeType() != null,
                        StorageDetail::getStockChangeType, storageDetailParam.getStockChangeType())
                .eq(StrUtil.isNotBlank(storageDetailParam.getOrderNo()),
                        StorageDetail::getOrderNo, storageDetailParam.getOrderNo())
                .and(
                        storageDetailParam.getIsOutbound() != null,
                        (Boolean.TRUE.equals(storageDetailParam.getIsOutbound()) ?
                                // Compare with StockChangeNum if getIsOutbound() is true
                                queryWrapper -> queryWrapper.lt(StorageDetail::getStockChangeNum, 0) :
                                // Compare with StockChangeNum if getIsOutbound() is false
                                queryWrapper -> queryWrapper.gt(StorageDetail::getStockChangeNum, 0)
                        )
                )
                .like(StrUtil.isNotBlank(storageDetailParam.getProductName()),
                        StorageDetail::getProductName, storageDetailParam.getProductName())
                .between(storageDetailParam.getStartTime() != null && storageDetailParam.getEndTime() != null,
                        BaseEntity::getCreateTime, storageDetailParam.getStartTime(), storageDetailParam.getEndTime())
                .orderByDesc(BaseEntity::getCreateTime)
                .orderByDesc(BaseEntity::getId)
                .page(new Page<>(storageDetailParam.getCurrent(), storageDetailParam.getSize()));
    }
}
