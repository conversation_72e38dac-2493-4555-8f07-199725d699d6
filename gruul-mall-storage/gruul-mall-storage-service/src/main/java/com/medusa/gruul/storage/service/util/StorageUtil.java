package com.medusa.gruul.storage.service.util;

import cn.hutool.core.util.BooleanUtil;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.storage.api.constant.StorageConstant;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * date 2023/3/6
 */
public interface StorageUtil {

    /**
     * 订单消费检查
     *
     * @param orderNo     订单号
     * @param successTask 成功任务
     * @param failTask    失败任务
     */
    static void orderNoCheck(String orderNo, Runnable successTask, Runnable failTask) {
        if (orderNo == null) {
            return;
        }
        String key = RedisUtil.key(StorageConstant.CACHE_KEY_ORDER_NO, orderNo);

        Boolean success = RedisUtil.getRedisTemplate()
                .opsForValue()
                .setIfAbsent(
                        key,
                        CommonPool.NUMBER_ONE,
                        CommonPool.NUMBER_TEN,
                        TimeUnit.MINUTES
                );
        success = BooleanUtil.isTrue(success);
        if (!success) {
            failTask.run();
            return;
        }
        try {
            successTask.run();
        } catch (Exception exception) {
            RedisUtil.delete(key);
            throw exception;
        }
    }

    /**
     * 生成sku的redis缓存key
     *
     * @param key shopId 店铺id | productId 商品id | skuId skuId | activityType 活动类型 | activityId   活动id
     * @return key
     */
    static String generateSkuRedisKey(ActivityShopProductSkuKey key) {
        return RedisUtil.key(StorageConstant.CACHE_KEY_PRODUCT_SKU_STORAGE, key.getActivityType(), key.getActivityId(), key.getShopId(), key.getProductId(), key.getSkuId());
    }

    /**
     * 生成sku的redis缓存key的通配符
     *
     * @param shopId       店铺id
     * @param activityType 活动类型
     * @param activityId   活动id
     * @return 通配符key
     */
    static String generateSkuRedisKeyPattern(OrderType activityType, Long activityId, Long shopId) {
        return RedisUtil.key(StorageConstant.CACHE_KEY_PRODUCT_SKU_STORAGE, activityType, activityId, shopId, "*", "*");
    }

    /**
     * 生成根据店铺id和商品id的缓存key通配符
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 通配符key
     */
    static String generateSkuRedisKeyPattern(Long shopId, Long productId) {
        return RedisUtil.key(StorageConstant.CACHE_KEY_PRODUCT_SKU_STORAGE, "*", "*", shopId, productId, "*");
    }


    /**
     * 生成redisson分布式锁key
     *
     * @param key shopId 店铺id | productId 商品id | skuId skuId | activityType 活动类型 | activityId   活动id
     * @return lock key
     */
    static String generateRedissonLockKey(ActivityShopProductSkuKey key) {
        return RedisUtil.key(StorageConstant.CACHE_KEY_PRODUCT_SKU_STORAGE, key.getActivityType(), key.getActivityId(), key.getShopId(), key.getProductId(), key.getSkuId(), "lock");
    }

    /**
     * 锁单的商品库存key
     * @param key
     * @return
     */
   static String generateSkuLockStockKey(ActivityShopProductSkuKey key){
       return RedisUtil.key(StorageConstant.CACHE_KEY_SKU_STORAGE_STOCK, key.getActivityType(), key.getShopId(), key.getProductId(), key.getSkuId());

   }
}
