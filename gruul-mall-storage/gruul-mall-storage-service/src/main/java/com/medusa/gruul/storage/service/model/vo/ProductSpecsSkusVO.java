package com.medusa.gruul.storage.service.model.vo;

import com.medusa.gruul.storage.api.entity.StorageSku;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * date 2022/7/15
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductSpecsSkusVO {
    /**
     * 规格组
     */
    private List<ProductSpecVO> specGroups;
    /**
     * sku列表
     */
    private List<StorageSku> skus;
}
