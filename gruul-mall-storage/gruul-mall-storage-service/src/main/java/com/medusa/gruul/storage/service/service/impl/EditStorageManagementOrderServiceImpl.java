package com.medusa.gruul.storage.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.storage.api.bo.OrderStockBO;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.bo.StSvBo;
import com.medusa.gruul.storage.api.constant.StorageConstant;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.enums.StockChangeType;
import com.medusa.gruul.storage.api.enums.StockType;
import com.medusa.gruul.storage.api.enums.StorageManagementOrderStatus;
import com.medusa.gruul.storage.api.rpc.StorageOrderRpcService;
import com.medusa.gruul.storage.service.model.dto.SkuStockItemDTO;
import com.medusa.gruul.storage.service.model.dto.StorageManagementOrderDTO;
import com.medusa.gruul.storage.service.model.enums.StorageError;
import com.medusa.gruul.storage.service.model.enums.StorageManagementOrderType;
import com.medusa.gruul.storage.service.mp.entity.StorageManagementOrder;
import com.medusa.gruul.storage.service.mp.entity.StorageManagementOrderItem;
import com.medusa.gruul.storage.service.mp.service.IStorageManagementOrderItemService;
import com.medusa.gruul.storage.service.mp.service.IStorageManagementOrderService;
import com.medusa.gruul.storage.service.service.EditStorageManagementOrderService;
import com.medusa.gruul.storage.service.service.SkuStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 创建仓储管理订单serviceImpl
 *
 * <AUTHOR>
 * @Description CreateStorageManagementOrderServiceImpl.java
 * @date 2023-07-25 17:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EditStorageManagementOrderServiceImpl implements EditStorageManagementOrderService {

    private final IStorageManagementOrderService storageManagementOrderService;

    private final SkuStockService skuStockService;

    private final IStorageManagementOrderItemService storageManagementOrderItemService;

    private final StorageOrderRpcService storageOrderRpcService;

    /**
     * 仓储管理订单创建
     *
     * @param storageManagementOrder 仓储管理订单DTO
     * @return 订单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = StorageConstant.CACHE_KEY_MANAGEMENT_PRODUCT_SKU_STORAGE, batchParamName = "#productIds", key = "#item")
    public String createStorageManagementOrder(StorageManagementOrderDTO storageManagementOrder, Set<Long> productIds) {
        StorageManagementOrder newStorageManagementOrder = new StorageManagementOrder();
        Long shopId = ISecurity.userMust().getShopId();
        //生成订单号
        String no = StorageConstant.STORAGE_MANAGEMENT_ORDER_NO_PREFIX + IdUtil.getSnowflakeNextIdStr();
        //计算库存
        Long totalNum = calculateStock(storageManagementOrder.getStorageManagementOrderItems(), storageManagementOrder.getStockChangeType());

        /*创建主订单
         */
        newStorageManagementOrder.setNo(no)
                .setStatus(StorageManagementOrderStatus.WAIT_COMPLETION)
                .setRemark(storageManagementOrder.getRemark())
                .setStorageManagementOrderType(storageManagementOrder.getStorageManagementOrderType())
                .setShopId(shopId)
                .setStockChangeType(storageManagementOrder.getStockChangeType())
                .setInventoryArea(storageManagementOrder.getInventoryArea())
                .setEvidence(storageManagementOrder.getEvidence())
                .setChangeStockTotal(totalNum);

        List<StorageManagementOrderItem> storageManagementOrderItems = new ArrayList<>();
        /* 库存校验
         */
        checkStorage(storageManagementOrder.getStorageManagementOrderItems(), storageManagementOrderItems, no, shopId, storageManagementOrder.getStockChangeType());
        storageManagementOrderService.save(newStorageManagementOrder);
        storageManagementOrderItemService.saveBatch(storageManagementOrderItems);
        return no;
    }

    @Override
    public void cancelStorageManagementOrder(Long id) {
        StorageManagementOrder storageManagementOrder = checkStorageManagementOrder(id);
        storageManagementOrder.setStatus(StorageManagementOrderStatus.CANCEL).setOperationAccomplishTime(LocalDateTime.now());
        storageManagementOrderService.updateById(storageManagementOrder);
    }

    private StockChangeType currentStockChangeType(StorageManagementOrderType orderType, StockChangeType stockChangeType, Integer num) {
        return orderType.getChangeable() ?
                num < 0 ? StockChangeType.OVERAGE_INBOUND
                        : StockChangeType.SHORTAGE_OUTBOUND
                : stockChangeType;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = StorageConstant.STORAGE_MANAGEMENT_ORDER, key = "#id")
    public void completeStorageManagementOrder(Long id) {
        StorageManagementOrder order = checkStorageManagementOrder(id);
        Long shopId = order.getShopId();
        List<StorageManagementOrderItem> orderItems = storageManagementOrderItemService.lambdaQuery().eq(StorageManagementOrderItem::getShopId, shopId).eq(StorageManagementOrderItem::getOrderNo, order.getNo()).list();
        StockChangeType stockChangeType = order.getStockChangeType();
        StorageManagementOrderType orderType = order.getStorageManagementOrderType();
        /* 库存校验
         */
        Boolean changeable = orderType.getChangeable();
        Map<ActivityShopProductSkuKey, SkuStockItemDTO> skuStockMap = orderItems.stream()
                .flatMap(orderItem -> orderItem.getSkuStockItems().stream())
                .filter(skuStockItem -> skuStockItem.getStockType() == StockType.LIMITED)
                .collect(Collectors.toMap(
                        stockItem -> (ActivityShopProductSkuKey) new ActivityShopProductSkuKey().setSkuId(stockItem.getSkuId())
                                .setProductId(stockItem.getProductId()).setShopId(shopId)
                                .setActivityType(OrderType.COMMON).setActivityId(0L),
                        stockItem -> stockItem
                ));
        //批量查询sku信息
        Map<ActivityShopProductSkuKey, StorageSku> skuMap = skuStockService.getSkusBatch(skuStockMap.keySet());
        //
        Set<SkuKeyStSvBO> skuKeyStSvs = skuStockMap.entrySet().stream().map((entry) -> {
            ActivityShopProductSkuKey key = entry.getKey();
            SkuStockItemDTO skuStock = entry.getValue();
            //库存变化量
            int stockIncrement = skuStock.getNum();
            StorageSku storageSku = stockCompare(entry, stockChangeType, skuMap);
            skuStock.setStock(storageSku.getStock());
            if (changeable) {
                stockIncrement = (int) (skuStock.getNum() - storageSku.getStock());
            }
            if (stockIncrement == 0) {
                return null;
            }
            return new SkuKeyStSvBO()
                    .setKey(key)
                    .setStockChangeType(this.currentStockChangeType(orderType, stockChangeType, stockIncrement))
                    .setStSv(new StSvBo()
                            .setStock(stockIncrement)
                            .setSpecName(skuStock.getSpecs())
                    );
        }).filter(Objects::nonNull).collect(Collectors.toSet());
        //更新订单数据
        storageManagementOrderService.updateById(order.setStatus(StorageManagementOrderStatus.COMPLETION).setOperationAccomplishTime(LocalDateTime.now()));
        //更新订单项数据
        if (changeable) {
            storageManagementOrderItemService.updateBatchById(orderItems);
        }
        //扣减库存
        if (CollUtil.isNotEmpty(skuKeyStSvs)) {
            storageOrderRpcService.getReduceSkuStock(new OrderStockBO().setSkuKeyStSvs(skuKeyStSvs).setNo(order.getNo()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = StorageConstant.CACHE_KEY_MANAGEMENT_PRODUCT_SKU_STORAGE, batchParamName = "#productIds", key = "#item")
    public void editStorageManagementOrder(StorageManagementOrderDTO storageManagementOrder, Set<Long> productIds) {
        Long id = storageManagementOrder.getId();
        // 检验订单
        StorageManagementOrder oldStorageManagementOrder = checkStorageManagementOrder(id);
        String no = oldStorageManagementOrder.getNo();
        Long shopId = oldStorageManagementOrder.getShopId();
        // 计算库存
        Long totalNum = calculateStock(storageManagementOrder.getStorageManagementOrderItems(), storageManagementOrder.getStockChangeType());

        /*修改主订单
         */
        oldStorageManagementOrder.setRemark(storageManagementOrder.getRemark()).setStorageManagementOrderType(storageManagementOrder.getStorageManagementOrderType()).setInventoryArea(storageManagementOrder.getInventoryArea()).setEvidence(storageManagementOrder.getEvidence()).setStockChangeType(storageManagementOrder.getStockChangeType()).setChangeStockTotal(totalNum);
        storageManagementOrderService.updateById(oldStorageManagementOrder);

        List<StorageManagementOrderItem> storageManagementOrderItems = new ArrayList<>();
        /* 库存校验
         */
        checkStorage(storageManagementOrder.getStorageManagementOrderItems(), storageManagementOrderItems, no, shopId, storageManagementOrder.getStockChangeType());
        // 先删除之前关联库存关联订单的商品信息
        storageManagementOrderItemService.lambdaUpdate().eq(StorageManagementOrderItem::getOrderNo, no).eq(StorageManagementOrderItem::getShopId, shopId).remove();
        storageManagementOrderItemService.saveBatch(storageManagementOrderItems);
    }


    public StorageManagementOrder checkStorageManagementOrder(Long id) {
        Long shopId = ISecurity.userMust().getShopId();
        StorageManagementOrder storageManagementOrder = storageManagementOrderService.lambdaQuery().eq(BaseEntity::getId, id).eq(StorageManagementOrder::getShopId, shopId).eq(StorageManagementOrder::getStatus, StorageManagementOrderStatus.WAIT_COMPLETION).one();
        if (storageManagementOrder == null) {
            throw new ServiceException("当前状态订单不存在,或当前状态订单不支持修改");
        }
        return storageManagementOrder;
    }


    public void checkStorage(List<StorageManagementOrderItem> storageManagementOrderItemsList, List<StorageManagementOrderItem> storageManagementOrderItems, String no, Long shopId, StockChangeType stockChangeType) {
        /* 库存校验
         */
        storageManagementOrderItemsList.forEach(storageManagementOrderItem -> {
            //assembleSkuMap
            Map<ActivityShopProductSkuKey, SkuStockItemDTO> skuStockMap = assembleSkuMap(storageManagementOrderItem, shopId);
            // 查询未参加活动的sku信息
            Map<ActivityShopProductSkuKey, StorageSku> skuMap = skuStockService.getSkusBatch(skuStockMap.keySet());
            for (Map.Entry<ActivityShopProductSkuKey, SkuStockItemDTO> entry : skuStockMap.entrySet()) {
                /* 库存比较
                 */
                StorageSku storageSku = stockCompare(entry, stockChangeType, skuMap);
                entry.getValue().setSpecs(storageSku.getSpecs());

            }
            StorageManagementOrderItem orderItem = new StorageManagementOrderItem();
            orderItem
                    .setOrderNo(no)
                    .setProductId(storageManagementOrderItem.getProductId())
                    .setPic(storageManagementOrderItem.getPic())
                    .setProductName(storageManagementOrderItem.getProductName())
                    .setShopId(shopId)
                    .setSkuStockItems(storageManagementOrderItem.getSkuStockItems());
            storageManagementOrderItems.add(orderItem);
        });
    }


    /**
     * 库存计算
     *
     * @param storageManagementOrderItems 仓储管理订单项
     * @param currentStockChangeType      变化类型
     * @return 库存量
     */
    public Long calculateStock(List<StorageManagementOrderItem> storageManagementOrderItems, StockChangeType currentStockChangeType) {
        return storageManagementOrderItems.stream()
                .flatMap(storageManagementOrderItem -> storageManagementOrderItem.getSkuStockItems()
                        .stream())
                .filter(skuStockItem -> skuStockItem.getStockType() == StockType.LIMITED)
                .peek(skuStockItem -> {
                    long num = skuStockItem.getNum() != null ? skuStockItem.getNum() : 0L;
                    if (currentStockChangeType == StockChangeType.ALLOCATION_OUTBOUND || currentStockChangeType == StockChangeType.OTHER_OUTBOUND) {
                        skuStockItem.setNum((int) -num);
                    }
                }).mapToLong(SkuStockItemDTO::getNum).sum();
    }


    /**
     * 组装Sku map
     *
     * @param storageManagementOrderItem 仓储管理订单子项信息
     * @param shopId                     店铺id
     * @return Map<ActivityShopProductSkuKey, SkuStockItemDTO>
     */
    private Map<ActivityShopProductSkuKey, SkuStockItemDTO> assembleSkuMap(StorageManagementOrderItem storageManagementOrderItem, Long shopId) {
        return storageManagementOrderItem.getSkuStockItems()
                .stream()
                .collect(Collectors.toMap(sku -> {
                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getSkuId());
                    key.setProductId(sku.getProductId()).setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L);
                    return key;
                }, sku -> sku));
    }


    /**
     * 库存比较
     *
     * @param entry           组装entry
     * @param stockChangeType 库存变化类型
     * @param skuMap          库存Map
     */
    private StorageSku stockCompare(Map.Entry<ActivityShopProductSkuKey, SkuStockItemDTO> entry, StockChangeType stockChangeType, Map<ActivityShopProductSkuKey, StorageSku> skuMap) {
        StorageSku storageSku = skuMap.get(entry.getKey());
        if (storageSku == null) {
            throw SystemCode.DATA_NOT_EXIST.exception();
        }
        // 出库比较库存
        if (stockChangeType == StockChangeType.OTHER_OUTBOUND || stockChangeType == StockChangeType.ALLOCATION_OUTBOUND) {
            // 其他出库 盘点出库 取反比较 当前库存是否足够
            StorageError.OUT_OF_STOCK.trueThrow(StockType.UNLIMITED != storageSku.getStockType() && -entry.getValue().getNum() > storageSku.getStock());
        }
        return storageSku;
    }
}
