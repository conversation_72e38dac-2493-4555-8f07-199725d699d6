package com.medusa.gruul.storage.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.mp.IManualTransaction;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.goods.api.enums.GoodsRabbit;
import com.medusa.gruul.storage.api.bo.OrderStockBO;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.bo.StSvBo;
import com.medusa.gruul.storage.api.constant.StorageConstant;
import com.medusa.gruul.storage.api.dto.ProductSkuPriceDTO;
import com.medusa.gruul.storage.api.dto.ProductSkuStockDTO;
import com.medusa.gruul.storage.api.dto.ShopProductKeyDTO;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.enums.StockChangeType;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;
import com.medusa.gruul.storage.service.model.dto.ProductSkuLimitDTO;
import com.medusa.gruul.storage.service.mp.mapper.StorageSkuMapper;
import com.medusa.gruul.storage.service.mp.service.IStorageSkuService;
import com.medusa.gruul.storage.service.mq.service.StorageMqService;
import com.medusa.gruul.storage.service.properties.StorageProperties;
import com.medusa.gruul.storage.service.service.SkuService;
import com.medusa.gruul.storage.service.service.SkuStockDetailService;
import com.medusa.gruul.storage.service.service.SkuStockService;
import com.medusa.gruul.storage.service.util.StorageUtil;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/6/22
 */
@Service
@RequiredArgsConstructor
public class SkuServiceImpl implements SkuService {

    private final SqlSessionFactory sqlSessionFactory;
    private final SkuStockService skuStockService;
    private final StorageProperties storageProperties;
    private final IStorageSkuService storageSkuService;
    private final StorageMqService storageRabbitService;
    private final SkuStockDetailService stockDetailService;
   private final RabbitTemplate rabbitTemplate;
    /**
     * 使用 事务保证一致性
     */
    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = StorageConstant.CACHE_KEY_PRODUCT_SKU_STORAGE, key = "#productId+'-limitLock'")
    public void setProductLimit(Long productId, List<ProductSkuLimitDTO> limits) {
        if (CollUtil.isEmpty(limits)) {
            return;
        }
        Long shopId = ISecurity.userMust().getShopId();
        // 转换成 sku key limit map
        Map<ActivityShopProductSkuKey, ProductSkuLimitDTO> skuKeyLimitMap = getSkuKeyLimitMap(shopId, productId, limits);

        /*更新数据库
         */
        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            StorageSkuMapper mapper = sqlSession.getMapper(StorageSkuMapper.class);
            for (Map.Entry<ActivityShopProductSkuKey, ProductSkuLimitDTO> entry : skuKeyLimitMap.entrySet()) {
                mapper.update(
                        null,
                        Wrappers.lambdaUpdate(StorageSku.class)
                                .set(StorageSku::getLimitType, entry.getValue().getLimitType())
                                .set(StorageSku::getLimitNum, entry.getValue().getLimitNum())
                                .eq(StorageSku::getActivityType, entry.getKey().getActivityType())
                                .eq(StorageSku::getActivityId, entry.getKey().getActivityId())
                                .eq(StorageSku::getShopId, entry.getKey().getShopId())
                                .eq(StorageSku::getProductId, entry.getKey().getProductId())
                                .eq(StorageSku::getId, entry.getKey().getSkuId())
                );
            }
            sqlSession.commit();
        }
        //更新缓存
        skuStockService.getSkusBatch(skuKeyLimitMap.keySet());
        //批量更新缓存
        RedisUtil.executePipelined(
                redisOperations -> {
                    HashOperations hashOperations = redisOperations.opsForHash();
                    for (Map.Entry<ActivityShopProductSkuKey, ProductSkuLimitDTO> entry : skuKeyLimitMap.entrySet()) {
                        String skuCacheKey = StorageUtil.generateSkuRedisKey(entry.getKey());
                        hashOperations.put(skuCacheKey, StorageConstant.FIELD_SKU_STORAGE_LIMIT_TYPE, entry.getValue().getLimitType());
                        hashOperations.put(skuCacheKey, StorageConstant.FIELD_SKU_STORAGE_LIMIT_NUM, entry.getValue().getLimitNum());
                    }
                }
        );

    }


    /**
     * 更新商品sku价格
     *
     * @param productId       商品id
     * @param productSkuPrice 商品sku价格
     */
    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = StorageConstant.CACHE_KEY_PRODUCT_SKU_STORAGE, key = "#productId+'-priceLock'")
    public void updateSkuPrice(Long productId, List<ProductSkuPriceDTO> productSkuPrice) {
        Long shopId = ISecurity.shopIdOrISysMust();

        // 转换成 sku key productSkuPrice map
        Map<ActivityShopProductSkuKey, ProductSkuPriceDTO> skuKeyPriceMap = getSkuKeyPriceMap(shopId, productId, productSkuPrice);

        // sqlSession.commit() 执行更新
        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            StorageSkuMapper mapper = sqlSession.getMapper(StorageSkuMapper.class);
            for (Map.Entry<ActivityShopProductSkuKey, ProductSkuPriceDTO> entry : skuKeyPriceMap.entrySet()) {
                mapper.update(
                        null,
                        Wrappers.lambdaUpdate(StorageSku.class)
                                .set(StorageSku::getPrice, entry.getValue().getPrice())
                                .set(StorageSku::getSalePrice, entry.getValue().getSalePrice())
                                .eq(StorageSku::getActivityType, entry.getKey().getActivityType())
                                .eq(StorageSku::getActivityId, entry.getKey().getActivityId())
                                .eq(StorageSku::getShopId, entry.getKey().getShopId())
                                .eq(StorageSku::getProductId, entry.getKey().getProductId())
                                .eq(StorageSku::getId, entry.getKey().getSkuId())
                );
            }
            sqlSession.commit();
        }
        //更新缓存
        skuStockService.getSkusBatch(skuKeyPriceMap.keySet());
        //批量更新缓存
        RedisUtil.executePipelined(
                redisOperations -> {
                    HashOperations hashOperations = redisOperations.opsForHash();
                    for (Map.Entry<ActivityShopProductSkuKey, ProductSkuPriceDTO> entry : skuKeyPriceMap.entrySet()) {
                        String skuCacheKey = StorageUtil.generateSkuRedisKey(entry.getKey());
                        hashOperations.put(skuCacheKey, StorageConstant.FIELD_SKU_STORAGE_SALE_PRICE, entry.getValue().getSalePrice());
                        hashOperations.put(skuCacheKey, StorageConstant.FIELD_SKU_STORAGE_PRICE, entry.getValue().getPrice());
                        if (null != entry.getValue().getSupplierPrice()) {
                            hashOperations.put(skuCacheKey, StorageConstant.FIELD_SKU_STORAGE_SUPPLIER_PRICE, entry.getValue().getSupplierPrice());
                        }
                    }
                }
        );
        List<ProductSkusVO.SkuVO> productSkus = storageSkuService.getProductSkusByShopProductKeys(Collections.singletonList(new ShopProductKeyDTO()
                .setShopId(shopId)
                .setProductId(productId)));
        ProductSkusVO productSkusVO = new ProductSkusVO()
                .setSkus(productSkus)
                .setShopId(shopId)
                .setProductId(productId);
        storageRabbitService.sendUpdateSkuPriceMsg(productSkusVO);

    }

    private Map<ActivityShopProductSkuKey, ProductSkuPriceDTO> getSkuKeyPriceMap(Long shopId, Long productId, List<ProductSkuPriceDTO> productSkuPrices) {
        return productSkuPrices.stream()
                .collect(
                        Collectors.toMap(
                                productSkuPrice -> {
                                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(productSkuPrice.getSkuId());
                                    key.setProductId(productId)
                                            .setShopId(shopId)
                                            .setActivityType(OrderType.COMMON)
                                            .setActivityId(0L);
                                    return key;
                                },
                                v -> v
                        )
                );
    }


    public Map<ActivityShopProductSkuKey, ProductSkuLimitDTO> getSkuKeyLimitMap(Long shopId, Long productId, List<ProductSkuLimitDTO> limits) {
        return limits.stream()
                .collect(
                        Collectors.toMap(
                                limit -> {
                                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(limit.getSkuId());
                                    key.setProductId(productId).setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L);
                                    return key;
                                },
                                v -> v
                        )
                );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSkuStock(Long productId, List<ProductSkuStockDTO> skuStocks) {
        Long shopId = ISecurity.shopIdOrISysMust();
        // 转换成 sku key对应的map
        Map<ActivityShopProductSkuKey, Optional<ProductSkuStockDTO>> skuKeyProductStockMap = skuStocks.stream()
                .collect(
                        Collectors.groupingBy(
                                sku -> {
                                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getSkuId());
                                    key.setProductId(productId).setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L);
                                    return key;
                                },
                                Collectors.reducing(
                                        (current, next) -> {
                                            current.setNum(current.getNum() + next.getNum());
                                            return current;
                                        }
                                )
                        )
                );
        //转换成 sku key 与销量 set
        Set<SkuKeyStSvBO> skuKeyStSvs = this.toSkyKeyStSvs(skuKeyProductStockMap);
        skuStockService.getSkusBatch(skuKeyProductStockMap.keySet());
        //更新缓存
        skuStockService.updateSkuStockCache(skuKeyStSvs);
        //发送 mq
        storageRabbitService.sendUpdateStockMsg(skuKeyStSvs);
        //更新库存操作明细
        stockDetailService.generateStockDetails(List.of(new OrderStockBO().setSkuKeyStSvs(skuKeyStSvs)));

        List<ActivityShopProductSkuKey> activityShopProductSkuKeys = new ArrayList<>();
        skuStocks.stream().forEach(each->{
            ActivityShopProductSkuKey activityShopProductSkuKey = new ActivityShopProductSkuKey();
            activityShopProductSkuKey.setSkuId(each.getSkuId()).setProductId(productId).setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L);
            activityShopProductSkuKeys.add(activityShopProductSkuKey);
        });
        IManualTransaction.afterCommit(() -> {
            rabbitTemplate.convertAndSend(GoodsRabbit.GOODS_BATCH_UPDATE_SELL_STATUS.exchange(),GoodsRabbit.GOODS_BATCH_UPDATE_SELL_STATUS.routingKey(),activityShopProductSkuKeys);
        });
    }

    /**
     * 更新 修改库存更新 缓存方法
     */
    private Set<SkuKeyStSvBO> toSkyKeyStSvs(Map<ActivityShopProductSkuKey, Optional<ProductSkuStockDTO>> skuKeyProductStockMap) {
        skuStockService.getSkusBatch(skuKeyProductStockMap.keySet());
        return skuKeyProductStockMap.entrySet()
                .stream()
                .map(
                        entry -> {
                            Optional<ProductSkuStockDTO> value = entry.getValue();
                            if (value.isEmpty()) {
                                return null;
                            }
                            ProductSkuStockDTO productSkuStock = value.get();
                            return new SkuKeyStSvBO()
                                    .setStockChangeType(productSkuStock.getNum() > 0 ? StockChangeType.EDITED_INBOUND : StockChangeType.EDITED_OUTBOUND)
                                    .setKey(entry.getKey())
                                    .setStSv(new StSvBo()
                                            .setStockType(productSkuStock.getStockType())
                                            .setStock(productSkuStock.getNum())
                                            .setSales(0)
                                    );

                        }
                ).collect(Collectors.toSet());
    }


    @Override
    public StorageSku getProductSku(ActivityShopProductSkuKey shopProductSkuKey) {
        Long shopId = shopProductSkuKey.getShopId();
        Long productId = shopProductSkuKey.getProductId();
        Long skuId = shopProductSkuKey.getSkuId();
        OrderType activityType = shopProductSkuKey.getActivityType();
        Long activityId = shopProductSkuKey.getActivityId();
        return RedisUtil.getCacheMap(
                StorageSku.class,
                () -> storageSkuService.lambdaQuery()
                        .eq(StorageSku::getShopId, shopId)
                        .eq(StorageSku::getProductId, productId)
                        .eq(StorageSku::getId, skuId)
                        .eq(StorageSku::getActivityType, activityType)
                        .eq(StorageSku::getActivityId, activityId)
                        .one(),
                Duration.ofSeconds(RedisUtil.expireWithRandom(storageProperties.getSkuExpireTime())),
                Duration.ofMinutes(CommonPool.NUMBER_THIRTY),
                StorageUtil.generateSkuRedisKey(shopProductSkuKey)
        );
    }


}
