package com.medusa.gruul.storage.service.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 
 *
 * <AUTHOR>
 * date 2022/7/6
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "gruul.storage")
public class StorageProperties {

    /**
     * sku缓存信息过期时间 单位:秒
     */
    private long skuExpireTime = 2 * 24 * 60 * 60;
    /**
     * task线程池配置
     */
    private TaskThreadPool taskThreadPool = new TaskThreadPool();

    /**
     * 线程池配置详情
     */
    @Getter
    @Setter
    public static class TaskThreadPool {

        /**
         * 线程池线程名前缀
         */
        private String threadNamePrefix = "Storage-Future";
        /**
         * 核心线程数
         */
        private int corePoolSize = 10;
        /**
         * 最大线程数
         */
        private int maxPoolSize = 25;
        /**
         * 线程存活时间长度
         */
        private int keepAliveSeconds = 60;
        /**
         * 任务队列长度
         */
        private int queueCapacity = 800;
    }

}
