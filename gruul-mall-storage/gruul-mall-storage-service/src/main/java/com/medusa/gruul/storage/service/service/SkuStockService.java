package com.medusa.gruul.storage.service.service;

import com.medusa.gruul.common.model.base.ActivityShopProductKey;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.goods.api.model.dto.ProductDeleteDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.entity.StorageSku;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品仓储服务
 *
 * <AUTHOR>
 * date 2022/6/21
 */
public interface SkuStockService {


    /**
     * 批量更新sku缓存 库存与销量
     *
     * @param skuKeyStSvs sku key与 库存销量
     */
    void updateSkuStockCache(Set<SkuKeyStSvBO> skuKeyStSvs);

    /**
     * 批量更新数据库存与销量
     * 1。加锁 同时锁住多个key
     * 2。获取批量执行器 批量更新数据库
     *
     * @param skuKeyStSvs sku key与 库存销量
     * <AUTHOR>
     */
    void updateSkuStockDb(Set<SkuKeyStSvBO> skuKeyStSvs);

    /**
     * 删除库存
     *
     * @param productDeleteDTO 商品删除dto
     */
    void productDeleteStorageSku(ProductDeleteDTO productDeleteDTO);


    /**
     * 批量关闭订单
     *
     * @param orderInfos 订单与订单库存信息
     */
    void orderCloseHandBatch(List<OrderInfo> orderInfos);


    /**
     * 批量查询缓存库存 若库存中不存在 则直接查询数据库
     * 数据库的数据将批量放到缓存里 当前需要保证 返回的结果已被缓存
     *
     * @param keys 店铺商品sku关键信息
     * @return 库存列表
     */
    Map<ActivityShopProductSkuKey, StorageSku> getSkusBatch(Set<ActivityShopProductSkuKey> keys);


    /**
     * 批量查询商品sku库存
     *
     * @param shopProductKeys 店铺id与商品id 组成的key集合
     * @return sku库存列表
     */
    Map<ActivityShopProductKey, List<StorageSku>> productSkuStockBatch(Set<ActivityShopProductKey> shopProductKeys);

}
