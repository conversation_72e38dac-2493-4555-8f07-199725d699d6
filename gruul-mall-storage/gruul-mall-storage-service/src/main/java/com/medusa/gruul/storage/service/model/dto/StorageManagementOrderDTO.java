package com.medusa.gruul.storage.service.model.dto;

import com.medusa.gruul.storage.api.enums.StockChangeType;
import com.medusa.gruul.storage.service.model.enums.StorageManagementOrderType;
import com.medusa.gruul.storage.service.mp.entity.StorageManagementOrderItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 仓储管理订单DTO
 *
 * <AUTHOR>
 * @Description StorageManagementOrderDTO.java
 * @date 2023-07-25 16:51
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class StorageManagementOrderDTO {
    private Long id;

    /**
     * 变化类型
     */

    private StockChangeType stockChangeType;


    /**
     * 凭证
     */
    private List<String> evidence;


    /**
     * 盘点区域
     */
    private String inventoryArea;


    /**
     * 库存管理订单类型
     */
    private StorageManagementOrderType storageManagementOrderType;


    /**
     * 备注
     */
    private String remark;


    /**
     * 商品sku仓储DTO
     */
    @Valid
    @Size(max = 10)
    @Size(min = 1)
    private List<StorageManagementOrderItem> storageManagementOrderItems;


}
