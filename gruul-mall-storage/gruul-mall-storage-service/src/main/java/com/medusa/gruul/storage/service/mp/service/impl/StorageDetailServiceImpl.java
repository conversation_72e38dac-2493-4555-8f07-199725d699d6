package com.medusa.gruul.storage.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.storage.service.mp.entity.StorageDetail;
import com.medusa.gruul.storage.service.mp.mapper.StorageDetailMapper;
import com.medusa.gruul.storage.service.mp.service.IStorageDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Description
 * @date 2023-07-27 14:44
 */
@Service
@RequiredArgsConstructor
public class StorageDetailServiceImpl extends ServiceImpl<StorageDetailMapper, StorageDetail> implements IStorageDetailService {
}
