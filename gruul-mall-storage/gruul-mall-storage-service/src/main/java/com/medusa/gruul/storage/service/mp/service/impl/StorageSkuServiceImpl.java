package com.medusa.gruul.storage.service.mp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.client.utils.TenantUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.medusa.gruul.common.model.base.ActivityShopKey;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.goods.api.model.vo.SupplierGoodsSkuVO;
import com.medusa.gruul.goods.api.model.vo.SupplierProductSkuVO;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.storage.api.dto.ShopProductKeyDTO;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.vo.ProductSaleVolumeVO;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;
import com.medusa.gruul.storage.api.vo.ProductStatisticsVO;
import com.medusa.gruul.storage.service.mp.mapper.StorageSkuMapper;
import com.medusa.gruul.storage.service.mp.service.IStorageSkuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商品 仓储 表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-21
 */
@Service
@RequiredArgsConstructor
public class StorageSkuServiceImpl extends ServiceImpl<StorageSkuMapper, StorageSku> implements IStorageSkuService {

	private final GoodsRpcService goodsRpcService;

	@Override
	public List<ProductStatisticsVO> getProductStatisticsList(boolean includeActivityStock, Set<ShopProductKey> shopProductKeys) {
		return baseMapper.getProductStatisticsList(includeActivityStock, shopProductKeys);
	}

	/**
	 * 获取店铺商品销量
	 *
	 * @param shopId 店铺id
	 * @param size   查询数量
	 * @return 店铺热销商品
	 */
	@Override
	public List<ProductSaleVolumeVO> getShopProductSaleVolume(Long shopId, Long size) {
		return baseMapper.getShopProductSaleVolume(shopId, size == null ? 4L : 5L);
	}

	/**
	 * 获取商品规格信息
	 *
	 * @param shopProductKeys 店铺与商品id集合
	 * @return 商品规格信息
	 */
	@Override
	public List<ProductSkusVO.SkuVO> getProductSkusByShopProductKeys(List<ShopProductKeyDTO> shopProductKeys) {
		return baseMapper.getProductSkusByShopProductKeys(shopProductKeys);
	}

	@Override
	public List<StorageSku> getSkusBatch(Set<ActivityShopProductSkuKey> keys) {
		return baseMapper.getSkus(keys);
	}


	@Override
	public Map<ActivityShopProductSkuKey, StorageSku> getSkusBatchMap(Set<ActivityShopProductSkuKey> keys) {
		//根据productId分组，获取goodsAddonSupporter.getSupperSkuByProductId，返回Map<productId, List<SupplierGoodsSkuVO>>
		Map<Long, List<SupplierGoodsSkuVO>> supplierGoodsSkuVOMap = Maps.newHashMap();
		//供应商产品
		Map<Long, List<SupplierProductSkuVO>> supplierProductSkuVOMap = Maps.newHashMap();
		TenantShop.disable(() -> keys.stream().collect(Collectors.groupingBy(ActivityShopProductSkuKey::getProductId)).forEach((k, v) -> {
            List<SupplierGoodsSkuVO> supperSkus = goodsRpcService.getSupperSkuByProductId(k, v.get(0).getShopId());
            supplierGoodsSkuVOMap.put(k, supperSkus);

            List<SupplierProductSkuVO> supplierProductSkus = goodsRpcService.getSupplierSkuByProductId(k);
            if (ObjectUtil.isNotEmpty(supplierProductSkus)) {
                supplierProductSkuVOMap.put(k, supplierProductSkus);
            }

        }));
		return this.toSkuKeyMap(this.getSkusBatch(keys), supplierGoodsSkuVOMap, supplierProductSkuVOMap);
	}


	@Override
	public List<StorageSku> getSkusBatchByPrefix(boolean haveProductId, Set<ActivityShopKey> keys) {
		return baseMapper.getSkusByPrefix(haveProductId, keys);
	}

	@Override
	public Map<ActivityShopProductSkuKey, StorageSku> getSkusBatchByPrefixMap(boolean haveProductId, Set<ActivityShopKey> keys) {
		return this.toSkuKeyMap(this.getSkusBatchByPrefix(haveProductId, keys));
	}

	@Override
	public void deleteSkusByPrefix(Set<ActivityShopKey> keys) {
		baseMapper.deleteSkusByPrefix(keys);
	}

    @Override
    public List<ProductStatisticsVO> getShopSales(Boolean sortAsc) {
		return baseMapper.getShopSales(sortAsc);
    }

	@Override
	public Long getShopSaleVolume(Long shopId) {
		return baseMapper.getShopSaleVolume(shopId);
	}

	private Map<ActivityShopProductSkuKey, StorageSku> toSkuKeyMap(List<StorageSku> skus) {
		return skus.stream()
				.collect(
						Collectors.toMap(
								sku -> {
									ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getId());
									key.setProductId(sku.getProductId()).setShopId(sku.getShopId()).setActivityType(sku.getActivityType()).setActivityId(sku.getActivityId());
									return key;
								},
								v -> v
						)
				);
	}
	//增加组合供应商价格
	private Map<ActivityShopProductSkuKey, StorageSku> toSkuKeyMap(List<StorageSku> skus
			, Map<Long, List<SupplierGoodsSkuVO>> supplierGoodsSkuVOMap
			, Map<Long, List<SupplierProductSkuVO>> supplierProductSkuVOMap) {
		return skus.stream()
				.collect(
						Collectors.toMap(
								sku -> {
									if (supplierGoodsSkuVOMap.containsKey(sku.getProductId())) {
										supplierGoodsSkuVOMap.get(sku.getProductId()).forEach(supplierGoodsSkuVO -> {
											if (sku.getId().equals(supplierGoodsSkuVO.getSkuId())) {
												sku.setSupplierPrice(supplierGoodsSkuVO.getPrice());
											}
										});
									}
									if (supplierProductSkuVOMap.containsKey(sku.getProductId())) {
										supplierProductSkuVOMap.get(sku.getProductId()).forEach(supplierProductSkuVO -> {
											if (sku.getId().equals(supplierProductSkuVO.getStorageSkuId())) {
												sku.setSupplierProductId(supplierProductSkuVO.getSupplierProductId());
											}
										});
									}
									ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getId());
									key.setProductId(sku.getProductId()).setShopId(sku.getShopId()).setActivityType(sku.getActivityType()).setActivityId(sku.getActivityId());
									return key;
								},
								v -> v
						)
				);
	}


}
