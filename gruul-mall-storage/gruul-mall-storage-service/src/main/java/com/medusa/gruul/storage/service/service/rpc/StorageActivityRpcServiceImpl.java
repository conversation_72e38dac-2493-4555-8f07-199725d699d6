package com.medusa.gruul.storage.service.service.rpc;

import cn.hutool.core.collection.CollUtil;
import com.medusa.gruul.common.model.base.ActivityShopKey;
import com.medusa.gruul.common.model.base.ActivityShopProductKey;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.IManualTransaction;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.search.api.model.ProductActivityBind;
import com.medusa.gruul.search.api.model.ProductActivityUnbind;
import com.medusa.gruul.search.api.rpc.SearchRpcService;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.bo.StSvBo;
import com.medusa.gruul.storage.api.dto.activity.ActivityCloseKey;
import com.medusa.gruul.storage.api.dto.activity.ActivityCreateDTO;
import com.medusa.gruul.storage.api.dto.activity.ActivitySkuDTO;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.enums.LimitOrderType;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import com.medusa.gruul.storage.api.rpc.StorageActivityRpcService;
import com.medusa.gruul.storage.service.mp.service.IStorageSkuService;
import com.medusa.gruul.storage.service.mq.service.StorageMqService;
import com.medusa.gruul.storage.service.service.SkuStockService;
import com.medusa.gruul.storage.service.util.StorageUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2023/3/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class StorageActivityRpcServiceImpl implements StorageActivityRpcService {

    private final SearchRpcService searchRpcService;
    private final SkuStockService skuStockService;
    private final IStorageSkuService storageSkuService;
    private final StorageMqService storageRabbitService;

    /**
     * 活动sku库存预处理
     *
     * @param activityCreateDTO 活动库存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityCreate(ActivityCreateDTO activityCreateDTO) {
        List<ActivitySkuDTO> stockSkus = activityCreateDTO.getSkus();
        if (CollUtil.isEmpty(stockSkus)) {
            return;
        }
        Long shopId = activityCreateDTO.getShopId();

        //转成对应的key
        Set<ActivityShopProductSkuKey> skuKeys = stockSkus.stream()
                .map(sku -> {
                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getSkuId());
                    key.setProductId(sku.getProductId()).setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L);
                    return key;
                })
                .collect(Collectors.toSet());
        // 查询未参加活动的 sku数九 根据key集合查询sku信息 并转成 map
        Map<ActivityShopProductSkuKey, StorageSku> skuMap = skuStockService.getSkusBatch(skuKeys);
        //转成key对应库存map
        Map<ActivityShopProductSkuKey, ActivitySkuDTO> keyStockMap = stockSkus.stream()
                .collect(
                        Collectors.toMap(
                                sku -> {
                                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getSkuId());
                                    key.setProductId(sku.getProductId()).setShopId(shopId).setActivityType(OrderType.COMMON).setActivityId(0L);
                                    return key;
                                },
                                sku -> sku
                        )
                );
        //生成库存数据
        OrderType activityType = activityCreateDTO.getActivityType();
        Long activityId = activityCreateDTO.getActivityId();
        boolean success = storageSkuService.saveBatch(
                keyStockMap.entrySet()
                        .stream()
                        .map(entry -> {
                                    ActivityShopProductSkuKey key = entry.getKey();
                                    StorageSku storageSku = skuMap.get(key);
                                    ActivitySkuDTO sku = entry.getValue();
                                    StorageSku newStorageSku = new StorageSku()
                                            .setActivityType(activityType)
                                            .setActivityId(activityId)
                                            .setShopId(shopId)
                                            .setProductId(sku.getProductId())
                                            .setStockType(StockType.LIMITED)
                                            .setStock(Long.valueOf(sku.getStock()))
                                            .setSalesVolume(0L)
                                            .setInitSalesVolume(0L)
                                            .setLimitType(LimitType.UNLIMITED)
                                            .setLimitNum(0)
                                            .setLimitOrderNum(0)
                                            .setLimitOrderType(LimitOrderType.UNLIMITED)
                                            .setSpecs(storageSku.getSpecs())
                                            .setImage(storageSku.getImage())
                                            .setPrice(storageSku.getPrice())
                                            .setSalePrice(sku.getSalePrice() == null ? storageSku.getSalePrice() : sku.getSalePrice())
                                            .setWeight(storageSku.getWeight());
                                    newStorageSku.setId(storageSku.getId());
                                    return newStorageSku;
                                }
                        ).toList()
        );
        SystemCode.DATA_ADD_FAILED.falseThrow(success);

        //批量校验redis中的库存并扣库存
        Set<SkuKeyStSvBO> skuKeyStSvs = keyStockMap.entrySet()
                .stream()
                .map(entry -> new SkuKeyStSvBO().setKey(entry.getKey()).setStSv(new StSvBo().setStock(-entry.getValue().getStock())))
                .collect(Collectors.toSet());
        skuStockService.updateSkuStockCache(skuKeyStSvs);
        //是否与其他活动互斥
        if (activityType.isExclusive()) {
            try {
                searchRpcService.activityBind(
                        new ProductActivityBind()
                                .setActivityType(activityType)
                                .setActivityId(activityId)
                                .setShopId(shopId)
                                .setProductIds(stockSkus.stream().map(ActivitySkuDTO::getProductId).collect(Collectors.toSet()))
                                .setStartTime(activityCreateDTO.getStartTime())
                                .setEndTime(activityCreateDTO.getEndTime())
                );
            } catch (Exception ex) {
                //回滚缓存库存
                skuKeyStSvs.forEach(skuKeyStSv -> skuKeyStSv.getStSv().setStock(-skuKeyStSv.getStSv().getStock()));
                skuStockService.updateSkuStockCache(skuKeyStSvs);
                throw ex;
            }
        }
        //发送扣库存 扣除数据库sku的库存 mq消息
        IManualTransaction.afterCommit(
                () -> storageRabbitService.sendUpdateStockMsg(skuKeyStSvs)
        );
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityClose(Set<ActivityCloseKey> activityCloses) {
        if (CollUtil.isEmpty(activityCloses)) {
            return;
        }
        //从数据库中查询数据
        Set<ActivityShopKey> keyPrefix = activityCloses.stream()
                .map(close -> {
                    ActivityShopKey key = new ActivityShopProductKey();
                    key.setShopId(close.getShopId()).setActivityType(close.getActivityType()).setActivityId(close.getActivityId());
                    return key;
                })
                .collect(Collectors.toSet());
        Map<ActivityShopProductSkuKey, StorageSku> skusBatchByPrefixMap = storageSkuService.getSkusBatchByPrefixMap(false, keyPrefix);
        if (CollUtil.isEmpty(skusBatchByPrefixMap)) {
            return;
        }
        Set<ActivityShopProductSkuKey> skuKeys = skusBatchByPrefixMap.keySet();
        //所有需要删除的缓存key
        Set<String> allKeys = skuKeys.stream()
                .map(StorageUtil::generateSkuRedisKey)
                .collect(Collectors.toSet());
        //需要归还至普通商品的库存和销量
        Set<SkuKeyStSvBO> skuKeyStSvs = skusBatchByPrefixMap.entrySet()
                .stream()
                .map(entry -> {
                    StorageSku sku = entry.getValue();
                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getId());
                    key.setProductId(sku.getProductId()).setShopId(entry.getKey().getShopId()).setActivityType(OrderType.COMMON).setActivityId(0L);
                    return new SkuKeyStSvBO()
                            .setKey(key)
                            .setStSv(new StSvBo().setStock(sku.getStock().intValue()));
                }).collect(Collectors.toSet());
        //删除redis中的数据
        RedisUtil.doubleDeletion(
                () -> {
                    //批量删除数据库数据
                    storageSkuService.deleteSkusByPrefix(keyPrefix);
                    // 批量查询sku库存数据
                    skuStockService.getSkusBatch(skuKeyStSvs.stream().map(SkuKeyStSvBO::getKey).collect(Collectors.toSet()));
                    //批量更新sku缓存库存数据
                    skuStockService.updateSkuStockCache(skuKeyStSvs);
                    //发送归还库存的 mq消息
                    storageRabbitService.sendUpdateStockMsg(skuKeyStSvs);

                },
                () -> RedisUtil.delete(allKeys)
        );
        Set<ProductActivityUnbind> unbinds = activityCloses.stream()
                .filter(activityClose -> activityClose.getActivityType().isExclusive())
                .map(activityClose -> new ProductActivityUnbind()
                        .setActivityType(activityClose.getActivityType())
                        .setActivityId(activityClose.getActivityId())
                        .setShopId(activityClose.getShopId())
                )
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(unbinds)) {
            searchRpcService.activityUnbind(unbinds);
        }
    }

}
