package com.medusa.gruul.storage.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.medusa.gruul.common.fastjson2.FastJson2;
import com.medusa.gruul.common.model.base.ActivityShopProductKey;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.goods.api.model.dto.ProductDeleteDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.order.api.pojo.SkuStock;
import com.medusa.gruul.storage.api.bo.OrderStockBO;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.bo.StSvBo;
import com.medusa.gruul.storage.api.constant.StorageConstant;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.enums.StockChangeType;
import com.medusa.gruul.storage.api.enums.StockType;
import com.medusa.gruul.storage.service.model.enums.StorageError;
import com.medusa.gruul.storage.service.mp.mapper.StorageSkuMapper;
import com.medusa.gruul.storage.service.mp.service.IStorageSkuService;
import com.medusa.gruul.storage.service.mq.service.StorageMqService;
import com.medusa.gruul.storage.service.properties.StorageProperties;
import com.medusa.gruul.storage.service.service.SkuStockDetailService;
import com.medusa.gruul.storage.service.service.SkuStockService;
import com.medusa.gruul.storage.service.util.LazyLoadScript;
import com.medusa.gruul.storage.service.util.StorageUtil;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.medusa.gruul.storage.service.util.StorageUtil.generateSkuLockStockKey;

/**
 * <AUTHOR>
 * date 2022/6/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SkuStockServiceImpl implements SkuStockService {
    private final StorageProperties storageProperties;
    private final RedissonClient redissonClient;
    private final SqlSessionFactory sqlSessionFactory;
    private final IStorageSkuService storageSkuService;
    private final StringRedisTemplate stringRedisTemplate;
    private final StorageMqService storageRabbitService;
    private final SkuStockDetailService stockDetailService;
    private static final RedisTemplate redisTemplate = RedisUtil.getRedisTemplate();

    /**
     * 渲染所有 redis lua脚本的key集合和对应参数
     *
     * @param skuKeyStSvs sku key与 库存销量
     * @return 校验结果
     */
    public Tuple3<List<String>, Object[], SkuKeyStSvBO[]> renderLuaKeysAndArgs(Set<SkuKeyStSvBO> skuKeyStSvs) {
        int size = skuKeyStSvs.size();
        Object[] stSvs = new Object[size];
        List<String> redisKeys = new ArrayList<>(size);
        SkuKeyStSvBO[] skuKeyStSvMap = new SkuKeyStSvBO[size];
        int index = 0;
        for (SkuKeyStSvBO skuKeyStSv : skuKeyStSvs) {
            redisKeys.add(StorageUtil.generateSkuRedisKey(skuKeyStSv.getKey()));
            stSvs[index] = JSON.toJSONString(skuKeyStSv.getStSv());
            skuKeyStSvMap[index] = skuKeyStSv;
            index++;
        }
        return Tuple.of(redisKeys, stSvs, skuKeyStSvMap);
    }

    @Override
    public void updateSkuStockCache(Set<SkuKeyStSvBO> skuKeyStSvs) {
        //渲染所有 redis key集合 和 下标对应的库存 数组
        Tuple3<List<String>, Object[], SkuKeyStSvBO[]> redisKeyAndStocks = this.renderLuaKeysAndArgs(skuKeyStSvs);
        //执行lua脚本扣库存
        //使用Redis事务执行Lua脚本扣库存
        List<String> keys = redisKeyAndStocks._1;
        Long success = stringRedisTemplate.execute(
                LazyLoadScript.UpdateStockAndSalesScript.SCRIPT,
                redisKeyAndStocks._1(),
                redisKeyAndStocks._2()
        );
        //更新成功
        if (success == null || success == 0 || success == keys.size()) {
            return;
        }
        int index = success.intValue();
        //更新失败，需要回滚
        this.rollback((SkuKeyStSvBO[]) ArrayUtil.copy(redisKeyAndStocks._3, new SkuKeyStSvBO[index], index));
        //抛出异常
        throw StorageError.OUT_OF_STOCK.dataEx(redisKeyAndStocks._2()[index]);
    }

    private void rollback(SkuKeyStSvBO[] skuKeyStSvArray) {
        Set<SkuKeyStSvBO> skuKeyStSvs = new HashSet<>();
        //库存销量取反
        for (SkuKeyStSvBO skuKeyStSvBO : skuKeyStSvArray) {
            StSvBo stSv = skuKeyStSvBO.getStSv();
            stSv.setStock(-stSv.getStock());
            stSv.setSales(-stSv.getSales());
            skuKeyStSvs.add(skuKeyStSvBO);
        }
        this.updateSkuStockCache(skuKeyStSvs);
    }

    /**
     * 批量更新库存与销量
     * 1。加锁 同时锁住多个key
     * 2。获取批量执行器 批量更新数据库
     *
     * @param skuKeyStSvs sku key与 库存销量
     * <AUTHOR>
     */
    @Override
    public void updateSkuStockDb(Set<SkuKeyStSvBO> skuKeyStSvs) {
        log.warn(skuKeyStSvs.toString());
        //批量锁
        RLock multiLock = redissonClient.getMultiLock(
                skuKeyStSvs.stream()
                        .map(SkuKeyStSvBO::getKey)
                        .map(StorageUtil::generateRedissonLockKey)
                        .map(redissonClient::getLock)
                        .toArray(RLock[]::new)
        );
        //加锁
        multiLock.lock();
        try {
            //批量执行器
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
            //批量更新数据库库存数据 并归还库存和销量
            try {
                StorageSkuMapper mapper = sqlSession.getMapper(StorageSkuMapper.class);
                for (SkuKeyStSvBO skuKeyStSv : skuKeyStSvs) {
                    ActivityShopProductSkuKey key = skuKeyStSv.getKey();
                    StSvBo value = skuKeyStSv.getStSv();
                    StockType stockType = value.getStockType();

                    LambdaUpdateWrapper<StorageSku> updateWrapper = Wrappers.lambdaUpdate(StorageSku.class)
                            //当库存类型为空时 表示为库存的消费 需要判断库存类型是否限制
                            .setSql(stockType == null, StrUtil.format(StorageConstant.SQL_STOCK_INCREMENT_SQL_WITH_STOCK_TYPE_TEMPLATE, value.getStock()))
                            //库存类型不为空 且库存类型为限制时 需要更新库存
                            .setSql(StockType.LIMITED == stockType, StrUtil.format(StorageConstant.SQL_STOCK_INCREMENT_SQL_TEMPLATE, value.getStock()))
                            .setSql(StrUtil.format(StorageConstant.SQL_SALES_VOLUME_INCREMENT_SQL_TEMPLATE, value.getSales()))
                            .eq(StorageSku::getActivityType, key.getActivityType())
                            .eq(StorageSku::getActivityId, key.getActivityId())
                            .eq(StorageSku::getShopId, key.getShopId())
                            .eq(StorageSku::getProductId, key.getProductId())
                            .eq(StorageSku::getId, key.getSkuId())
                            .and(value.getStock() < 0, qw -> qw.last(StrUtil.format(StorageConstant.SQL_STOCK_CONDITION_SQL_TEMPLATE, -value.getStock())));
                    mapper.update(null, updateWrapper);
                }
                sqlSession.commit();
            } catch (Exception exception) {
                log.error("库存操作失败", exception);
                sqlSession.rollback();
                throw SystemCode.DATA_UPDATE_FAILED.exception();
            } finally {
                sqlSession.close();
            }
        } finally {
            //释放锁
            multiLock.unlock();
        }
    }

    /**
     * 删除库存
     *
     * @param productDeleteDTO 商品删除数据关键key
     */
    @Override
    public void productDeleteStorageSku(ProductDeleteDTO productDeleteDTO) {
        //删除redis中的数据
        Set<String> keys = new HashSet<>(CommonPool.NUMBER_THIRTY);
        productDeleteDTO.getProductIds().forEach(productId -> {
            String cacheKey = RedisUtil.key(StorageConstant.CACHE_KEY_PRODUCT_SKU_STORAGE, productDeleteDTO.getShopId(), CommonPool.NUMBER_ZERO, productId, "*");
            keys.add(cacheKey);
        });
        RedisUtil.delete(keys);

    }

    /**
     * 更新锁订单库存的缓存
     *
     * @param skuStock
     */
    public void updateSkuStockLock(OrderStockBO skuStock) {
        skuStock.getSkuKeyStSvs().stream().forEach(skuKeyStSvBO -> {
            Integer stockLockNum = RedisUtil.getCacheObject(generateSkuLockStockKey(skuKeyStSvBO.getKey()));
            if (null != stockLockNum && stockLockNum > 0) {
                RedisUtil.expire(generateSkuLockStockKey(skuKeyStSvBO.getKey()), 31, TimeUnit.MINUTES);
                if (stockLockNum < Math.abs(skuKeyStSvBO.getStSv().getStock())) {
                    RedisUtil.setCacheObject(generateSkuLockStockKey(skuKeyStSvBO.getKey()), 0);
                    return;
                }
                redisTemplate.opsForValue().decrement(generateSkuLockStockKey(skuKeyStSvBO.getKey()), Math.abs(skuKeyStSvBO.getStSv().getStock()));
            }
        });

    }

    @Override
    public void orderCloseHandBatch(List<OrderInfo> orderInfos) {
        //库存明细参数
        List<OrderStockBO> orderStocks = new ArrayList<>(orderInfos.size());
        //数据处理
        Map<ActivityShopProductSkuKey, StSvBo> skuKeyStSvMap = new HashMap<>(CommonPool.NUMBER_FIFTEEN);
        for (OrderInfo orderInfo : orderInfos) {
            Long activityId = orderInfo.getActivityId();
            OrderType activityType = orderInfo.getActivityType();
            List<SkuStock> skuStocks = orderInfo.getSkuStocks();
            OrderStockBO currentStock = new OrderStockBO()
                    .setNo(orderInfo.getOrderNo())
                    .setSkuKeyStSvs(new HashSet<>());
            for (SkuStock skuStock : skuStocks) {
                ActivityShopProductSkuKey key = new ActivityShopProductSkuKey();
                key.setSkuId(skuStock.getSkuId()).setProductId(skuStock.getProductId()).setShopId(skuStock.getShopId()).setActivityType(activityType).setActivityId(activityId);
                StSvBo stSvBo = skuKeyStSvMap.computeIfAbsent(key, k -> new StSvBo());
                //库存增加 销量减少
                stSvBo.setStock(stSvBo.getStock() + skuStock.getNum());
                stSvBo.setSales(stSvBo.getSales() - skuStock.getNum());
                //使用新对象 避免逻辑处理是修改源数据
                currentStock.getSkuKeyStSvs().add(
                        new SkuKeyStSvBO()
                                .setKey(key)
                                .setStSv(new StSvBo().setStock(stSvBo.getStock()).setSales(stSvBo.getSales()))
                                .setStockChangeType(StockChangeType.ORDER_CANCELLED_INBOUND)
                );
            }
            orderStocks.add(currentStock);
        }
        //更新锁订单库存的缓存
        orderStocks.stream().forEach(orderStockBO -> this.updateSkuStockLock(orderStockBO));
        Set<ActivityShopProductSkuKey> keys = skuKeyStSvMap.keySet();
        //查询缓存，保证所有正常数据都在缓存中
        Map<ActivityShopProductSkuKey, StorageSku> skusBatch = this.getSkusBatch(keys);

        // 不存在表示原有sku已经被删除 库存归还至普通的sku
        Set<ActivityShopProductSkuKey> newKeys = new HashSet<>();
        keys.stream()
                .filter(key -> !skusBatch.containsKey(key))
                .forEach(key -> {
                    StSvBo stSvBo = skuKeyStSvMap.get(key);
                    skuKeyStSvMap.remove(key);
                    key.setActivityType(OrderType.COMMON).setActivityId(0L);
                    StSvBo commonStSvBo = skuKeyStSvMap.get(key);
                    if (commonStSvBo == null) {
                        skuKeyStSvMap.put(key, stSvBo);
                        newKeys.add(key);
                        return;
                    }
                    commonStSvBo.setStock(commonStSvBo.getStock() + stSvBo.getStock());
                    commonStSvBo.setSales(commonStSvBo.getSales() + stSvBo.getSales());
                });
        //新增key不为空则重新查询缓存
        if (CollUtil.isNotEmpty(newKeys)) {
            this.getSkusBatch(newKeys);
        }
        //不存在表示
        // 批量更新缓存的库存销量
        Set<SkuKeyStSvBO> skuKeyStSvs = skuKeyStSvMap.entrySet()
                .stream()
                .map(entry -> new SkuKeyStSvBO().setKey(entry.getKey()).setStSv(entry.getValue()))
                .collect(Collectors.toSet());
        this.updateSkuStockCache(skuKeyStSvs);
        //发送更新数据库库存mq消息
        storageRabbitService.sendUpdateStockMsg(skuKeyStSvs);
        //保存库存明细
        stockDetailService.generateStockDetails(orderStocks);
    }

    @Override
    public Map<ActivityShopProductSkuKey, StorageSku> getSkusBatch(Set<ActivityShopProductSkuKey> keys) {
        if (CollUtil.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        // 从缓存中获取的SKU
        List<StorageSku> storageSkus = this.getCacheSkuBatch(keys);
        Map<ActivityShopProductSkuKey, StorageSku> skuKeyStorageSkuMap = this.skusToMap(storageSkus);
        //如果数量相等 则直接返回
        if (skuKeyStorageSkuMap.size() == keys.size()) {
            return skuKeyStorageSkuMap;
        }
        Set<ActivityShopProductSkuKey> keySet = keys.stream().filter(key -> !skuKeyStorageSkuMap.containsKey(key)).collect(Collectors.toSet());
        if (CollUtil.isEmpty(keySet)) {
            return skuKeyStorageSkuMap;
        }
        // 从数据库中获取的SKU
        Map<ActivityShopProductSkuKey, StorageSku> dbKeySkuMap = storageSkuService.getSkusBatchMap(keySet);
        //如果数据库查询出的结果为空 则说明数据设置不正确
        if (CollUtil.isEmpty(dbKeySkuMap)) {
            log.error("数据设置不正确，未查询到足够的数据:{}", keySet);
        }
        //将数据库数据放进缓存
        this.cacheSkuBatch(dbKeySkuMap);
        // 合并 缓存和数据哭数据
        HashMap<ActivityShopProductSkuKey, StorageSku> skuKeySkuMap = new HashMap<>(skuKeyStorageSkuMap);
        skuKeySkuMap.putAll(dbKeySkuMap);
        return skuKeySkuMap;
    }

    @Override
    public Map<ActivityShopProductKey, List<StorageSku>> productSkuStockBatch(Set<ActivityShopProductKey> activityShopProductKeys) {
        if (CollUtil.isEmpty(activityShopProductKeys)) {
            return Collections.emptyMap();
        }
        Map<ActivityShopProductSkuKey, StorageSku> skusBatchByPrefixMap = storageSkuService.getSkusBatchByPrefixMap(true, new HashSet<>(activityShopProductKeys));
        if (CollUtil.isEmpty(skusBatchByPrefixMap)) {
            return Collections.emptyMap();
        }
        //从缓存中取数据 去不到再去数据库中取数据
        Map<ActivityShopProductSkuKey, StorageSku> skusBatchMap = this.getSkusBatch(skusBatchByPrefixMap.keySet());
        return skusBatchMap.entrySet().stream()
                .collect(
                        Collectors.groupingBy(
                                entry -> {
                                    ActivityShopProductSkuKey key = entry.getKey();
                                    ActivityShopProductKey activityShopProductKey = new ActivityShopProductKey();
                                    activityShopProductKey.setActivityType(key.getActivityType()).setActivityId(key.getActivityId());
                                    activityShopProductKey.setProductId(key.getProductId()).setShopId(key.getShopId());
                                    return activityShopProductKey;
                                },
                                Collectors.mapping(
                                        Map.Entry::getValue,
                                        Collectors.toList()
                                )
                        )
                );
    }

    /**
     * sku列表转sku map
     *
     * @param storageSkus sku列表
     * @return sku Map
     */
    public Map<ActivityShopProductSkuKey, StorageSku> skusToMap(List<StorageSku> storageSkus) {
        return storageSkus.stream()
                .collect(
                        Collectors.toMap(
                                sku -> {
                                    ActivityShopProductSkuKey key = new ActivityShopProductSkuKey();
                                    key.setSkuId(sku.getId()).setProductId(sku.getProductId()).setShopId(sku.getShopId()).setActivityType(sku.getActivityType()).setActivityId(sku.getActivityId());
                                    return key;
                                },
                                v -> v
                        )
                );
    }


    /**
     * 从缓存中取sku数据列表
     *
     * @param keys sku key集合
     * @return sku数据列表
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public List<StorageSku> getCacheSkuBatch(Set<ActivityShopProductSkuKey> keys) {
        List<Object> caches = RedisUtil.executePipelined(
                redisOperations -> {
                    HashOperations hashOperations = redisOperations.opsForHash();
                    for (ActivityShopProductSkuKey key : keys) {
                        hashOperations.entries(StorageUtil.generateSkuRedisKey(key));
                    }
                }
        );

        return FastJson2.convert(
                caches.stream().filter(cache -> {
                    if (!(cache instanceof Map map)) {
                        return false;
                    }
                    return !map.isEmpty();
                }).toList(), new TypeReference<>() {
                });
    }

    /**
     * 批量缓存sku数据
     *
     * @param dbKeySkuMap key对应 sku map
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void cacheSkuBatch(Map<ActivityShopProductSkuKey, StorageSku> dbKeySkuMap) {
        if (CollUtil.isEmpty(dbKeySkuMap)) {
            return;
        }
        RedisUtil.executePipelined(
                redisOperations -> {
                    HashOperations hashOperations = redisOperations.opsForHash();
                    for (Map.Entry<ActivityShopProductSkuKey, StorageSku> entry : dbKeySkuMap.entrySet()) {
                        String key = StorageUtil.generateSkuRedisKey(entry.getKey());
                        // 将sku数据放入缓存
                        hashOperations.putAll(key, RedisUtil.toBean(entry.getValue(), Map.class));
                        // 设置过期时间
                        redisOperations.expire(key, Duration.ofSeconds(RedisUtil.expireWithRandom(storageProperties.getSkuExpireTime())));
                    }
                }
        );
    }
}
