package com.medusa.gruul.storage.service.mq;

import com.medusa.gruul.common.mq.rabbit.RabbitConstant;
import com.medusa.gruul.goods.api.model.dto.ProductDeleteDTO;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.bo.StSvBo;
import com.medusa.gruul.storage.service.service.SkuStockService;
import com.medusa.gruul.storage.service.util.StorageUtil;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.converter.SmartMessageConverter;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/6/9
 */
@Component
@RequiredArgsConstructor
public class StorageRabbitListener {

    private final SkuStockService skuStockService;
    private final SmartMessageConverter smartMessageConverter;

    /**
     * 更新sku库存
     */
    @SuppressWarnings("unchecked")
    @RabbitListener(queues = StorageQueueNames.UPDATE_SKU_STOCK_QUEUE, containerFactory = RabbitConstant.BATCH_LISTENER_CONTAINER_FACTORY, executor = "storageTaskExecutor")
    public void updateSkuStock(List<Message> messages, Channel channel) throws IOException {
        skuStockService.updateSkuStockDb(
                //反序列化 合并相同key的库存销量
                messages.stream()
                        .flatMap(message -> ((Set<SkuKeyStSvBO>) smartMessageConverter.fromMessage(message)).stream())
                        .collect(
                                Collectors.groupingBy(
                                        SkuKeyStSvBO::getKey,
                                        Collectors.reducing(
                                                (currentBo, nextBo) -> {
                                                    StSvBo current = currentBo.getStSv();
                                                    StSvBo next = nextBo.getStSv();
                                                    current.setStock(current.getStock() + next.getStock());
                                                    current.setSales(current.getSales() + next.getSales());
                                                    if (next.getStockType() != null) {
                                                        current.setStockType(next.getStockType());
                                                    }
                                                    return currentBo;
                                                }
                                        )
                                )
                        ).values()
                        .stream()
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toSet())
        );
        //批量 确认消息
        for (Message message : messages) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        }
    }

    /**
     * 订单创建失败
     */
    @RabbitListener(queues = StorageQueueNames.ORDER_CREATE_FAILED_QUEUE)
    public void orderCreateFailed(OrderInfo orderInfo, Channel channel, Message message) throws IOException {
        StorageUtil.orderNoCheck(
                orderInfo.getOrderNo(),
                () -> {
                },
                () -> skuStockService.orderCloseHandBatch(Collections.singletonList(orderInfo))
        );
        //批量 确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }


    /**
     * 订单关闭
     */
    @RabbitListener(queues = StorageQueueNames.ORDER_CLOSED, containerFactory = RabbitConstant.BATCH_LISTENER_CONTAINER_FACTORY, executor = "storageTaskExecutor")
    public void orderClose(List<Message> messages, Channel channel) throws IOException {
        skuStockService.orderCloseHandBatch(
                //反序列化
                messages.stream()
                        .map(message -> (OrderInfo) smartMessageConverter.fromMessage(message))
                        .collect(Collectors.toList())
        );
        //批量 确认消息
        for (Message message : messages) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        }
    }


    /**
     * 删除库存 sku
     *
     * @param productDeleteDTO delete
     */
    @RabbitListener(queues = StorageQueueNames.PRODUCT_DELETE)
    public void productDeleteStorageSku(ProductDeleteDTO productDeleteDTO, Channel channel, Message message) throws IOException {
        skuStockService.productDeleteStorageSku(productDeleteDTO);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

}
