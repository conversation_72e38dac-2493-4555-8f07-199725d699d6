package com.medusa.gruul.storage.service.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * date 2022/6/21
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ProductStorageDTO {
    /**
     * 商品id
     */
    @NotNull
    private Long productId;
    /**
     * sku 仓储信息
     */
    @NotNull
    @Valid
    @Size(min = 1)
    private List<ProductSkuLimitDTO> skuStocks;
}
