package com.medusa.gruul.storage.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.medusa.gruul.common.model.base.ActivityShopProductSkuKey;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.global.model.helper.CompletableTask;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.storage.api.bo.OrderStockBO;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.dto.SkuCopy;
import com.medusa.gruul.storage.api.dto.StoragesCopyDTO;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.enums.StockChangeType;
import com.medusa.gruul.storage.service.addon.StorageAddonSupporter;
import com.medusa.gruul.storage.service.mp.entity.StorageDetail;
import com.medusa.gruul.storage.service.mp.service.IStorageDetailService;
import com.medusa.gruul.storage.service.mp.service.IStorageSkuService;
import com.medusa.gruul.storage.service.service.SkuStockDetailService;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2023/8/3
 */
@Service
@RequiredArgsConstructor
public class SkuStockDetailServiceImpl implements SkuStockDetailService {

    private final Executor storageExecutor;
    private final GoodsRpcService goodsRpcService;
    private final StorageAddonSupporter storageAddonSupporter;
    private final IStorageDetailService storageDetailService;
    private final IStorageSkuService storageSkuService;
    private ShopRpcService shopRpcService;

    @Override
    @Async("storageExecutor")
    public void generateStockDetails(List<OrderStockBO> orderStocks) {
        Map<Long, Set<ShopProductKey>> shopIdProductKeysMap = this.shopIdToProductKeyMap(orderStocks);
        if (CollUtil.isEmpty(shopIdProductKeysMap)) {
            return;
        }
        Tuple2<Set<ShopProductKey>, Set<ShopProductKey>> shopAndSupplierProductKeysTuple = this.shopAndSupplierProductKeys(shopIdProductKeysMap);
        Set<ShopProductKey> shopProductKeys = shopAndSupplierProductKeysTuple._1();
        Set<ShopProductKey> supplierProductKeys = shopAndSupplierProductKeysTuple._2();
        if (CollUtil.isEmpty(shopProductKeys) && CollUtil.isEmpty(supplierProductKeys)) {
            return;
        }
        //批量查询供应商的商品信息
        CompletableFuture<Map<ShopProductKey, Product>> supplierProductsMapTask = CompletableFuture.supplyAsync(
                () -> {
                    if (CollUtil.isEmpty(supplierProductKeys)) {
                        return Map.of();
                    }
                    Map<ShopProductKey, Product> result = storageAddonSupporter.getSupplierProductBatch(supplierProductKeys);
                    return result == null ? Map.of() : result;
                },
                storageExecutor
        );

        //获取所有商品信息
        Map<ShopProductKey, Product> productMap = new HashMap<>(CommonPool.NUMBER_FIFTEEN);
        ////rpc 批量查询店铺的商品信息
        productMap.putAll(CollUtil.isEmpty(shopProductKeys) ? Map.of() : goodsRpcService.getProductBatch(shopProductKeys));
        productMap.putAll(CompletableTask.getOrThrowException(supplierProductsMapTask));

        Set<ActivityShopProductSkuKey> collect = orderStocks.stream()
                .map(OrderStockBO::getSkuKeyStSvs)
                .flatMap(Set::stream)
                .filter(skuKeyStSvBO -> CollUtil.isNotEmpty(skuKeyStSvBO.getStSv().getSpecName()))
                .map(SkuKeyStSvBO::getKey)
                .collect(Collectors.toSet());
        Map<ActivityShopProductSkuKey, StorageSku> skusBatch;
        if (CollUtil.isNotEmpty(collect)) {
            List<StorageSku> skus = storageSkuService.getSkusBatch(collect);
            skusBatch = skus.stream()
                    .collect(
                            Collectors.toMap(
                                    sku -> {
                                        ActivityShopProductSkuKey key = new ActivityShopProductSkuKey().setSkuId(sku.getId());
                                        key.setProductId(sku.getProductId()).setShopId(sku.getShopId()).setActivityType(sku.getActivityType()).setActivityId(sku.getActivityId());
                                        return key;
                                    },
                                    v -> v
                            )
                    );
        } else {
            skusBatch = null;
        }

        storageDetailService.saveBatch(
                orderStocks.stream()
                        .flatMap(orderStock -> {
                                    String orderNo = orderStock.getNo();
                                    return orderStock.getSkuKeyStSvs()
                                            .stream()
                                            //库存变化为0得不进行更新
                                            .filter(skuKeyStSvBO -> skuKeyStSvBO.getStSv().getStock() != CommonPool.NUMBER_ZERO)
                                            .map(
                                                    stSvBo -> {
                                                        ActivityShopProductSkuKey currentKey = stSvBo.getKey();
                                                        String productName = "unknown";
                                                        SellType sellType = SellType.OWN;
                                                        Product product = productMap.get(new ShopProductKey().setProductId(currentKey.getProductId()).setShopId(currentKey.getShopId()));
                                                        if (product != null) {
                                                            productName = product.getName();
                                                            sellType = product.getSellType();
                                                        }
                                                        return new StorageDetail()
                                                                .setShopId(currentKey.getShopId())
                                                                .setSkuId(currentKey.getSkuId())
                                                                .setProductId(currentKey.getProductId())
                                                                .setStockChangeType(stSvBo.getStockChangeType())
                                                                .setOrderNo(orderNo)
                                                                .setStockChangeNum((long) stSvBo.getStSv().getStock())
                                                                .setSpecName(stSvBo.getStSv().getSpecName() != null ? stSvBo.getStSv().getSpecName() : CollUtil.isNotEmpty(skusBatch) ? skusBatch.get(currentKey).getSpecs() : new ArrayList<>())
                                                                .setProductName(productName)
                                                                .setSellType(sellType);
                                                    }
                                            );
                                }
                        ).toList()
        );
    }

    @Override
    public void purchaseToStorageDetails(StoragesCopyDTO storagesCopy) {
        Set<SkuCopy> targets = storagesCopy.getTargets();

        List<StorageDetail> storageDetails = targets.stream()
                .filter(target -> target.getNum() > 0)
                .map(target -> new StorageDetail()
                        .setSellType(SellType.PURCHASE)
                        .setShopId(storagesCopy.getTargetShopId())
                        .setProductId(target.getSourceKey().getProductId())
                        .setSkuId(target.getSourceKey().getSkuId())
                        .setStockChangeType(StockChangeType.PURCHASE_INBOUND)
                        .setOrderNo(storagesCopy.getTransactionId())
                        .setSpecName(List.of())
                        .setStockChangeNum((long) target.getNum())
                        .setProductName(target.getProductName()))
                .collect(Collectors.toList());

        storageDetailService.saveBatch(storageDetails);
    }


    /**
     * 获取店铺 id 与对应的商品 key 集合
     *
     * @param orderStocks 订单库存信息
     * @return key:shopId value:productKeys
     */
    private Map<Long, Set<ShopProductKey>> shopIdToProductKeyMap(List<OrderStockBO> orderStocks) {
        if (CollUtil.isEmpty(orderStocks)) {
            return Map.of();
        }
        Map<Long, Set<ShopProductKey>> shopIdToProductKeyMap = new HashMap<>(orderStocks.size());
        for (OrderStockBO orderStock : orderStocks) {
            Set<SkuKeyStSvBO> skuKeyStSvs = orderStock.getSkuKeyStSvs();
            if (CollUtil.isEmpty(skuKeyStSvs)) {
                continue;
            }
            for (SkuKeyStSvBO skuKeyStSv : skuKeyStSvs) {
                ActivityShopProductSkuKey key = skuKeyStSv.getKey();
                ShopProductKey shopProductKey = new ShopProductKey()
                        .setProductId(key.getProductId())
                        .setShopId(key.getShopId());
                shopIdToProductKeyMap.computeIfAbsent(key.getShopId(), k -> new HashSet<>()).add(shopProductKey);
            }
        }
        return shopIdToProductKeyMap;
    }

    /**
     * 根据是否是供应商分组
     *
     * @param shopIdProductKeysMap key:shopId value:productKeys
     * @return tuple._1() 为非供应商店铺的productKeys tuple._2() 为供应商店铺的productKeys
     */
    private Tuple2<Set<ShopProductKey>, Set<ShopProductKey>> shopAndSupplierProductKeys(Map<Long, Set<ShopProductKey>> shopIdProductKeysMap) {
        Tuple2<Set<ShopProductKey>, Set<ShopProductKey>> tuple = new Tuple2<>(new HashSet<>(), new HashSet<>());
        // rpc 获取shopList
        List<ShopInfoVO> shopInfos = shopRpcService.getShopInfoByShopIdList(shopIdProductKeysMap.keySet());
        if (CollUtil.isEmpty(shopInfos)) {
            return tuple;
        }
        Map<Long, Boolean> isSupplierShopIdsMap = shopInfos.stream()
                .collect(
                        Collectors.toMap(
                                ShopInfoVO::getId,
                                shopInfoVO -> shopInfoVO.getShopMode() == ShopMode.SUPPLIER
                        )
                );
        shopIdProductKeysMap.forEach(
                (key, productKeys) -> {
                    Boolean isSupplier = isSupplierShopIdsMap.get(key);
                    if (isSupplier == null) {
                        return;
                    }
                    (isSupplier ? tuple._2() : tuple._1()).addAll(productKeys);
                }
        );
        return tuple;
    }

    @Lazy
    @Autowired
    public void setShopRpcService(ShopRpcService shopRpcService) {
        this.shopRpcService = shopRpcService;
    }
}
