package com.medusa.gruul.storage.service.model.vo;

import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * date 2022/7/15
 */
@Getter
@Setter
@ToString
public class ProductSkuVO {
    /**
     * id
     */
    private Long id;
    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 商品id
     */
    private Long productId;
    /**
     * 库存类型 1 无限库存 2 有限
     */
    private StockType stockType;
    /**
     * 商品库存
     */
    private Long stock;
    /**
     * 销量
     */
    private Long salesVolume;
    /**
     * 初始销量
     */
    private Integer initSalesVolume;
    /**
     * 限购类型 1 不限购 , 2 商品限购 , 3 规格限购
     */
    private LimitType limitType;
    /**
     * 限购数量
     */
    private Integer limitNum;
    /**
     * 规格id列表
     */
    private List<Long> specsIds;
    /**
     * 规格名称列表
     */
    private List<String> specs;
    /**
     * sku图片
     */
    private String image;
    /**
     * 原价 单位 豪 1豪 = 0.01分
     */
    private Long price;
    /**
     * 真实销售价 单位豪 1豪 = 0.01分
     */
    private Long salePrice;
    /**
     * 重量
     */
    private BigDecimal weight;
}
