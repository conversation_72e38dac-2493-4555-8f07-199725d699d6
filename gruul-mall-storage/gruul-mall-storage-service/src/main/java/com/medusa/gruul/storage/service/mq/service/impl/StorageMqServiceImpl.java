package com.medusa.gruul.storage.service.mq.service.impl;

import com.medusa.gruul.search.api.enums.SearchRabbit;
import com.medusa.gruul.storage.api.bo.SkuKeyStSvBO;
import com.medusa.gruul.storage.api.enums.StorageRabbit;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;
import com.medusa.gruul.storage.service.mq.service.StorageMqService;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * date 2023/8/3
 */
@Service
@RequiredArgsConstructor
public class StorageMqServiceImpl implements StorageMqService {

    private final RabbitTemplate rabbitTemplate;

    @Override
    public void sendUpdateStockMsg(Set<SkuKeyStSvBO> skuKeyStSvs) {
        rabbitTemplate.convertAndSend(
                StorageRabbit.UPDATE_SKU_STOCK.exchange(),
                StorageRabbit.UPDATE_SKU_STOCK.routingKey(),
                skuKeyStSvs
        );
    }

    @Override
    public void sendUpdateSkuPriceMsg(ProductSkusVO productSkusVO) {
        rabbitTemplate.convertAndSend(
                SearchRabbit.UPDATE_SKU_PRICE.exchange(),
                SearchRabbit.UPDATE_SKU_PRICE.routingKey(),
                productSkusVO
        );
    }
}
