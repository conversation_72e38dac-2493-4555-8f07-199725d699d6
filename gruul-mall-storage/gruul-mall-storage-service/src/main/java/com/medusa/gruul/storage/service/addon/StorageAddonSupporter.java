package com.medusa.gruul.storage.service.addon;

import com.medusa.gruul.common.addon.supporter.annotation.AddonMethod;
import com.medusa.gruul.common.addon.supporter.annotation.AddonSupporter;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.goods.api.entity.Product;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description StorageAddonSupporter.java
 * @date 2023-08-02 14:02
 */
@AddonSupporter(id = "storageAddonSupporter")
public interface StorageAddonSupporter {

    /**
     * 供应商商品批量查询
     *
     * @param shopProductKeys key
     * @return Map<ShopProductKey, Product>
     */
    @AddonMethod(returnType = Map.class)
    Map<ShopProductKey, Product> getSupplierProductBatch(Set<ShopProductKey> shopProductKeys);
}
